import http, { httpUpload } from '@/utils/http'
import { dataType, successCallbackResult } from '../types'

// AI 语音识别文字
export const recognition = (data : dataType = {}) : Promise<successCallbackResult> => {
	return httpUpload({
		url: '/ai/speech/tencent/recognition/file',
		method: 'POST',
		...data
	})
}

// AI 语音合成
export const synthesis = (data : dataType = {}) : Promise<successCallbackResult> => {
	return httpUpload({
		url: '/ai/speech/tencent/recognition/synthesis',
		method: 'POST',
		...data
	})
}

// 历史列表
export const history = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/page',
		method: 'POST',
		...data
	})
}

//oss文件上传
export const ossUpload = (data : dataType = {}) : Promise<successCallbackResult> => {
	return httpUpload({
		url: '/oss/upload',
		method: 'POST',
		...data
	})
}

/**
 * @description: 评论
 */
export const comment = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/comment',
		method: 'POST',
		...data
	})
}

/**
 * @description: 删除聊天记录
 */
export const delComment = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/delete',
		method: 'POST',
		...data
	})
}

/**
 * @description: 会话分组列表
 */
export const sessionGroup = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/session/group',
		method: 'POST',
		...data
	})
}

/**
 * @description: 删除聊天会话
 */
export const sessionDelete = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/session/delete',
		method: 'POST',
		...data
	})
}

/**
 * @description: 今天的会话sid
 */
export const findTodayLastSid = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/history/findTodayLastSid',
		method: 'POST',
		...data
	})
}

// 语音识别(oss)
export const recognitionOss = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/ai/speech/tencent/recognition/oss',
		method: 'POST',
		...data
	})
}

// 文件列表
export const page = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/fileInfo/page',
		method: 'POST',
		...data
	})
}

// 获取昨日经营数据
export const queryBusinessYesterdayData = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryBusinessYesterdayData',
		method: 'POST',
		...data,
		need: true
	})
}

// 获取本月经营数据
export const queryBusinessMonthData = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryBusinessMonthData',
		method: 'POST',
		...data,
		need: true
	})
}

// 获取今日售卡数据
export const queryTodayCardSalesDataRequest = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryTodayCardSalesDataRequest',
		method: 'POST',
		...data,
		need: true
	})
}

// 获取昨日售卡数据
export const queryYesterdayCardSalesData = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryYesterdayCardSalesData',
		method: 'POST',
		...data,
		need: true
	})
}

// 获取本月售卡数据
export const queryMonthCardSalesData = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryMonthCardSalesData',
		method: 'POST',
		...data,
		need: true
	})
}

// 本月舆情数据
export const queryMonthPublicOpinionData = (data : dataType = {}) : Promise<successCallbackResult> => {
	return http({
		url: '/labelData/queryMonthPublicOpinionData',
		method: 'POST',
		...data,
		need: true
	})
}

// 通过文件名获取文件链接
export const downloadByFileName = (data : dataType = {}, fileName : string,) => {
	return http({
		url: `/oss/download/${fileName}`,
		method: 'GET',
		...data
	})
}

// 通过文件id取文件链接
export const downloadByFileId = (data : dataType = {}, id : string,) => {
	return http({
		url: `/fileInfo/${id}`,
		method: 'GET',
		...data
	})
}