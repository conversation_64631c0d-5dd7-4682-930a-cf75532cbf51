import dayjs from "dayjs";
import { tabItem } from "@/types/index.d";
import { queryBusinessMonthData, queryBusinessYesterdayData, queryMonthCardSalesData, queryMonthPublicOpinionData, queryTodayCardSalesDataRequest, queryYesterdayCardSalesData } from "../api/ai";

export const data : readonly tabItem[] = [
	{
		label: '昨日经营数据',
		value: 0
	},
	{
		label: '本月经营数据',
		value: 1
	},
	{
		label: '今日售卡数据',
		value: 2
	},
	{
		label: '昨日售卡数据',
		value: 3
	}
]

export const labels : readonly tabItem[] = [
	{
		label: '本月售卡数据',
		value: 4
	},
	{
		label: '本月舆情数据',
		value: 5
	},
	{
		label: '政策速达',
		value: 6
	}
]

export const reportFormApiEnum = {
	1: { // 获取昨日经营数据
		api: queryBusinessYesterdayData,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
				dateType: 'day',
				orgId,
				orgLevel,
				title: `${name}，昨日经营数据如下：`
			}
		},
		options: {
			activeName: 'dataCockpit',
			tabName: 'operations',
			dateType: 'day'
		}
	},
	2: { // 获取本月经营数据
		api: queryBusinessMonthData,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().format('YYYY-MM'),
				dateType: 'month',
				orgId,
				orgLevel,
				title: `${name}，本月经营数据如下：`
			}
		},
		options: {
			activeName: 'dataCockpit',
			tabName: 'operations',
			dateType: 'month'
		}
	},
	3: { // 获取今日售卡数据
		api: queryTodayCardSalesDataRequest,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().format('YYYY-MM-DD'),
				dateType: 'day',
				orgId,
				orgLevel,
				title: `${name}，今日售卡数据如下：`
			}
		}
	},
	4: { // 获取昨日售卡数据
		api: queryYesterdayCardSalesData,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
				dateType: 'day',
				orgId,
				orgLevel,
				title: `${name}，昨日售卡数据如下：`
			}
		},
		options: {
			activeName: 'dataCockpit',
			tabName: 'member',
			dateType: 'day'
		}
	},
	5: { // 获取本月售卡数据
		api: queryMonthCardSalesData,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().format('YYYY-MM'),
				dateType: 'month',
				orgId,
				orgLevel,
				title: `${name}，本月售卡数据如下：`
			}
		},
		options: {
			activeName: 'dataCockpit',
			tabName: 'member',
			dateType: 'month'
		}
	},
	7: { // 本月舆情数据
		api: queryMonthPublicOpinionData,
		params: (orgId : string, orgLevel : string, name : string) => {
			return {
				queryDate: dayjs().format('YYYY-MM'),
				dateType: 1,
				orgId,
				orgLevel,
				title: `${name}，本月舆情数据如下：`
			}
		},
		options: {
			activeName: 'dataCockpit',
			tabName: 'monitor',
			dateType: 'month'
		}
	}
}

export type listItemProp = {
	data : listItemDataProp[];
	label : number;
	[k : string] : any;
}