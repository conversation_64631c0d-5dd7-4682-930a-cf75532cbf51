<template>
	<view class="fl base">
		<slot></slot>
		<image src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-07/1730959801118.png"
			lazy-load mode="aspectFill" class="ai-logo"></image>
		<text class="ai-tip">我是尚小美，您的AI店长助手，您在运营酒店的过程中，有什么问题都可以问我哦～</text>
		<view class="base-tags" :class="{'ele-step': step1}">
			<scroll-view scroll-x="true" class="scroll-view">
				<view class="item" v-for="item in data" :key="item.value" @tap="addtag(item)">
					<text>{{ item.label }}</text>
				</view>
			</scroll-view>
			<cGuideContentVue v-model="step1" content="3、点击标签后，AI店长助手给出对应的数据" :current="3" top="240rpx"
				@complete="topInstance?.setStepValue()" />
			<scroll-view scroll-x="true" class="scroll-view">
				<view class="item" v-for="item in data1" :key="item.value" @tap="addtag(item)">
					<text>{{ item.label }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
	<cGuideMaskVue v-model="step1" />
</template>

<script setup lang="ts">
	import { computed, getCurrentInstance, inject, unref } from 'vue';
	import { setTime } from '@/utils/util';
	import { dataType, tabItem } from '@/types/index.d'
	import { debounce } from '@/utils/util';
	import cGuideContentVue from '@/components/c-guide/c-guide-content.vue';
	import cGuideMaskVue from '@/components/c-guide/c-guide-mask.vue';
	import { labels as data1 } from '../enums'
	defineProps({
		data: {
			type: Array<tabItem>,
			default: () => []
		}
	})

	const emits = defineEmits(['addTag']),
		instance = getCurrentInstance(),
		topInstance : dataType = inject('topInstance'),
		step1 = computed(() => {
			return topInstance?.stepValue?.value == 3
		})
	const addtag = (item : tabItem) => {
		if (unref(step1)) return
		instance.appContext.config.globalProperties.$uma.trackEvent('012', { from: item.label })
		emits('addTag', item)
	}
</script>

<style scoped lang="scss">
	.base {
    background: linear-gradient(3.4deg, #ffb2a100 0%, #fa81661a 32%, #f75e3b33 100%);

		&-tags {
			margin-top: 6rpx;
		}

		.ai-logo {
			width: 240rpx;
			height: 240rpx;
			margin: 0 auto;
		}

		.ai-tip {
			padding: 20rpx 28rpx;
			margin: 0 24rpx;
			font-weight: bold;
			font-size: 30rpx;
			border: 2rpx solid #ffffff;
			background: linear-gradient(90deg, #ffffffcc 0%, #ffffff4d 100%);
			border-radius: 0 32rpx 32rpx 32rpx;
		}

		.ele-step {
			position: relative;
			z-index: 111;
			touch-action: none;

			.scroll-view {
				pointer-events: none;
			}
		}

		.scroll-view {
			white-space: nowrap;

			.item {
				display: inline-block;
				padding: 22rpx 20rpx;
				min-width: 184rpx;
				margin-top: 24rpx;
				margin-right: 16rpx;
				border: 1rpx solid #cccccc99;
				font-size: 24rpx;
				font-weight: bold;
				color: #444;
				text-align: center;
				background: #fff;
				border-radius: 200rpx;

				&:first-child {
					margin-left: 24rpx;
				}
			}
		}
	}
</style>