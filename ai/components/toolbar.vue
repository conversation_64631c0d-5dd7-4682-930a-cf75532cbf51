<template>
	<view class="fr thumbs-up">
		<label v-if="feedback.content.text" class="fr copy" @tap="onCopy">
			<image
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-24/1740385589113.png"
				mode="aspectFill" lazy-load class="icon-copy"></image>
			<text class="copy__text">复制</text>
		</label>
		<label class="fr" @tap="giveTheThumbsUp">
			<image v-if="feedback.state == 0"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-03/1735886702150.png"
				mode="aspectFill" lazy-load class="thumbs-up__icon"></image>
			<image v-if="feedback.state == 1"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-14/1744622158223.png"
				mode="aspectFill" lazy-load class="thumbs-up__icon"></image>
			<text v-if="feedback.state == 0 || feedback.state == 1" class="thumbs-up__text"
				:class="{'theme-primary': feedback.state == 1}">满意</text>
		</label>
		<label class="fr" @tap="oppose">
			<image v-if="feedback.state == 0"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-03/1735886780385.png"
				mode="aspectFill" lazy-load class="thumbs-up__icon"></image>
			<image v-if="feedback.state == 2"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-14/1744622186824.png"
				mode="aspectFill" lazy-load class="thumbs-up__icon"></image>
			<text v-if="feedback.state == 0 || feedback.state == 2" class="thumbs-up__text"
				:class="{'theme-primary': feedback.state == 2}">不满意</text>
		</label>

	</view>
	<opposeFeedbackVue ref="opposeFeedbackRef" @success="onSuccessOpposeFeedback" />
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import { trackEvent } from 'umtrack-wx';
	import opposeFeedbackVue from './opposeFeedback.vue';
	import { comment } from '../api/ai';
	import { showToast } from '@/utils/util';
	const props = defineProps({
		feedback: {
			type: Object,
			defualt: () => { }
		},
		botCode: {
			type: Number,
			default: 1
		},
		fn: {
			type: Function,
			default: () => { }
		}
	})

	const opposeFeedbackRef = ref<InstanceType<typeof opposeFeedbackVue>>(null)

	const onSuccessOpposeFeedback = () => {
		props.feedback.state = 2
	}

	/**
	 * @description: 反对
	 */
	const oppose = () => {
		if(props.feedback.state == 0){
			// trackEvent('069')
			opposeFeedbackRef.value.open({
				botCode: props.botCode,
				state: 2,
				requestId: props.feedback.requestId
			})
		} else {
			giveTheThumbsUp()
		}
	}

	/**
	 * @description: 点赞
	 */
	const giveTheThumbsUp = async () => {
		let state = 1
		if (props.feedback.state == 0) {
			state = 1
			// trackEvent('068')
		} else {
			state = 0
		}
		try {
			await comment({
				APP_BASE_URL: 'aiURL',
				header: props.fn(),
				data: {
					botCode: props.botCode,
					state: state,
					requestId: props.feedback.requestId,
					comments: []
				}
			})
			props.feedback.state = state
			if (state == 1) {
				showToast('感谢您的反馈')
			}
		} catch (err) {
			console.log("🚀 ~ giveTheThumbsUp ~ err:", err)
		}
	}
	/**
	 * @description: 复制
	 */
	const onCopy = () => {
		uni.setClipboardData({
			data: props.feedback.content.text,
			success: () => {
				showToast('已复制')
			}
		})
	}
</script>

<style scoped lang="scss">
	.thumbs-up {
		max-width: 630rpx;
		padding-left: 96rpx;
		margin-bottom: 20rpx;
		justify-content: flex-start;
		color: #999;

		&__icon {
			width: 30rpx;
			height: 31.58rpx;
			margin-right: 12rpx;
		}

		&__text {
			margin-right: 40rpx;
			font-size: 24rpx;
			font-weight: bold;
		}
	}
	
	.icon-copy {
		flex-shrink: 0;
		width: 30rpx;
		height: 30rpx;
		margin-right: 12rpx;
	}

	.copy {
		margin-right: 40rpx;
		font-size: 24rpx;
		
		&__text{
			font-weight: bold;
		}
	}
</style>