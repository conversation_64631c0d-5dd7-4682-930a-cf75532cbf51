<template>
	<view class="fr">
		<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl echarts-line">
			<routeLinkVue v-if="content?.detailLink" :data="{ title: content?.detailText, path: content?.detailLink }"/>
			<view class="echarts-line__title">
				<text class="echarts-line__title-text">{{ content?.title}}</text>
			</view>
			<view class="fr canvas" v-if="content?.data">
				<view class="canvas-wrap">
					<qiun-data-charts :canvasId="id" :canvas2d="true" type="pie" :opts="opts"
						:chart-data="chartData"></qiun-data-charts>
				</view>
				<view class="fl legend" v-if="chartData.series">
					<view class="fr legend-item" v-for="(item, index) in chartData.series[0].data" :key="index">
						<text class="legend-shape" :style="{background: opts.color[index]}"></text>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</view>
			<view class="fr" v-else>
				<text>暂无数据</text>
			</view>
		</view>
	</view>

</template>

<script setup>
	import {
		getCurrentInstance,
		onMounted,
		ref
	} from 'vue';
	import routeLinkVue from './routeLink.vue';
	const props = defineProps({
		content: {
			type: Object,
			default: () => {}
		},
		id: {
			type: [Number, String],
			required: true
		}
	})
	let chartData = ref({}),
		opts = ref({
			width: uni.upx2px(300),
			height: uni.upx2px(300),
			fontSize: uni.upx2px(20),
			animation: true,
			dataLabel: false,
			background: "#FFFFFF",
			color: ["#EB8F83", "#D6AD45", "#BFED9A", "#27D6D6", "#8178FF", "#3CA272", "#FC8452", "#9A60B4",
				"#ea7ccc"
			],
			padding: [0, 5, 0, 5],
			enableScroll: false,
			legend: {
				show: false,
				position: 'right',
				float: 'right',
				padding: uni.upx2px(5),
				lineHeight: uni.upx2px(48),
				fontColor: '#707070',
			},

			extra: {
				pie: {
					activeOpacity: 0.5,
					activeRadius: 0,
					offsetAngle: 0,
					labelWidth: 0,
					border: false,
					borderWidth: 0
				}
			}
		})
	const setCharts = () => {
		chartData.value = {
			series: [{
				legendShape: 'rect',
				data: props.content.data.map(item => {
					const name = `${item.name}:${item.count}(${item.percentage})`
					return {
						name: name,
						value: Number(item.count == '-' ? 0 : item.count),
					}
				})
			}],
		}
	}
	onMounted(() => {
		if (props.content.data) {
			setCharts()
		}
	})
</script>

<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.echarts-line {
		overflow: hidden;
		padding: 20rpx 28rpx 20rpx 50rpx;
		margin: 0 60rpx 20rpx;
		background-color: #F5F5F5;
		border-radius: 16rpx;

		&__title {
			margin-bottom: 20rpx;
			font-weight: bold;

			&-text {
				font-size: 30rpx;
			}
		}
	}

	.canvas {
		&-wrap {
			flex-shrink: 0;
			width: 300rpx;
			height: 300rpx;
			margin-right: 40rpx;
		}

		.legend {
			font-size: 20rpx;
			color: #707070;

			&-item {
				margin-bottom: 24rpx;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}

		.legend-shape {
			flex-shrink: 0;
			width: 16rpx;
			height: 16rpx;
			margin-right: 8rpx;
			border-radius: 4rpx;
		}
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		width: 72rpx;
		height: 72rpx;
	}
</style>