<template>
	<view class="fr send" v-if="content.sender == 'user'">
		<text user-select class="send-text">{{ content.text }}</text>
	</view>
	<view v-else class="fr answer">
		<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl send-text__left rich-text">
			<!-- #ifdef MP-WEIXIN -->
			<towxml v-if="isFlow == 1" :nodes="typewriter"></towxml>
			<!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<rich-text style="font-size: 30rpx !important;" v-if="isFlow != 1 && content.current" class="rich-text__tag"
				:nodes="addStyleToPre(marked(markdownText))"></rich-text>
			<rich-text style="font-size: 30rpx !important;" v-if="isFlow != 1 && !content.current" class="rich-text__tag"
				:nodes="addStyleToPre(marked(content.text))"></rich-text>
			<!-- #endif -->
			<view class="fl linked" v-if="attr">
				<!-- 标题 -->
				<view class="fl__title">
					<text class="fl__title-text">{{ attr?.content?.title}}</text>
				</view>
				<!-- 如果data是对象 -->
				<template v-if="isObject(attr?.content?.data)">
					<view class="fr" @tap="jump">
						<text class="theme-primary">{{ attr?.content?.data?.title }}</text>
					</view>
				</template>
				<!-- 如果data是数组 -->
				<template v-else-if="Array.isArray(attr?.content?.data)">
					<view class="fr" v-for="(item, index) in attr?.content?.data" :key="index" @tap="openDocument(item)">
						<text class="theme-primary">{{ item?.title }}</text>
					</view>
				</template>
			</view>
		</view>
	</view>
	<view v-if="videoSrc">
		<videoFullScrrenVue ref="videoFullScreenRef" :videoUrl="videoSrc" @close="onclose"/>
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		computed,
		getCurrentInstance,
		onUnmounted,
		inject,
		nextTick
	} from 'vue';
	// #ifndef MP-WEIXIN
	import {
		marked
	} from 'marked'
	// #endif
	import videoFullScrrenVue from './videoFullScreen.vue'
	import { fileExtensions } from '@/utils/util'
	import { useCommon } from '@/hooks/useGlobalData'
	const props = defineProps({
		content: {
			type: Object,
			default: () => {}
		},
		isFlow: {
			type: Number,
			default: 0
		},
		attr: {
			type: Object,
			default: null
		}
	})
	const topInstance = inject('topInstance')
	const instance = getCurrentInstance()
	const { route_to_view } = useCommon(instance)
	const videoSrc = ref(null)
	const videoFullScreenRef = ref(null)
	// #ifndef MP-WEIXIN
	const emits = defineEmits(['update', 'complete'])
	let interval = null
	let index = ref(0)
	let markdownText = ref('')
	const parseMarkdown = (text) => {
		clearInterval(interval)
		if (index.value == 0) {
			markdownText.value = ''
		}
		const arr = text.split('')
		interval = setInterval(() => {
			if (index.value < arr.length) {
				const str = arr[index.value]
				if (str) {
					markdownText.value += str
					index.value++
					emits('update')
				}
			} else {
				emits('complete')
				clearInterval(interval)
			}
		}, 1000 / 60)
	}
	
	function addStyleToPre(htmlString) {
		// 使用正则表达式找到所有的<pre>标签，并给它们添加style属性
		const updatedHtml = htmlString.replace(/<pre\b[^>]*>/gi, function(match) {
			// 检查是否已包含style属性
			if (!match.includes('style')) {
				// 如果没有style属性，添加它
				return match.replace(/>/, ' style="white-space:pre-wrap;" >');
			} else {
				// 如果已有style属性，确保white-space:pre-wrap被包含在内
				return match.replace(/(style="[^"]*")/i, function(styleMatch) {
					if (!styleMatch.includes('white-space:pre-wrap')) {
						// 如果style中没有white-space:pre-wrap，则添加它
						return styleMatch + ' white-space:pre-wrap;';
					} else {
						// 如果已有，则直接返回原样
						return styleMatch;
					}
				});
			}
		});
		return updatedHtml;
	}
	
	onMounted(() => {
		if (props.content.current) {
			parseMarkdown(props.content.text)
		}
	})
	
	onUnmounted(() => {
		clearInterval(interval)
	})
	// #endif
	
	// 跳转
	const jump = () => {
		if (props.attr?.content?.data?.linkedUrl) {
			if (props.attr.content.data.linkedUrl.includes('http') || props.attr.content.data.linkedUrl.includes('https')) {
				openDocument(props.attr.content.data)
				return
			}
			uni.navigateTo({
				url: props.attr.content.data.linkedUrl
			})
		}
	}
	
	// 打开文档
	const openDocument = (item) => {
		if (fileExtensions.test(item.linkedUrl)) {
			uni.showLoading({
				title: '加载中...'
			})
			uni.downloadFile({
				url: item.linkedUrl,
				success: (res) => {
					uni.hideLoading()
					uni.openDocument({
						filePath: res.tempFilePath,
						fileType: item.linkedUrl.split('.').pop(),
						success: () => {
							console.log('打开文档成功')
						},
						fail: (err) => {
							console.log('打开文档失败', err)
						}
					})
				},
				fail: (err) => {
					console.log("🚀 ~ file: linked.vue:75 ~ openDocument ~ err:", err)
					uni.hideLoading()
				}
			})
			return
		}
		route_to_view(`/public/pages/webview/webview?thirdLink=true&url=${encodeURIComponent(item.linkedUrl)}`)
	}
	
	// 判断是否是对象
	function isObject(value) {
		return Object.prototype.toString.call(value) === '[object Object]';
	}
	
	const typewriter = computed(() => {
		return markdown(props.content.text)
	})
	
	const markdown = (value) => {
		return instance.appContext.config.globalProperties.$towxml(value, 'markdown', {
			events: {
				tap: (e) => {
					console.log("🚀 ~ file: text.vue:60 ~ markdown ~ e:", e)
					if (e.currentTarget.dataset?.data?.tag == 'img') {
						uni.previewImage({ urls: [e.currentTarget.dataset.data.attrs.src] })
					} else if (e.currentTarget.dataset?.data?.tag == 'navigator') {
						const extions = e.currentTarget.dataset.data.attrs.href.split('.').pop()
						if (extions == 'mp4') {
							videoSrc.value = e.currentTarget.dataset.data.attrs.href
							setTimeout(() => {
								if(videoFullScreenRef.value) {
									videoFullScreenRef.value.open(0)
									topInstance.setShowHeader(false)
								}
							}, 300)
							return
						}
						uni.navigateTo({ url: '/public/pages/webview/webview?thirdLink=true&url=' + encodeURIComponent(e.currentTarget.dataset.data.attrs.href) })
					}
				}
			}
		})
	}
	
	const onclose = () => {
		topInstance.setShowHeader(true)
	}
</script>

<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.send {
		justify-content: flex-end;
	}

	.send-text {
		max-width: 630rpx;
		margin-bottom: 24rpx;
		margin-right: 24rpx;
		padding: 20rpx 28rpx;
		font-size: 30rpx;
		color: #fff;
		background-image: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
		border-radius: 48rpx 4rpx 48rpx 48rpx;
		word-break: break-all;
		user-select: auto
	}

	.send-text__left {
		overflow: hidden;
		max-width: 630rpx;
		padding-left: 35rpx;
		margin-left: 60rpx;
		margin-bottom: 24rpx;
		color: #222;
		background-color: #F5F5F5;
		background-image: none;
		word-break: break-all;
		border-radius: 32rpx;
	}

	.rich-text {
		padding: 30rpx 30rpx 30rpx 50rpx;
		user-select: auto;

		&__tag {
			// font-size: 30rpx !important;
		}
	}
	
	.linked{
		padding-top: 32rpx;
		font-size: 30rpx;
		
		.fl__title {
			margin-bottom: 20rpx;
			font-weight: bold;
		}
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}
	
	.video_xxx {
		opacity: 0;
		width: 0;
		height: 0;
		display: inline;
	}
</style>
