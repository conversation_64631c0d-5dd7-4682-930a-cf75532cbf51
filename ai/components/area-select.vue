<template>
	<view class="fr">
		<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl">
			<view class="fl__title">
				<text class="fl__title-text">{{ content?.title }}</text>
			</view>
			<view class="fr btn-section">
				<view class="btn" :class="[{'disabled':disabled}]" v-for="(item,index) in content?.data" :key="index"
					@click="!disabled ? $emit('click',item,content) : null">{{ item }}
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	const props = defineProps({
		content: {
			type: Object,
			default: () => { }
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})

	defineEmits(['click'])
</script>


<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.fl {
		overflow: hidden;
		padding: 20rpx 28rpx 20rpx 50rpx;
		margin: 0 60rpx 20rpx;
		font-size: 30rpx;
		background-color: #F5F5F5;
		border-radius: 32rpx;
		width: 630rpx;

		&__title {
			margin-bottom: 20rpx;
			font-weight: bold;
		}
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}

	.btn-section {

		.btn {
			width: 136rpx;
			height: 68rpx;
			border-radius: 200rpx;
			opacity: 1;
			border: 1rpx solid #9999994d;
			background: linear-gradient(90deg, #ffffff 0%, #ffffffcc 100%);
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 68rpx;
			text-align: center;
			margin-right: 20rpx;
		}

		.disabled {
			color: #999999;
		}
	}
</style>