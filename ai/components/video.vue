<template>
	<view class="fr answer">
		<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="send-text__left">
			<view class="video-title" v-if="content.videoTitle">{{ content.videoTitle }}</view>
			<view class="video-box" v-if="content.data">
				<video :id="id" :src="content.data" class="video" :show-center-play-btn="false" :show-fullscreen-btn="false"
					:controls="false" :style="{width: videoWidth + 'px', height: videoHeight + 'px'}" :direction="0"
					@loadedmetadata="loadedmetadata">
				</video>
				<view v-if="videoWidth" class="fr pause" @tap="onFullScreen">
					<image
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-09/1744167110682.png"
						mode="aspectFill" class="pause-image"></image>
				</view>
				<view v-if="videoWidth" class="duration">{{ formatTime(totalTime)}}</view>
			</view>
			<up-loading-icon v-if="!content.data && !videoWidth" color="#1e61fc"></up-loading-icon>
		</view>
		<view v-if="content.data">
			<videoFullScreenVue ref="videoFullScreenRef" :videoUrl="content.data" :currentTime="currentTime"
				@timeupdate="ontimeupdate" @close="onClose" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance, nextTick, onMounted, ref, inject } from 'vue';
	import videoFullScreenVue from './videoFullScreen.vue';
	const props = defineProps({
		content: {
			type: Object,
			default: () => { }
		},
		id: {
			type: String,
			default: () => { }
		}
	})
	const emits = defineEmits(['loaded'])
	const topInstance = inject('topInstance') as any
	const videoWidth = ref(0)
	const videoHeight = ref(0)
	const currentTime = ref(0)
	const totalTime = ref(0)
	const videoContext = ref<UniApp.VideoContext>(null)
	const show = ref(false)
	const videoFullScreenRef = ref<InstanceType<typeof videoFullScreenVue>>(null)

	// 当媒体加载完成时触发
	const loadedmetadata = (e : any) => {
		const { width, height, duration } = e.detail
		const ratio = width / height
		let w = width > uni.upx2px(520 * 0.7) ? uni.upx2px(520 * 0.7) : width
		let h = w / ratio
		videoWidth.value = w
		videoHeight.value = h
		totalTime.value = duration
		if (props.content.current) {
			emits('loaded')
		}
	}

	// 格式化时间
	const formatTime = (seconds : number) => {
		const hours = Math.floor(seconds / 3600); // 获取小时数
		const minutes = Math.floor((seconds % 3600) / 60); // 获取分钟数
		const remainingSeconds = Math.floor(seconds % 60); // 获取剩余秒数
		const formattedSeconds = remainingSeconds.toString().padStart(2, '0'); 		// 将秒数转换为两位数表示，例如9变为09
		const formattedMinutes = minutes.toString().padStart(2, '0');		// 将分钟数转换为两位数表示
		let timeStr = `${formattedMinutes}:${formattedSeconds}`;		// 拼接成时分秒格式，如果小时为0，则不显示小时部分
		if (hours > 0) {
			const formattedHours = hours.toString().padStart(2, '0');
			timeStr = `${formattedHours}:${timeStr}`;
		}
		return timeStr;
	}

	const ontimeupdate = (time : number) => {
		currentTime.value = time
	}

	const onFullScreen = () => {
		videoContext.value.pause()
		videoFullScreenRef.value.open(currentTime.value)
		topInstance.setShowHeader(false)
	}

	const onClose = (time : number) => {
		currentTime.value = time
		videoContext.value.seek(currentTime.value)
		topInstance.setShowHeader(true)
	}

	onMounted(() => {
		videoContext.value = uni.createVideoContext(props.id, getCurrentInstance())
	})
</script>

<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.send {
		justify-content: flex-end;
	}

	.send-text__left {
		max-width: 630rpx;
		padding: 35rpx;
		margin-left: 60rpx;
		margin-bottom: 24rpx;
		background-color: #F5F5F5;
		border-radius: 32rpx;

		.video-title {
			margin-bottom: 20rpx;
			margin-left: 24rpx;
			font-size: 30rpx;
			font-weight: bold;
		}

		.video-box {
			position: relative;
			display: inline-block;
			overflow: hidden;
			margin-left: 24rpx;
			border-radius: 16rpx;

			.pause {
				position: absolute;
				right: 0;
				left: 0;
				bottom: 0;
				top: 0;
				z-index: 1;
				justify-content: center;
				width: 100rpx;
				height: 100rpx;
				margin: auto;
				background-color: rgba(0, 0, 0, .5);
				border-radius: 100%;

				&-image {
					width: 35rpx;
					height: 46.94rpx;
				}
			}

			.duration {
				position: absolute;
				right: 10rpx;
				bottom: 10rpx;
				z-index: 1;
				padding: 8rpx 16rpx;
				font-size: 24rpx;
				color: #fff;
				border-radius: 8rpx;
				background-color: rgba(0, 0, 0, .5);
			}
		}

		.video {
			vertical-align: middle;
		}
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}
</style>