<template>
  <!-- 弹出层组件，mode为left，show为true，safeAreaInsetTop为true，关闭时触发onClose事件 -->
  <up-popup mode="left" :show="show" :safeAreaInsetTop="true" @close="onClose">
    <!-- 弹出层内容 -->
    <view class="fl popup-content">
      <!-- 弹出层头部 -->
      <view class="fl popup-content__header">
        <!-- 关闭按钮，点击时触发onClose事件 -->
        <image
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-20/1740050959409.png"
          mode="aspectFill" lazy-load class="icon-swtich" @tap="onClose"></image>
      </view>
      <!-- 用户信息 -->
      <view class="fr popup-content__user">
        <!-- 用户头像 -->
        <image :src="globalData.user?.avatar" mode="aspectFill" lazy-load class="avatar"></image>
        <view class="fl">
          <!-- 用户名 -->
          <text class="username">{{ globalData.user?.userName }}</text>
          <!-- 用户身份 -->
          <text class="user-identity">{{ globalData.user?.roleName }}</text>
          <!-- 员工ID -->
          <text class="userid">员工ID：{{ globalData.user?.userId }}</text>
        </view>
      </view>
      <!-- 弹出层主体内容，高度为contentHeight -->
      <view class="popup-content__main" :style="{height: contentHeight + 'px'}">
        <template v-for="(item, index) in historyRecords" :key="index">
          <view class="fl history-item" v-if="item.groupItems?.length">
            <view class="history-item__title">{{ item.groupName }}</view>
            <view
              class="history-item__content" hover-class="active" :class="{'active': s.sessionId == sid}"
              v-for="s in item.groupItems"
              :key="s.id" @longpress="longpress(s.id, s.sessionId)" @tap="openHistorySession(s.sessionId)">
              {{ s.sessionName }}
            </view>
          </view>
        </template>
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
  // 引入vue相关函数
  import { ref, nextTick, getCurrentInstance, onMounted } from 'vue'
  // 引入全局数据hook
  import { useCommon } from '@/hooks/useGlobalData'
  import { showModal, showToast } from '@/utils/util'
  import { sessionGroup, sessionDelete } from '../api/ai'

  type Prop = {
    getHeader: () => any;
    sid: string;
  }
  const props = withDefaults(defineProps<Prop>(), {})
  const emits = defineEmits<{
    (e: 'openHistory', args: string): void;
    (e: 'deleteHistory'): void;
    (e: 'close'): void;
    (e: 'open'): void
  }>()

  // 定义show变量，初始值为false
  const show = ref(false)
  // 获取当前实例
  const instance = getCurrentInstance()
  // 获取全局数据
  const {globalData} = useCommon(instance)
  // 定义contentHeight变量，初始值为0
  const contentHeight = ref(0)
  // 用于存储历史记录
  const historyRecords = ref([])

  // 计算弹出层主体内容的高度
  const calculatedAltitude = async () => {
    // 等待DOM更新
    await nextTick()
    // 使用uni.createSelectorQuery获取弹出层主体内容的节点信息
    uni.createSelectorQuery().in(instance).select('.popup-content__main').boundingClientRect((res: UniApp.NodeInfo) => {
      // 计算弹出层主体内容的高度
      const {screenHeight, safeAreaInsets: {bottom}} = uni.getSystemInfoSync()
      contentHeight.value = screenHeight - res.top - (bottom || 34)
    }).exec()
  }

  // 定义一个长按事件
  const longpress = async (id: number | string, sessionId: string) => {
    const res = await new Promise<UniApp.ShowModalRes>(resolve => showModal({
      title: '',
      content: '确定删除该对话吗？',
      success: (n: UniApp.ShowModalRes) => resolve(n)
    }))
    if (res.confirm) {
      await sessionDelete({
        APP_BASE_URL: 'aiURL',
        header: props.getHeader(),
        data: {id}
      })
      showToast({
        title: '删除成功',
        success: () => {
          // 重新获取历史记录列表
          getHistoryList()
          if (sessionId == props.sid) {
            emits('deleteHistory')
          }
        }
      })
    }
  }

  // 定义一个异步函数，用于获取历史记录列表
  const getHistoryList = async () => {
    try {
      const res = await sessionGroup({
        APP_BASE_URL: 'aiURL',
        header: props.getHeader(),
        data: {}
      })
      historyRecords.value = res.data
    } catch (err) {
      console.log("🚀 ~ file: sessionHistory.vue:92 ~ getHistoryList ~ err:", err)
    }
  }

  // 打开历史会话
  const openHistorySession = (sessionId: string) => {
    emits('openHistory', sessionId)
    onClose()
  }

  // 打开弹出层
  const open = async () => {
    emits('open')
    // 设置show为true
    show.value = true
    // 计算弹出层主体内容的高度
    calculatedAltitude()
    // 获取历史记录列表
    await getHistoryList()
  }

  // 关闭弹出层
  const onClose = () => {
    // 设置show为false
    show.value = false
    emits('close')
  }

  defineExpose({
    open,
    onClose
  })
</script>

<style scoped lang="scss">
  .popup-content {
    width: 70vw;

    .icon-swtich {
      align-self: flex-end;
      width: 48rpx;
      height: 36.19rpx;
      margin-right: 24rpx;
    }

    &__header {
      margin-bottom: 40rpx;
    }

    &__user {
      margin: 0 40rpx 80rpx;

      .fr {
        margin-bottom: 24rpx;
      }

      .username {
        margin-right: 32rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #000;
      }

      .userid {
        font-weight: bold;
        color: #666;
      }

      .user-identity {
        padding: 12rpx 24rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #FFFFFF;
        background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
        border-radius: 200rpx;
        text-align: center;
      }

      text {
        margin-bottom: 24rpx;
        align-self: flex-start;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .avatar {
      flex-shrink: 0;
      width: 100rpx;
      height: 100rpx;
      margin-right: 40rpx;
      border-radius: 100%;
    }

    &__main {
      overflow-y: auto;

      .history-item {
        margin-left: 20rpx;
        margin-right: 20rpx;
        margin-bottom: 60rpx;

        &__title {
          margin-bottom: 20rpx;
          font-weight: bold;
          color: #666;
          text-indent: 20rpx;
        }

        &__content {
          overflow: hidden;
          padding: 20rpx 0;
          font-size: 30rpx;
          font-weight: bold;
          text-indent: 20rpx;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #000;

          &.active {
            color: $uni-text-color;
            background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
            border-radius: 16rpx;
          }
        }
      }
    }
  }
</style>