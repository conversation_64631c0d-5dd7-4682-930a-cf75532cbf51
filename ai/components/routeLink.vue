<template>
	<view class="route-link" @tap="route_to_view(data.path)">
		{{ data.title }}
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';

	type Prop = {
		data: {
			title: string;
			path: string;
		}
	}
	
	withDefaults(defineProps<Prop>(), {})
	
	const { route_to_view } = useCommon(getCurrentInstance())
</script>

<style scoped lang="scss">
	.route-link{
		margin-bottom: 20rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: $uni-color-primary;
		text-decoration: underline;
	}
</style>