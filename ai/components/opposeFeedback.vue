<template>
  <up-popup :show="show" :safe-area-inset-bottom="false">
    <view class="fl popup-content" :style="{'padding-bottom': safeBottom + 'px'}">
      <text class="fr feedback-title">反馈</text>
      <view class="fr feedback-tags">
        <template v-for="item in tagsList" :key="item.value">
          <view
            class="fr feedback-tags__item" :class="{'feedback-tags__item-active': getActive(item.label)}"
            @tap="onTagItemClick(item.label)">
            <text>{{ item.label }}</text>
          </view>
        </template>
      </view>
      <view class="fl textarea">
				<textarea
          class="el-textarea" v-model="form.description"
          placeholder="很抱歉当前内容未能满足您的需求，为了更好地理解问题并改进服务，请您花1分钟时间告诉我们：（1）哪里不符合您的预期？（例如：信息不准确/不完整/格式问题）（2）希望如何优化？（需求、补充方向或参考案例）我们将认真分析您的建议，并尽快回复。感谢您助力尚小美成长！"
          placeholder-style="color:#999999;font-size:24rpx;" :show-confirm-bar="false" cursor-spacing="50"
          maxlength="200"></textarea>
      </view>
      <view class="fr btn-groups">
        <text class="fr btn btn-info" @tap="close">取消</text>
        <text class="fr btn btn-primary" @tap="confirm">确定</text>
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
  import { inject, nextTick, reactive, ref } from 'vue'
  import { comment } from '../api/ai'
  import { showToast } from '../../utils/util'

  defineOptions({
    options: {
      styleIsolation: "shared"
    }
  })
  const emits = defineEmits(['success'])
  const topInstance = inject('topInstance') as any

  // 反馈标签列表
  const tagsList = Object.freeze([
    {label: '答非所问', value: 1},
    {label: '未理解要求', value: 2},
    {label: '存在事实错误', value: 3},
    {label: '内容不专业', value: 4},
    {label: '系统报错', value: 5},
    {label: '存在违法信息', value: 6}
  ])
  // 控制弹窗显示
  const show = ref(false)
  const form = reactive({
    comments: [],
    description: '1'
  })
  const extra = ref({})
  const safeBottom = uni.getSystemInfoSync().safeAreaInsets.bottom || 34

  /**
   * @description: 选中状态
   */
  const getActive = (label: string): boolean => {
    return form.comments.includes(label)
  }

  // 当标签项被点击时
  const onTagItemClick = (label: string): void => {
    if (form.comments.includes(label)) {
      form.comments.splice(form.comments.indexOf(label), 1)
    } else {
      form.comments.push(label)
    }
  }
  /**
   * @description: 确定反馈
   */
  const confirm = async () => {
    let comments = [...form.comments]
    if (form.description) {
      comments.push(form.description)
    }
    await comment({
      APP_BASE_URL: 'aiURL',
      header: {
        Authorization: uni.getStorageSync('token'),
        application: 'SMART_OPERATIONS_MANAGER',
      },
      data: {
        ...extra.value,
        comments: comments
      }
    })
    showToast('感谢您的反馈')
    emits('success')
    show.value = false
  }

  /**
   * @description: 入口
   */
  const open = async (query = {}) => {
    show.value = true
    await nextTick()
    extra.value = query
    form.comments = []
    form.description = ''
    topInstance.setShowHeader(false)
  }

  const close = () =>{
    topInstance.setShowHeader(true)
    show.value = false
  }

  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
  .popup-content {
    padding-left: 20rpx;
    padding-right: 20rpx;
  }

  .feedback-title {
    justify-content: center;
    padding: 32rpx 0;
    border-bottom: 2rpx solid #F2F2F2;
  }

  .feedback-tags {
    padding: 40rpx 0 24rpx;
    flex-wrap: wrap;

    &__item {
      justify-content: center;
      width: calc((100% - 32rpx) / 3);
      height: 48rpx;
      margin-bottom: 16rpx;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      font-size: 22rpx;
      border-radius: 200rpx;
      color: #999999;

      &:nth-child(3n+2) {
        margin-left: 16rpx;
        margin-right: 16rpx;
      }

      &-active {
        background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);

        text {
          color: $uni-text-color;
        }
      }
    }
  }

  .textarea {
    height: 284rpx;
    padding: 20rpx;
    margin-bottom: 36rpx;
    border-radius: 20rpx;
    opacity: 1;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

    .el-textarea {
      width: 100%;
      font-size: 24rpx;
    }
  }

  .btn-groups {
    padding-top: 40rpx;
    padding-left: 86rpx;
    padding-right: 86rpx;
    border-top: 2rpx solid #F2F2F2;

    .btn {
      justify-content: center;
      width: 240rpx;
      height: 76rpx;
      border-radius: 8rpx;

      &-info {
        margin-right: 60rpx;
        color: $uni-text-color;
        border: 2rpx solid $uni-text-color;
      }

      &-primary {
        color: #fff;
        background-color: $uni-text-color;
      }
    }
  }
</style>