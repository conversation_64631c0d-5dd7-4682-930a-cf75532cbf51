<!--
 * @Description: 发送文字组件
 * @Author: gu_shiyuan" <EMAIL>
 * @Date: 2024-10-17 17:47:00
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2025-03-03 14:28:03
 * @FilePath: /smart-marketing-manager/ai/components/input.vue
-->

<template>
	<view class="fr input-fr" v-if="modelValue == 'input'">
		<view class="fr input-box" :class="{focus: isFocus}">
			<textarea cursor-spacing="40" catchtouchmove="false" :auto-height="true" :show-confirm-bar="false"
				:adjust-position="true" maxlength="500" v-model="text" placeholder="发消息…" class="input textarea"
				placeholder-style="font-size:30rpx;color:#999;" @confirm="onConfirm" @blur="onblur" @focus="onfoucs"></textarea>

			<view class="fr footer-btn__icon">
				<image v-if="!text" src="../images/sound.png" mode="aspectFill" lazy-load class="ai-say"
					@tap="emits('update:modelValue', 'say')"></image>
				<image v-else
					src="https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-10-11/send.png"
					mode="aspectFill" lazy-load class="ai-say" @tap="onSend"></image>
			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { nextTick, ref, watch } from 'vue';
	import { setTime } from '@/utils/util';
	defineProps({
		modelValue: {
			type: String,
			default: 'input'
		}
	})
	const isFocus = ref(false)
	const onblur = () => {
		isFocus.value = false
	}
	const onfoucs = () => {
		if (!isFocus.value) {
			isFocus.value = true
		}
	}

	const emits = defineEmits(['update:modelValue', 'send']),
		text = ref(''),
		onSend = () => {
			onConfirm({
				detail: { value: text.value }
			})
			text.value = ''
		},
		onConfirm = (e) => {
			emits('send', {
				totalTime: new Date().getTime(),
				content: {
					text: e.detail.value,
					time: setTime(),
				},
				type: 'text'
			})
			text.value = ''
		}
</script>

<style scoped lang="scss">
	.input-box {
		box-sizing: border-box;
		position: relative;
		flex: 1;
		margin: 0 24rpx;
		min-height: 100rpx;
		background: #ffffff;
		border: 1rpx solid transparent;
		border-radius: 32rpx;
		box-shadow: 0 4rpx 16rpx 0 #0000001a;

		&.focus {
			border: 1rpx solid $uni-color-primary;
		}
	}

	.textarea {
		flex: 1;
		width: auto;
		height: 100rpx;
		padding-left: 24rpx;
	}

	.footer-btn__icon {
		flex-shrink: 0;
		padding: 0 20rpx;

		.ai-say {
			width: 48rpx;
			height: 48rpx;
		}
	}
</style>