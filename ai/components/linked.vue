<template>
	<view class="fr">
		<!-- 用户头像 -->
		<image src="../images/logo_small.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl">
			<!-- 标题 -->
			<view class="fl__title">
				<text class="fl__title-text">{{ content?.title}}</text>
			</view>
			<!-- 如果data是对象 -->
			<template v-if="isObject(content?.data)">
				<view class="fr" @tap="jump">
					<text class="theme-primary">{{ content?.data?.title }}</text>
				</view>
			</template>
			<!-- 如果data是数组 -->
			<template v-else-if="Array.isArray(content?.data)">
				<view class="fr" v-for="(item, index) in content?.data" :key="index" @tap="openDocument(item)">
					<text class="theme-primary">{{ item?.title }}</text>
				</view>
			</template>
		</view>
	</view>

</template>

<script setup lang="ts">
	import { getCurrentInstance } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { fileExtensions } from '@/utils/util'
	const { route_to_view } = useCommon(getCurrentInstance())

	const props = defineProps({
		// 内容
		content: {
			type: Object,
			default: () => { }
		}
	})

	// 跳转
	const jump = () => {
		if (props.content?.data?.linkedUrl) {
			if (props.content.data.linkedUrl.includes('http') || props.content.data.linkedUrl.includes('https')) {
				openDocument(props.content.data)
				return
			}
			uni.navigateTo({
				url: props.content.data.linkedUrl
			})
		}
	}

	// 打开文档
	const openDocument = (item : any) => {
		if (fileExtensions.test(item.linkedUrl)) {
			uni.showLoading({
				title: '加载中...'
			})
			uni.downloadFile({
				url: item.linkedUrl,
				success: (res) => {
					uni.hideLoading()
					uni.openDocument({
						filePath: res.tempFilePath,
						fileType: item.linkedUrl.split('.').pop(),
						success: () => {
							console.log('打开文档成功')
						},
						fail: (err) => {
							console.log('打开文档失败', err)
						}
					})
				},
				fail: (err) => {
					console.log("🚀 ~ file: linked.vue:75 ~ openDocument ~ err:", err)
					uni.hideLoading()
				}
			})
			return
		}
		route_to_view(`/public/pages/webview/webview?thirdLink=true&url=${encodeURIComponent(item.linkedUrl)}`)
	}

	// 判断是否是对象
	function isObject(value : any) {
		return Object.prototype.toString.call(value) === '[object Object]';
	}
</script>

<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.fl {
		overflow: hidden;
		padding: 20rpx 28rpx 20rpx 50rpx;
		margin: 0 60rpx 20rpx;
		font-size: 30rpx;
		background-color: #F5F5F5;
		border-radius: 32rpx;

		&__title {
			margin-bottom: 20rpx;
			font-weight: bold;
		}
	}

	.theme-primary {
		text-decoration: underline;
		word-break: break-all;
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}
</style>