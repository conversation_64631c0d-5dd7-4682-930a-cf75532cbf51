<template>
	<view class="fr send">
		<view class="fr send-wrap">
			<text v-if="btnStatus == true && isComplete == false" class="sound-tips" @tap="playVoice">继续播放</text>
			<view class="fr send-text" @tap="playVoice" :style="{width:  width + 'rpx'}">
				<text>{{ content.time }}s</text>
				<view class="voice" :class="[btnStatus ? 'static' : 'wave']">
					<text class="wifi-circle first"></text>
					<text class="wifi-circle second"></text>
					<text class="wifi-circle third"></text>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script setup lang="ts">
	import { computed, onMounted, ref } from 'vue';
	const props = defineProps({
		content: {
			type: Object,
			default: () => { }
		}
	})
	const btnStatus = ref(false), isComplete = ref(false),
		width = computed(() => {
			return parseInt((450 / 60 * props.content?.time).toString()) + 140
		})
	const innerAudioContext = uni.createInnerAudioContext();
	const playVoice = () => {
		btnStatus.value = !btnStatus.value
		if (btnStatus.value) {
			innerAudioContext.play()
		} else {
			innerAudioContext.pause()
		}
	}

	onMounted(() => {
		if (props.content?.filePath) {
			innerAudioContext.src = props.content?.filePath
		}
		innerAudioContext.onEnded(() => {
			isComplete.value = true
			btnStatus.value = false
		})
	})
</script>

<style scoped lang="scss">
	.send {
		justify-content: flex-end;
		.send-wrap{
			margin-bottom: 24rpx;
		}
		&-text {
			justify-content: flex-end;
			min-width: 140rpx;
			max-width: 450rpx;
			margin-right: 24rpx;
			padding: 20rpx 28rpx;
			font-size: 30rpx;
			text-align: right;
			color: #fff;
			background-color: $uni-color-primary;
			border-radius: 32rpx 4rpx 32rpx 32rpx;
		}

		.sound-tips {
			padding: 8rpx 16rpx;
			margin-right: 20rpx;
			font-size: 20rpx;
			color: #ccc;
			background-color: rgba(0, 0, 0, .6);
			border-radius: 40rpx;
		}
	}

	.voice {
		position: relative;
		overflow: hidden;
		width: 40rpx;
		height: 40rpx;
		transform: rotate(-45deg);

		.wifi-circle {
			border: 4rpx solid #FFF;
			border-radius: 50%;
			position: absolute;
		}

		.first {
			width: 5rpx;
			height: 5rpx;
			background: rgb(51, 50, 50);
			top: 35rpx;
			left: 35rpx;
		}

		.second {
			width: 25rpx;
			height: 25rpx;
			top: 25rpx;
			left: 25rpx;
		}

		.third {
			width: 40rpx;
			height: 40rpx;
			top: 15rpx;
			left: 15rpx;
		}
	}

	.static {
		@keyframes fadeInOut {
			0% {
				opacity: 0;
				/*初始状态 透明度为0 */
			}

			100% {
				opacity: 1;
				/*结尾状态 透明度为1 */
			}
		}

		.second {
			animation: fadeInOut 1s infinite 0.2s;
		}

		.third {
			animation: fadeInOut 1s infinite 0.4s;
		}
	}
</style>