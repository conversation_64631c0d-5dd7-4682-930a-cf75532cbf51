<template>
	<view class="fr">
		<image src="../images/logo_small.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl">
			<view class="fl__title">
				<text class="fl__title-text">{{ messageData?.title}}</text>
			</view>
			<view class="table">
				<scroll-view :scroll-y="messageData?.data.records.length > 8 ? true : false"
					:style="{height:messageData?.data.records.length > 8 ? '548rpx' : '100%'}" @scrolltolower="scrolltolower">
					<view class="contont">
						<view class="fr cell" @tap="jump(item)" v-for="(item,index) in messageData?.data.records" :key="index">
							<text class="theme-primary link_title">{{ item.fileName }}</text>
							<text class="link_time">{{dayjs(item.updateTime).format('YYYY-MM-DD')}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import dayjs from 'dayjs';
	import { hideLoading, showLoading, showToast } from '@/utils/util';
	import { downloadByFileId, downloadByFileName, page } from '../api/ai';
	import { reactive, ref, watch } from 'vue';

	const messageData = ref<any>()
	const pagination = ref({ pageNum: 1, pageSize: 10, pages: null })

	const props = defineProps({
		content: {
			type: Object,
			default: () => { }
		}
	})

	const jump = async (item) => {
		try {
			const data = await downloadByFileId({
				APP_BASE_URL: 'aiURL',
				header: {
					...getHeader()
				},
				data: { prefix: 'operationsManager' }
			}, item.id)
			if (data) {
				showLoading('加载中')
				uni.downloadFile({
					url: data.data.filePath,
					timeout: 3000,
					success: function (res) {
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function () {
								hideLoading()
								console.log('打开文件成功');
							},
							fail: function (err) {
								hideLoading()
								showToast('打开文件失败')
								console.log('打开文件失败', err);
							}
						});
					},
					fail: function (err) {
						console.log(err);
						hideLoading()
					}
				});
			}
		} catch (e) {
			console.error('通过文件名获取文件链接 error', e)
		}
	}

	watch(
		() => props.content,
		(v) => {
			messageData.value = v
			pagination.value.pageNum = v.data.current
			pagination.value.pageSize = v.data.size
			pagination.value.pages = v.data.pages
		},
		{ immediate: true }
	)

	const scrolltolower = () => {
		if (pagination.value.pageNum++ >= pagination.value.pages) return
		getList()
	}

	// 请求参数headers
	const getHeader = () => {
		return {
			Authorization: uni.getStorageSync('token'),
			application: 'SMART_OPERATIONS_MANAGER',
		}
	}

	// 文件列表
	const getList = async () => {
		try {
			console.log(pagination.value.pageNum);
			const params = {
				fileClass: 1,
				pageNum: pagination.value.pageNum++,
				pageSize: pagination.value.pageSize
			}
			const { data } = await page({
				APP_BASE_URL: 'aiURL',
				header: {
					...getHeader()
				},
				data: params
			})
			if (data) {
				pagination.value.pageNum = data.current
				pagination.value.pages = data.pages
				if (messageData.value.data.records.length > 0) {
					messageData.value.data.records.push(...data.records)
				} else {
					messageData.value.data.records = data
				}
			}
		} catch (e) {
			console.error('文件列表 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.fr {
		position: relative;
	}

	.fl {
		overflow: hidden;
		padding: 20rpx 28rpx 20rpx 50rpx;
		margin: 0 60rpx 20rpx;
		font-size: 30rpx;
		background-color: #F5F5F5;
		border-radius: 32rpx;
		width: 630rpx;

		&__title {
			margin-bottom: 20rpx;
			font-weight: bold;
		}
	}

	.theme-primary {
		text-decoration: underline;
		word-break: break-all;
	}

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}

	.table {
		width: 100%;
		height: 100%;

		.contont {
			width: 100%;
			height: 100%;

			.cell {
				justify-content: space-between;
				margin-bottom: 54rpx;
				position: relative;

				&:last-child {
					margin-bottom: 0;
				}

				&::before {
					position: absolute;
					content: '';
					height: 1px;
					width: 100%;
					background: #E7E7E7;
					bottom: -27rpx;
					left: 0;
				}

				&:last-child::before {
					display: none;
				}

				.link_title {
					font-size: 28rpx;
					line-height: 28rpx;
					width: 400rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.link_time {
					font-size: 24rpx;
					line-height: 24rpx;
					color: #1f2428b3;
				}
			}
		}
	}
</style>