<template>
	<up-popup :show="show" mode="center" :safe-area-inset-bottom="false" :safe-area-inset-top="false"
		:close-on-click-overlay="false">
		<view class="fl video-wrapper">
			<video id="myVideo" :style="{ width: videoStyle?.width + 'px', height: videoStyle?.height + 'px', transform: transform}"
				class="video" :src="videoUrl" :controls="false" @loadedmetadata="onloadedmetadata" @timeupdate="ontimeupdate"
				@ended="onended" :show-center-play-btn="false" @tap="clickVideo" object-fit="fill" @waiting="onwaiting">
			</video>
			<image v-if="Object.keys(closeIconStyle)?.length" :style="closeIconStyle"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-07/1743988538426.png"
				mode="aspectFill" class="video-close" @tap="onClose"></image>
			<view v-if="Object.keys(controlsStyle)?.length" class="fr controls" :style="controlsStyle">
				<view class="fr switch" @tap="onSwitch">
					<image v-if="playState"
						src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-09/1744167059592.png"
						mode="aspectFill" class="switch-icon"></image>
					<image v-else
						src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-09/1744167110682.png"
						mode="aspectFill" class="switch-icon"></image>
				</view>
				<view class="fl controls-progress" @touchstart="ontouchstart" @touchmove="ontouchmove" @touchend="ontouchend">
					<view class="controls-duration">{{ showProgressTime }} / {{ showTotalTime }}</view>
					<view class="fl progress">
						<view class="progress-line__outer" @tap="fastForward">
							<view class="progress-line" :style="{width: progressBarWidth + 'px'}"></view>
						</view>
						<view class="progress-bar" :style="{transform: 'translateX('+progressBarWidth+'px)'}"></view>
					</view>
					<!-- <slider :min="0" :max="totalTime" :value="progressTime" class="controls-slider" activeColor="#fff"
						background-color="rgba(255, 255, 255, .3)" block-size="8" @change="onchange" @changing="onchanging">
					</slider> -->
				</view>
				<view class="controls-seek" @tap="clickTheDoubleSpeedButton">{{ seek }}x</view>
				<view class="fr controls-fullscreen" @tap="setDirection">
					<image
						src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-07/1743997851260.png"
						class="fullscreen-icon"></image>
				</view>
			</view>
			<view class="controls-seek__mask" v-if="showSeek" :style="seekPosStyle">
				<view class="seek-item" v-for="item in seekList" :key="item" :class="{'seek-item--active': item == seek }"
					@tap="playbackRate(item)">{{ item }}x</view>
				<!-- <view class="seek-item" @tap="showSeek = false">取消</view> -->
			</view>
			<view v-if="!playState" class="fr pause" @tap="onSwitch"
				:style="{transform: direction == 'vertical'? 'rotate(0)' : 'rotate(90deg)'}">
				<image
					src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-09/1744167110682.png"
					mode="aspectFill" class="pause-image"></image>
			</view>
		</view>
	</up-popup>
</template>

<script setup lang="ts">
	import { computed, getCurrentInstance, onMounted, ref, nextTick, watch } from 'vue';
	import { throttle } from '@/utils/util';

	type directionType = 'horizontal' | 'vertical'

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const props = defineProps({
		videoUrl: {
			type: String,
			default: '',
			required: true
		},
		currentTime: {
			type: Number,
			default: 0
		}
	})
	const emits = defineEmits<{
		(e : 'timeupdate', args : number) : void,
		(e : 'close', args : number) : void
	}>()
	const instance = getCurrentInstance()
	const videoContext = ref<UniApp.VideoContext>(null)
	const originVideoWidth = ref(0)
	const originVideoHeight = ref(0)
	const totalTime = ref(0)
	const direction = ref<directionType | null>()
	const playState = ref(false)
	const progressTime = ref(0)
	const dragState = ref(false)
	const seek = ref('1.0')
	const progressBarTotalWidth = ref(0)
	const start = ref(0)
	const seekList : readonly string[] = ['0.5', '0.8', '1.0', '1.25', '1.5']
	const showSeek = ref(false)
	const seekPosStyle = ref('')
	const show = ref(false)

	const aspectRatio = computed(() => {
		return originVideoWidth.value / originVideoHeight.value
	})

	const showTotalTime = computed(() => {
		return formatTime(totalTime.value)
	})

	const showProgressTime = computed(() => {
		return formatTime(progressTime.value)
	})

	const videoStyle = computed(() => {
		const { windowWidth, windowHeight, safeAreaInsets } = uni.getSystemInfoSync()
		const screenMaxHeight = windowHeight - safeAreaInsets.top - safeAreaInsets.bottom
		if(direction.value == 'horizontal') {
			const maxHeight = originVideoWidth.value > screenMaxHeight ? screenMaxHeight : originVideoWidth.value
			const maxWidth = maxHeight / aspectRatio.value
			const maxHeight1 = windowWidth * aspectRatio.value

			if(maxWidth > windowWidth) {
				return {
					width: maxHeight1,
					height: windowWidth
				}
			} else {
				return {
					width: maxHeight,
					height: maxWidth
				}
			}
		} else if(direction.value == 'vertical') {
			const maxWidth = originVideoWidth.value > windowWidth ? windowWidth : originVideoWidth.value
			const maxHeight = maxWidth / aspectRatio.value
			return {
				width: maxWidth,
				height: maxHeight > screenMaxHeight ? screenMaxHeight : maxHeight
			}
		}
	})

	const closeIconStyle = computed(() => {
		if (direction.value == 'horizontal') {
			return {
				bottom: '60px',
				right: '40px'
			}
		} else {
			return {
				top: uni.getMenuButtonBoundingClientRect().top + 4 + 'px',
				left: '20px'
			}
		}
	})

	const transform = computed(() => {
		if (direction.value == 'horizontal') {
			return 'rotate(90deg)'
		} else {
			return 'rotate(0deg)'
		}
	})

	const controlsStyle = computed(() => {
		const { windowHeight, windowWidth, safeAreaInsets } = uni.getSystemInfoSync()
		if (direction.value == 'horizontal') {
			return {
				transform: 'rotate(90deg)',
				width: windowHeight - (safeAreaInsets.bottom || 28) - safeAreaInsets.top + 'px',
				top: 0,
			}
		} else {
			return {
				transform: 'rotate(0deg)',
				width: windowWidth + 'px',
				bottom: 0,
				paddingBottom: (safeAreaInsets.bottom || 28) + 'px'
			}
		}
	})

	const dimensionTimeRatio = computed(() => {
		return totalTime.value / progressBarTotalWidth.value
	})

	const progressBarWidth = computed(() => {
		const realWidth = (progressTime.value / totalTime.value) * progressBarTotalWidth.value
		return realWidth >= progressBarTotalWidth.value - uni.upx2px(14) ? progressBarTotalWidth.value - uni.upx2px(14) : realWidth
	})

	const ended = computed(() => {
		return progressTime.value >= totalTime.value
	})

	// 格式化时间
	const formatTime = (seconds : number) => {
		const hours = Math.floor(seconds / 3600); // 获取小时数
		const minutes = Math.floor((seconds % 3600) / 60); // 获取分钟数
		const remainingSeconds = Math.floor(seconds % 60); // 获取剩余秒数
		const formattedSeconds = remainingSeconds.toString().padStart(2, '0'); 		// 将秒数转换为两位数表示，例如9变为09
		const formattedMinutes = minutes.toString().padStart(2, '0');		// 将分钟数转换为两位数表示
		let timeStr = `${formattedMinutes}:${formattedSeconds}`;		// 拼接成时分秒格式，如果小时为0，则不显示小时部分
		if (hours > 0) {
			const formattedHours = hours.toString().padStart(2, '0');
			timeStr = `${formattedHours}:${timeStr}`;
		}
		return timeStr;
	}

	// 当媒体加载完成时触发
	const onloadedmetadata = (e : any) => {
		console.log("🚀 ~ onloadedmetadata ~ e:", e)
		const { width, height, duration } = e.detail
		originVideoWidth.value = width
		originVideoHeight.value = height
		totalTime.value = duration
	}
	// 视频出现缓冲时触发
	const onwaiting = (e) => {
		console.log("🚀 ~ onwaiting ~ onwaiting:", e)
	}

	// 监听播放进度更新
	const ontimeupdate = (e : { detail : { currentTime : number } }) => {
		if (!dragState.value && playState.value) {
			progressTime.value = e.detail.currentTime
		}
		emits('timeupdate', e.detail.currentTime)
	}

	// 切换播放状态
	const onSwitch = () => {
		if (ended.value) {
			progressTime.value = 0
			videoContext.value.seek(0)
		}
		if (playState.value) {
			videoContext.value.pause()
		} else {
			videoContext.value.play()
		}
		playState.value = !playState.value
	}

	// 监听视频播放结束
	const onended = () => {
		progressTime.value = totalTime.value
		playState.value = false
		videoContext.value.pause()
	}

	// 设置媒体速率
	const playbackRate = (rate : string) => {
		seek.value = rate
		videoContext.value.playbackRate(Number(seek.value))
		showSeek.value = false
	}

	// 计算倍速弹窗的位置
	const clickTheDoubleSpeedButton = () => {
		if (showSeek.value) {
			showSeek.value = false
			return
		}
		uni.createSelectorQuery().in(instance).select('.controls-seek').boundingClientRect((res : UniApp.NodeInfo) => {
			if (direction.value == 'vertical') {
				seekPosStyle.value = `left: ${res.left}px; top: ${res.top - 10}px;transform: translate3d(-50%, -100%, 0)`
			} else {
				seekPosStyle.value = `left: ${res.right}px; top: ${res.top}px;transform-origin:bottom left;transform: translate3d(20rpx, -125%, 0) rotate(90deg)`
			}
			showSeek.value = true
		}).exec()
	}

	// 计算进度条宽度
	const calculateProgressBarWidth = () => {
		uni.createSelectorQuery().in(instance).select('.progress').boundingClientRect((res : UniApp.NodeInfo) => {
			progressBarTotalWidth.value = direction.value == 'vertical' ? res.width : res.height
		}).exec()
	}

	// 设置视频方向
	const setDirection = () => {
		if (direction.value == 'vertical') {
			direction.value = 'horizontal'
		} else {
			direction.value = 'vertical'
		}
	}

	// 关闭全屏
	const onClose = () => {
		show.value = false
		emits('close', progressTime.value)
	}

	const ontouchstart = (e : any) => {
		dragState.value = true
		start.value = direction.value == 'vertical' ? e.changedTouches[0].clientX : e.changedTouches[0].clientY
	}

	const ontouchmove = throttle((e : any) => {
		const move = direction.value == 'vertical' ? e.changedTouches[0].clientX : e.changedTouches[0].clientY
		const diff = move - start.value
		const time = Math.abs(diff) * dimensionTimeRatio.value
		if (diff < 0) {
			progressTime.value -= time
			if (progressTime.value <= 0) {
				progressTime.value = 0
			}
		} else {
			progressTime.value += time
			if (progressTime.value >= totalTime.value) {
				progressTime.value = totalTime.value
			}
		}
		videoContext.value.seek(progressTime.value)
		start.value = move
	}, 1000 / 60)

	const ontouchend = () => {
		dragState.value = false
	}

	const fastForward = (e) => {
	}

	const clickTop = () => {
		if (showSeek.value) {
			showSeek.value = false
		}
	}

	const clickVideo = () => {
		if (!showSeek.value) {
			onSwitch()
		}
	}
	// 完成一次拖动后触发的事件
	const onchange = (e) => {
		dragState.value = false
		progressTime.value = e.detail.value
		videoContext.value.seek(progressTime.value)
	}

	// 拖动进度条时触发的事件
	const onchanging = throttle((e) => {
		if (!dragState.value) {
			dragState.value = true
		}
		progressTime.value = e.detail.value
		videoContext.value.seek(progressTime.value)
	}, 1000 / 60)

	watch(() => [controlsStyle.value, direction.value], ([v, _]) => {
		if (v && Object.keys(v)?.length) {
			progressBarTotalWidth.value = 0
			nextTick(calculateProgressBarWidth)
		}
	}, { immediate: true, deep: true })

	onMounted(() => {
		videoContext.value = uni.createVideoContext('myVideo', instance)
	})

	const open = async (time : number) => {
		direction.value = null
		show.value = true
		await nextTick()
		direction.value = 'vertical'
		progressTime.value = time
		videoContext.value.seek(progressTime.value)
		videoContext.value.play()
		playState.value = true
	}

	defineExpose({
		open
	})
</script>

<style scoped lang="scss">
	.video-wrapper {
		justify-content: center;
		align-items: center;
		width: 100vw;
		height: 100vh;
		background-color: #000;

		.video {
			transform-origin: 50% 50%;
		}

		.controls {
			box-sizing: border-box;
			position: fixed;
			left: 0;
			z-index: 1;
			padding: 28rpx;
			background-color: rgba(0, 0, 0, .3);
			transform-origin: left bottom;

			.switch {
				justify-content: center;
				width: 52rpx;
				height: 52rpx;
				margin-right: 24rpx;
			}

			.switch-icon {
				width: 36rpx;
				height: 52rpx;
			}

			.controls-progress,
			.controls-slider {
				flex: 1;
				margin: 0;
			}

			.controls-duration {
				margin-bottom: 8rpx;
				font-size: 26rpx;
				font-weight: bold;
				color: #fff;
			}

			.controls-seek {
				margin: 0 24rpx;
				color: #fff;
			}

			.controls-fullscreen {
				justify-content: center;
				width: 60rpx;
				height: 60rpx;

				.fullscreen-icon {
					width: 48rpx;
					height: 48rpx;
				}
			}

			.progress {
				position: relative;
				height: 14rpx;
				padding-top: 4rpx;
				box-sizing: content-box;

				.progress-bar {
					position: absolute;
					left: 0;
					top: 0;
					width: 14rpx;
					height: 14rpx;
					border-radius: 100%;
					background-color: #fff;
				}

				.progress-line__outer {
					height: 6rpx;
					background-color: rgba(255, 255, 255, .3);
					border-radius: 200rpx;
				}

				.progress-line {
					height: 6rpx;
					background-color: #fff;
				}
			}
		}
	}

	.video-close {
		position: fixed;
		z-index: 2;
		width: 60rpx;
		height: 60rpx;
	}


	.controls-seek__mask {
		position: fixed;
		z-index: 2;
		background-color: rgba(0, 0, 0, .3);
		border-radius: 8rpx;

		.seek-item {
			padding: 14rpx 28rpx;
			font-size: 26rpx;
			font-weight: bold;
			color: #fff;
			text-align: center;

			&:last-child {
				border-bottom: none;
			}

			&--active {
				color: #f90;
			}
		}
	}


	.pause {
		position: fixed;
		right: 0;
		left: 0;
		bottom: 0;
		top: 0;
		z-index: 2;
		justify-content: center;
		width: 160rpx;
		height: 160rpx;
		margin: auto;
		background-color: rgba(0, 0, 0, .5);
		border-radius: 100%;

		&-image {
			width: 56rpx;
			height: 75.11rpx;
		}
	}
</style>