<template>
	<view class="time">
		<text>{{ showTime }}</text>
	</view>
</template>

<script setup lang="ts">
	import { computed } from 'vue';
	import { formatDate, setDay, setTime } from '@/utils/util';
	const props = defineProps({
		time: {
			type: Number,
			default: new Date().getTime()
		}
	})
	const showTime = computed(() => {
		const  time = formatDate(new Date(props.time))
		const today = formatDate()
		if(time == today){
			return setTime(new Date(props.time))
		} else {
			const  diff = 1000 * 60 * 60 * 24
			const  todayTimes = new Date().getTime()
			if(todayTimes - props.time < diff){
				return `昨天 ${setTime(new Date(props.time))}`
			} else {
				const dateTime = setTime(new Date(props.time))
				const value = Number(dateTime.substring(0, 2))
				let  timeText = ''
				if(value < 12){
					timeText = '上午'
				} else if(value >= 18){
					timeText = '晚上'
				} else if(value >=12 && value< 18){
					timeText = '下午'
				} else {
					timeText = '凌晨'
				}
				const currentYear = new Date().getFullYear()
				const  dataYear = new Date(props.time).getFullYear()
				const currentDate = formatDate(new Date(props.time))
				if(currentYear != dataYear){
					return `${currentDate.replace(/-/, '年').replace(/-/, '月')}日  ${timeText}${dateTime}`
				}
				return `${currentDate.substring(5).replace(/-/, '月')}日  ${timeText}${dateTime}`
			}
		}
	})
</script>

<style scoped lang="scss">
	.time {
		padding: 40rpx 0;
		font-size: 24rpx;
		text-align: center;
		color: #999;
	}
</style>