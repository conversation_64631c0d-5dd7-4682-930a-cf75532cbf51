<template>
	<!-- 如果isShowScrollIcon为true，则显示cover-view组件 -->
	<view v-if="isShowScrollIcon" class="scroll-bottom" :style="{bottom: footerHeight + 'px'}"
		@tap="onScrollBottom">
		<!-- 显示scroll_bottom.png图片 -->
		<image src="../images/scroll_bottom.png" lazy-load mode="aspectFill"
			class="scroll-bottom__icon"></image>
	</view>
</template>

<script setup lang="ts">
	// 定义Prop类型
	type Prop = {
		isShowScrollIcon : boolean;
		footerHeight : number;
	}
	// 使用withDefaults函数为Prop设置默认值
	withDefaults(defineProps<Prop>(), {})
	
	// 定义组件的触发事件
	const emits = defineEmits<{
		(e : 'scrollBottom') : void
	}>()
	
	// 定义onScrollBottom函数
	const onScrollBottom = () => {
		emits('scrollBottom')
	}
</script>

<style scoped lang="scss">
	.scroll-bottom {
		overflow: hidden;
		position: fixed;
		z-index: 2;
		left: 50%;
		width: 120rpx;
		height: 120rpx;
		aspect-ratio: 1;
		transform: translate3d(-50%, 0, 0);
		border-radius: 50%;

		&__icon {
			width: 120rpx;
			height: 120rpx;
		}
	}
</style>