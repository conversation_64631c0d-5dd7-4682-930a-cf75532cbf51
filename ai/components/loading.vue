<template>
	<view class="fr loading-left" v-if="direction == 'left'">
		<view class="fr">
			<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="logo"></image>
			<view class="fr loading-left__text">
				<text v-for="item in 3" :key="item" class="circle" :class="{active: item -1 == index}"></text>
			</view>
		</view>
	</view>
	<view class="fr loading-right" v-else>
		<view class="fr loading-right__text">
			<text v-for="item in 3" :key="item" class="circle" :class="{active: item - 1 == index}"></text>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, onUnmounted, ref } from 'vue'
	type loadingDirection = 'left' | 'right'

	defineProps({
		direction: {
			type: String,
			default: 'left' as loadingDirection
		}
	})

	const index = ref(0)
	let timer = null
	onMounted(() => {
		timer = setInterval(() => {
			index.value++
			if (index.value == 3) {
				index.value = 0
			}
		}, 300)
	})
	onUnmounted(() => {
		clearInterval(timer)
		timer = null
	})
</script>

<style scoped lang="scss">
	.loading-left {
		padding: 0 60rpx 0 24rpx;
		margin-bottom: 20rpx;
		.fr {
			position: relative;
		}

		.logo {
			position: absolute;
			top: 8rpx;
			left: 0;
			z-index: 1;
			width: 72rpx;
			height: 72rpx;
		}

		.loading-left__text {
			justify-content: center;
			width: 146rpx;
			height: 88rpx;
			margin-left: 36rpx;
			padding-left: 36rpx;
			background-color: #f5f5f5;
			border-radius: 32rpx;

			.circle {
				width: 16rpx;
				height: 16rpx;
				background-color: rgba(0, 0, 0, .2);
				border-radius: 100%;


				&:nth-child(2) {
					margin: 0 12rpx;
				}

				@for $i from 1 through 3 {
					&:nth-child(#{$i}) {
						&.active {
							background-color: rgba(0, 0, 0, $i * 0.2 + 0.2);
						}
					}
				}
			}
		}
	}

	.loading-right {
		justify-content: flex-end;
		margin-bottom: 20rpx;
		
		&__text {
			justify-content: center;
			width: 120rpx;
			height: 88rpx;
			margin-right: 36rpx;
      background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
			border-radius: 32rpx 0 32rpx 32rpx;

			.circle {
				width: 16rpx;
				height: 16rpx;
				background-color: rgba(255, 255, 255, .2);
				border-radius: 100%;

				&:nth-child(2) {
					margin: 0 12rpx;
				}

				@for $i from 1 through 3 {
					&:nth-child(#{$i}) {
						&.active {
							background-color: rgba(255, 255, 255, $i * 0.2 + 0.2);
						}
					}
				}
			}
		}
	}
</style>