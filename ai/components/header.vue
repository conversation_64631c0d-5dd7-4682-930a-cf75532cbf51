<template>
	<cover-view class="header" :style="{height: menuButton.bottom + 8 + 'px'}" :class="{'nav-bg': isShow}">
		<cover-view class="status-bar" :style="{height: menuButton.top + 'px'}"></cover-view>
		<cover-view class="fr" :style="{width: menuButton.left + 'px'}">
			<cover-view class="fr arrow-left" @tap="goBack">
				<cover-image src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-27/1743075768861.png" mode="aspectFill" lazy-load class="arrow-left__img"></cover-image>
			</cover-view>
			<cover-view class="fr header-content">
				<cover-view class="fr">
					<cover-image
						src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-20/1740050785904.png"
						mode="aspectFill" lazy-load class="icon-swtich" @tap="openDrawer"></cover-image>
					<cover-image src="../images/logo_small.png" lazy-load mode="aspectFill" class="ai-logo"></cover-image>
					<cover-view class="fl">
						<cover-view class="title">尚小美</cover-view>
						<cover-view class="sub-title">你的专属AI助手</cover-view>
					</cover-view>
				</cover-view>
				<cover-image
					src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-06/1741233657018.png"
					mode="aspectFill" class="sound" @tap="startANewConversation"></cover-image>
			</cover-view>
		</cover-view>
	</cover-view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';

	type Prop = {
		isShowNavBg: boolean;
		menuButton: {
			left: number;
			top: number;
			bottom: number;
			height: number;
			width: number;
		}
	}
	const props = withDefaults(defineProps<Prop>(), {})
	
	const emits = defineEmits<{
		(e: 'openDrawer'): void,
		(e: 'startNewSession'): void
	}>()
	
	const isShow = ref(false)
	
	watch(() => props.isShowNavBg, v => {
		isShow.value = v
	},{ immediate: true})

	/**
	 * @description:自定义返回
	 */
	const goBack = () => uni.navigateBack()
	/**
	 * @description: 开启新的对话
	 */
	const startANewConversation = () => {
		emits('startNewSession')
	}
	/**
	 * @description: 打开抽屉
	 */
	const openDrawer = () => {
		emits('openDrawer')
	}
</script>

<style scoped lang="scss">
	.header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 2;
		background-color: rgba(#F7FAFF, 0);

		&.nav-bg {
			background-color: rgba(#F7FAFF, 1);
		}

		.arrow-left {

			justify-content: flex-end;
			align-items: center;
			flex-shrink: 0;
			margin-left: 10rpx;
			width: 48rpx;
			height: 48rpx;
			
			&__img{
				width: 30.17rpx;
				height: 30.17rpx;
			}
		}

		.ai-logo {
			flex-shrink: 0;
			width: 66rpx;
			height: 66rpx;
			margin-right: 16rpx;
			border-radius: 100%;
		}

		&-content {
			flex: 1;
			justify-content: space-between;
			padding: 0 24rpx;
		}

		.title {
			margin-bottom: 4rpx;
			font-size: 26rpx;
			font-weight: bold;
			color: #000;
		}

		.sub-title {
			line-height: 24rpx;
			font-size: 20rpx;
			color: #999;
      width: 200rpx;
		}

		.sound {
			width: 40rpx;
			height: 40rpx;
		}

		.icon-swtich {
			flex: none;
			width: 40rpx;
			height: 30.17rpx;
			margin-right: 32rpx;
		}
	}
</style>