<template>
	<view class="fr">
		<image src="../images/logo_answer.png" mode="aspectFill" lazy-load class="user-avatar"></image>
		<view class="fl table">
      <view class="table-title" v-if="reportFormApiEnum[content.labelType]?.options">
        <text class="table-title__link" @click="navigatorTo(content.labelType)">前往数据驾驶舱，查看更多维度</text>
      </view>
			<view class="table-title">
        <text class="table-title__text">{{ data.title }}</text>
			</view>
			<scroll-view scroll-x="true" class="table-wrap">
        <view class="thead">
          <text
            class="td"
            :class="{'td-width': data.thead?.length <= 3}"
            :style="data.thead?.length <= 3 ? { flex: 1 } : { width: '150rpx' }"
            v-for="(item, index) in data.thead"
            :key="index"
          >
            {{ item }}
          </text>
        </view>
        <view class="tbody">
          <view class="row" v-for="(p, index) in data.tbody" :key="index">
            <view
              class="td"
              :class="{'td-width': data.thead?.length <= 3}"
              :style="data.thead?.length <= 3 ? { flex: 1 } : { width: '150rpx' }"
              v-for="(sub, i) in p"
              :key="i"
            >
              <text :style="son.style" v-for="(son, k) in sub" :key="k">{{ son.label }}</text>
            </view>
          </view>
        </view>

      </scroll-view>
		</view>
	</view>

</template>

<script setup lang="ts">
  import { ComponentInternalInstance, computed, getCurrentInstance } from 'vue'
	import { dataType } from '@/types/index.d';
	import { isObject } from '@/utils/util'
  import { reportFormApiEnum } from "@/ai/enums";
  import { useCommon } from "@/hooks/useGlobalData";

  const { findTargetPageIndex } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const props = defineProps({
		content: {
			type: Object,
			default: () => { }
		}
	}),
		data = computed(() => {
			const { data: content, title } = props.content;
			let temp = JSON.parse(JSON.stringify(content));

			// 提取表头并过滤掉值为 null 的列
			const theadEntries = Object.entries(temp.shift()).filter(([_, value]) => value !== null);
			const thead = theadEntries.map(([_ , value]) => value);
			// 获取有效的键，以便过滤 tbody 中的列
			const validKeys = theadEntries.map(([key]) => key);

			// 处理表体
			const tbody = temp.map((item: dataType) => {
				return validKeys.map(key => {
					const value = item[key];
					if (isObject(value)) {
						return Object.entries(value).map(v => {
							return {
								label: v[0].substring(2),
								style: v[1]
							}
						})
					}
					return [{
						label: value,
						style: {}
					}];
				})
			});

			temp = null;
			return {
				thead,
				title,
				tbody
			};
		});

  const navigatorTo = (labelType: number | undefined) => {
    const { options } = reportFormApiEnum[labelType]
    const pages = getCurrentPages()
    findTargetPageIndex(pages, ['public/pages/index/index'],(index)=>{
      if (index > -1) {
        pages[index].pageOptions = { ...options, areaData: props.content.areaData }; // 设置目标页面的 closeModal 属性
        uni.navigateBack({ delta: pages.length - index - 1 });
      }
    })
  }
</script>

<style lang="scss" scoped>
	.fr {
		position: relative;
	}

	.table {
    width: 100%;
    overflow: hidden;
    padding: 20rpx 28rpx 20rpx 50rpx;
    margin: 0 60rpx 20rpx;
    background-color: #F5F5F5;
    border-radius: 32rpx;

    &-title {
      margin-bottom: 20rpx;
      font-weight: bold;

      &__text {
        font-size: 30rpx;
      }

      &__link {
        color: #1e61fc;
        text-decoration: underline;
      }
    }

    &-wrap {
      overflow: hidden;
      white-space: nowrap;
      border-radius: 20rpx;

      .thead {
        display: flex; /* 确保 thead 是 flex 容器 */
        color: #fff;
        background: #FFE0D9;
        min-width: max-content;

        .td {
          color: #1f2428;
          font-size: 22rpx;
          font-weight: bold;
          text-align: center;
          line-height: 22rpx;
        }
      }

      .td {
        display: inline-block;
        width: 150rpx;
        padding: 20rpx 0;
        text-align: center;
        font-weight: normal;
      }

      .td-width {
        flex: 1; /* 确保 flex 布局生效 */
      }

      .tbody {
        min-width: max-content;

        .row {
          display: flex; /* 确保 row 是 flex 容器 */
          font-size: 22rpx;
          color: #000;
          background: #FFF9F7;

          &:nth-child(even) {
            background: #FFF3F0;
          }

          .td:nth-child(1) {
            color: #444;
          }

          .rate {
            color: #FE5031;
          }
        }
      }
    }
  }

	.user-avatar {
		position: absolute;
		top: 8rpx;
		left: 24rpx;
		z-index: 1;
		width: 72rpx;
		height: 72rpx;
	}
</style>