<template>
	<view class="wrap-container">
		<view class="fr wrap" v-if="modelValue == 'say'" :class="{'ele-step': step, 'wrap-press': isLongTap}">
			<view class="fr speak-btn" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend" id="speak">
				<view class="fr speak-btn" :class="{press: isLongTap, status: speakStatus == 'move'}">
					<view class="fr" v-show="isLongTap">
						<view class="fr speak-box speak-box__left">
							<text v-for="item in 15" :key="item" class="speak-box__line"></text>
						</view>
						<view class="fr speak-box speak-box__right">
							<text v-for="item in 15" :key="item" class="speak-box__line"></text>
						</view>
					</view>
					<text class="speak-btn_text" v-show="!isLongTap">按住说话</text>
				</view>
			</view>

			<view class="fr footer-btn__icon" :class="{'hidden': speakStatus != ''}" @tap="onToggle">
				<image src="../images/<EMAIL>" mode="aspectFill" lazy-load class="ai-toggle"></image>
			</view>
			<cGuideContentVue v-model="step" arrow="down" content="4、支持语音对话或文字对话" :current="4" top="-200rpx"
				@complete="topInstance?.setStepValue()" />
		</view>
		<view class="fl tip" v-if="isLongTap">
			<text class="tip-text"
				:style="speakStatusObj?.[speakStatus]?.style">{{speakStatusObj?.[speakStatus]?.text}}</text>
		</view>
		<cGuideMaskVue v-model="step" />
	</view>

</template>

<script setup lang="ts">
	import { ref, onMounted, inject, computed, getCurrentInstance } from 'vue'
	import { showModal, showToast } from '@/utils/util';
	import cGuideContentVue from '@/components/c-guide/c-guide-content.vue';
	import cGuideMaskVue from '@/components/c-guide/c-guide-mask.vue';
	import { dataType } from '@/types/index.d';
	defineProps({
		modelValue: {
			type: String,
			default: 'input'
		}
	})
	const emits = defineEmits(['update:modelValue', 'send'])
	const isLongTap = ref(false),
		instance = getCurrentInstance(),
		topInstance : dataType = inject('topInstance'),
		step = computed(() => {
			return topInstance?.stepValue?.value == 4
		})

	const recorderManager : UniApp.RecorderManager = uni.getRecorderManager()
	// 说话状态对象
	const speakStatusObj = {
		start: {
			text: '松手发送，上移取消',
			style: {
				'font-size': '24rpx',
				color: '#1E61FC'
			}
		},
		move: {
			text: '松手取消',
			style: {
				color: '#FE5031',
				'font-size': '24rpx'
			}
		}
	}
	// 说话sssss状态值
	const speakStatus = ref(''), up = ref(false), down = ref(false), btnTop = ref(0)

	// 点击语音和输入按钮
	const onToggle = () => {
		emits('update:modelValue', 'input')
	}

	// 开始录音
	const startRecord = () => {
		recorderManager.start({
			duration: 60000,
			format: 'wav'
		})
	}
	
	// 是否发送录音
	let cancelStatus = null

	// 停止录音	
	const stopRecord = () => {
		recorderManager.stop()
	}

	// 按着
	const touchstart = (e) => {
		if (isLongTap.value) return
		uni.vibrateShort()
		speakStatus.value = 'start'
		cancelStatus = 1
		isLongTap.value = true
		startRecord()
		uni.authorize({
			scope: 'scope.record',
			success() {
				console.log('授权成功')
			},
			fail() {
				isLongTap.value = false
				cancelStatus = 0
				showModal({
					title: '您未授权录音功能,去设置',
					success(res) {
						if (res.confirm) {
							uni.openSetting()
						}
					}
				})
			}
		})
	}

	// 移动ssss
	const touchmove = (e) => {
		// console.log('touchmove', e)
		const curPageY = e.changedTouches[0].pageY
		if (curPageY < btnTop.value) {
			down.value = false
			if (!up.value) {
				uni.vibrateShort()
			}
			up.value = true
			speakStatus.value = 'move'
			cancelStatus = 0
		} else {
			if (curPageY > btnTop.value) {
				up.value = false
				if (!down.value) {
					uni.vibrateShort()
				}
				down.value = true
				speakStatus.value = 'start'
				cancelStatus = 1
			}
		}
	}

	// 松手
	const touchend = (e) => {
		// console.log('touchend', e)
		// console.log("cancelStatus", cancelStatus)
		up.value = false
		down.value = false
		isLongTap.value = false
		speakStatus.value = ''
		if (cancelStatus) {
			stopRecord()
		} else {
			recorderManager.pause()
		}
	}
	
	// 录音结束
	const onStep = () => {
		recorderManager.onStop((res) => {
			console.log("res", res)
			instance.appContext.config.globalProperties.$uma.trackEvent('015')
			isLongTap.value = false
			if (res.duration < 1000) {
				showToast('说话时间太短')
				return
			}
			emits('send', {
				type: 'video',
				content: {
					filePath: res.tempFilePath,
					time: parseInt((res.duration / 1000).toString())
				},
				totalTime: new Date().getTime()
			})
		});
	}
	
	// 计算按钮位置
	const btnPosition = () => {
		uni.createSelectorQuery().in(instance).select('.wrap-container').boundingClientRect((res: UniApp.NodeInfo) => {
			btnTop.value = res.top
		}).exec()
	}
	
	onMounted(() => {
		btnPosition()
		onStep()
	})
</script>

<style scoped lang="scss">
	.wrap-container {
		position: relative;
	}

	.footer-btn__icon {
		overflow: hidden;
		padding: 0 20rpx;
		width: auto;
		transition: 0.5s;

		.ai-toggle {
			width: 48rpx;
			height: 48rpx;
		}

		&.hidden {
			width: 0px;
			padding: 0;
		}
	}

	.wrap {
		margin: 0 24rpx;
		// border: 1rpx solid #cccccc;
		background: #ffffff;
		border-radius: 32rpx;
		box-shadow: 0 4rpx 16rpx 0 #0000001a;

		&-press {
			border-color: #fff;
		}
	}

	.tip {
		position: absolute;
		top: -120rpx;
		left: 0;
		right: 0;
		justify-content: center;
		overflow: hidden;
		color: #222;

		&-text {
			height: 120rpx;
			line-height: 150rpx;
			text-align: center;
			background: #fff;
		}
	}

	.speak-btn {
		flex: 1;
		justify-content: center;
		height: 100rpx;
		border-radius: 32rpx;

		.press {
			background-color: $uni-color-primary;
		}

		.status {
			background-color: #FE5031;
		}

		&_text {
			margin-left: 88rpx;
		}

		.speak-box {
			.speak-box__line {
				width: 6rpx;
				margin-left: 10rpx;
				background-color: #fff;
				height: 8rpx;
				border-radius: 6rpx;
			}

		}

		.speak-box__right {
			.speak-box__line {
				animation: opacity 1.5s ease-in-out infinite;
			}
		}

		.speak-box__right {
			@for $i from 1 through 15 {
				.speak-box__line:nth-child(#{$i}) {
					animation-delay: calc(0.1s * $i);
				}
			}
		}

		.speak-box__left {
			.speak-box__line {
				animation: opacity 1.5s ease-in-out infinite reverse;
			}
		}

		.speak-box__left {
			@for $i from 1 through 15 {
				.speak-box__line:nth-child(#{$i}) {
					animation-delay: calc(-0.1s * $i);
				}
			}
		}
	}

	@keyframes opacity {
		0%, 100% {
			height: 8rpx;
		}
		15%, 85%{
			height: 25rpx;
		}
		25%, 75%{
			height: 12rpx;
		}
		35%, 65%{   
			height: 25rpx;
		}
		45%, 55%{
			height: 10rpx;
		}
	}

	.ele-step {
		position: relative;
		z-index: 111;
		touch-action: none;

		.speak-btn,
		.footer-btn__icon {
			pointer-events: none;
		}
	}
</style>