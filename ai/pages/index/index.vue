<template>
	<view v-if="globalData"  class="container">
		<!--头部导航-->
		<headerVue v-if="isShowHeader" :isShowNavBg="isShowNavBg" :menuButton="menuButton"
			@startNewSession="startNewSession" @openDrawer="openDrawer" />
		<!--对话内容-->
		<scroll-view scroll-y="true" class="scroll-view" :scroll-anchoring="true" :scroll-into-view="currentView"
			@scrolltoupper="scrolltoupper" upper-threshold="50" @scroll="onScroll" :scroll-top="jumpScrollTop" :enable-back-to-top="false">
			<!--顶部快捷操作-->
			<baseVue @addTag="onClickTag" :data="result">
				<view :style="{height:  menuButton.bottom + 8 + 'px'}"></view>
				<up-loading-icon v-if="downloading"></up-loading-icon>
			</baseVue>
			<!--20rpx避免当初始数据为空时用户loading距离顶部不够-->
			<view v-if="!list.length" style="height: 20rpx"></view>
			<block v-for="(item, index) in list" :key="index">
				<timeVue :time="item.label" />
				<block v-for="(sub, i) in item.data" :key="i">
					<textVue :isFlow="isFlow" v-if="sub.type == 'text'" :content="sub.content" :id="'id' + sub.totalTime"
						:key="'id' + sub.totalTime" @update="onUpdateScrollTop" @complete="onComplete" :attr="sub.attr" />
					<videoVue v-if="sub.type == 'video'" :content="sub.content" :id="'id' +sub.totalTime"
						@loaded="() => onLoadedVideo(index, i)" />
					<uchartsPieVue v-if="sub.type == 'pie'" :id="'id' + sub.totalTime" :content="sub.content" />
					<tableVue v-if="sub.type == 'table'" :id="'id' + sub.totalTime" :content="sub.content" />
					<linkedVue v-if="sub.type == 'linked'" :id="'id' + sub.totalTime" :content="sub.content" />
          <linkedListVue v-if="sub.type == 'pageLinked'" :id="'id' + sub.totalTime" :content="sub.content" />
          <areaSelectVue v-if="sub.type == 'button'" :id="'id' + sub.totalTime" :content="sub.content"
            :disabled="i !== item.data.length - 1 || index !== list.length - 1 " @click="handleBtnClick" />
					<toolbarVue v-if="showToolbar(sub)" :feedback="sub" :bot-code="botCode" :fn="getHeader" />
				</block>
			</block>
			<!--发送方loading-->
			<loadingVue direction="right" v-if="userLoading" />
			<!--回复方loading-->
			<loadingVue v-if="assistantLoading" />
			<!--撑起来scroll-view底部高度透明色-->
			<view class="bottom-area" id="bottomArea" :style="{height: footerHeight + 'px'}"></view>
		</scroll-view>
		<!--底部操作栏-->
		<view class="fl footer bg">
			<!--底部快捷操作-->
			<scroll-view :scroll-x="true" class="tabs">
				<view class="tab-item" v-for="item in result" :key="item.value" @tap="onClickTag(item)">
					<text>{{ item.label }}</text>
				</view>
				<view class="tab-item" v-for="item in labels" :key="item.value" @tap="onClickTag(item)">
					<text>{{ item.label }}</text>
				</view>
			</scroll-view>
			<!--语音和输入框-->
			<view class="footer-btn" v-if="isShowHeader">
				<inputVue v-model="useStatus" @send="send" />
				<speakingVue v-model="useStatus" @send="send" />
			</view>
			<view :style="{height: safeAreaInsetsHeight + 'px'}"></view>
		</view>
		<!--滚动图标-->
		<scrollTopIconVue :isShowScrollIcon="isShowScrollIcon" :footerHeight="footerHeight"
			@scrollBottom="onScrollBottom" />
	</view>
	<!--历史对话-->
	<sessionHistoryVue ref="sessionHistoryRef" :getHeader="getHeader" :sid="sid" @openHistory="openHistory"
		@deleteHistory="startNewSession" @open="isShowHeader = false" @close="isShowHeader = true" />
  <cAreaPicker ref="cAreaPickerRef" @setArea="queryReportForm" :showWindow="false" />
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
	import { computed, nextTick, ref, unref, reactive, getCurrentInstance, ComponentInternalInstance, provide, toRaw } from 'vue';
	import headerVue from '../../components/header.vue';
	import baseVue from '../../components/base.vue';
	import timeVue from '../../components/time.vue';
	import textVue from '../../components/text.vue';
	import inputVue from '../../components/input.vue';
	import speakingVue from '../../components/speaking.vue';
	import uchartsPieVue from '../../components/ucharts-pie.vue';
	import tableVue from '../../components/table.vue'
	import linkedVue from '../../components/linked.vue';
	import loadingVue from '../../components/loading.vue';
	import toolbarVue from '../../components/toolbar.vue';
	import sessionHistoryVue from '../../components/sessionHistory.vue';
	import scrollTopIconVue from '../../components/scrollTopIcon.vue';
	import videoVue from '../../components/video.vue';
  import linkedListVue from '../../components/linked-list.vue';
  import areaSelectVue from '../../components/area-select.vue';
  import cAreaPicker from '@/components/c-area-picker/index.vue'
	import { dataType, newPageInstance, tabItem } from '@/types/index.d';
	import { recognition as recognitionApi, history as historyApi, recognitionOss as recognitionOssApi, ossUpload, findTodayLastSid as findTodayLastSidApi } from '../../api/ai'
	import { debounce, setTime, getUuid, fullDate, groupDataByFiveMinutes, showToast, throttle} from '@/utils/util';
	import { useCommon } from '@/hooks/useGlobalData';
	import { useLocalEnv, protocl, localEnv } from '@/utils/env'
  import { data, labels, type listItemProp, reportFormApiEnum } from '../../enums'
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
	// 接口域名
	const aiURL = useLocalEnv ? protocl.aiURL : process.env.aiURL

	// websocket实例
	let socketTask = null

	let pageWaitingTime = 0
	// 流式进行状态
	let flowBasedState = false
	// 机器人id
  const botCode = localEnv == 'development' ? 15 : 16
	// 是否流式输出 1是流式
	const isFlow = 1
	// 是否是多对话
	const isMultipleConversation = 1

	const menuButton = uni.getMenuButtonBoundingClientRect()
	const { windowHeight, safeAreaInsets } = uni.getSystemInfoSync()
	const safeAreaInsetsHeight = safeAreaInsets.bottom || 34

	// 表示数据是否已经加载
	let dataLoaded = false

	// 初始化时间
	const intialTime = fullDate()

	const footerHeight = ref(0),	// 定义底部高度
		// 是否显示滚动图标
		isShowScrollIcon = ref(false),
		useStatus = ref('say'),
		// 上下文消息历史记录
		messageHistoryLimit = ref([]),
		// 分页参数
		pagination = reactive({
			pageSize: 10,
			pageNum: 1,
			total: 0,
			loading: false
		}),
		// 下拉加载loading
		downloading = ref(false),
		instance = getCurrentInstance() as ComponentInternalInstance,
		{ globalData, trackEvent, route_to_view } = useCommon(instance),
		// scroll-view 当前项
		currentView = ref('id0'),

		// scroll-view 设置的距离顶部的高度
		jumpScrollTop = ref(0.00001),
		// 是否显示导航栏背景
		isShowNavBg = ref(false),
		// scroll-view 滚动高度
		scrollHeight = ref(0),
		// 用户加载状态
		userLoading = ref(false),
		// 回复加载状态
		assistantLoading = ref(false)
	// 对话数据源
	let list = ref<listItemProp[]>([])
	// 会话sessionId
	const sid = ref(getUuid())
  const cAreaPickerRef = ref()
  const currentContent = ref() // 当前消息

	// 存储sessionHistoryVue组件的实例
	const sessionHistoryRef = ref<InstanceType<typeof sessionHistoryVue>>(null)

	const isShowHeader = ref(true)

	// 底部快捷按钮
	const result = computed(() => {
		return globalData.value?.shop?.groupName == '业主' ? data.slice(0, 3) : data
	})

	// 设置头部显示隐藏避免层级问题
	const setShowHeader = (value : boolean) => {
		isShowHeader.value = value
	}

	provide('topInstance', {
		setShowHeader
	})

	// 请求参数headers
	const getHeader = () => {
		return {
			Authorization: uni.getStorageSync('token'),
			application: 'SMART_OPERATIONS_MANAGER',
		}
	}

	/**
	 * @description: 监听scroll-view滚动
	 */
	const onScroll = (e) => {
		const scroll_top = e.detail.scrollTop
		scrollHeight.value = e.detail.scrollHeight
		// 如果滚动条距离顶部的高度大于44
		if (scroll_top > 44) {
			isShowNavBg.value = true
		} else {
			if (isShowNavBg.value) {
				isShowNavBg.value = false
			}
		}
		// 如果滚动条距离顶部的高度加上窗口的高度小于滚动条的总高度的一半
		if (scroll_top + windowHeight < scrollHeight.value / 2) {
			// 如果滚动条图标不显示
			if (!isShowScrollIcon.value) {
				// 设置滚动条图标显示
				isShowScrollIcon.value = true
			}
			// 否则，如果滚动条距离顶部的高度加上窗口的高度大于等于滚动条的总高度减去50
		} else if (scroll_top + windowHeight >= scrollHeight.value - 50) {
			// 如果滚动条图标显示
			if (isShowScrollIcon.value) {
				// 设置滚动条图标不显示
				isShowScrollIcon.value = false
			}
		}
	}

	/**
	 * @description: 返回底部
	 */
	const onScrollBottom = () => {
		jumpScrollTop.value = scrollHeight.value + (Math.random() / 10000) // scroll-view scrollTop如果值相同则不滚动
		isShowScrollIcon.value && (isShowScrollIcon.value = false)
	}

	// 打字机更新滚动高度

	const onUpdateScrollTop = throttle(async () => {
		await nextTick()
		jumpScrollTop.value = scrollHeight.value + (Math.random() / 10000)
	}, 1000 / 60)

	// 打字机执行完成
	const onComplete = () => {

	}

	/**
	 * @description: 视频缓冲成功后
	 */
	const onLoadedVideo = (index : number, i : number) => {
		list.value[index].data[i].content.loaded = true
		onUpdateScrollTop()
	}

	/**
	 * @description: 是否显示工具栏
	 */
	const showToolbar = (sub : any) => {
		return (sub.type != 'video' && sub.content?.sender != 'user' && sub.eventType == 0 && !flowBasedState) || (sub.type == 'video' && ((sub.content?.current && sub.content?.loaded) || (!sub.content?.current && !sub.content?.loaded)))
	}

	/**
	 * @description: 消息是否在5分钟之内
	 */
	const includes = (dateTimes : number) => {
		return dateTimes - list.value[list.value.length - 1].label > 1000 * 60 * 5
	}

	/**
	 * @description : 设置存储消息对话数据
	 */
	const savePos = (e : any) => {
		if (list.value.length == 0) {
			list.value.push({
				label: e.totalTime,
				data: []
			})
		}
		if (includes(e.totalTime)) {
			const obj = {
				label: e.totalTime,
				data: [e]
			}
			list.value.push(obj)
		} else {
			list.value[list.value.length - 1].data.push(e)
		}
	}

	/**
	 * @description: 语音识别文字
	 */
	const recognition = async (e) => {
		const res = await recognitionApi({
			APP_BASE_URL: 'aiURL',
			name: 'audio',
			filePath: e.content.filePath,
			header: getHeader(),
			formData: {
				lang: 'zh-CN-XiaohanNeural'
			}
		})
		console.log("语音识别结果：", res.data)
		sendSocketMessage(res.data)
	}

	/**
	 * @description: oss语音识别文字
	 */
	const recognitionOss = async (e : any) => {
		try {
			const res = await ossUpload({
				APP_BASE_URL: 'aiURL',
				header: getHeader(),
				name: 'file',
				filePath: e.content.filePath,
			})
			const data = await recognitionOssApi({
				APP_BASE_URL: 'aiURL',
				header: {
					...getHeader(),
					'Content-Type': "application/x-www-form-urlencoded"
				},
				data: {
					lang: 'zh-CN-XiaohanNeural',
					fileName: res.data
				}
			})
			if (data.data == '') {
				showToast('未识别到文字')
				userLoading.value = false
				return
			}
			sendSocketMessage(data.data)
		} catch (e) {
			userLoading.value = false
			console.log("上传语音oss接口报错", e)
		}
	}

	/**
	 * @description: 滚动到scroll-view指定元素
	 */
	const scrollIntoView = (value : string, duration : number = 300) => {
		let timer = setTimeout(() => {
			clearTimeout(timer)
			timer = null
			currentView.value = value
		}, duration)
	}

	/**
	 * @description: 发送语音或者文字
	 */
	const send = debounce(async (e : any) => {
		if (flowBasedState) {
			showToast('稍等片刻，等回复后再发送哦~')
			return
		}
		if (userLoading.value || assistantLoading.value) return
		userLoading.value = true
		nextTick(() => {
			onScrollBottom()
			if (e.type == 'video') {
				try {
					recognitionOss(e)
				} catch (e) {
					userLoading.value = false
					showToast('识别失败，请重试')
					console.log('语音识别接口报错', e)
				}
			} else {
				sendSocketMessage(e.content.text)
			}
		})
	}, 300)

	/**
	 * @description: websocket 发送消息
	 */
	const sendSocketMessage = (text : string) => {
		const sendMsg = () => {
			if (text) {
				messageHistoryLimit.value.push({
					message: text,
					sender: 'user',
					time: fullDate()
				})
			}

			if (messageHistoryLimit.value.length < 5) {
				messageHistoryLimit.value.unshift({
					message: '我是尚小美，您的AI店长助手，您在运营酒店的过程中，有什么问题都可以问我哦～',
					sender: 'assistant',
					time: intialTime
				})
			}

			socketTask.send({
				data: JSON.stringify({
					isContextChat: true,
					modelType: 'tencent',
					sid: sid.value,
					botCode,
					isFlow,
					token: uni.getStorageSync('token'),
					messageHistory: messageHistoryLimit.value.slice(messageHistoryLimit.value.length - 1)
				}),
				success() {
					console.log("🚀 ~ success ~ 发送成功:", '发送成功')
					const totalTime = new Date().getTime()
					savePos({
						type: 'text',
						content: {
							text: text,
							sender: 'user'
						},
						totalTime: totalTime
					})
					nextTick(() => {
						currentView.value = 'id' + totalTime
						userLoading.value = false
						assistantLoading.value = true
					})
				},
				fail(err : any) {
					userLoading.value = false
					showToast('发送失败')
					console.log('发送失败', err)
				}
			})
		}
		if (socketTask?.readyState != 1) {
			webScoketInit(() => sendMsg())
		} else {
			sendMsg()
		}
	}

	/**
	 * @description: 点击快捷按钮
	 */
	const onClickTag = (item : tabItem, quickly : boolean = true) => {
    if (quickly) {
      trackEvent('019')
    }
    send({
      type: 'text',
      content: {
        time: setTime(),
        text: item.label,
        sender: 'user'
      },
      totalTime: new Date().getTime()
    })
	}

	/**
	 * description: 查找今天的最后一个sid
	 */
	const findTodayLastSid = async () => {
		const res = await findTodayLastSidApi({
			APP_BASE_URL: 'aiURL',
			header: getHeader()
		})
		return res.data
	}
	/**
	 * @description: 合并数据
	 */
	const handleCompare = (data : any[]) => {
		let newArray = []
		for (let item of data) {
			if (item.recordId) {
				const index = newArray.findIndex(n => n.recordId == item.recordId)
				if (index > -1) {
					newArray[index].attr = item
				} else {
					newArray.push(item)
				}
			} else {
				newArray.push(item)
			}
		}
		return newArray
	}

	/**
	 * @description: 获取历史消息记录
	 */
	const history = async () => {
		try {
			const { pageSize } = pagination
			const params : any = {
				pageSize,
				pageNum: 1,
				sessionId: sid.value
			}
			if (list.value?.[0]?.data?.[0]?.id) {
				params.lastId = list.value[0].data[0].id
			}
			const res = await historyApi({
				APP_BASE_URL: 'aiURL',
				header: getHeader(),
				data: params
			})
			const { records } = res.data
			messageHistoryLimit.value = [...records, ...unref(messageHistoryLimit)]
			const data = records.map((item : dataType) => {
				let content = null, type = 'text'
				const commonObj = {
					id: item.id,
					state: item.state,
					recordId: item.recordId,
					requestId: item.requestId,
					totalTime: item.createTime,
				}
				try {
					content = JSON.parse(item.content)
					if (content.showType == 'text') {
						return {
							type: type,
							content: {
								sender: item.sender,
								text: content.data,
							},
							eventType: content.eventType,
							...commonObj
						}
					} else {
						return {
							type: content.showType == 'chart' ? content.chartType : content.showType,
							content: {
								sender: item.sender,
								...content,
								videoTitle: content.showType == 'video' ? content.title : ''
							},
							eventType: content.eventType,
							...commonObj
						}
					}
				} catch (noWatch) {
					return {
						type: type,
						content: {
							sender: item.sender,
							text: item.content
						},
						eventType: content?.eventType,
						...commonObj
					}
				}
			}).reverse()
			const historyData = list.value.reduce((pre : dataType[], cur : dataType) => {
				return pre.concat(...cur.data)
			}, [])
			const totalData = [...handleCompare(data), ...historyData]
			// 将totalData按照每5分钟分组
			list.value = groupDataByFiveMinutes(totalData)
			console.log("🚀 ~ file: index.vue:596 ~ history ~ list.value:", list.value)

			await nextTick()
			uni.createSelectorQuery().select('.scroll-view').fields({ size: true, scrollOffset: true }, (node : UniApp.NodeInfo) => {
				jumpScrollTop.value = node.scrollHeight - scrollHeight.value
			}).exec()

			if (records.length < pageSize) {
				pagination.loading = false
			} else {
				pagination.loading = true
			}
		} catch (e) {
			console.log('获取历史记录', e)
		} finally {
			downloading.value = false
		}
	}

	// 下拉加载更多
	const scrolltoupper = async () => {
		if (!pagination.loading) return
		downloading.value = true
		history()
	}


	/**
	 * @description: 新对话
	 */
	const startNewSession = (sessionId : string) => {
		sid.value = sessionId || getUuid()
		list.value = []
		webScoketInit()
	}
	/**
	 * @description: 打开抽屉
	 */
	const openDrawer = () => {
		sessionHistoryRef.value.open()
	}

	/**
	 * @description: 打开历史记录
	 */
	const openHistory = (sessionId : string) => {
    if (socketTask) {
      closeWebsocket()
    }
		jumpScrollTop.value = 0
		scrollHeight.value = 0
		startNewSession(sessionId)
		history()
	}

  /**
   * @description: 选择区域
   * @param item
   * @param content
   */
  const handleBtnClick = (item : string, content : any) => {
    currentContent.value = content
    if (item === '指定组织') {
      cAreaPickerRef.value.show = true
    } else { // 全部组织
      queryReportForm()
    }
  }

  // 接口查询报表数据
  const queryReportForm = async (orgId : string | number = '', orgLevel : string = '', name : string = '全部组织') => {
    assistantLoading.value = true
    try {
      console.log(currentContent.value)
      const { api, params } = reportFormApiEnum[currentContent.value.labelType]
      const { code, data, message } = await api({
        APP_BASE_URL: 'baseURL_sh_ccs',
        header: {
          ...getHeader()
        },
        data: params(orgId, orgLevel, name)
      })
      if (code === 200) {
        if (data && Object.keys(data).length > 0) {
          const sendMsg = () => {
            messageHistoryLimit.value.push({
              message: { ...data, eventType: '2' },
              sender: 'assistant',
              time: fullDate()
            })
            socketTask.send({
              data: JSON.stringify({
                isContextChat: true,
                modelType: 'tencent',
                sid: sid.value,
                botCode,
                token: uni.getStorageSync('token'),
                messageHistory: messageHistoryLimit.value.slice(messageHistoryLimit.value.length - 1)
              }),
              success() {
                console.log('发送成功')
              },
              fail(err : any) {
                console.log('发送失败', err)
              }
            })
          }
          if (socketTask?.readyState != 1) {
            webScoketInit(() => sendMsg())
          } else {
            sendMsg()
          }
          assistantLoading.value = false
          messageHistoryLimit.value.push({
            message: JSON.stringify(data),
            sender: 'assistant',
            time: fullDate()
          })
          const sendText = (data : any) => {
            const totalTime = new Date().getTime()
            savePos({
              type: "text",
              content: {
                text: data,
                sender: 'assistant',
                current: true
              },
              totalTime: totalTime
            })
            nextTick(() => {
              let timer = setTimeout(() => {
                clearTimeout(timer)
                timer = null
                currentView.value = 'id' + totalTime
              }, 300)
            })
          }
          try {
            if (data && Object.keys(data).length > 0) {
              const totalTime = new Date().getTime()
              savePos({
                type: data.showType == 'chart' ? data.chartType : data.showType,
                content: { ...data, labelType: currentContent.value.labelType, areaData: {orgId, level: orgLevel, orgName: name}},
                totalTime: totalTime
              })
              nextTick(() => {
                let timer = setTimeout(() => {
                  clearTimeout(timer)
                  timer = null
                  currentView.value = 'id' + totalTime
                }, 300)
              })
            } else {
              if (data == '') {
                showToast('未识别到文字')
              } else {
                sendText(data)
              }
            }
          } catch (e) {
            console.log(e, 'e是字符串')
            if (data == '') {
              showToast('未识别到文字')
              assistantLoading.value = false
            } else {
              sendText(data)
            }
          }
        }
      } else {
        showToast(message)
        assistantLoading.value && (assistantLoading.value = false)
      }
    } catch (e) {
      console.error('接口查询报表数据 error', e)
    }
  }


  /**
	 * @description: 返回当前页面携带参数
	 */
	const returnCarryParameter = async () => {
		const pages = getCurrentPages()
		const currentPage : newPageInstance = pages[pages.length - 1]
		if (currentPage && currentPage.refresh) {
			list.value = []
			await history()
			onClickTag({ label: decodeURIComponent(currentPage.$vm.query), value: null })
			delete currentPage.refresh
			currentPage.$vm.query = null
		}
	}

	// 监听webSocket打开事件
	const onOpen = (callback ?: Function) => {
		console.log('监听webSocket打开事件')
		callback?.()
	}
	// 监听webSocket接受到服务器的消息事件
	const onMessage = (res : any) => {
		// console.log('监听 WebSocket 接受到服务器的消息事件', res)
		if (res.data != 'this is the heartbeat message') {
			assistantLoading.value = false
			messageHistoryLimit.value.push({
				message: res.data,
				sender: 'assistant',
				time: fullDate()
			})
			const sendText = (data : any, source ?: any) => {
				const totalTime = new Date().getTime()
				savePos({
					type: "text",
					content: {
						text: data,
						sender: 'assistant',
						current: true
					},
					recordId: source.recordId,
					state: 0,
					requestId: source.requestId,
					totalTime: totalTime
				})
				nextTick(() => scrollIntoView('id' + totalTime))
			}

			const handleData = async (data : any) => {
				if (data.showType == 'text') {
					// 一句话结束
					if (data.finalResult && !flowBasedState) {
						sendText(data.data, data)
						onUpdateScrollTop()
					}
					// 连续段落
					if (!data.finalResult && !flowBasedState) {
						sendText(data.data, data)
						onUpdateScrollTop()
						flowBasedState = true
					}
					if (!data.finalResult && flowBasedState) {
						list.value[list.value.length - 1].data[list.value[list.value.length - 1].data.length - 1].content.text += data.data?.substring(list.value[list.value.length - 1].data[list.value[list.value.length - 1].data.length - 1].content.text.length)
						onUpdateScrollTop()
					}

					if (data.finalResult && flowBasedState) {
						list.value[list.value.length - 1].data[list.value[list.value.length - 1].data.length - 1].content.text += data.data?.substring(list.value[list.value.length - 1].data[list.value[list.value.length - 1].data.length - 1].content.text.length)
						onUpdateScrollTop()
						flowBasedState = false
					}
				} else {

					const totalTime = new Date().getTime()
					const params = {
						type: data.showType == 'chart' ? data.chartType : data.showType,
						content: {
							...data,
							sender: 'assistant',
							current: true,
						},
						recordId: data.recordId,
						eventType: data.eventType,
						requestId: data.requestId,
						state: 0,
						totalTime: totalTime
					}
					if(data.showType == 'video') {
						params.content.videoTitle = data.title
					}
					// TODO 直接插入最后一条数据中待优化
					if (data.showType == 'linked' && data.eventType == '1') {
						list.value[list.value.length - 1].data[list.value[list.value.length - 1].data.length - 1].attr = params
						onUpdateScrollTop()
						return
					}
					savePos(params)
					nextTick(() => scrollIntoView('id' + totalTime))
				}
			}

			try {
				if (res.data.indexOf('{') != -1) {
					const data = JSON.parse(res.data)
					handleData(data)
				} else {
					if (res.data == '') {
						showToast('未识别到文字')
					} else {
						sendText(res.data)
					}
				}
			} catch (e) {
				console.log(e, 'e是字符串')
				if (res.data == '') {
					showToast('未识别到文字')
					assistantLoading.value = false
				} else {
					sendText(res.data)
				}
			}
		}
	}
	// 监听webSocket错误事件
	const onError = (err : any) => {
		console.log("🚀 ~ file: index.vue:671 ~ onError ~ 监听webSocket错误事件:", err)
		flowBasedState = false
		assistantLoading.value && (assistantLoading.value = false)
	}
	// 监听websocket断开
	const onClose = (res : any) => {
		console.log("🚀 ~ file: index.vue:669 ~ onClose ~ 监听websocket断开:", res)
		flowBasedState = false
		assistantLoading.value && (assistantLoading.value = false)
		if (res.code != 1000) {
			webScoketInit()
		}
	}
	/**
	 * @description: 初始化websocket
	 */
	const webScoketInit = (callback ?: Function) => {
		const url = (aiURL + '/streamChat/' + sid.value).replace('http', 'ws').replace('https', 'wss')
		socketTask = uni.connectSocket({
			url,
			header: {
				...getHeader(),
				isMultipleConversation
			},
			success() {
				console.log('初始化创建websocket')
			}
		})
		if (socketTask) {
			socketTask.onOpen(() => onOpen(callback))
			socketTask.onMessage(onMessage)
			socketTask.onError(onError)
			socketTask.onClose(onClose)
		}
	}


	/**
	 * @description: 关闭销毁webscoket
	 */
	const closeWebsocket = () => {
		if (socketTask) {
			socketTask.close({
				code: 1000,
				success: () => {
					console.log('关闭成功')
				}
			})
			socketTask = null
		}
	}

  /**
   * @description: 计算底部的高度
   */
  const calcaute = () => {
    uni.createSelectorQuery().select('.footer').boundingClientRect((res : UniApp.NodeInfo) => {
      footerHeight.value = res.height
    }).exec()
  }

	onLoad(async (opts) => {
		pageWaitingTime = Date.now()
		await nextTick(calcaute)
		const sessionId = await findTodayLastSid()
		if (sessionId) {
			sid.value = sessionId
			await history()
		}
		dataLoaded = true
		// 如果opts中有query属性
		if (opts?.query) {
			webScoketInit(() => onClickTag({ label: decodeURIComponent(opts.query), value: null }, false))
		} else {
			webScoketInit()
		}
	})


	onShow(() => {
		if (!socketTask && dataLoaded) {
			webScoketInit()
		}

		returnCarryParameter()
	})

	onHide(() => {
		closeWebsocket()
	})

	onUnload(() => {
    trackEvent('017', { seconds: '时长' + (Date.now() - pageWaitingTime) + '毫秒', duration: '停留时长' + (Date.now() - pageWaitingTime) + '毫秒'})
    closeWebsocket()
	})
</script>

<style scoped lang="scss">
	.container {
		height: 100vh;

		.scroll-view {
			overflow-anchor: auto;
			height: 100vh;
			transition: all 0.3s ease;
			background-color: #fff;

			:deep() {
				.u-loading-icon {
					margin-bottom: 20rpx;
				}
			}
		}

		.footer {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			padding-top: 20rpx;

			&.bg {
				background-color: rgba(255, 255, 255, .96);
			}

			.tabs {
				white-space: nowrap;
				margin-bottom: 20rpx;

				.tab-item {
					display: inline-block;
					min-width: 184rpx;
					padding: 22rpx 20rpx;
					margin-left: 16rpx;
					border: 1rpx solid #cccccc99;
					font-size: 24rpx;
					font-weight: bold;
					color: #4f4f4f;
					text-align: center;
					background-color: #fff;
					border-radius: 200rpx;
				}
			}
		}
	}

	:deep() {
		.u-popup__content {
			border-radius: 0;
		}
	}
</style>