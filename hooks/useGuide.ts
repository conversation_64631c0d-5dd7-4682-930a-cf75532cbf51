import { ref, getCurrentInstance, ComponentInternalInstance } from 'vue'
import { useCommon } from '@/hooks/useGlobalData';
import { checkGuideCompleted as checkGuideCompletedApi, completeGuide as completeGuideApi } from '@/api'
export const useGuide = () => {
	const { globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const guideVersion = '1.0.0'
	const stepValue = ref(0)
	const checkGuideCompleted = async () => {
		const staffId = globalData.value?.shop?.staffId
		const shopId = globalData.value?.shop?.shopId
		if (!staffId || !shopId) return
		const res = await checkGuideCompletedApi({
			staffId,
			shopId,
			guideVersion
		})
		if (res.data == false) {
			stepValue.value = 1
			uni.setStorageSync('guide', 0)
			return true
		} else {
			uni.setStorageSync('guide', 1)
		}
	}
	const completeGuide = async () => {
		const staffId = globalData.value?.shop?.staffId
		if (!staffId) return
		try {
			await completeGuideApi({
				staffId,
				guideVersion
			})
			uni.setStorageSync('guide', 1)
			uni.setStorageSync('showHourseAgrement', 1)
			return true
		} catch (e) {
			console.log('引导执行完')
		}
	}
	return {
		checkGuideCompleted,
		completeGuide,
		stepValue
	}
}