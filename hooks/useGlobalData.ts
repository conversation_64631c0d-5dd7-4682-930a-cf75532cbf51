import { ComponentInternalInstance, ref } from 'vue'
import { dataType, newPageInstance } from '../types'
import { showToast, debounce } from '@/utils/util'
import { throttle } from '@/uni_modules/uview-plus';
import { pushError as pushErrorApi } from '@/api'
export const globalData : dataType = ref({})
export const setGlobalData = (Vue : ComponentInternalInstance, data : dataType) : void => {
	// Vue.appContext.config.globalProperties.$globalData = data
	globalData.value = data
}
export const getGlobalData = (Vue : ComponentInternalInstance) => {
	return Vue.appContext.config.globalProperties.$globalData
}

export const useCommon = (Vue : ComponentInternalInstance) => {
	const route_to_view = (path : string, callback ?: () => void) => {
		throttle(() => {
			uni.navigateTo({
				url: path,
				success() {
					callback && callback()
				},
				fail(err) {
					console.log(err);
				}
			})
		}, 1000)
	}
	const setClipboardData = (data : string) => {
		uni.setClipboardData({
			data,
			success() {
				showToast('内容已复制')
			}
		})
	}

	const setRouteParams = (route: string) => {
		const pages = getCurrentPages()
		const prevPage: newPageInstance = pages[pages.length - 2]
		if(prevPage && prevPage.route == route){
			prevPage.refresh = true
		}
	}
	const trackEvent = (id: String, opts?: dataType) => {
		Vue.appContext.config.globalProperties.$uma.trackEvent(id, opts)
	}

	const customBack = () => {
		const pages = getCurrentPages()
		if (pages.length > 1) {
			uni.navigateBack()
		} else {
			uni.reLaunch({
				url: '/public/pages/index/index',
			})
		}
	}

	/**
	 * 查找符合条件的第一个页面的索引，并执行回调函数
	 * @param {Array<Page.PageInstance<AnyObject, {}> & {}>} pages 当前页面栈的实例
	 * @param {string[]} targetPages 目标页面
	 * @param {*} callback 回调函数
	 */
	 const findTargetPageIndex = (pages, targetPages, callback) => {
		// 从后往前遍历
		for (let i = pages.length - 1; i >= 0; i--) {
			if (targetPages.includes(pages[i].route)) {
				// 如果找到目标页面，执行回调函数
				if (callback) {
					callback(i, pages[i]); // 回调函数中传入目标页面索引和目标页面
				}
				return i; // 返回目标页面索引
			}
		}
		// 如果没有找到目标页面，执行回调函数并传入 -1 和 null
		if (callback) {
			callback(-1, null);
		}
		return -1; // 返回 -1 表示未找到
	};
	 
	return {
		globalData: getGlobalData(Vue),
		route_to_view,
		setClipboardData,
		setRouteParams,
		trackEvent,
		customBack,
		findTargetPageIndex
	}
}

export const countDown = (text: string = '获取验证码') => {
	const countdownTime = ref(60); // 倒计时时间，单位为秒
	const isCountingDown = ref(false); // 是否正在倒计时
	const buttonText = ref(text); // 按钮文本
	let intervalId : number | null = null; // 倒计时定时器ID

	const startCountdown = () => {
		if (isCountingDown.value) return;

		isCountingDown.value = true;
		buttonText.value = `${countdownTime.value}秒`;

		intervalId = setInterval(() => {
			if (countdownTime.value <= 0) {
				stopCountdown();
			} else {
				countdownTime.value--;
				buttonText.value = `${countdownTime.value}秒`;
			}
		}, 1000);
	};

	const stopCountdown = () => {
		if (intervalId !== null) {
			clearInterval(intervalId);
			intervalId = null;
		}
		isCountingDown.value = false;
		countdownTime.value = 60;
		buttonText.value = text;
	};

	return {
		buttonText,
		startCountdown
	}
}

const { platform, model, deviceId } = uni.getSystemInfoSync()

/**
 * @description: 错误上报接口
 */
export const pushError = (data : any = {}) => {
	if (platform == 'devtools') return
	pushErrorApi({
		...data,
		source: `智慧运营管家:（机型${platform},版本${model}）`,
		deviceId: deviceId
	})
}