/**
 * 导航栏高度
 */
import { onBeforeMount } from 'vue'

export function useNav() {
  const navInfo = uni.getStorageSync('navInfo')
  if (navInfo) return navInfo

  // #ifdef MP-WEIXIN
  let statusBarHeight: number = 0; // 状态栏的高度
  let navigatorHeight: number = 0; // 导航栏高度
  let menuHeight: number = 0; // 胶囊高度
  let menuTop: number = 0; // 胶囊与顶部的距离
  let totalHeight: number = 0; // 总高度
  return new Promise((resolve) => {
    onBeforeMount(() => {
      uni.getSystemInfo({
        success: (res: any) => {
          statusBarHeight = res.statusBarHeight;
        },
      });

      const menu: any = uni.getMenuButtonBoundingClientRect();
      menuHeight = menu.height; // 胶囊高度
      menuTop = menu.top; // 胶囊与顶部的距离
      // 导航栏高度= （胶囊顶部距离-状态栏高度） x 2 + 胶囊的高度
      navigatorHeight = (menu.top - statusBarHeight) * 2 + menu.height;
      // 总高度 = 状态栏的高度 + 导航栏高度
      totalHeight = statusBarHeight + navigatorHeight;
      // 保存到本地
      uni.setStorageSync('navInfo', {
        statusBarHeight,
        navigatorHeight,
        menuHeight,
        menuTop,
        totalHeight,
      })
      resolve({
        statusBarHeight,
        navigatorHeight,
        menuHeight,
        menuTop,
        totalHeight,
      });
    });
  });
  // #endif
}
