<template>
	<view class="subsection">
		<view class="item" :class="{'item_active':modelValue==index}" v-for="(item,index) in list" :key="index"
			@click="change(item,index)">
			{{getText(item)}}
		</view>
	</view>
</template>

<script setup>
	import { watch } from 'vue';
	const props = defineProps({
		list: {
			type: Array,
			default: () => []
		},
		modelValue: {
			type: [String, Number],
			default: () => 0
		},
		keyName: {
			type: String,
			default: () => 'name'
		}
	})
	const emit = defineEmits(['update:modelValue', 'change'])
	const change = (item, index) => {
		if (index == props.modelValue) return
		emit('update:modelValue', index)
		emit('change', item)
	}
	const getText = (item) => {
		return typeof item === 'object' ? item[props.keyName] : item
	}
</script>

<style scoped lang="scss">
	.subsection {
		display: inline-flex;
		align-items: center;
		border: 1px solid #f2f2f2;
		border-radius: 252.63rpx;
		box-sizing: border-box;

		.item {
			color: #1f2428b3;
			font-size: 22rpx;
			text-align: center;
			line-height: 22rpx;
			padding: 14rpx 28rpx;
		}

		.item_active {
			border-radius: 252.63rpx;
			opacity: 1;
			background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
			color: #ffffff;
			font-size: 22rpx;
			font-weight: bold;
			line-height: 22rpx;
		}
	}
</style>