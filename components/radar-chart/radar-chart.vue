<template>
	<view class="char">
		<qiun-data-charts type="radar" :opts="opts" :chartData="chartData" :inScrollView="true" />
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	const props = defineProps({
		chartData: {
			type: Object,
			default: () => { }
		}
	})

	const opts = ref({
		color: ["#FF4D4D"],
		padding: [5, 5, 5, 5],
		dataLabel: false,
		enableScroll: false,
		legend: {
			show: false,
		},
		extra: {
			tooltip: {
				showBox: false
			},
			radar: {
				gridType: "radar",
				gridColor: "#CCCCCC",
				gridCount: 1,
				radius: 80,
				opacity: 0,
				labelShow: true,
				labelColor: "#1F2428",
				border: true,
				max: props.chartData?.quotaOneLevelScores?.reduce((prev, current) => {
					if (current > prev) {
						return Math.ceil(current);
					} else {
						return Math.ceil(prev);
					}
				})
			}
		}
	})
</script>

<style lang="scss">
	.char {
		width: 100%;
		height: 100%;
	}
</style>