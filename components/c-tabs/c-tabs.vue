<template>
	<view class="container">
		<up-tabs v-if="isBig" :list="list" :current="modelValue" @change="change" lineWidth="88rpx" lineHeight="88rpx"
			:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
	        color: '#ffffff',
	        fontWeight: 'bold',
					fontSize: '36rpx',
					lineHeight: '36rpx',
					zIndex:1
	    }" :inactiveStyle="{
	        color: '#ffffff',
					fontSize: '26rpx',
					lineHeight: '26rpx'
	    }" itemStyle="width:169rpx;height: 84rpx;">
		</up-tabs>
		<up-tabs v-else :list="list" :current="modelValue" @change="change" lineWidth="60rpx" lineHeight="60rpx"
			:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="{
	        color: '#1F2428',
	        fontWeight: 'bold',
					fontSize: activeFontSize + 'rpx',
					lineHeight: activeFontSize + 'rpx',
					zIndex:1,
	    }" :inactiveStyle="{
	        color: '#1F2428',
					fontSize: inactiveFontSize + 'rpx',
					lineHeight: inactiveFontSize + 'rpx'
	    }" itemStyle="height: 60rpx;align-items:flex-start;" v-bind="$attrs">
		</up-tabs>
	</view>
</template>

<script setup lang="ts">
	import { withDefaults } from 'vue';
	const lineBg = 'data:image/png;base64,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'

	interface Tab {
		name : string;
		[key : string] : any;
	};

	const props = withDefaults(
		defineProps<{
			list : Tab[];
			modelValue : string | number;
			isBig : Boolean;
			activeFontSize : Number;
			inactiveFontSize : Number;
		}>(),
		{
			list: () => [],
			modelValue: -1,
			isBig: () => true,
			activeFontSize: () => 30,
			inactiveFontSize: () => 22
		}
	);

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const emit = defineEmits(['update:modelValue', 'change'])

	const change = (item) => {
		if (item.index == props.modelValue) return
		emit('update:modelValue', item.index)
		emit('change', item)
	}
	const bottom = props.isBig ? '-20rpx' : '0rpx'
</script>

<style scoped lang="scss">
	:deep() {
		.u-tabs__wrapper__nav__line {
			bottom: v-bind(bottom) !important;
		}

		.u-tabs__wrapper__nav__item {
			cursor: none !important;
		}
	}

	.tabs {
		color: #ffffff;
		overflow-x: auto;
		overflow-y: hidden;
		height: 84rpx;
		box-sizing: border-box;

		.item {
			font-size: 26rpx;
			text-align: center;
			line-height: 26rpx;
			margin-right: 60rpx;
			align-items: center;
			position: relative;
			white-space: nowrap;

			.text {
				z-index: 200;
				color: #ffffff;
			}

			&-active {
				font-size: 36rpx;
				font-weight: bold;
				text-align: center;
				line-height: 36rpx;
			}

			.tab-active {
				position: absolute;
				width: 60rpx;
				height: 60rpx;
				flex-shrink: 0;
				z-index: 1;
				top: 8rpx;
			}
		}
	}
</style>