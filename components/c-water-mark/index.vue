<template>
  <view v-if="watermark.length > 0">
    <zmm-watermark :watermark="watermark" color="#000000" :opacity="0.1" :fontSize="14" :margin="50" :column="2" :rotate="-30" v-bind="$attrs"></zmm-watermark>
  </view>
</template>

<script setup lang="ts">
  import { ComponentInternalInstance, getCurrentInstance, onMounted, ref } from 'vue'
  import { useCommon } from '@/hooks/useGlobalData'

  const instance = getCurrentInstance() as ComponentInternalInstance
  const {globalData} = useCommon(instance)

  const watermark = ref<string>('')

  onMounted(() => {
    instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
      if (res) {
        watermark.value = `<h5>${globalData.value.user.userId}${globalData.value.user.userName}</h5>`
      }
    })
  })
</script>