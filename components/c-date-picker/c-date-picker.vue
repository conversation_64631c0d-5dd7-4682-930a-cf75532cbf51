<template>
  <view class="date-picker">
    <view class="date-content">
      <view class="date-group">
        <view class="date-item" :class="{ active: dateType === 'day' }" @click="setDateType('day')">日</view>
        <view class="date-item" :class="{ active: dateType === 'month' }" @click="setDateType('month')">月</view>
        <view class="date-item" :class="{ active: dateType === 'quarter' }" @click="setDateType('quarter')">季</view>
        <view class="date-item" :class="{ active: dateType === 'year' }" @click="setDateType('year')">年</view>
        <view v-if="showRange" class="date-item" :class="{ active: dateType === 'range' }" @click="setDateType('range')">自定义</view>
      </view>
      <image v-if="showRule" class="date-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-27/1751005663275.png" mode="aspectFill" @click.stop="showModal"></image>
    </view>
    <view class="date-range">
      <text class="mr-12">{{ dateShow }}</text>
      <up-icon name="arrow-down" color="#1F2428" size="16rpx" />
    </view>

    <!-- 日 -->
    <up-datetime-picker
      v-model="dayValue"
      :show="dayShow"
      :min-date="Date.parse(`${new Date().getFullYear() - 1}/1/1`)"
      :max-date="maxDate ? Date.parse(maxDate) : Date.now() - 24 * 60 * 60 * 1000"
      mode="date"
      :itemHeight="50"
      @confirm.stop="handleDayConfirm"
      @cancel.stop="dayShow = false"
    />
    <!-- 月 -->
    <up-datetime-picker
      v-model="monthValue"
      :show="monthShow"
      :min-date="Date.parse(`${new Date().getFullYear() - 1}/1/1`)"
      :max-date="maxDate ? Date.parse(maxDate) : Date.now() - 24 * 60 * 60 * 1000"
      mode="year-month"
      :itemHeight="50"
      @confirm.stop="handleMonthConfirm"
      @cancel.stop="monthShow = false"
    />
    <!-- 季 -->
    <up-picker
      :default-index="[1, Math.floor(new Date().getMonth() / 3)]"
      :show="quarterShow"
      :columns="quarterColumns"
      :itemHeight="50"
      @confirm.stop="handleQuarterConfirm"
      @cancel.stop="quarterShow = false"
    />
    <!-- 年 -->
    <up-picker :default-index="[1]" :show="yearShow" :columns="yearColums" :itemHeight="50" @confirm.stop="handleYearConfirm" @cancel.stop="yearShow = false" />
    <!-- 自定义 -->
    <!-- <up-calendar :show="customShow" mode="range" min-date="2000-01-01" max-date="2030-12-31" @confirm="handleCustomConfirm" @close="customShow = false" /> -->
    <uni-calendar
      ref="calendar"
      :insert="false"
      :range="true"
      :start-date="`${new Date().getFullYear() - 1}-1-1`"
      :end-date="formatDate(Date.now() - 24 * 60 * 60 * 1000)"
      @confirm.stop="handleCustomConfirm"
    />
    <cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
  </view>
</template>

<script lang="ts" setup name="cDatePicker">
  import { ref, watch } from 'vue'
  import { formatMonth, formatDate, showToast } from '@/utils/util'
  import cModal from "@/components/c-modal/index";
  defineOptions({
    options: {
      styleIsolation: 'shared',
    },
  })

  const props = defineProps({
    showRange: {
      type: Boolean,
      default: true,
    },

    maxDate: {
      type: String,
      default: '',
    },

    showRule:{
      type: Boolean,
      default: false,
    }
  })

  const emit = defineEmits(['setDate', 'setDateRange'])

  const quarterColumns = ref<Array<Array<string | number>>>([[], []])
  const yearColums = ref<Array<Array<string | number>>>([[]])
  const setColums = () => {
    const year = new Date().getFullYear()
    for (let i = year - 1; i <= year; i++) {
      yearColums.value[0].push(i)
    }

    quarterColumns.value[0] = yearColums.value[0]
    quarterColumns.value[1][0] = '第一季度'
    quarterColumns.value[1][1] = '第二季度'
    quarterColumns.value[1][2] = '第三季度'
    quarterColumns.value[1][3] = '第四季度'
  }

  const dayValue = ref(Date.now() - 24 * 60 * 60 * 1000)
  const monthValue = ref(Date.parse(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`))
  const dayShow = ref<boolean>(false)
  const monthShow = ref<boolean>(false)
  const quarterShow = ref<boolean>(false)
  const yearShow = ref<boolean>(false)
  const dateType = ref<string>('day')
  const calendar = ref<any>(null)
  const setDateType = (type: string) => {
    dateType.value = type
    switch (type) {
      case 'day':
        dayShow.value = true
        break
      case 'month':
        monthShow.value = true
        break
      case 'quarter':
        quarterShow.value = true
        break
      case 'year':
        yearShow.value = true
        break
      case 'range':
        calendar.value.open()
        break
      default:
    }
  }

  const dateShow = ref<string>(formatDate(dayValue.value))
  const handleDayConfirm = (e: { value: number | string }) => {
    dateShow.value = formatDate(e.value)
    dayShow.value = false
    emit('setDate', dateType.value, new Date(e.value).getFullYear(), dateShow.value)
  }

  const handleMonthConfirm = (e: { value: number | string }) => {
    dateShow.value = formatMonth(e.value)
    monthShow.value = false
    emit('setDate', dateType.value, new Date(e.value).getFullYear(), new Date(e.value).getMonth() + 1)
  }

  const handleQuarterConfirm = (e: any) => {
    dateShow.value = `${e.value[0]}年${e.value[1]}`
    quarterShow.value = false
    emit('setDate', dateType.value, e.value[0], e.indexs[1] + 1)
  }

  const handleYearConfirm = (e: any) => {
    dateShow.value = `${e.value[0]}年`
    yearShow.value = false
    emit('setDate', dateType.value, e.value[0])
  }

  const handleCustomConfirm = (e: any) => {
    if (!e.range.data.length) {
      showToast('请选择时间段。')
      return
    }
    if (e.range.data[0].slice(0, 4) !== e.range.data[e.range.data.length - 1].slice(0, 4)) {
      showToast('请在同一年度内选择日期。')
      return
    }
    dateShow.value = `${e.range.data[0]}至${e.range.data[e.range.data.length - 1]}`
    calendar.value.close()
    emit('setDate', dateType.value, e.range.data[0], e.range.data[e.range.data.length - 1])
  }
  // 弹窗部分
  const modalShow = ref<boolean>(false);
  const modalTitle = ref<string>("");
  const modalContent = ref<string>("");
  const showModal = () => {
    modalShow.value = true;
    modalTitle.value = "日期说明";
    const _content = `
    移动端间夜：该日期代表账务日期。\n
    APP首次下单：该日期代表离店日期。\n
    APP首次登录：该日期代表首次登录日期。 \n
    `
    modalContent.value = _content;
  };  
  watch(
    () => props.maxDate,
    (newValue) => {
      if (newValue < dateShow.value) {
        dateShow.value = newValue
      }
    }
  )

  setColums()

  defineExpose({ dateShow, dateType })
</script>

<style lang="scss">
  .date-picker {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding-top: 40rpx;
    .date-content {
      display: flex;
      .date-group {
        display: flex;
        align-items: center;
        height: 48rpx;
        margin: 0 20rpx 40rpx 0;
        border: 2rpx solid #9999994d;
        background: #ffffffe6;
        border-radius: 24rpx;

        .date-item {
          padding: 0 24rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }
      .date-info {
        width: 22rpx;
        height: 22rpx;
        margin-top: 10rpx;
        margin-right: 20rpx;
      }
    }

    .date-range {
      height: 48rpx;
      padding: 0rpx 40rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #1f2428;
      border: 2rpx solid #9999994d;
      background: #ffffffe6;
      border-radius: 24rpx;
      display: flex;
      align-items: center;
      .mr-12 {
        margin-right: 12rpx;
      }
    }
    .u-popup__content {
      border-radius: 40rpx !important;

      .u-toolbar {
        height: 156rpx !important;
        padding: 0 40rpx !important;

        .u-toolbar__wrapper__cancel {
          font-size: 30rpx !important;
          line-height: 30rpx !important;
          color: #999999 !important;
        }

        .u-toolbar__title {
          font-size: 36rpx !important;
          line-height: 36rpx !important;
          color: #1f2428 !important;
        }

        .u-toolbar__wrapper__confirm {
          font-size: 30rpx !important;
          line-height: 30rpx !important;
          color: #c35943 !important;
        }
      }

      .u-picker__view__column__item {
        font-size: 26rpx !important;
        color: #1f2428 !important;
      }
    }

    .uni-calendar__content {
      border-radius: 40rpx !important;

      .uni-calendar--fixed-top {
        border-top: none;
        height: 156rpx !important;
        padding: 0 40rpx !important;

        .uni-calendar__header-text {
          font-size: 30rpx !important;
          color: #999999 !important;
        }

        .uni-calendar__header-text {
          font-size: 30rpx !important;
          color: #c35943 !important;
        }
      }

      .uni-calendar-item--isDay {
        color: #fff !important;
        background: #f75e3b !important;
      }

      .uni-calendar-item--before-checked {
        background: #f75e3b !important;
      }

      .uni-calendar-item--multiple {
        background: #ffb2a1 !important;
      }
    }
  }
</style>
