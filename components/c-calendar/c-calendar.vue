<template>
	<view>
		<view class="calendar-picker" @click="open" :class="{'custom':showAction}">
			<view class="fr">
				<image class="img" v-if="showIcon"
					src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-31/1730357644772.png"
					alt="" />
				<view class="date-text">{{showValue}}</view>
			</view>
			<!-- 使用 v-if 判断 showAction 和插槽内容 -->
			<template v-if="showAction || $slots.right">
				<slot name="right">
					<!-- 默认的搜索按钮 -->
					<view class="search" @click="$emit('search',value)">
						查询
					</view>
				</slot>
			</template>
			<!-- 如果没有传入 showAction 或没有插槽内容，显示默认图标 -->
			<up-icon v-else name="arrow-down" color="#999999" size="22rpx" />
		</view>
		<uni-calendar ref="calendar" :insert="false" :range="true"
			:end-date="formatDate(new Date(new Date().setDate(new Date().getDate() - 1)))" @confirm="confirm" v-bind="$attrs"/>
	</view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	import { formatDate, showToast } from '@/utils/util';

	const props = defineProps({
		showAction: {
			type: Boolean,
			default: false
		},
		showIcon: {
			type: Boolean,
			default: true
		},
		modelValue: {
			type: Array,
			default: () => []
		}
	})

	const emit = defineEmits(['confirm', 'search', 'update:modelValue'])

	const calendar = ref(), showValue = ref(''), value = ref()
	const open = () => {
		calendar.value.open();
	}

	const confirm = (e) => {
		console.log(e);
		if (!e.range.data.length) {
			showToast('请选择时间段');
			return;
		}
		emit('update:modelValue', [e.range.data[0], e.range.data[e.range.data.length - 1]])
		value.value = e
		emit('confirm', e)
		calendar.value.close();
	}

	watch(() => props.modelValue,
		(v) => {
			if (v.length > 0) {
				showValue.value = props.modelValue[0] + ' 至 ' + props.modelValue[1]
			}
		},
		{ deep: true, immediate: true }
	)

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
</script>

<style lang="scss">
	.custom {
		padding-right: 0 !important;
	}

	.calendar-picker {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 200rpx;
		padding: 0 40rpx;
		border: 2rpx solid #f2f2f2;
		background: #F5F5F5;
		height: 64rpx;

		.img {
			width: 28rpx;
			height: 26.96rpx;
			flex-shrink: 0;
			margin-right: 20rpx;
		}

		.date-text {
			color: #1f2428;
			font-size: 22rpx;
			line-height: 22rpx;
		}
	}


	.search {
		padding: 12rpx 28rpx;
		border-radius: 150rpx;
		background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
		color: #ffffff;
		font-size: 24rpx;
		font-weight: bold;
		line-height: 24rpx;
		margin-right: 8rpx;
	}

	:deep() {
		.uni-calendar__content {
			border-radius: 40rpx !important;

			.uni-calendar--fixed-top {
				border-top: none;
				height: 156rpx !important;
				padding: 0 40rpx !important;

				.uni-calendar__header-text {
					font-size: 30rpx !important;
					color: #999999 !important;
				}

				.uni-calendar__header-text {
					font-size: 30rpx !important;
					color: #C35943 !important;
				}
			}

			.uni-calendar-item--isDay {
				color: #fff !important;
				background: #F75E3B !important;
			}

			.uni-calendar-item--before-checked {
				background: #F75E3B !important;
			}

			.uni-calendar-item--multiple {
				background: #FFB2A1 !important;
			}
		}
	}
</style>