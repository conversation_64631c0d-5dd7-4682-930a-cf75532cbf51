<template>
  <view class="c-date">
    <view class="date-wraper" @click="handleOpen">
      <text class="date-text">{{ dateShow }}</text>
      <up-icon v-if="!dayShow" name="arrow-down" color="#1F2428" size="16rpx" />
      <up-icon v-else name="arrow-up" color="#1F2428" size="16rpx" />
    </view>

    <!-- 日 -->
    <up-datetime-picker v-model="dayValue" :show="dayShow" :min-date="minDate && Date.parse(minDate)" :max-date="maxDate ? Date.parse(maxDate) : Date.now() - 24 * 60 * 60 * 1000" mode="date" :itemHeight="50" @confirm="handleDayConfirm" @cancel="dayShow = false" />
    <!-- 月 -->
    <up-datetime-picker v-model="monthValue" :show="monthShow" :min-date="minDate && Date.parse(minDate)" :max-date="maxDate ? Date.parse(maxDate) : Date.now() - 24 * 60 * 60 * 1000" mode="year-month" :itemHeight="50" @confirm="handleMonthConfirm" @cancel="monthShow = false" />
  </view>
</template>

<script lang="ts" setup name="cDate">
import { ref, watch } from 'vue';
import { formatDate, formatMonth } from '@/utils/util.ts';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const props = defineProps({
  dateType: {
    type: String,
    default: 'day'
  },

  minDate: {
    default: null,
  },

  maxDate: {
    default: null,
  }
});

const emit = defineEmits(['setDate', 'setDateRange']);

const dayValue = ref(Date.now() - 24 * 60 * 60 * 1000);
const dayShow = ref<boolean>(false);
const monthValue = ref(Date.parse(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`));
const monthShow = ref<boolean>(false);
const dateShow = ref<string>(props.dateType === 'day' ? formatDate(dayValue.value) : formatMonth(monthValue.value));
const handleDayConfirm = (e: { value: number | string }) => {
  dateShow.value = formatDate(e.value);
  dayShow.value = false;
  emit('setDate', dateShow.value);
};

const handleMonthConfirm = (e: { value: number | string }) => {
  dateShow.value = formatMonth(e.value);
  monthShow.value = false;
  emit('setDate', dateShow.value);
};

const handleOpen = () => {
  if (props.dateType === 'day') {
    dayShow.value = true
  } else {
    monthShow.value = true
  }
}

watch(() => props.dateType, (val) => {
  if (val === 'day') {
    dateShow.value = formatDate(dayValue.value)
  } else {
    dateShow.value = formatMonth(monthValue.value)
  }
})

watch(() => props.maxDate, (newValue) => {
  if (!newValue) return
  if (newValue < dateShow.value) {
    dayValue.value = Date.parse(newValue)
    monthValue.value = Date.parse((newValue as string).slice(0, 7) + '-01')
    dateShow.value = newValue;
  }
})
defineExpose({monthValue,dateShow})
</script>

<style lang="scss">
.c-date {
  display: flex;
  justify-content: flex-end;

  .date-wraper {
    height: 54rpx;
    display: flex;
    align-items: center;
    padding: 0 16rpx;
    border: 2rpx solid #f2f2f2;
    border-radius: 10rpx;

    .date-text {
      padding-right: 36rpx;
      font-size: 22rpx;
      color: #1F2428;
    }
  }

  .u-popup__content {
    border-radius: 40rpx !important;

    .u-toolbar {
      height: 156rpx !important;
      padding: 0 40rpx !important;

      .u-toolbar__wrapper__cancel {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #999999 !important;
      }

      .u-toolbar__title {
        font-size: 36rpx !important;
        line-height: 36rpx !important;
        color: #1F2428 !important;
      }

      .u-toolbar__wrapper__confirm {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #C35943 !important;
      }
    }

    .u-picker__view__column__item {
      font-size: 26rpx !important;
      color: #1F2428 !important;
    }
  }
}
</style>
