<template>
	<view class="fl mask-fixed__step" :class="[arrow]" v-if="visible">
		<view class="step-desc">
			<slot><text>{{content}}</text></slot>
		</view>
		<view class="fr">
			<view class="fr">
				<text>{{ current }}/</text>
				<text class="grey">{{ max }}</text>
			</view>
			<text class="fr step-btn theme-primary" @click.stop="complete">{{ confirmText }}</text>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, computed, getCurrentInstance, ref, watch } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	const props = defineProps({
		modelValue: {
			type: Boolean,
			default: false
		},
		content: {
			type: String,
			default: '如果您负责多个门店，或者有多个身份，点此切换'
		},
		current: {
			type: Number,
			default: 1
		},
		top: {
			type: String,
			default: '130rpx'
		},
		confirmText: {
			type: String,
			default: '下一步'
		},
		arrow: {
			type: String,
			default: 'up'
		}
	}), emits = defineEmits(['update:modelValue', 'complete'])
	const visible = ref(props.modelValue), { globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance),
		max = computed(() => {
			const groupName = globalData.value.shop?.groupName
			if (groupName == '店长') {
				return 6
			} else if (groupName == '业主') {
				return 7
			} else {
				return 4
			}
		})
	watch(() => props.modelValue, v => {
		visible.value = v
	})
	const complete = () => {
		emits('complete')
	}
</script>

<style scoped lang="scss">
	.mask-fixed__step {
		position: absolute;
		top: v-bind(top);
		right: 0;
		margin: auto;
		padding: 28rpx;
		font-size: 28rpx;
		width: 510rpx;
		background: linear-gradient(124.7deg, #4298ff 0%, #1e61fc 100%);
		border-radius: 20rpx;

		&>.fr {
			justify-content: space-between;
			font-size: 24rpx;
			color: #fff;

			.grey {
				color: rgba(255, 255, 255, 0.57);
			}
		}
		&.up, &.down{
			left: 0;
		}
		&.up::before {
			content: "";
			position: absolute;
			top: -15rpx;
			right: 50rpx;
			width: 0;
			height: 0;
			border-left: 15rpx solid transparent;
			border-right: 15rpx solid transparent;
			border-bottom: 15rpx solid #256CFD;
		}

		&.down::before {
			content: "";
			position: absolute;
			bottom: -15rpx;
			left: 50rpx;
			width: 0;
			height: 0;
			border-left: 15rpx solid transparent;
			border-right: 15rpx solid transparent;
			border-top: 15rpx solid #3E93FF;
		}
		&.right::before {
			content: "";
			position: absolute;
			bottom: -15rpx;
			right: 50rpx;
			width: 0;
			height: 0;
			border-left: 15rpx solid transparent;
			border-right: 15rpx solid transparent;
			border-top: 15rpx solid #256CFD;
		}
		
		.step-desc {
			margin-bottom: 20rpx;
			color: #fff;
		}

		.step-btn {
			justify-content: center;
			width: 168rpx;
			height: 58rpx;
			font-size: 24rpx;
			background-color: #fff;
			border-radius: 162rpx;
		}
	}
</style>