<template>
	<view class="page-layout"  :style="{'padding-bottom': bottomGap ? safeAreaInsets_bottom : 0}">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	defineProps({
		bottomGap: {
			type: Boolean,
			default: true
		}
	})
	const safeAreaInsets_bottom = (uni.getSystemInfoSync().safeAreaInsets.bottom || 34) + 'px'
</script>

<style lang="scss" scoped>
	.page-layout {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}
</style>