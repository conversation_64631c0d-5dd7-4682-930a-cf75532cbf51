<template>
  <view class="btn-group" v-if="module === 1">
    <view v-for="item in btnList" :key="item.value" class="btn-item" :class="{ active: tabType === item.value }" @click.stop="setBtnType(item)">
      {{ item.name }}
    </view>
  </view>
  <cTabs v-if="module === 2" :list="btnList" v-model:modelValue="tabIndex" textColor="#1F2428" @change="handleTabChange"></cTabs>
</template>

<script lang="ts" setup name="cBtnGroup">
import cTabs from '@/components/c-tabs2/index.vue'
import { computed, ref } from 'vue';

const props = defineProps({
  level: {
    type: String,
    default: '0',
  },

  tabType: {
    type: String,
    default: '10',
  },

  module: {
    type: Number,
    default: 1, // 1 - 按钮 2 - tab
  },

  showShop: { // 是否显示门店按钮
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:tabType', 'change'])

const btnList = computed(() => {
  const list = [
    {
      name: '地区总部',
      value: '10'
    },
    {
      name: '大区',
      value: '30'
    },
    {
      name: '城区',
      value: '40'
    },
    {
      name: '门店',
      value: '50'
    }
  ]

  if (Number(props.level) <= 10) { // 集团、地区总部
    emit('update:tabType', '10')
    return list.filter((item) => Number(item.value) <= (props.showShop ? 50 : 40))
  } else if (Number(props.level) === 30) { // 大区
    emit('update:tabType', '30')
    return list.filter((item) => Number(item.value) >= 30 && Number(item.value) <= (props.showShop ? 50 : 40))
  } else if (Number(props.level) === 40) { // 城区
    emit('update:tabType', '40')
    return list.filter((item) => Number(item.value) >= 40)
  }
})

const setBtnType = (item: { name: string, value: string }) => {
  if (props.tabType === item.value) return
  emit('update:tabType', item.value)
  emit('change', item.value)
}

const tabIndex = ref<number>(0)
const handleTabChange = () => {
  emit('update:tabType', btnList.value[tabIndex.value].value)
  emit('change', btnList.value[tabIndex.value].value)
}
</script>

<style lang="scss" scoped>
.btn-group {
  display: inline-flex;
  align-items: center;
  height: 48rpx;
  border: 2rpx solid #f2f2f2;
  border-radius: 24rpx;

  .btn-item {
    padding: 0 24rpx;
    font-size: 22rpx;
    line-height: 44rpx;
    color: rgba(31, 36, 40, 0.7);
  }

  .active {
    border-radius: 22rpx;
    background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
    font-weight: bold;
    color: #fff;
  }
}
</style>