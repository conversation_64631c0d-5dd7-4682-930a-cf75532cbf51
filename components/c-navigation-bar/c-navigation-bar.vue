<template>
  <view class="navigation-bar-view text-center" :style="{ 'padding-top': `${statusBarHeight * 2}rpx`, 'line-height': `${navigatorHeight * 2}rpx` }">
    {{ navigationTitle }}
    <view v-if="showBack" class="back-view" :style="{ top: `${statusBarHeight * 2 + (navigatorHeight - 18)}rpx` }" @click="backClick()">
      <up-icon name="arrow-left" :size="18" :color="color" />
    </view>
  </view>
</template>

<script setup lang="ts" name="cNavigationBar">
import { ref } from 'vue';
import { useNav } from '@/hooks/useNav';

const props = defineProps({
  navigationTitle: {
    type: String,
    default: '',
  },
  showBack: {
    type: Boolean,
    default: true,
  },
  goBack: {
    type: [Function, Number],
    default: 0,
  },
  color: {
    type: String,
    default: '#fff'
  }
});

const activeColor = ref<string>(props.color)
const backClick = () => {
  if (typeof props.goBack === 'function') {
    props.goBack();
  }
  else {
    uni.navigateBack();
  }
};
const statusBarHeight = ref<number>(0); // 状态栏的高度
const navigatorHeight = ref<number>(0); // 导航栏高度
const menuHeight = ref<number>(0); // 胶囊高度
const menuTop = ref<number>(0); // 胶囊与顶部的距离
const totalHeight = ref<number>(0); // 总高度
if (useNav().statusBarHeight) {
  statusBarHeight.value = useNav().statusBarHeight;
  navigatorHeight.value = useNav().navigatorHeight;
  menuHeight.value = useNav().menuHeight;
  menuTop.value = useNav().menuTop;
  totalHeight.value = useNav().totalHeight;
} else {
  useNav().then((res: any) => {
    statusBarHeight.value = res.statusBarHeight;
    navigatorHeight.value = res.navigatorHeight;
    menuHeight.value = res.menuHeight;
    menuTop.value = res.menuTop;
    totalHeight.value = res.totalHeight;
  });
}
</script>

<style lang="scss">
.navigation-bar-view {
  position: sticky;
  top: 0;
  width: 100%;
  text-align: center;
  font-size: 36rpx;
  color: v-bind(activeColor);
  z-index: 99;

  .back-view {
    position: absolute;
    left: 30rpx;
  }
}
</style>
