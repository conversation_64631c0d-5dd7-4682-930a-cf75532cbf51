<template>
  <view class="z-paging-table-container">
    <!-- 表格头部 -->
    <view v-if="showHeader" class="table-header" :class="{ 'header-sticky': stickyHeader }">
      <uni-table :border="border">
        <uni-thead>
          <uni-tr>
            <uni-th 
              v-for="(column, index) in columns" 
              :key="column.key"
              :width="column.width"
              :align="column.align || 'left'"
              :sortable="column.sortable"
              :fixed="column.fixed"
              :fixed-left="getFixedLeft(index)"
              @sort-change="handleSortChange"
            >
              {{ column.title }}
            </uni-th>
          </uni-tr>
        </uni-thead>
      </uni-table>
    </view>

    <!-- z-paging 容器 -->
    <z-paging 
      ref="paging"
      v-model="dataList"
      :refresher-enabled="refresherEnabled"
      :loading-more-enabled="loadingMoreEnabled"
      :empty-view-text="emptyText"
      :auto-show-back-to-top="autoShowBackToTop"
      :lower-threshold="lowerThreshold"
      @query="queryList"
      @refresh="handleRefresh"
      @scroll="handleScroll"
    >
      <!-- 自定义顶部内容 -->
      <template #top>
        <slot name="top"></slot>
      </template>

      <!-- 表格内容 -->
      <view class="table-content">
        <uni-table 
          :data="dataList" 
          :border="border" 
          :stripe="stripe"
          :loading="false"
          :empty-text="''"
          :row-key="rowKey"
          @selection-change="handleSelectionChange"
        >
          <!-- 如果不显示表头，这里显示表头 -->
          <uni-thead v-if="!showHeader">
            <uni-tr>
              <uni-th 
                v-for="(column, index) in columns" 
                :key="column.key"
                :width="column.width"
                :align="column.align || 'left'"
                :sortable="column.sortable"
                :fixed="column.fixed"
                :fixed-left="getFixedLeft(index)"
                @sort-change="handleSortChange"
              >
                {{ column.title }}
              </uni-th>
            </uni-tr>
          </uni-thead>
          
          <!-- 数据行 -->
          <uni-tr 
            v-for="(row, rowIndex) in dataList" 
            :key="getRowKey(row, rowIndex)"
            :key-value="getRowKey(row, rowIndex)"
          >
            <uni-td 
              v-for="(column, colIndex) in columns" 
              :key="column.key"
              :width="column.width"
              :align="column.align || 'left'"
              :fixed="column.fixed"
              :fixed-left="getFixedLeft(colIndex)"
            >
              <slot 
                :name="column.key" 
                :row="row" 
                :column="column" 
                :index="rowIndex"
                :value="getColumnValue(row, column.key)"
              >
                {{ getColumnValue(row, column.key) }}
              </slot>
            </uni-td>
          </uni-tr>
        </uni-table>
      </view>

      <!-- 自定义底部内容 -->
      <template #bottom>
        <slot name="bottom"></slot>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

interface Column {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
}

interface Props {
  columns: Column[]
  border?: boolean
  stripe?: boolean
  rowKey?: string
  emptyText?: string
  showHeader?: boolean
  stickyHeader?: boolean
  refresherEnabled?: boolean
  loadingMoreEnabled?: boolean
  autoShowBackToTop?: boolean
  lowerThreshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  border: true,
  stripe: true,
  rowKey: 'id',
  emptyText: '暂无数据',
  showHeader: true,
  stickyHeader: true,
  refresherEnabled: true,
  loadingMoreEnabled: true,
  autoShowBackToTop: true,
  lowerThreshold: 50
})

const emit = defineEmits([
  'query',
  'refresh', 
  'selection-change', 
  'sort-change',
  'scroll'
])

// 数据列表
const dataList = ref([])
const paging = ref()

// 计算固定列的左偏移量
const getFixedLeft = (index: number) => {
  let left = 0
  for (let i = 0; i < index; i++) {
    const column = props.columns[i]
    if (column.fixed) {
      const width = typeof column.width === 'string' 
        ? parseInt(column.width) 
        : (column.width || 120)
      left += width
    } else {
      break // 遇到非固定列就停止计算
    }
  }
  return left
}

// 获取行键值
const getRowKey = (row: any, index: number) => {
  return row[props.rowKey] || index
}

// 获取列值
const getColumnValue = (row: any, key: string) => {
  const keys = key.split('.')
  let value = row
  for (const k of keys) {
    value = value?.[k]
  }
  return value ?? '-'
}

// 查询数据
const queryList = (pageNo: number, pageSize: number) => {
  emit('query', pageNo, pageSize)
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理选择变化
const handleSelectionChange = (e: any) => {
  emit('selection-change', e)
}

// 处理排序变化
const handleSortChange = (e: any) => {
  emit('sort-change', e)
}

// 处理滚动
const handleScroll = (e: any) => {
  emit('scroll', e)
}

// 完成数据加载
const complete = (data: any[], noMore = false) => {
  if (paging.value) {
    paging.value.complete(data, noMore)
  }
}

// 重置数据
const reset = () => {
  if (paging.value) {
    paging.value.reset()
  }
}

// 手动触发刷新
const refresh = () => {
  if (paging.value) {
    paging.value.refresh()
  }
}

// 手动触发加载更多
const loadMore = () => {
  if (paging.value) {
    paging.value.loadMore()
  }
}

// 暴露方法给父组件
defineExpose({
  complete,
  reset,
  refresh,
  loadMore,
  paging
})
</script>

<style lang="scss" scoped>
.z-paging-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  z-index: 100;
  
  &.header-sticky {
    position: sticky;
    top: 0;
  }
  
  /* 隐藏表头的边框，避免重复 */
  :deep(.uni-table) {
    border: none;
    
    .uni-table-th {
      border-bottom: none;
    }
  }
}

.table-content {
  flex: 1;
  
  /* 当显示独立表头时，隐藏内容区域的表头 */
  :deep(.uni-thead) {
    display: none;
  }
}

/* 固定列阴影效果 */
:deep(.fixed-column) {
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

/* z-paging 样式调整 */
:deep(.z-paging-container) {
  height: 100%;
}

:deep(.z-paging-content) {
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    &.header-sticky {
      position: relative; /* 小屏幕下取消粘性定位 */
    }
  }
}
</style>
