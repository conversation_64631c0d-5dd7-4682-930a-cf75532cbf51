<template>
	<view class="table">
		<view class="container">
			<view class="th" v-if="showHeader">
				<view class="tr">
					<template v-for="(item,index) in header" :key="index">
						<view class="td" :style="getStyle(item)" @click.stop="item.sort ? sort(item) : item.info ? showModal(item.name, item.info, item) : () => {}">
							<template v-if="item.headerSlot">
								<slot :name="item.headerSlot"></slot>
							</template>
							<template v-else>
								<view class="td-text">
									<text class="title">{{item.name}}</text>
									<text v-if="item.subTitle" class="sub-title">{{item.subTitle}}</text>
								</view>
								<image v-if="item.info" class="td-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
								<view v-if="item.sort" class="td-sort">
									<up-icon name="arrow-up-fill" :color="item.sortType === 'asc' ? '#1F2428' : '#ccc'" size="16rpx" />
									<up-icon name="arrow-down-fill" :color="item.sortType === 'desc' ? '#1F2428' : '#ccc'" size="16rpx" />
								</view>
							</template>
						</view>
					</template>
				</view>
			</view>
			<view class="tbody">
				<z-paging v-if="isZPaging" :style="{'height':height ? height +'rpx' : '100%'}" :loading-more-enabled="isPaginate" ref="pagingRef" v-model="dataList" :refresher-enabled="false" :fixed="false" @query="(pageNo:number, pageSize:number) => queryList(pageNo, pageSize)">
					<view class="tr" v-for="(item,index) in dataList" >
						<view class="td" :style="getStyle(i)" v-for="(i,idx) in header" :key="idx">
							<!-- 含有子项处理 -->
              <template v-if="item.sub?.length">
								<template v-if="i.canCollapse">
							  	<view class="target-item" @click.stop="toggleItem(item, index)">
										<text class="item-text mr-12">{{ item.dataName }}</text>
                  	<up-icon :name="isExpanded(item) ?'arrow-down-fill':'arrow-up-fill'" size="16rpx" />
                	</view>
								</template>

                <template v-else-if="i.slot">
								  <slot :data="item" :prop="i.prop" :index="index"></slot>
						   	</template>
								<text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto'}">{{item[i.prop] || '-'}}</text>
						  </template>

							<template v-else-if="i.canClick">
								<view v-if="i.type === 'info'" class="target-item" @click.stop="showModal(item.dataName, item.dataDesc, item)">
									<text class="item-text">{{ item.dataName }}</text>
									<text class="item-unit" v-if="i.showUnit && item.unit && item.unit !== '%'">/{{ item.unit }}</text>
									<image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
								</view>
								<view v-if="i.type === 'choose'" class="name-item">
                  <text :class="['item-text', item.canClick && 'line-item']" @click="chooseItem(item)">{{ item.orgName }}</text>
                </view>
							</template>

							<template v-else-if="i.slot">
								<slot :data="item" :prop="i.prop" :index="index"></slot>
							</template>
							<text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto', 'sub-item': isChild(item)}">{{item[i.prop] || '-'}}</text>
						</view>
					</view>

					<template v-slot:empty>
						<empty></empty>
					</template>
				</z-paging>
				<template v-else>
					<scroll-view v-if="list.length > 0" scroll-y="auto" :style="[{ 'max-height': scrollViewHeight ? scrollViewHeight : '100%' }, customStyle]" @scrolltolower="$emit('scrolltolower')">
						<view class="tr" v-for="(item,index) in list" :key="index">
							<view class="td" :style="getStyle(i)" v-for="(i,idx) in header" :key="idx">
								<template v-if="i.canClick">
									<view v-if="i.type === 'info'" class="target-item" @click.stop="showModal(item.dataName, item.dataDesc, item)" >
										<text class="item-text">{{ item.dataName }}</text>
										<text class="item-unit" v-if="i.showUnit && item.unit !== '%'">/{{ item.unit }}</text>
										<image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
									</view>
									<view v-if="i.type === 'choose'" class="name-item">
					          <text :class="['item-text', item.canClick && 'line-item' ]" @click="chooseItem(item)">{{ item.orgName }}</text>
					        </view>
								</template>
								<template v-else-if="i.slot">
									<slot :data="item" :prop="i.prop" :index="index"></slot>
								</template>
								<text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto' }">{{item[i.prop] || '-'}}</text>
							</view>
						</view>
            <up-loadmore v-if="loadingMore" :status="status" icon-size="22rpx" font-size="22rpx" :line="status === 'nomore'" loadingText="正在加载" color="#a4a4a4" />
					</scroll-view>
					<view class="empty-text" v-else-if="emptyText">{{emptyText}}</view>
					<view class="empty-img" v-else>
						<empty></empty>
						<!-- 暂无数据 -->
					</view>
				</template>
			</view>
			<slot></slot>
		</view>
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts">
	import { ref, computed } from 'vue';
	import type zPaging from 'z-paging/components/z-paging/z-paging.vue';
	import cModal from '@/components/c-modal/index'
	import empty from '@/components/empty/empty.vue'

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	interface Header {
		name : string;
		prop : string;
		sort ?: boolean;
		sortType ?: string;
		slot ?: string;
		[key : string] : any;
	};

	interface List {
		[key : string] : any;
	};

	const props = withDefaults(
		defineProps<{
			showHeader: Boolean;
			header : Header[];
			list : List[];
			cellWidth : Number | String;
			cellHeight : Number | String;
			height : Number | String;
			isPaginate: Boolean;
			isZPaging: Boolean;
			emptyText: String;
      customStyle: object;
      loadingMore?: boolean;
		}>(), {
		showHeader: () => true,
		header: () => [],
		list: () => [],
		cellWidth: () => 'auto',
		cellHeight: () => '84',
		height: () => 0,
		isPaginate: () => true,
		isZPaging: () => false,
		emptyText: () => '',
    customStyle: () => ({}),
    loadingMore: () => false
	}
	);
	const cellWidth = props.cellWidth !== 'auto' ? props.cellWidth + 'rpx' : 'auto'
	const cellHeight = props.cellHeight !== 'auto' ? props.cellHeight + 'rpx' : 'auto'

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')
	const showModal = (title: string, content: string, data: any) => {
		modalShow.value = true
		modalTitle.value = title
		modalContent.value = content
	}

  const status = ref('loadmore')

	const getStyle = (item: Header) => {
		if (item.width) {
			return { width: item.width + 'rpx' }
		} else {
			return { flex: '1' }
		}
	}
	
	const scrollViewHeight = computed(() => {
		if(props.height == 0) return props.height
		return handleUnit(props.height)
	})
	
	const handleUnit = (value:String | Number) => {
		const flag = value.toString().includes('px')
		if (flag) return value
		else return value + 'rpx'
	}

	const emit = defineEmits(['updateList', 'getMore', 'scrolltolower', 'chooseItem']);
	const sort = (item: Header) => {
		if (!item.sort)
			return;

		if (item.sortType === 'asc') {
			item.sortType = 'desc';
		}
		else if (item.sortType === 'desc') {
			item.sortType = '';
		}
		else {
			item.sortType = 'asc';
		}

		emit('updateList', item);

		props.header.forEach((col : Header) => {
			if (col.prop !== item.prop) {
				col.sortType = '';
			}
		});
	};

	const chooseItem = (data: any) => {
		emit('chooseItem', data)
	}

	const pagingRef = ref<InstanceType<typeof zPaging> | null>(null);
	const queryList = (pageNum: number, pageSize: number) => {
		emit('getMore', pageNum, pageSize)
	};

	const dataList = ref<Array<any>>([])
	const setData = (list: Array<any>) => {
		pagingRef.value.complete(list);
	}

	const reload = () => {
		pagingRef.value.reload();
	}

	// 本地分页
	const setLocalPaging = (list: Array<any>) => {
		pagingRef.value.setLocalPaging(list);
	}

	const clearData = () => {
		pagingRef.value?.clear()
	}
  
	// 数据源含有子项
  const expandedKeys = ref<Set<string>>(new Set()); // 使用唯一标识符记录展开状态
  
  // 生成唯一标识，使用 UUID 保证唯一性
  const generateUniqueKey = (item: any) => {
    if (item.id) return `id-${item.id}`;
    if (item.dataName) {
      // 结合数据内容生成哈希值，避免重名问题
      let hash = 0;
      for (let i = 0; i < item.dataName.length; i++) {
        const char = item.dataName.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return `name-${hash}`;
    }
    return `uuid-${Math.random().toString(36).substring(2, 9)}`;
  }
  
  // 递归移除子项(只做一层子项处理)
  const removeChildren = (data: Array<any>, key: string) => {
    return data.filter((d) => {
      if (d.__parentKey === key) {
        return false;
      }
      return true;
    });
  }
  
  const toggleItem = (item: any, index: number) => {
    if (!item.sub || item.sub.length === 0) return;
    
    // 生成唯一标识
    const key =  generateUniqueKey(item);
  
    const isExpanded = expandedKeys.value.has(key);
  
    let newData = [...dataList.value];
  
    if (isExpanded) {
      // 折叠：递归移除所有 __parentKey === 当前项的数据
      newData = removeChildren(newData, key);
    } else {
      // 准确找到当前父项在 newData 中的位置
      const actualIndex = newData.findIndex((d) => {
        if (d.__isChild) return false;
        const currentKey = generateUniqueKey(d);
        return currentKey === key;
      });
  
      if (actualIndex !== -1) {
        // 先移除已存在的子项，避免重复插入
        newData = removeChildren(newData, key);
  
        // 插入子项并打上 parentKey 标记
        const newSubItems = item.sub.map((sub) => ({
          ...sub,
          __isChild: true,
          __parentKey: key,
          __originalKey: generateUniqueKey(sub) // 记录子项唯一标识
        }));
        newData.splice(actualIndex + 1, 0, ...newSubItems);
      }
    }
  
    isExpanded ? expandedKeys.value.delete(key) : expandedKeys.value.add(key);
    dataList.value = newData;
  }
  
  const isExpanded = (item: any): boolean => {
    const key = generateUniqueKey(item);
    return expandedKeys.value.has(key);
  }
  
  const isChild = (item: any): boolean => {
    return !!item.__parentKey;
  }

	defineExpose({
		setData,
		clearData,
		reload,
		setLocalPaging,
    customLoadStatus: status
	})
</script>

<style lang="scss">
	.table {
		height: 100%;
		color: #1f2428;
		font-size: 22rpx;
		text-align: center;
		line-height: 22rpx;
		overflow-x: auto;
		background: #fff;
		border-radius: 28rpx;

		.container {
			min-width: max-content;
			height: 100%;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.th {
			font-weight: bold;
			background: #FFE0D9;
			
			.tr{
				height: 84rpx;

				.td {
					background: #FFE0D9;
				}
			}
		}

		.tr {
			display: flex;
			align-items: center;
			height: v-bind(cellHeight);
		}

		.td {
			position: relative;;
			display: flex;
			align-items: center;
			justify-content: center;
			width: v-bind(cellWidth);
			padding: 0 8rpx;
			word-break: break-all;
			height: v-bind(cellHeight);

			.td-text {
				display: flex;
				flex-direction: column;
				align-items: center;
				color: #1f2428;
				font-size: 22rpx;
				line-height: 32rpx;

				.title {
					font-size: 22rpx;
					line-height: 22rpx;
					font-weight: bold;
				}

				.sub-title {
					padding-top: 8rpx;
					font-size: 18rpx;
					line-height: 18rpx;
				}
			}

			.td-info {
				margin-left: 8rpx;
				width: 22rpx;
				height: 22rpx;
			}

			.td-sort {
				margin-left: 8rpx;
			}
		}

		.tbody {
			flex: 1;
			overflow: hidden;
			line-height: 32rpx;
			display: flex;
			flex-direction: column;

			.tr {
				padding: 32rpx 0;
			}
			.tr:nth-child(odd) {
				background: #f75e3b0a;
			}

			.tr:nth-child(even) {
				background: #f75e3b14;
			}

			.td-text-clamp {
				display: -webkit-box;//将盒子转换为弹性盒子
				-webkit-box-orient: vertical;//文本显示方式，默认水平
				-webkit-line-clamp: 2;//设置显示多少行
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.sub-item {
				color: #1f2428b3;
			}
		}

		.empty-text {
			height: 84rpx;
			text-align: center;
			background: #f75e3b0a;
			line-height: 84rpx;
		}
		
		.empty-img {
		}

		.target-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;

			.item-unit {
				padding-top: 4rpx;
				font-size: 18rpx;
				line-height: 18rpx;
			}

			.item-info {
				margin-left: 8rpx;
				width: 22rpx;
				height: 22rpx;
			}
			.mr-12 {
				margin-right: 12rpx;
			}
		}

		.name-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.item-text {
				display: -webkit-box;
				max-width: 120rpx;
				overflow: hidden;//文本超出隐藏
				text-overflow: ellipsis;
				-webkit-box-orient: vertical;//文本显示方式，默认水平
				-webkit-line-clamp: 2;//设置显示多少行
			}
			.item-arrow{
				margin-left: 20rpx;
				font-size: 30rpx;
			}
			.line-item {
				color: #C35943;
				// text-decoration: underline;
			}

			.item-info {
				padding-top: 12rpx;
				display: flex;
				align-items: center;

				.info-text {
					font-size: 18rpx;
					line-height: 20rpx;
					color: #999999;
				}

				.item-icon {
					width: 12rpx;
					height: 16rpx;
					margin: 0 8rpx;
				}

				.item-num {
					font-size: 20rpx;
					line-height: 20rpx;
					font-weight: bold;
					font-family: "DIN Bold";
				}

				.up-num {
					color: #FF4D4D;
				}

				.down-num {
					color: #56CC93;
				}
			}
		}
	}
</style>

<style lang="scss">
z-paging {
	height: 100%;
	overflow: auto;

	.zp-l-text-rpx {
		font-size: 22rpx !important;
	}
}
</style>
