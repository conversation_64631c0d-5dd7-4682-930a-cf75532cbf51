<template>
	<view class="table-page">
    <uni-table stripe :has-fixed-columns="hasFixedColumns" :fixed-columns-width="fixedColumnsWidth">
      <template #fixed>
        <!-- 表头行 -->
        <uni-thead v-if="showHeader">
          <uni-tr>
            <template v-for="(item,index) in header">
              <uni-th v-if="item.fixed" :key="index" :align="item.align || 'center'" :width="item.width">
                <view class="th-item flex items-center justify-center"  @click.stop="item.sort ? sort(item) : item.info ? showModal(item.name, item.info, item) : () => {}">
                  <template v-if="item.headerSlot">
                    <slot :name="item.headerSlot"></slot>
                  </template>
                  <template v-else>
                    <view class="td-text">
                      <text class="title">{{item.name}}</text>
                      <text v-if="item.subTitle" class="sub-title">{{item.subTitle}}</text>
                    </view>
                    <image v-if="item.info" class="td-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
                    <view v-if="item.sort" class="td-sort">
                      <up-icon name="arrow-up-fill" :color="item.sortType === 'asc' ? '#1F2428' : '#ccc'" size="16rpx" />
                      <up-icon name="arrow-down-fill" :color="item.sortType === 'desc' ? '#1F2428' : '#ccc'" size="16rpx" />
                    </view>
                  </template>
                </view>
              </uni-th>
            </template>
          </uni-tr>
        </uni-thead>

        <!-- 表格数据行 -->
        <template v-for="(item,index) in list" :key="index">
          <uni-tr>
            <template v-for="(i,idx) in header"> 
              <uni-td v-if="i.fixed" :key="idx" :align="item.align || 'center'" :width="i.width">
                <template v-if="i.canClick">
                  <view v-if="i.type === 'info'" class="target-item" @click.stop="showModal(item.dataName, item.dataDesc, item)">
                    <text class="item-text">{{ item.dataName }}</text>
                    <text class="item-unit" v-if="i.showUnit && item.unit && item.unit !== '%'">/{{ item.unit }}</text>
                    <image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
                  </view>
                  <view v-if="i.type === 'choose'" class="name-item">
                    <text :class="['item-text', item.canClick && 'line-item']" @click="chooseItem(item)">{{ item.orgName }}</text>
                  </view>
                </template>

                <template v-else-if="i.slot">
                  <slot :data="item" :prop="i.prop" :index="index"></slot>
                </template>
                <text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto', 'sub-item': isChild(item)}">{{item[i.prop] || '-'}}</text>
              </uni-td>
            </template>
          </uni-tr>
          <template v-if="item.sub?.length">
            <uni-tr v-for="(sub, i) in item.sub" :key="`sub${index}${i}`">
              <template v-for="(i,idx) in header"> 
                <uni-td v-if="i.fixed" :key="item.prop + index" :align="item.align || 'center'">
                  <template v-if="item.slot">
                    <slot :data="sub" :prop="item.prop" :index="index"></slot>
                  </template>
                  <text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto'}">{{sub[item.prop] || '-'}}</text>
                </uni-td>
              </template>
            </uni-tr>
          </template>
        </template>
      </template>
      <!-- 表头行 -->
        <uni-thead v-if="showHeader">
          <uni-tr>
            <template v-for="(item,index) in header">
              <uni-th v-if="!item.fixed" :key="index" :align="item.align || 'center'" :width="item.width">
                <view class="th-item flex items-center justify-center"  @click.stop="item.sort ? sort(item) : item.info ? showModal(item.name, item.info, item) : () => {}">
                  <template v-if="item.headerSlot">
                    <slot :name="item.headerSlot"></slot>
                  </template>
                  <template v-else>
                    <view class="td-text">
                      <text class="title">{{item.name}}</text>
                      <text v-if="item.subTitle" class="sub-title">{{item.subTitle}}</text>
                    </view>
                    <image v-if="item.info" class="td-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
                    <view v-if="item.sort" class="td-sort">
                      <up-icon name="arrow-up-fill" :color="item.sortType === 'asc' ? '#1F2428' : '#ccc'" size="16rpx" />
                      <up-icon name="arrow-down-fill" :color="item.sortType === 'desc' ? '#1F2428' : '#ccc'" size="16rpx" />
                    </view>
                  </template>
                </view>
              </uni-th>
            </template>
          </uni-tr>
        </uni-thead>

        <!-- 表格数据行 -->
        <template v-for="(item,index) in list" :key="index">
          <uni-tr>
            <template v-for="(i,idx) in header"> 
              <uni-td v-if="!i.fixed" :key="idx" :align="item.align || 'center'" :width="i.width">
                <template v-if="i.canClick">
                  <view v-if="i.type === 'info'" class="target-item" @click.stop="showModal(item.dataName, item.dataDesc, item)">
                    <text class="item-text">{{ item.dataName }}</text>
                    <text class="item-unit" v-if="i.showUnit && item.unit && item.unit !== '%'">/{{ item.unit }}</text>
                    <image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
                  </view>
                  <view v-if="i.type === 'choose'" class="name-item">
                    <text :class="['item-text', item.canClick && 'line-item']" @click="chooseItem(item)">{{ item.orgName }}</text>
                  </view>
                </template>

                <template v-else-if="i.slot">
                  <slot :data="item" :prop="i.prop" :index="index"></slot>
                </template>
                <text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto', 'sub-item': isChild(item)}">{{item[i.prop] || '-'}}</text>
              </uni-td>
            </template>
          </uni-tr>
          <template v-if="item.sub?.length">
            <uni-tr v-for="(sub, i) in item.sub" :key="`sub${index}${i}`">
              <template v-for="(i,idx) in header"> 
                <uni-td v-if="!i.fixed" :key="item.prop + index" :align="item.align || 'center'">
                  <template v-if="item.slot">
                    <slot :data="sub" :prop="item.prop" :index="index"></slot>
                  </template>
                  <text v-else class="td-text" :class="{'td-text-clamp' : cellHeight !== 'auto'}">{{sub[item.prop] || '-'}}</text>
                </uni-td>
              </template>
            </uni-tr>
          </template>
        </template>

			<empty v-if="!list.length" :text="emptyText"></empty>
    </uni-table>
  </view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts">
	import { ref, computed, watch } from 'vue';
	import type zPaging from 'z-paging/components/z-paging/z-paging.vue';
	import cModal from '@/components/c-modal/index'
	import empty from '@/components/empty/empty.vue'

  const tableData = ref([
    {
      id: 1,
      name: '王小虎1',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    },
    {
      id: 2,
      name: '王小虎2',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    },
    {
      id: 3,
      name: '王小虎3',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    },
    {
      id: 4,
      name: '王小虎4',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    },
    {
      id: 5,
      name: '王小虎5',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    },
    {
      id: 6,
      name: '王小虎6',
      age: 18,
      email: '<EMAIL>',
      phone: '13800138000',
    }
  ]);

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	interface Header {
		name : string;
		prop : string;
		sort ?: boolean;
		sortType ?: string;
		slot ?: string;
		[key : string] : any;
	};

	interface List {
		[key : string] : any;
	};

	const props = withDefaults(
		defineProps<{
			showHeader: Boolean;
			header : Header[];
			list : List[];
			cellWidth : Number | String;
			cellHeight : Number | String;
			height : Number | String;
			isPaginate: Boolean;
			isZPaging: Boolean;
			emptyText: String;
      customStyle: object;
      loadingMore?: boolean;
      hasFixedColumns?: boolean;
      fixedColumnsWidth?: number | string;
		}>(), {
		showHeader: () => true,
		header: () => [],
		list: () => [],
		cellWidth: () => 'auto',
		cellHeight: () => '84',
		height: () => 0,
		isPaginate: () => true,
		isZPaging: () => false,
		emptyText: () => '',
    customStyle: () => ({}),
    loadingMore: () => false,
    hasFixedColumns: () => false,
    fixedColumnsWidth: () => 'auto'
	}
	);
	const cellWidth = props.cellWidth !== 'auto' ? props.cellWidth + 'rpx' : 'auto'
	const cellHeight = props.cellHeight !== 'auto' ? props.cellHeight + 'rpx' : 'auto'

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')
	const showModal = (title: string, content: string, data: any) => {
		modalShow.value = true
		modalTitle.value = title
		modalContent.value = content
	}

  const status = ref('loadmore')

	const getStyle = (item: Header, index?: number) => {
		if (item.width) {
			return { width: item.width + 'rpx', left: (index ? 0 : scrollLeft.value) + 'px', zIndex: index ? 0 : 1 }
		} else {
			return { flex: '1' }
		}
	}
	
	const scrollViewHeight = computed(() => {
		if(props.height == 0) return props.height
		return handleUnit(props.height)
	})
	
	const handleUnit = (value:String | Number) => {
		const flag = value.toString().includes('px')
		if (flag) return value
		else return value + 'rpx'
	}

	const emit = defineEmits(['updateList', 'getMore', 'scrolltolower', 'chooseItem']);
	const sort = (item: Header) => {
		if (!item.sort)
			return;

		if (item.sortType === 'asc') {
			item.sortType = 'desc';
		}
		else if (item.sortType === 'desc') {
			item.sortType = '';
		}
		else {
			item.sortType = 'asc';
		}

		emit('updateList', item);

		props.header.forEach((col : Header) => {
			if (col.prop !== item.prop) {
				col.sortType = '';
			}
		});
	};

	const chooseItem = (data: any) => {
		emit('chooseItem', data)
	}

	const pagingRef = ref<InstanceType<typeof zPaging> | null>(null);
	const queryList = (pageNum: number, pageSize: number) => {
		emit('getMore', pageNum, pageSize)
	};

	const dataList = ref<Array<any>>([])
	const setData = (list: Array<any>) => {
		pagingRef.value.complete(list);
	}

	const reload = () => {
		pagingRef.value.reload();
	}

	// 本地分页
	const setLocalPaging = (list: Array<any>) => {
		pagingRef.value.setLocalPaging(list);
	}

	const clearData = () => {
		pagingRef.value?.clear()
	}
  
	// 数据源含有子项
  const expandedKeys = ref<Set<string>>(new Set()); // 使用唯一标识符记录展开状态
  
  // 生成唯一标识，使用 UUID 保证唯一性
  const generateUniqueKey = (item: any) => {
    if (item.id) return `id-${item.id}`;
    if (item.dataName) {
      // 结合数据内容生成哈希值，避免重名问题
      let hash = 0;
      for (let i = 0; i < item.dataName.length; i++) {
        const char = item.dataName.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return `name-${hash}`;
    }
    return `uuid-${Math.random().toString(36).substring(2, 9)}`;
  }
  
  // 递归移除子项(只做一层子项处理)
  const removeChildren = (data: Array<any>, key: string) => {
    return data.filter((d) => {
      if (d.__parentKey === key) {
        return false;
      }
      return true;
    });
  }
  
  const toggleItem = (item: any, index: number) => {
    if (!item.sub || item.sub.length === 0) return;
    
    // 生成唯一标识
    const key =  generateUniqueKey(item);
  
    const isExpanded = expandedKeys.value.has(key);
  
    let newData = [...dataList.value];
  
    if (isExpanded) {
      // 折叠：递归移除所有 __parentKey === 当前项的数据
      newData = removeChildren(newData, key);
    } else {
      // 准确找到当前父项在 newData 中的位置
      const actualIndex = newData.findIndex((d) => {
        if (d.__isChild) return false;
        const currentKey = generateUniqueKey(d);
        return currentKey === key;
      });
  
      if (actualIndex !== -1) {
        // 先移除已存在的子项，避免重复插入
        newData = removeChildren(newData, key);
  
        // 插入子项并打上 parentKey 标记
        const newSubItems = item.sub.map((sub) => ({
          ...sub,
          __isChild: true,
          __parentKey: key,
          __originalKey: generateUniqueKey(sub) // 记录子项唯一标识
        }));
        newData.splice(actualIndex + 1, 0, ...newSubItems);
      }
    }
  
    isExpanded ? expandedKeys.value.delete(key) : expandedKeys.value.add(key);
    dataList.value = newData;
  }
  
  const isExpanded = (item: any): boolean => {
    const key = generateUniqueKey(item);
    return expandedKeys.value.has(key);
  }
  
  const isChild = (item: any): boolean => {
    return !!item.__parentKey;
  }

	defineExpose({
		setData,
		clearData,
		reload,
		setLocalPaging,
    customLoadStatus: status
	})
</script>

<style lang="scss">
.table {
	height: 100%;
	color: #1f2428;
	font-size: 22rpx;
	text-align: center;
	line-height: 22rpx;
	overflow-x: auto;
	background: #fff;
	border-radius: 28rpx;

	.th-item {
		font-weight: bold;
		background: #FFE0D9;
		height: 84rpx;
	}

	.container {
		min-width: max-content;
		height: 100%;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}

	.th {
		font-weight: bold;
		background: #FFE0D9;
		
		.tr{
			height: 84rpx;

			.td {
				background: #FFE0D9;
			}
		}
	}

	.tr {
		display: flex;
		align-items: center;
		height: v-bind(cellHeight);
	}

	.td {
		position: relative;;
		display: flex;
		align-items: center;
		justify-content: center;
		width: v-bind(cellWidth);
		padding: 0 8rpx;
		word-break: break-all;
		height: v-bind(cellHeight);

		.td-text {
			display: flex;
			flex-direction: column;
			align-items: center;
			color: #1f2428;
			font-size: 22rpx;
			line-height: 32rpx;

			.title {
				font-size: 22rpx;
				line-height: 22rpx;
				font-weight: bold;
			}

			.sub-title {
				padding-top: 8rpx;
				font-size: 18rpx;
				line-height: 18rpx;
			}
		}

		.td-info {
			margin-left: 8rpx;
			width: 22rpx;
			height: 22rpx;
		}

		.td-sort {
			margin-left: 8rpx;
		}
	}

	.tbody {
		flex: 1;
		overflow: hidden;
		line-height: 32rpx;
		display: flex;
		flex-direction: column;

		.tr {
			padding: 32rpx 0;
		}
		.tr:nth-child(odd) {
			background: #f75e3b0a;
		}

		.tr:nth-child(even) {
			background: #f75e3b14;
		}

		.td-text-clamp {
			display: -webkit-box;//将盒子转换为弹性盒子
			-webkit-box-orient: vertical;//文本显示方式，默认水平
			-webkit-line-clamp: 2;//设置显示多少行
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.sub-item {
			color: #1f2428b3;
		}
	}

	.empty-text {
		height: 84rpx;
		text-align: center;
		background: #f75e3b0a;
		line-height: 84rpx;
	}
	
	.empty-img {
	}

	.target-item {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;

		.item-unit {
			padding-top: 4rpx;
			font-size: 18rpx;
			line-height: 18rpx;
		}

		.item-info {
			margin-left: 8rpx;
			width: 22rpx;
			height: 22rpx;
		}
		.mr-12 {
			margin-right: 12rpx;
		}
	}

	.name-item {
		display: flex;
		flex-direction: column;
		align-items: center;

		.item-text {
			display: -webkit-box;
			max-width: 120rpx;
			overflow: hidden;//文本超出隐藏
			text-overflow: ellipsis;
			-webkit-box-orient: vertical;//文本显示方式，默认水平
			-webkit-line-clamp: 2;//设置显示多少行
		}
		.item-arrow{
			margin-left: 20rpx;
			font-size: 30rpx;
		}
		.line-item {
			color: #C35943;
			// text-decoration: underline;
		}

		.item-info {
			padding-top: 12rpx;
			display: flex;
			align-items: center;

			.info-text {
				font-size: 18rpx;
				line-height: 20rpx;
				color: #999999;
			}

			.item-icon {
				width: 12rpx;
				height: 16rpx;
				margin: 0 8rpx;
			}

			.item-num {
				font-size: 20rpx;
				line-height: 20rpx;
				font-weight: bold;
				font-family: "DIN Bold";
			}

			.up-num {
				color: #FF4D4D;
			}

			.down-num {
				color: #56CC93;
			}
		}
	}
}

.table-page {
	height: 100%;
	overflow-x: auto;
	background: #fff;
	border-radius: 28rpx;

  .uni-table-th {
    padding: 0;
    font-weight: bold;
		background: #FFE0D9;
		height: 84rpx;
    overflow: hidden;

    .th-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0 8rpx;
      word-break: break-all;

      .td-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #1f2428;
        font-size: 22rpx;
        line-height: 32rpx;

        .title {
          font-size: 22rpx;
          line-height: 22rpx;
          font-weight: bold;
        }

        .sub-title {
          padding-top: 8rpx;
          font-size: 18rpx;
          line-height: 18rpx;
        }
      }

      .td-info {
        margin-left: 8rpx;
        width: 22rpx;
        height: 22rpx;
      }

      .td-sort {
        margin-left: 8rpx;
      }
    }
  }

  .uni-table-tr {
    display: flex;
  }

  .uni-table-td {
    position: relative;;
    display: flex;
    align-items: center;
    justify-content: center;
    width: v-bind(cellWidth);
    padding: 0 8rpx;
    word-break: break-all;
    height: v-bind(cellHeight);
    color: #1f2428;
    font-size: 22rpx;
    text-align: center;
    line-height: 32rpx;

    .td-text {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        font-size: 22rpx;
        line-height: 22rpx;
        font-weight: bold;
      }

      .sub-title {
        padding-top: 8rpx;
        font-size: 18rpx;
        line-height: 18rpx;
      }
    }
  }

  .td-info {
    margin-left: 8rpx;
    width: 22rpx;
    height: 22rpx;
  }

  .td-sort {
    margin-left: 8rpx;
  }

  .uni-table-tr:nth-child(odd) {
    background: #f75e3b0a;
  }

  .uni-table-tr:nth-child(even) {
    background: #f75e3b14;
  }

  .td-text-clamp {
    display: -webkit-box;//将盒子转换为弹性盒子
    -webkit-box-orient: vertical;//文本显示方式，默认水平
    -webkit-line-clamp: 2;//设置显示多少行
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .sub-item {
    color: #1f2428b3;
  }

  .empty-text {
    height: 84rpx;
    text-align: center;
    background: #f75e3b0a;
    line-height: 84rpx;
  }

  .target-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .item-unit {
      padding-top: 4rpx;
      font-size: 18rpx;
      line-height: 18rpx;
    }

    .item-info {
      margin-left: 8rpx;
      width: 22rpx;
      height: 22rpx;
    }
    .mr-12 {
      margin-right: 12rpx;
    }
  }

  .name-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .item-text {
      display: -webkit-box;
      max-width: 120rpx;
      overflow: hidden;//文本超出隐藏
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;//文本显示方式，默认水平
      -webkit-line-clamp: 2;//设置显示多少行
    }
    .item-arrow{
      margin-left: 20rpx;
      font-size: 30rpx;
    }
    .line-item {
      color: #C35943;
      // text-decoration: underline;
    }

    .item-info {
      padding-top: 12rpx;
      display: flex;
      align-items: center;

      .info-text {
        font-size: 18rpx;
        line-height: 20rpx;
        color: #999999;
      }

      .item-icon {
        width: 12rpx;
        height: 16rpx;
        margin: 0 8rpx;
      }

      .item-num {
        font-size: 20rpx;
        line-height: 20rpx;
        font-weight: bold;
        font-family: "DIN Bold";
      }

      .up-num {
        color: #FF4D4D;
      }

      .down-num {
        color: #56CC93;
      }
    }
  }
}
</style>

<style lang="scss">
z-paging {
	height: 100%;
	overflow: auto;

	.zp-l-text-rpx {
		font-size: 22rpx !important;
	}
}
</style>
