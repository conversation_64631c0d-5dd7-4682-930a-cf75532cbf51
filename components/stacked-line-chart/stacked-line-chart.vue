<template>
	<view>
		<view class="full-screen fr">
			<view class="full-screen-btn fr" @click="handleFullScreen">
				<text class="btn-text btn-text-primary">全屏查看</text>
				<up-icon name="arrow-right" color="#C35943" size="20rpx" />
			</view>
		</view>
		<view class="chars">
			<qiun-data-charts type="area" :opts="opts" :chartData="chartData" :inScrollView="true" :tapLegend="false"
				tooltipFormat="tooltipDemo2" :canvas2d="true" />
		</view>
	</view>

	<up-popup :show="isFullScreen" mode="center" @close="handleClose" @open="handleOpen" round="0"
		:customStyle="{width:'100%',height:'100%'}">
		<view class="full-screen full-screen-rotate fr" @click="handleClose">
			<view class="full-screen-btn  fr">
				<text class="btn-text btn-text-primary">退出全屏</text>
				<up-icon name="arrow-right" color="#C35943" size="20rpx" />
			</view>
		</view>
		<view class="full-screen-chart">
			<qiun-data-charts type="area" :opts="opts_full_screen" :inScrollView="true" :chartData="fullScreenChartData"
				:ontouch="true" :reshow="reShow" :tapLegend="false" tooltipFormat="tooltipDemo2" :canvas2d="true" />
		</view>
    <cWaterMark :rotate="60"></cWaterMark>
	</up-popup>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	const isFullScreen = ref<Boolean>(false)
	const reShow = ref<Boolean>(false)
	const chartData = ref({})
	const fullScreenChartData = ref({})

	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		},
		fullScreenData: {
			type: Object,
			default: () => { }
		},
		customFullScreenEvent: {
			type: Boolean,
			default: false
		}
	})

	const emit = defineEmits(['customFullScreenEvent'])

	const opts = ref({
		color: ["#F75E3B", "#43BF83"],
		padding: [20, 0, 0, 10],
		enableScroll: true,
		legend: {
			fontSize: '10',
			fontColor: '#1F2428',
			lineHeight: '10'
		},
		xAxis: {
			disableGrid: true,
			fontColor: '#7D8082',
			fontSize: '10',
			axisLineColor: '#F2F2F2',
			marginTop: 8,
			rotateLabel: true,
			rotateAngle: 30,
			itemCount: 7,
			scrollAlign: 'right',
		},
		yAxis: {
			gridType: "dash",
			gridColor: "#F2F2F2",
			dashLength: 4,
			data: [{
				axisLineColor: '#F2F2F2',
				fontColor: '#7D8082',
				fontSize: '10',
			}]
		},
		extra: {
			// line: {
			// 	type: "straight",
			// 	width: 2,
			// 	activeType: "hollow",
			// 	linearType: 'custom'
			// }
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		}
	})

	const opts_full_screen = ref({
		color: ["#F75E3B", "#43BF83"],
		padding: [78, 70, 28, 78],
		enableScroll: true,
		rotate: true,
		legend: {
			fontSize: '10',
			fontColor: '#1F2428',
			lineHeight: '10'
		},
		xAxis: {
			disableGrid: true,
			fontColor: '#7D8082',
			fontSize: '10',
			axisLineColor: '#F2F2F2',
			itemCount: 12,
			scrollAlign: 'right',
			marginTop: 8,
			rotateLabel: true,
			rotateAngle: 30,
		},
		yAxis: {
			gridType: "dash",
			gridColor: "#F2F2F2",
			dashLength: 4,
			data: [{
				axisLineColor: '#F2F2F2',
				fontColor: '#7D8082',
				fontSize: '10',
			}]
		},
		extra: {
			// line: {
			// 	type: "straight",
			// 	width: 2,
			// 	activeType: "hollow",
			// 	linearType: 'custom'
			// }
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		}
	})


	const handleOpen = () => {
		reShow.value = true
	}
	const handleClose = () => {
		reShow.value = false
		isFullScreen.value = false
	}

	const handleFullScreen = () => {
		if (props.customFullScreenEvent) {
			emit('customFullScreenEvent')
		} else {
			isFullScreen.value = true
		}
	}

	watch(
		() => props.data,
		(v) => {
			const res = {
				categories: props.data.categories,
				series: [
					{
						name: props.data.value1.name,
						data: props.data.value1.data,
						legendShape: 'circle',
						pointShape: 'none',
						textColor: '#FF4D4D',
						textSize: "12",
						textOffset: '-4',
						linearColor: [
							[0, '#F75E3B'],
							[1, '#FFB2A1']
						],
					},
					{
						name: props.data.value2.name,
						data: props.data.value2.data,
						legendShape: 'circle',
						pointShape: 'none',
						textColor: '#56CC93',
						textSize: "12",
						textOffset: '-4',
						linearColor: [
							[0, '#43BF83'],
							[1, '#BCE3D0']
						]
					}
				]
			}
			chartData.value = JSON.parse(JSON.stringify(res));
		}
	)

	watch(
		() => props.fullScreenData,
		(v) => {
			const res = {
				categories: props.fullScreenData.categories,
				series: [
					{
						name: props.fullScreenData.value1.name,
						data: props.fullScreenData.value1.data,
						legendShape: 'circle',
						pointShape: 'none',
						textColor: '#FF4D4D',
						textSize: "12",
						textOffset: '-4',
						linearColor: [
							[0, '#F75E3B'],
							[1, '#FFB2A1']
						],
					},
					{
						name: props.fullScreenData.value2.name,
						data: props.fullScreenData.value2.data,
						legendShape: 'circle',
						pointShape: 'none',
						textColor: '#56CC93',
						textSize: "12",
						textOffset: '-4',
						linearColor: [
							[0, '#43BF83'],
							[1, '#BCE3D0']
						]
					}
				]
			}
			fullScreenChartData.value = JSON.parse(JSON.stringify(res));
		}
	)

	defineExpose({ isFullScreen })
</script>

<style lang="scss" scoped>
	.full-screen {
		justify-content: flex-end;
		margin-bottom: 40rpx;


		&-btn {
			border-radius: 200rpx;
			border: 1rpx solid #ffffff;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			padding: 8rpx 16rpx;

			.btn-text {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;

				&-primary {
					color: #c35943;
				}
			}
		}
	}

	.full-screen-rotate {
		z-index: 2000000;
		position: absolute;
		bottom: 40rpx;
		right: 40rpx;
		transform: rotate(90deg);
	}

	.chars {
		width: 100%;
		height: 612rpx;
	}

	.full-screen-chart {
		width: 100%;
		height: 100%;
	}
</style>