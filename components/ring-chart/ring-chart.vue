<template>
	<view class="ring-charts" v-if="data && Object.keys(data).length > 0">
		<qiun-data-charts type="ring" :opts="opts" :inScrollView="true" :chartData="chartData" :tooltipShow="false"
			:canvas2d="true" />
		<view class=" float-layer" v-if="type === 'a' && Object.keys(data).length > 0">
			<view class="block fl" v-for="(item,index) in data.originData" :key="index">
				<view class="title fr row">
					<view class="spot left" v-if="(index+1)%2!==0" :style="{background:color[index]}"></view>
					<view class="name">{{ item.dataName }}</view>
					<view class="spot right" v-if="(index+1)%2===0" :style="{background:color[index]}"></view>
				</view>
				<view class="proportion row" :class="[(index+1)%2===0 ? 'r':'l']">{{item.count}}
					<text v-if="item.rate!='-'">({{item.rate}})</text>
				</view>
				<view class="fr month-on-month row" :class="[(index+1)%2===0 ? 'r':'l']">
					<view class="text">环比</view>
					<view class="fr value">
						<image v-if="item.hb !== '-'" class="img"
							:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${item.hb.indexOf('-') == -1 ? '1729644716250' : '1729644734157'}.png`" />
						<text class="num"
							:class="[item.hb === '-' ?  '' : item.hb.indexOf('-') == -1 ? 'up-text' : 'down-text']">{{item.hb !== '-' ? item.hb.replace(/-/g, '') : item.hb}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="float-layer" v-else-if="type === 'b' && Object.keys(data).length > 0">
			<view class="block fl" v-for="(item,index) in data.originData" :key="index">
				<view class="title fr row">
					<view class="spot left" v-if="(index+1)%2!==0" :style="{background:color[index]}"></view>
					<view class="name">{{ item[bKeys.labelKey]  }}</view>
					<view class="spot right" v-if="(index+1)%2===0" :style="{background:color[index]}"></view>
				</view>
				<view class="proportion row" :class="[(index+1)%2===0 ? 'r':'l']">{{item[bKeys.valueKey]}}
					<text style="color: #1f2428b3;" v-if="item[bKeys.rateKey] && item[bKeys.rateKey]!='-'">({{item[bKeys.rateKey]}})</text>
				</view>
			</view>
		</view>
		<view class="float-layer" v-else-if="type === 'c' && Object.keys(data).length > 0">
			<view class="block fl  c">
				<view class="fr row" v-for="(item,index) in data.originData" :key="index">
					<view class="spot left" :style="{background:color[index]}"></view>
					<view class="name">{{ item[cKeys.labelKey] }}：</view>
					<text class="rate">{{ item[cKeys.valueKey] }}</text>
				</view>
			</view>
		</view>
	</view>
	<emptyVue v-else />
</template>

<script setup lang="ts" name="ringCharts">
	import { ref, watch } from 'vue';
	import emptyVue from '@/components/empty/empty.vue';

	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		},
		type: {
			type: String,
			default: 'a'
		},
		bKeys: {
			type: Object,
			default: () => ({
				labelKey: 'dataName', // 默认用来显示的标签字段
				valueKey: 'num',      // 默认用来显示的值字段
				rateKey: 'rate'
			})
		},
		cKeys: {
			type: Object,
			default: () => ({
				labelKey: 'dataName', // 默认用来显示的标签字段
				valueKey: 'rate'      // 默认用来显示的值字段
			})
		}
	})
	const chartData = ref({})
	const color = ["#FCD80D", "#F75E3B", "#FF8D1A", "#43BF83", "#6046F2", "#61C5FF", '#8BCC00', '#1474FB']
	const opts = ref({
		rotate: false,
		rotateLock: false,
		color: color,
		padding: props.type === 'c' ? [5, 5, 5, 135] : [5, 5, 5, 5],
		dataLabel: true,
		enableScroll: false,
		legend: {
			show: false,
		},
		title: {
			name: ''
		},
		subtitle: {
			name: ''
		},
		extra: {
			tooltip: {
				showBox: false
			},
			ring: {
				ringWidth: 20,
				customRadius: 70,
				activeOpacity: 1,
				activeRadius: 10,
				offsetAngle: 0,
				labelWidth: 15,
				border: false,
				borderWidth: 3,
				borderColor: "#FFFFFF",
			}
		}
	})

	watch(
		() => props.data,
		() => {
			const res = {
				series: [
					{
						data: props.data.data
					}
				]
			}
			chartData.value = JSON.parse(JSON.stringify(res));
		}
	)
</script>

<style lang="scss" scoped>
	.ring-charts {
		width: 100%;
		height: 100%;
		position: relative;
		z-index: 2;
	}

	.float-layer {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		z-index: 0;

		.block {
			width: 218rpx;
			height: max-content;
			border-radius: 20rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f500 100%);
			padding: 20rpx;

			.row {
				margin-bottom: 12rpx;
				white-space: nowrap;

				&:last-child {
					margin-bottom: 0;
				}
			}

			&:nth-child(even) {
				background: linear-gradient(90deg, #f5f5f500 0%, #f5f5f5 100%);
				align-items: flex-end;
			}

			.spot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: linear-gradient(225deg, #ffeca6 0%, #fcd80d 100%);
				margin-right: 12rpx;
				flex-shrink: 0;
			}

			.spot.left {
				margin-right: 12rpx;
			}

			.spot.right {
				margin-right: 0;
				margin-left: 12rpx;
			}

			.name {
				color: #1f2428;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;
			}

			.proportion {
				color: #1f2428b3;
				font-size: 24rpx;
				font-weight: bold;
				line-height: 24rpx;
			}

			.l {
				margin-left: 28rpx;
			}

			.r {
				margin-right: 28rpx;
			}

			.month-on-month {
				.text {
					color: #1f2428b3;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 8rpx;
				}

				.value {
					.img {
						width: 16rpx;
						height: 20rpx;
						flex-shrink: 0;
						margin-right: 8rpx;
					}

					.num {
						color: #ff4d4d;
						font-size: 24rpx;
						font-weight: 700;
						font-family: "DIN Bold";
						line-height: 24rpx;
					}
				}
			}

			.up-text {
				color: #FF4D4D !important;
			}

			.down-text {
				color: #56CC93 !important;
			}
		}

		.c {
			width: 246rpx;

			.row {
				margin-bottom: 14rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.name {
					color: #1f2428b3;
					font-size: 22rpx;
					line-height: 22rpx;
				}

				.rate {
					color: #1f2428b3;
					font-size: 24rpx;
					font-weight: 700;
					font-family: "Din Bold";
					line-height: 24rpx;
				}
			}
		}
	}
</style>