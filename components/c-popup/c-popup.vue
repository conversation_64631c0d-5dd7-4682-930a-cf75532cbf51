 <template>
   <up-popup :show="show" mode="center" @close="handleClose" @open="handleOpen">
        <slot name="content"></slot>
      <cWaterMark :rotate="60"></cWaterMark>
  </up-popup>
</template>

<script setup lang="ts" name="cPopup">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:show'])

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const handleClose = () => {
  emits('update:show', false)
}

const handleOpen = () => {
  emits('update:show', true)
}

</script>

<style lang="scss" scoped>
// 确保 u-popup 内容区域全屏
:deep(.u-popup__content) {
 width: 90vw;
 height: 80vh;
}
</style>
 
 
