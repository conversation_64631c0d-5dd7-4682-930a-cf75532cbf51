<template>
	<view class="empty" :class="{'background':hasBackground}">
		<image class="img"
			src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-30/1730282036601.png"
			mode=""></image>
		<text class="text">{{text}}</text>
	</view>
</template>

<script setup lang="ts">
	withDefaults(
		defineProps<{
			text : String,
			hasBackground : Boolean
		}>(),
		{
			text: () => '暂无数据',
			hasBackground: () => false
		}
	)
</script>

<style lang="scss" scoped>
	.background {
		width: 100%;
		height: 392rpx !important;
		border-radius: 23.62rpx;
		background: #fff;
	}

	.empty {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		padding: 20rpx;

		.img {
			width: 200rpx;
			height: 186rpx;
			flex-shrink: 0;
		}

		.text {
			color: rgba(31, 36, 40, 0.7);
			font-size: 22rpx;
			text-align: center;
			line-height: 22rpx;
			margin-top: 30rpx;
		}
	}
</style>