<template>
  <view class="enhanced-table-container">
    <!-- 表格头部工具栏 -->
    <view v-if="showToolbar" class="table-toolbar">
      <view class="toolbar-left">
        <slot name="toolbar-left">
          <text class="table-title">{{ title }}</text>
        </slot>
      </view>
      <view class="toolbar-right">
        <slot name="toolbar-right">
          <button v-if="showRefresh" class="toolbar-btn" @click="handleRefresh">
            <text class="iconfont icon-refresh"></text>
            刷新
          </button>
        </slot>
      </view>
    </view>

    <!-- 表格容器 -->
    <view class="table-wrapper" :style="{ height: tableHeight }">
      <uni-table 
        :data="tableData" 
        :border="border" 
        :stripe="stripe"
        :loading="loading"
        :empty-text="emptyText"
        :row-key="rowKey"
        @selection-change="handleSelectionChange"
      >
        <!-- 表头 -->
        <uni-thead>
          <uni-tr>
            <uni-th 
              v-for="(column, index) in columns" 
              :key="column.key"
              :width="column.width"
              :align="column.align || 'left'"
              :sortable="column.sortable"
              :fixed="column.fixed"
              :fixed-left="getFixedLeft(index)"
              @sort-change="handleSortChange"
            >
              {{ column.title }}
            </uni-th>
          </uni-tr>
        </uni-thead>
        
        <!-- 数据行 -->
        <uni-tr 
          v-for="(row, rowIndex) in tableData" 
          :key="getRowKey(row, rowIndex)"
          :key-value="getRowKey(row, rowIndex)"
        >
          <uni-td 
            v-for="(column, colIndex) in columns" 
            :key="column.key"
            :width="column.width"
            :align="column.align || 'left'"
            :fixed="column.fixed"
            :fixed-left="getFixedLeft(colIndex)"
          >
            <slot 
              :name="column.key" 
              :row="row" 
              :column="column" 
              :index="rowIndex"
              :value="getColumnValue(row, column.key)"
            >
              {{ getColumnValue(row, column.key) }}
            </slot>
          </uni-td>
        </uni-tr>
      </uni-table>
    </view>

    <!-- 分页器 -->
    <view v-if="showPagination" class="table-pagination">
      <view class="pagination-info">
        <text>共 {{ total }} 条记录，第 {{ currentPage }}/{{ totalPages }} 页</text>
      </view>
      <view class="pagination-controls">
        <button 
          class="page-btn" 
          :disabled="currentPage <= 1"
          @click="handlePageChange(currentPage - 1)"
        >
          上一页
        </button>
        <button 
          class="page-btn" 
          :disabled="currentPage >= totalPages"
          @click="handlePageChange(currentPage + 1)"
        >
          下一页
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Column {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  render?: (value: any, row: any, index: number) => string
}

interface Props {
  columns: Column[]
  data: any[]
  title?: string
  border?: boolean
  stripe?: boolean
  loading?: boolean
  emptyText?: string
  rowKey?: string
  showToolbar?: boolean
  showRefresh?: boolean
  showPagination?: boolean
  pageSize?: number
  total?: number
  currentPage?: number
  tableHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  data: () => [],
  title: '数据表格',
  border: true,
  stripe: true,
  loading: false,
  emptyText: '暂无数据',
  rowKey: 'id',
  showToolbar: true,
  showRefresh: true,
  showPagination: false,
  pageSize: 10,
  total: 0,
  currentPage: 1,
  tableHeight: 'auto'
})

const emit = defineEmits([
  'selection-change', 
  'sort-change', 
  'refresh', 
  'page-change'
])

// 表格数据
const tableData = computed(() => props.data)

// 总页数
const totalPages = computed(() => {
  return Math.ceil(props.total / props.pageSize)
})

// 计算固定列的左偏移量
const getFixedLeft = (index: number) => {
  let left = 0
  for (let i = 0; i < index; i++) {
    const column = props.columns[i]
    if (column.fixed) {
      const width = typeof column.width === 'string' 
        ? parseInt(column.width) 
        : (column.width || 120)
      left += width
    } else {
      break // 遇到非固定列就停止计算
    }
  }
  return left
}

// 获取行键值
const getRowKey = (row: any, index: number) => {
  return row[props.rowKey] || index
}

// 获取列值
const getColumnValue = (row: any, key: string) => {
  const keys = key.split('.')
  let value = row
  for (const k of keys) {
    value = value?.[k]
  }
  return value ?? '-'
}

// 处理选择变化
const handleSelectionChange = (e: any) => {
  emit('selection-change', e)
}

// 处理排序变化
const handleSortChange = (e: any) => {
  emit('sort-change', e)
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理分页变化
const handlePageChange = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    emit('page-change', page)
  }
}
</script>

<style lang="scss" scoped>
.enhanced-table-container {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
  
  .toolbar-left {
    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .toolbar-right {
    .toolbar-btn {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px 16px;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      
      &:hover {
        background-color: #40a9ff;
      }
      
      .iconfont {
        font-size: 14px;
      }
    }
  }
}

.table-wrapper {
  overflow: auto;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  
  .pagination-info {
    font-size: 14px;
    color: #666;
  }
  
  .pagination-controls {
    display: flex;
    gap: 8px;
    
    .page-btn {
      padding: 8px 16px;
      border: 1px solid #d9d9d9;
      background-color: #fff;
      color: #333;
      border-radius: 4px;
      font-size: 14px;
      
      &:not(:disabled):hover {
        border-color: #1890ff;
        color: #1890ff;
      }
      
      &:disabled {
        background-color: #f5f5f5;
        color: #ccc;
        cursor: not-allowed;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    
    .toolbar-left,
    .toolbar-right {
      text-align: center;
    }
  }
  
  .table-pagination {
    flex-direction: column;
    gap: 12px;
    
    .pagination-info {
      text-align: center;
    }
  }
}
</style>
