<template>
	<view :class="[fixed ? `c-tab-bar--${fixed}` : '']">
		<view class="c-tab-bar">
			<view class="c-tab-bar__block">
				<view class="c-tab-bar__block-item" :class="{'c-tab-bar__block-item--active': value == item.name}"
					v-for="item in tabs" :key="item.name" @click="onClick(item.name)">
					<image :src="value == item.name ? (item.name == 'ai' ? item.icon : item.activeIcon) : item.icon"
						mode="aspectFill" class="icon" :class="{ai: item.name == 'ai'}">
					</image>
					<text v-if="item.name != 'ai'">{{item.title}}</text>
				</view>
			</view>
			<image src="/static/tabbar/vector.png" mode="aspectFill" class="c-tab-bar__ani"></image>
		</view>
	</view>

</template>

<script setup lang="ts">
	import {
		computed,
		watch,
		ref,
	} from 'vue'
	import { dataType } from '@/types/index.d';
	const props = defineProps({
		tabs: {
			type: Array<dataType>,
			default: () => [
				{
					title: '首页',
					name: 'home',
					icon: '/static/tabbar/home.png',
					activeIcon: '/static/tabbar/home-active.png'
				},
				{
					title: '数舱',
					name: 'dataCockpit',
					icon: '/static/tabbar/dataCockpit.png',
					activeIcon: '/static/tabbar/dataCockpit-active.png'
				},
				{
					title: 'AI助手',
					name: 'ai',
					icon: '/static/tabbar/ai.png',
					activeIcon: '/static/tabbar/ai.png'
				},
				{
					title: '工作台',
					name: 'workbench',
					icon: '/static/tabbar/workbench.png',
					activeIcon: '/static/tabbar/workbench-active.png'
				},
				{
					title: '我的',
					name: 'my',
					icon: '/static/tabbar/my.png',
					activeIcon: '/static/tabbar/my-active.png'
				}
			]
		},
		model: {
			type: String,
			default: 'store'
		},
		fixed: {
			type: String,
			default: 'bottom'
		}
	})
	const emits = defineEmits(['change'])
	const value = ref(props.model)
	watch(() => props.model, v => {
		if (v != 'ai') {
			value.value = v
		}
	})
	const left = computed(() => {
		const index = props.tabs.findIndex(item => item.name == value.value)
		return `calc((100% / ${props.tabs.length}) / 2 * ${2 * index + 1})`
	})
	const onClick = (name : string) => {
		emits('change', name)
	}
</script>

<style scoped lang="scss">
	.c-tab-bar--bottom {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 2;
	}

	.c-tab-bar {
		position: relative;
		height: 192rpx;
		background-image: url('https://sunmei-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-09-24/<EMAIL>');
		background-size: 100% 100%;
		background-repeat: no-repeat;

		&__block {
			display: flex;
			height: 164rpx;
			padding-bottom: 24rpx;

			&-item {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: flex-end;

				text {
					color: #999999;
					font-size: 20rpx;
					font-weight: bold;
				}

				&--active {
					text {
						color: #C35943;
					}
				}

				&:nth-child(3) {
					justify-content: flex-start;

					.icon {
						width: 120rpx;
						height: 120rpx;
						margin-bottom: 0;
					}
				}
			}
		}

		&__ani {
			position: absolute;
			bottom: 0;
			left: v-bind(left);
			transform: translateX(-50%);
			width: 110rpx;
			height: 28rpx;
			transition: left .5s;
		}
	}

	.icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 10rpx;
	}
</style>