<template>
  <view class="ui-area-picker"  v-if="showWindow">
    <view class="area-wraper">
      <view class="area-left" @click="show = !show">
        <text class="picker-text">
          {{ defaultName || title }}
            <!-- 鲁中一区 -->
        </text>
        <up-icon v-if="!show" name="arrow-down" color="#C2BDBC" size="24rpx" />
        <up-icon v-else name="arrow-up" color="#C2BDBC" size="24rpx" />
      </view>
      <slot name="right" v-if="$slots.right"></slot>
      <view v-else-if="showShop" class="area-right" @click="toSearch">
        <image class="shop-img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644837689.png" mode="aspectFill"></image>
        <text class="shop-text">门店</text>
      </view>
    </view>
  </view>
  <up-picker title="选择区域" :immediate-change="false" :default-index="defaultIndex" :show="show" :columns="columns" key-name="orgName" @change="handleChange" @confirm="handleConfirm" @cancel="show = false" />
</template>

<script lang="ts" setup name="cAreaPicker">
import { ref, getCurrentInstance, ComponentInternalInstance, nextTick } from 'vue';
import { successCallbackResult } from '@/types'
import type { GetOrgListRes } from '@/api/operations';
import { getOrgList } from '@/api/operations';
import { useCommon } from '@/hooks/useGlobalData';
import { showToast } from '@/utils/util';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const props = defineProps({
  showShop: {
    type: Boolean,
    default: true,
  },
  defaultName: {
    type: String,
    default: ''
  },
	showWindow: {
		type: Boolean,
		default: true,
	}
});

const emit = defineEmits(['setArea', 'toSearch']);

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)

const defaultIndex = ref<Array<number>>();
const indexs = ref<Array<number>>([]);
const show = ref<boolean>(false);
const columns = ref<Array<Array<GetOrgListRes.data>>>([]);
const title = ref<string>('');
const parentId = ref<string>('');
const parentLevel = ref<string>('');

const getColums = (id: string, level: string) => {
  getOrgList({ parentId: id }).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    if (!res.data.length) return;

    const list = res.data.map((item: GetOrgListRes) => {
      return {
        oldId: item.id,
        id: (item.orgId!) as string,
        orgName: item.orgName,
        parentId: item.parentId,
        level: item.level,
      };
    });

    if (Number(level) <= 0) {
      columns.value[0] = columns.value[0].concat(list as [GetOrgListRes.data]);
    } else {
      const rolelevel = Number(userInfo.value.roleLevel) <= 0 ? 1 : Number(userInfo.value.roleLevel) / 10;
      const dataIndex = rolelevel === 1 && Number(level) / 10 === 3 ? Number(level) / 10 - rolelevel : Number(level) / 10 - rolelevel + 1
      for (let i = dataIndex; i < columns.value.length; i++) {
        columns.value[i] =  [{id: '', orgName: '全部'}];
      }
      nextTick(() => columns.value[dataIndex] = columns.value[dataIndex].concat(list as [GetOrgListRes.data]))
    }
  });
};

const setData = () => {
  if (Number(userInfo.value.roleLevel) <= 10) {
    columns.value = [[], [
      {
        id: '',
        orgName: '全部',
      },
    ], [
      {
        id: '',
        orgName: '全部',
      },
    ]];
    defaultIndex.value = [0, 0, 0, 0];
  }
  else if (Number(userInfo.value.roleLevel) === 30) {
    columns.value = [[], [
      {
        id: '',
        orgName: '全部',
      },
    ]];
    defaultIndex.value = [0, 0];
  }
  else if (Number(userInfo.value.roleLevel) === 40) {
    columns.value = [[]];
    defaultIndex.value = [0];
  }
  indexs.value = defaultIndex.value!;

  const list = userInfo.value.orgList?.map((item) => {
    return {
      id: item.orgId,
      orgName: item.orgName,
      level: item.level,
    };
  });
  title.value = list?.[0].orgName || '';
  parentId.value = list?.[0].id || '';
  parentLevel.value = userInfo.value.roleLevel || '';
  columns.value[0] = list as [GetOrgListRes.data];

  getColums(parentId.value, parentLevel.value);
};

const handleChange = (e: any) => {
  parentId.value = e.value[e.columnIndex].id;
  parentLevel.value = e.value[e.columnIndex].level;

  if (e.columnIndex + 1 < 4)
    columns.value[e.columnIndex + 1]?.splice(1);
  if (parentId.value)
    getColums(parentId.value, parentLevel.value);
};

const handleConfirm = (e: any) => {
  show.value = false;
  const list = e.value.filter((item: any) => item.id);
  title.value = list[list.length - 1].orgName;
  emit('setArea', list[list.length - 1].id, list[list.length - 1].level, title.value);
};

setData();

const toSearch = () => {
  emit('toSearch')
}

defineExpose({show})
</script>

<style lang="scss">
.ui-area-picker {
  position: relative;
  height: 64rpx;

  .area-wraper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .area-left {
      height: 100%;
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 40rpx;
      border-radius: 32rpx;
      background: #F9EEEC;

      .picker-text {
        font-size: 24rpx;
        font-weight: bold;
        color: #1F2428;
      }
    }

    .area-right {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 28rpx;
      margin-left: 20rpx;
      border-radius: 32rpx;
      background: #F9EEEC;

      .shop-img {
        width: 28rpx;
        height: 25rpx;
      }

      .shop-text {
        padding-left: 8rpx;
        font-size: 22rpx;
        color: #1F2428;
        font-weight: bold;
      }
    }
  }
}

.u-popup__content {
	border-radius: 40rpx !important;

	.u-toolbar {
		height: 156rpx !important;
		padding: 0 40rpx !important;

		.u-toolbar__wrapper__cancel {
			font-size: 30rpx !important;
			line-height: 30rpx !important;
			color: #999999 !important;
		}

		.u-toolbar__title {
			font-size: 36rpx !important;
			line-height: 36rpx !important;
			color: #1F2428 !important;
		}

		.u-toolbar__wrapper__confirm {
			font-size: 30rpx !important;
			line-height: 30rpx !important;
			color: #C35943 !important;
		}
	}

	.u-picker__view__column__item {
		font-size: 26rpx !important;
		color: #1F2428 !important;
		padding: 0 8rpx !important;
	}

	.u-picker__view__column:nth-child(2) {
		flex: 1.5 !important;
	}
}
</style>
