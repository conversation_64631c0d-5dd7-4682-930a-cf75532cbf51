<template>
  <up-tabs
    v-if="isBig"
    :list="list"
    :current="modelValue"
    lineWidth="82rpx"
    lineHeight="84rpx"
    :lineColor="`url(${bgUrl}) 100% 100%`"
    :activeStyle="{
      color: textColor,
      fontWeight: 'bold',
      fontSize: '36rpx',
    }"
    :inactiveStyle="{
      color: textColor,
      fontSize: '26rpx',
    }"
    :itemStyle="{
      width: '150rpx',
      minWidth: '100rpx',
      height: '124rpx',
      paddingBottom: '48rpx',
      paddingTop: '40rpx',
      zIndex: 1,
    }"
    @change="changeTab"
    @click="clickTab"
  >
  </up-tabs>
  <up-tabs
    v-else
    :list="list"
    :current="modelValue"
    lineWidth="60rpx"
    lineHeight="61rpx"
    :lineColor="`url(${bgUrl}) 100% 100%`"
    :activeStyle="{
      color: textColor,
      fontWeight: 'bold',
      fontSize: '30rpx',
    }"
    :inactiveStyle="{
      color: textColor,
      fontSize: '22rpx',
    }"
    :itemStyle="{
      width: itemWidth + 'rpx',
      height: '66rpx',
      paddingBottom: '32rpx',
      zIndex: 1,
    }"
    @change="changeTab"
    @click="clickTab"
  >
  </up-tabs>
</template>

<script lang="ts" setup>
import { withDefaults } from 'vue';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

interface Tab {
  name : string;
  [key : string] : any;
};

const props = withDefaults(
  defineProps<{
    list : Tab[];
    modelValue : string | number;
    isBig: boolean,
    itemWidth: number,
    textColor: string,
    bgUrl: string
  }>(),
  {
    list: () => [],
    modelValue: 0,
    isBig: true,
    itemWidth: 100,
    textColor: '#fff',
    bgUrl: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644870474.png'
  }
);

const emit = defineEmits(['update:modelValue', 'change', 'click'])

const changeTab = (item: any) => {
  emit('update:modelValue', item.index)
  emit('change', item)
}

const clickTab = (item: any) => {
  emit('click', item)
}
</script>

<style>
.u-tabs__wrapper__nav__item__text--disabled {
  color: #fff !important;
}
</style>