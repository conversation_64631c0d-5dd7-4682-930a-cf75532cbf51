<template>
  <view class="c-modal">
    <up-modal v-if="isRichText" :show="show" :title="title"  @confirm="handleConfirm">
			<view class="slot-content">
				<rich-text :nodes="content"></rich-text>
        <slot name="info"></slot>
			</view>
		</up-modal>
    <up-modal v-else :show="show" :title="title" :content="content" @confirm="handleConfirm">
     </up-modal>
  </view>
</template>

<script setup lang="ts" name="cModal">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  isRichText:{
    type: Boolean,
    default: false,
  }
});

const emits = defineEmits(['update:show', 'confirm'])

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const handleConfirm = () => {
  emits('update:show', false)
  emits('confirm')
}
</script>

<style lang="scss">
.c-modal {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .u-popup__content {
    border-radius: 20rpx !important;
  }

  .u-modal {
    width: 510rpx !important;
    padding: 40rpx;

    .u-modal__title {
      padding: 0 !important;
      font-size: 40rpx !important;
      line-height: 36rpx;
      color: #1F2428 !important;
    }

    .u-modal__content {
      padding: 30rpx 0 !important;

      .u-modal__content__text {
        font-size: 28rpx !important;
        line-height: 48rpx !important;
        color: #1F2428 !important;
      }
    }

    .u-line {
      display: none;
    }

    .u-modal__button-group__wrapper {
      height: 80rpx !important;
      background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
      border-radius: 40rpx;

      .u-modal__button-group__wrapper__text {
        font-size: 28rpx !important;
        color: #fff !important;
        font-weight: bold;
      }
    }
  }
  .slot-content{
    width: 100%;
    line-height: 48rpx;
    font-size: 28rpx;
    font-family: "苹方-繁";
    .red {
      color: #C35943;
      font-weight:bold;
    }
  }
}
</style>