<template>
	<template v-if="chartData && Object.keys(chartData).length > 0">
		<view class="full-screen fr">
			<view class="full-screen-btn fr" @click="isFullScreen = true">
				<text class="btn-text btn-text-primary">全屏查看</text>
				<up-icon name="arrow-right" color="#C35943" size="20rpx" />
			</view>
		</view>
		<view class="char">
			<qiun-data-charts type="mix" :opts="opts" :chartData="chartData" :tapLegend="false" :canvas2d="true" />
		</view>
	</template>
	<template v-else>
		<emptyVue />
	</template>

	<up-popup :show="isFullScreen" mode="center" @close="handleClose" @open="handleOpen" round="0"
		:customStyle="{width:'100%',height:'100%'}" @touchmove.stop.prevent="false">
		<view class="full-screen full-screen-rotate fr" @click="handleClose">
			<view class="full-screen-btn  fr">
				<text class="btn-text btn-text-primary">退出全屏</text>
				<up-icon name="arrow-right" color="#C35943" size="20rpx" />
			</view>
		</view>
		<view class="full-screen-chart">
			<qiun-data-charts type="mix" :opts="opts_full_screen" :chartData="chartData" :ontouch="true" :inScrollView="true"
				:tapLegend="false" :reshow="reShow" :canvas2d="true" v-bind="$attrs" />
		</view>
    <cWaterMark :rotate="60"></cWaterMark>
	</up-popup>
</template>

<script setup lang="ts">
	import { nextTick, ref, watch } from 'vue';
	import emptyVue from '@/components/empty/empty.vue';
	const isFullScreen = ref<Boolean>(false)
	const reShow = ref<Boolean>(false)
	const chartData = ref({})

	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		},
		type: {
			type: String,
			default: 'a'
		}
	})

	const opts = ref({
		color: props.type === 'a' ? ["#F75E3B", "#FF8C1A", "#43BF83"] : ["#43BF83", "#FF8C1A", '#F75E3B'],
		padding: [20, 0, 0, 5],
		enableScroll: true,
		legend: {
			fontSize: '10',
			fontColor: '#1F2428',
			lineHeight: '10'
		},
		xAxis: {
			showTitle: false,
			disableGrid: true,
			fontColor: 'rgba(31, 36, 40, 0.7)',
			fontSize: '10',
			marginTop: 8,
			rotateLabel: true,
			rotateAngle: 30,
			itemCount: 7,
			scrollAlign: 'right',
		},
		yAxis: {
			showTitle: false,
			disabled: false,
			disableGrid: false,
			splitNumber: 5,
			gridColor: "#CCCCCC",
			gridType: "dash",
			dashLength: 2,
			fontColor: 'rgba(31, 36, 40, 0.7)',
			fontSize: '10',
			padding: 10,
			data: [
				{
					position: "left",
					title: "折线",
					axisLineColor: '#F2F2F2',
					fontColor: '#7D8082',
					fontSize: '10',
				},
				{
					disabled: true,
					position: "right",
					title: "柱状图",
					textAlign: "left",
					axisLineColor: '#F2F2F2',
					fontColor: '#7D8082',
					fontSize: '10',
				}
			]
		},
		extra: {
			mix: {
				column: {
					width: 10,
					barBorderCircle: true,
					linearType: 'custom',
					seriesGap: 8,
					customColor: props.type === 'a' ? ['#FFB2A1', '#FFCF9E', '#BCE3D0'] : ['#BCE3D0', '#FFCF9E', '#FFB2A1'],
				},
				line: {
					type: "curve",
					width: 2,
					linearType: 'custom'
				}
			}
		}
	})

	const opts_full_screen = ref({
		color: props.type === 'a' ? ["#F75E3B", "#FF8C1A", "#43BF83"] : ["#43BF83", "#FF8C1A", '#F75E3B'],
		padding: [78, 70, 38, 48],
		enableScroll: true,
		rotate: true,
		legend: {
			fontSize: '10',
			fontColor: '#1F2428',
			lineHeight: '10'
		},
		xAxis: {
			showTitle: false,
			disableGrid: true,
			fontColor: 'rgba(31, 36, 40, 0.7)',
			fontSize: '10',
			marginTop: 8,
			rotateLabel: true,
			rotateAngle: 30,
			itemCount: 7,
			scrollAlign: 'right',
		},
		yAxis: {
			showTitle: false,
			disabled: false,
			disableGrid: false,
			splitNumber: 5,
			gridColor: "#CCCCCC",
			gridType: "dash",
			dashLength: 2,
			fontColor: 'rgba(31, 36, 40, 0.7)',
			fontSize: '10',
			padding: 10,
			data: [
				{
					position: "left",
					title: "折线"
				},
				{
					disabled: true,
					position: "right",
					title: "柱状图",
					textAlign: "left"
				}
			]
		},
		extra: {
			mix: {
				column: {
					width: 10,
					barBorderCircle: true,
					seriesGap: 8,
					linearType: 'custom',
					customColor: props.type === 'a' ? ['#FFB2A1', '#FFCF9E', '#BCE3D0'] : ['#BCE3D0', '#FFCF9E', '#FFB2A1'],
				},
				line: {
					type: "curve",
					opacity: 0.2,
					addLine: true,
					width: 2,
					gradient: true,
					activeType: "hollow"
				}
			}
		}
	})


	const handleOpen = () => {
		reShow.value = true
	}
	const handleClose = () => {
		reShow.value = false
		isFullScreen.value = false
	}

	watch(
		() => props.data,
		(v) => {
			if (v && JSON.stringify(v) !== '{}') {
				const series = v.values.map((item) => {
					const baseSeries = {
					};

					if (item.type === 'column') {
						return {
							type: 'column',
							textColor: 'transparent',
							legendShape: 'circle',
							...item
						};
					} else if (item.type === 'line') {
						console.log(item.linearColor);
						return {
							type: 'line',
							legendShape: 'circle',
							style: 'curve',
							pointShape: 'none',
							textColor: item.textColor || '#000',
							textSize: '12',
							textOffset: '-4',
							linearColor: item.linearColor || [
								[0, '#FF8C1A'],
								[1, '#FFCF9E']
							],
							...item
						};
					}

					return baseSeries;
				});

				const res = {
					categories: v.categories,
					series,
				};
				chartData.value = JSON.parse(JSON.stringify(res));
			} else {
				chartData.value = {};
			}
		}
	);
	// watch(
	// 	() => props.data,
	// 	(v) => {
	// 		if (v && JSON.stringify(v) != '{}') {
	// 			const res = {
	// 				categories: props.data.categories,
	// 				series: [
	// 					{
	// 						name: props.data.value1.name,
	// 						data: props.data.value1.data,
	// 						type: "column",
	// 						legendShape: 'circle',
	// 						textColor: 'transparent',
	// 					},
	// 					{
	// 						name: props.data.value2.name,
	// 						data: props.data.value2.data,
	// 						type: "line",
	// 						style: "curve",
	// 						legendShape: 'circle',
	// 						pointShape: 'none',
	// 						textColor: '#FF8C1A',
	// 						textSize: "12",
	// 						textOffset: '-4',
	// 						linearColor: [
	// 							[0, '#FF8C1A'],
	// 							[1, '#FFCF9E']
	// 						],
	// 					},
	// 					{
	// 						name: props.data.value3.name,
	// 						data: props.data.value3.data,
	// 						type: "line",
	// 						style: "curve",
	// 						legendShape: 'circle',
	// 						pointShape: 'none',
	// 						textColor: '#43BF83',
	// 						textSize: "12",
	// 						textOffset: '-4',
	// 						linearColor: [
	// 							[0, '#43BF83'],
	// 							[1, '#BCE3D0']
	// 						]
	// 					}
	// 				]
	// 			}
	// 			chartData.value = JSON.parse(JSON.stringify(res));
	// 		} else {
	// 			chartData.value = {}
	// 		}
	// 	}
	// )
</script>

<style lang="scss">
	.char {
		width: 100%;
		height: 672rpx;
	}

	.full-screen {
		justify-content: flex-end;
		margin-bottom: 40rpx;


		&-btn {
			border-radius: 200rpx;
			border: 1rpx solid #ffffff;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			padding: 8rpx 16rpx;

			.btn-text {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;

				&-primary {
					color: #c35943;
				}
			}
		}
	}

	.full-screen-rotate {
		z-index: 2000000;
		position: absolute;
		bottom: 40rpx;
		right: 40rpx;
		transform: rotate(90deg);
	}

	.full-screen-chart {
		width: 100%;
		height: 100%;
	}
</style>