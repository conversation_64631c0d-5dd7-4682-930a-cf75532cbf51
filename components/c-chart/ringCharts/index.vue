<template>
  <view class="ring-charts" :style="{height: legendPositon === 'bottom' ? '620rpx' : '520rpx'}">
    <template v-if="chartData && Object.keys(chartData).length > 0">
      <qiun-data-charts 
        ref="chartRef"
        type="ring"
        :opts="opts"
        :inScrollView="true"
        :chartData="chartData"
        :reshow="isShowRotate"
        @getIndex="getIndex"
        :canvas2d="true"
        :canvasId="`ringChart${new Date().getTime()}`"
      />

      <view class="ring-tooltip" v-show="tooltipShow">
        <slot></slot>
      </view>

      <view v-if="legendPositon === 'bottom'" class="ring-legend">
        <view class="legend-item" v-for="(item, index) in chartData?.series[0].data" :key="item.name">
          <view class="icon" :style="{background: opts.color[index]}"></view>
          <text class="text">{{ item.name }}</text>
        </view>
      </view>

      <view v-if="legendPositon === 'right'" class="ring-legend-right">
        <view class="legend-item" v-for="(item, index) in chartData?.series[0].data" :key="item.name">
          <view class="icon" :style="{background: opts.color[index]}"></view>
          <text class="text">{{ item.name }}</text>
        </view>
      </view>

      <view v-if="legendPositon === 'left'" class="ring-legend-left">
        <view class="legend-item" v-for="(item, index) in chartData?.series[0].data" :key="item.name">
          <view class="icon" :style="{background: opts.color[index]}"></view>
          <text class="text">{{ item.name }}</text>
        </view>
      </view>
    </template>
    <template v-else>
      <emptyVue />
    </template>
  </view>
</template>

<script setup lang="ts" name="ringCharts">
import { ref, watch } from 'vue';
import emptyVue from '@/components/empty/empty.vue';
import uCharts from '../../../uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts'

interface SeriesItem {
  name: string,
  value: number | string,
  labelShow: boolean
}

interface ChartData {
  series: Array<{data: Array<SeriesItem>}>
}

interface TooltipInfo {
  name: string,
  value: number,
  rateTb: number,
  rateHb: number,
  nightSaleCount: number,
  nightSaleTb: number,
  nightSaleHb: number,
  constitute?: Array<{
    name: string,
    value: number,
  }>
}

const props = defineProps({
  detail: {
    type: Object,
    default: () => {}
  },

  legendPositon: {
    type: String,
    default: 'bottom'
  }
})

const emit = defineEmits(['updateTooltip'])

const opts = ref({})

const setOpts = () => {
  opts.value = {
    rotate: false,
    rotateLock: false,
    update: true,
    color: props.detail.length > 4 ? ["#43BF83","#6046F2","#FCD80D","#F75E3B","#1474FB","#61C5FF","#FF8D1A"] : ["#43BF83","#FCAD0D","#F75E3B","#1474FB"],
    padding: props.legendPositon === 'bottom' ? [20,0,20,100] : [20,50,20,50],
    dataLabel: true,
    enableScroll: false,
    legend: {
      show: false,
    },
    title:{
      name:''
    },
    subtitle:{
      name:''
    },
    extra: {
      tooltip: {
        showBox: false
      },
      ring: {
        ringWidth: 20,
        customRadius: 80,
        activeOpacity: 1,
        activeRadius: 10,
        offsetAngle: -90,
        labelWidth: 15,
        border: false,
      }
    }
  }
}

const chartData = ref<ChartData>()
const chartRef = ref()
const isShowRotate = ref<boolean>(false)
const getData = () => {
  const res: ChartData = {
    series: [
      {
        data: []
      }
    ]
  }

  for(let i = 0; i < props.detail.length; i++) {
    const item = props.detail[i]
    res.series[0].data.push({
      name: item.name,
      value: item.value,
      labelShow: false,
    })
  }

  chartData.value = res
  isShowRotate.value = true
  tooltipShow.value = false
  setTimeout(() => {
    isShowRotate.value = false
  }, 500)
}

watch(() => props.detail, () => {
  if (!props.detail.length) return
  setOpts()
  setTimeout(() => {
    getData()
  }, 100)
}, {deep: true, immediate: true})

const tooltipInfo = ref<TooltipInfo>()
const tooltipShow = ref<boolean>(false)
const getIndex = (e)=>{
  if (e.currentIndex < 0) {
    tooltipShow.value = false
    return
  }
  tooltipShow.value = true
  tooltipInfo.value = props.detail[e.currentIndex]
  emit('updateTooltip', tooltipInfo.value)
}
</script>

<style lang="scss">
.ring-charts {
  position: relative;
  width: 100%;

  .chartsview {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ring-tooltip {
    position: absolute;
    top: 0;
    left: 0;
    padding: 20rpx;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f500 100%);
    border-radius: 20rpx;
  }

  .ring-legend {
    position: absolute;
    bottom: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;

    .legend-item {
      display: flex;
      align-items: center;
      padding: 0 20rpx 20rpx;

      .icon {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
      }

      .text {
        padding-left: 8rpx;
        font-size: 20rpx;
        line-height: 20rpx;
        color: #1F2428;
      }
    }
  }

  .ring-legend-right, .ring-legend-left {
    padding: 13rpx 20rpx;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 174rpx;
    right: 0;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f500 100%);
    border-radius: 20rpx;

    .legend-item {
      display: flex;
      align-items: center;
      padding: 7rpx 0;

      .icon {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
      }

      .text {
        padding-left: 8rpx;
        font-size: 20rpx;
        line-height: 20rpx;
        color: #1F2428;
      }
    }
  }

  .ring-legend-left {
    left: 0;
    right: inherit;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f500 100%);
  }
}
</style>