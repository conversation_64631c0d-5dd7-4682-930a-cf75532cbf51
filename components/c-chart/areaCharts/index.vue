<template>
  <view class="area-charts">
    <template v-if="minChartData && Object.keys(minChartData).length > 0">
      <view class="charts-btn">
        <view class="btn-right">
          <text class="btn-text" @click="handleOpen">全屏查看</text>
          <up-icon name="arrow-right" color="#C35943" size="20rpx" />
        </view>

        <slot></slot>
      </view>

      <view class="charts-wraper">
        <qiun-data-charts 
          type="area"
          :opts="opts"
          :inScrollView="true"
          :chartData="minChartData"
          :canvas2d="true"
          :tapLegend="false"
          tooltipFormat="tooltipDemo2"
          :canvasId="`areaChart${new Date().getTime()}`"
        />
      </view>
    </template>
    <view class="empty-wraper" v-else>
      <emptyVue />
    </view>

    <up-popup :show="isFullScreen" mode="center"  @close="handleClose" @open="handleOpen">
      <view class="full-screen">
        <view class="btn-right">
          <text class="btn-text" @click="handleClose">退出全屏</text>
          <up-icon name="arrow-right" color="#C35943" size="20rpx" />
        </view>

        <view class="charts-wraper-big">
          <qiun-data-charts 
            type="area"
            :opts="bigOpts"
            :inScrollView="true"
            :chartData="chartData"
            :ontouch="true"
            :reshow="isShowRotate"
            :canvas2d="true"
            :tapLegend="false"
            tooltipFormat="tooltipDemo2"
            :canvasId="`areaBigChart${new Date().getTime()}`"
          />
        </view>
      </view>
      <cWaterMark :rotate="60"></cWaterMark>
    </up-popup>
  </view>
</template>

<script setup lang="ts" name="areaCharts">
import { ref, watch } from 'vue';
import emptyVue from '@/components/empty/empty.vue';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

export interface SeriesItem {
  name: string,
  index: number,
  type: string,
  legendShape: string,
  pointShape?: string,
  linearColor?: Array<Array<number | string>>,
  data: Array<number | string>
}

export interface ChartData {
  categories: Array<string>,
  series: Array<SeriesItem>
}

const props = defineProps({
  lineNum: {
    type: Number,
    default: 1,
  },
  detail: {
    type: Object,
    default: () => {}
  },
  leftUnit: {
    type: String,
    default: ''
  },
});

const isFullScreen = ref<boolean>(false)
const isShowRotate = ref<boolean>(false)

const opts = ref({
  color: ["#F75E3B", "#43BF83"],
  padding: [15,0,0,0],
  dataLabel: false,
  enableScroll: true,
  legend: {
    position: 'bottom',
    fontSize: 10,
    fontColor: '#1F2428',
    padding: 20,
    itemGap: 30,
    lineHeight: 20
  },
  xAxis: {
    disableGrid: true,
    title: "",
    axisLineColor: '#F2F2F2',
    fontColor: '#858789',
    fontSize: 10,
    lineHeight: 10,
    marginTop: 8,
    itemCount: 7,
    scrollAlign: 'right',
    rotateLabel: true,
    rotateAngle: 30
  },
  yAxis: {
    disabled: false,
    disableGrid: false,
    splitNumber: 4,
    gridType: "dash",
    dashLength: 3,
    gridColor: "#F2F2F2",
    padding: 10,
    showTitle: true,
    data: [
    {
        position: "left",
        min: 0,
        axisLineColor: '#F2F2F2',
        fontColor: '#858789',
        fontSize: 10,
        tofix: 1,
        title: props.leftUnit,
        titleFontSize: 10,
        titleOffsetY: -5,
        titleFontColor: '#858789',
      }
    ]
  },
  extra: {
    area: {
      type: "curve",
      opacity: 0.2,
      addLine: true,
      width: 2,
      gradient: true,
      activeType: "solid"
    }
  }
})
const bigOpts = ref({
  color: ["#F75E3B", "#43BF83"],
  padding: [50, 30, 0, 80],
  dataLabel: false,
  enableScroll: true,
  scrollPosition: 'right',
  rotate: true,
  legend: {
    position: 'bottom',
    fontSize: 10,
    fontColor: '#1F2428',
    padding: 20,
    itemGap: 30,
    lineHeight: 20
  },
  xAxis: {
    disableGrid: true,
    title: "",
    axisLineColor: '#F2F2F2',
    fontColor: '#858789',
    fontSize: 10,
    lineHeight: 10,
    marginTop: 8,
    itemCount: 14,
    scrollAlign: 'right',
    rotateLabel: true,
    rotateAngle: 30
  },
  yAxis: {
    disabled: false,
    disableGrid: false,
    splitNumber: 4,
    gridType: "dash",
    dashLength: 3,
    gridColor: "#F2F2F2",
    padding: 10,
    showTitle: true,
    data: [
    {
        position: "left",
        min: 0,
        axisLineColor: '#F2F2F2',
        fontColor: '#858789',
        fontSize: 10,
        tofix: 1,
        title: props.leftUnit,
        titleFontSize: 10,
        titleOffsetY: -5,
        titleFontColor: '#858789',
      }
    ]
  },
  extra: {
    area: {
      type: "curve",
      opacity: 0.2,
      addLine: true,
      width: 2,
      gradient: true,
      activeType: "none"
    }
  }
})

const chartData = ref<ChartData>()
const minChartData = ref<ChartData>()
const getData = () => {
  const res: ChartData = {
    categories: props.detail.categories,
    series: []
  }

  for(let i = 0; i < props.lineNum; i++) {
    const item = props.detail.data[i]

    res.series.push({
      name: item.name,
      index: 0,
      pointShape: 'none',
      type: "area",
      legendShape: 'circle',
      data: item.data
    })
  }

  chartData.value = res
  if (props.detail.categories.length > 7) {
    let series = JSON.parse(JSON.stringify(res.series))
    series.forEach((item) => {
      item.data = item.data.slice(props.detail.categories.length - 7)
    })

    minChartData.value = {
      categories: props.detail.categories.slice(props.detail.categories.length - 7),
      series
    }
  } else {
    minChartData.value = res
  }
}
const handleOpen = () => {
  isFullScreen.value = true
  isShowRotate.value = true
  setTimeout(() => {
    isShowRotate.value = false
  }, 500)
}

const handleClose = () => {
  isFullScreen.value = false
  isShowRotate.value = true
  setTimeout(() => {
    isShowRotate.value = false
  }, 500)
}

watch(() => props.detail, () => {
  if (props.detail.data.length) {
    getData()
  }
}, {deep: true, immediate: true})
</script>

<style lang="scss">
.area-charts {
  width: 100%;

  .empty-wraper {
    width: 100%;
    height: 600rpx;
  }

  .charts-wraper {
    width: 100%;
    height: 600rpx;
  }

  .charts-btn {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 40rpx;

    .btn-right {
      display: flex;
      align-items: center;
      height: 38rpx;
      padding: 0 16rpx;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      border-radius: 19rpx;

      .btn-text {
        padding-right: 8rpx;
        font-size: 22rpx;
        color: #C35943;
      }
    }
  }

  .chartsview {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .full-screen {
    position: relative;
    width: 750rpx;
    height: 100vh;
    padding: 40rpx;

    .btn-right {
      position: absolute;
      bottom: 88rpx;
      right: -10rpx;
      transform: rotate(90deg);

      display: flex;
      align-items: center;
      height: 38rpx;
      padding: 0 16rpx;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      border-radius: 19rpx;
      z-index: 99;

      .btn-text {
        padding-right: 8rpx;
        font-size: 22rpx;
        color: #C35943;
      }
    }

    .charts-wraper-big {
      width: 100%;
      height: 100%;
    }
  }

  .u-popup__content {
    width: 100vw;
    height: 100vh;
  }
}
</style>
