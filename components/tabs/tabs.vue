<template>
	<view :class="['tabs', className]">
		<view v-for="(item, index) in tabs" :key="item.value" class="tab-item" :class="{'tab-item--active': tabValue == item.value}" @click="onTabActive(item.value)">
			<text class="tab-item__text">{{ item.label }}</text>
		</view>
	</view>
</template>

<script setup>
import { watch, ref } from "vue"
	const props = defineProps({
		className: {
			type: String,
			default: ''
		},
		tabs: {
			type: Array,
			defult: () => []
		},
		color: {
			type: String,
			default: '#999'
		},
		fontSize: {
			type: String,
			default: '32rpx'
		},
		activeColor: {
			type: String,
			default: '#014DF6'
		},
		borderColor: {
			type: String,
			default: '#fff'
		},
		backgroundColor:{
			type: String,
			default: '#fff'
		},
		modelValue: {
			type: [String, Number],
			default: 0
		}
	})
	const emits = defineEmits(['update:modelValue', 'change'])
	const tabValue = ref(props.tabs[0].value)
	watch(() => props.modelValue, v => {
		tabValue.value = v
	}, {immediate: true})
	
	const onTabActive = (value) => {
		tabValue.value = value
		emits('update:modelValue', value)
		emits('change', value)
	}
	
</script>

<style lang="scss" scoped>
	.tabs{
		display: flex;
		align-items: center;
		
		.tab-item{
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 32rpx 0;
			.tab-item__text{
				font-size: v-bind(fontSize);
				color: v-bind(color);
			}
		}
		&:not(&-border) {
			.tab-item--active{
				position: relative;
				.tab-item__text{
					color: v-bind(activeColor);	
					font-weight: bold;
				}
				&::after{
					content:"";
					position: absolute;
					bottom:0;
					left:50%;
					width: 160rpx;
					height: 4rpx;
					transform: translateX(-50%);
					background-color: v-bind(activeColor);
				}
			}
		}
		
		&-border{
			overflow: hidden;
			border: 2rpx solid v-bind(borderColor);
			border-radius: 8rpx;
			
			.tab-item:nth-child(2){
				border-left: 1rpx solid #fff;
				border-right: 1rpx solid #fff;
			}
			.tab-item.tab-item--active{
				background-color: v-bind(backgroundColor);
				.tab-item__text{
					color: v-bind(activeColor);
				}
			}
			
		}
		
		&-fill{
			justify-content: space-between;
			.tab-item{
				flex: auto;
				padding: 20rpx 0;
				.tab-item__text {
					padding: 18rpx 46rpx;
				}
			}
			.tab-item--active{
				.tab-item__text{
					color: #fff !important;
					background-color: $uni-color-primary;
					border-radius: 46rpx;
				}
				&::after{
					display: none;
				}
			}
		}
	}
	
</style>