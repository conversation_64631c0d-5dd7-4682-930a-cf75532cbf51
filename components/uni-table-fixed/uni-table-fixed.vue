<template>
  <view class="uni-table-fixed-container">
    <!-- 固定列区域 -->
    <view 
      v-if="fixedColumns.length > 0" 
      class="fixed-columns" 
      :style="{ width: fixedWidth + 'px' }"
    >
      <view class="fixed-table-wrapper">
        <uni-table 
          :border="border" 
          :stripe="stripe" 
          :loading="loading"
          :empty-text="emptyText"
          :data="data"
          :row-key="rowKey"
          @selection-change="handleSelectionChange"
        >
          <!-- 固定表头 -->
          <uni-thead>
            <uni-tr>
              <uni-th 
                v-for="column in fixedColumns" 
                :key="column.key"
                :width="column.width"
                :align="column.align"
                :sortable="column.sortable"
                @sort-change="handleSortChange"
              >
                {{ column.title }}
              </uni-th>
            </uni-tr>
          </uni-thead>
          
          <!-- 固定数据行 -->
          <uni-tr 
            v-for="(row, index) in data" 
            :key="getRowKey(row, index)"
            :key-value="getRowKey(row, index)"
          >
            <uni-td 
              v-for="column in fixedColumns" 
              :key="column.key"
              :width="column.width"
              :align="column.align"
            >
              <slot 
                :name="column.key" 
                :row="row" 
                :column="column" 
                :index="index"
              >
                {{ getColumnValue(row, column.key) }}
              </slot>
            </uni-td>
          </uni-tr>
        </uni-table>
      </view>
    </view>

    <!-- 可滚动区域 -->
    <view class="scrollable-columns" :style="{ marginLeft: fixedWidth + 'px' }">
      <scroll-view 
        class="scroll-container"
        scroll-x="true"
        scroll-y="false"
        @scroll="handleScroll"
      >
        <view class="scrollable-table-wrapper" :style="{ minWidth: scrollableWidth + 'px' }">
          <uni-table 
            :border="border" 
            :stripe="stripe" 
            :loading="loading"
            :empty-text="emptyText"
            :data="data"
            :row-key="rowKey"
            @selection-change="handleSelectionChange"
          >
            <!-- 可滚动表头 -->
            <uni-thead>
              <uni-tr>
                <uni-th 
                  v-for="column in scrollableColumns" 
                  :key="column.key"
                  :width="column.width"
                  :align="column.align"
                  :sortable="column.sortable"
                  @sort-change="handleSortChange"
                >
                  {{ column.title }}
                </uni-th>
              </uni-tr>
            </uni-thead>
            
            <!-- 可滚动数据行 -->
            <uni-tr 
              v-for="(row, index) in data" 
              :key="getRowKey(row, index)"
              :key-value="getRowKey(row, index)"
            >
              <uni-td 
                v-for="column in scrollableColumns" 
                :key="column.key"
                :width="column.width"
                :align="column.align"
              >
                <slot 
                  :name="column.key" 
                  :row="row" 
                  :column="column" 
                  :index="index"
                >
                  {{ getColumnValue(row, column.key) }}
                </slot>
              </uni-td>
            </uni-tr>
          </uni-table>
        </view>
      </scroll-view>
    </view>

    <!-- 固定列阴影 -->
    <view 
      v-if="fixedColumns.length > 0 && showShadow" 
      class="fixed-shadow"
      :style="{ left: fixedWidth + 'px' }"
    ></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface Column {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right' | boolean
  sortable?: boolean
}

interface Props {
  columns: Column[]
  data: any[]
  border?: boolean
  stripe?: boolean
  loading?: boolean
  emptyText?: string
  rowKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  data: () => [],
  border: false,
  stripe: false,
  loading: false,
  emptyText: '没有更多数据',
  rowKey: 'id'
})

const emit = defineEmits(['selection-change', 'sort-change', 'scroll'])

// 滚动状态
const showShadow = ref(false)

// 计算固定列和可滚动列
const fixedColumns = computed(() => {
  return props.columns.filter(col => col.fixed === 'left' || col.fixed === true)
})

const scrollableColumns = computed(() => {
  return props.columns.filter(col => !col.fixed || col.fixed === 'right')
})

// 计算宽度
const fixedWidth = computed(() => {
  return fixedColumns.value.reduce((total, col) => {
    const width = typeof col.width === 'string' ? parseInt(col.width) : (col.width || 120)
    return total + width
  }, 0)
})

const scrollableWidth = computed(() => {
  return scrollableColumns.value.reduce((total, col) => {
    const width = typeof col.width === 'string' ? parseInt(col.width) : (col.width || 120)
    return total + width
  }, 0)
})

// 获取行键值
const getRowKey = (row: any, index: number) => {
  return row[props.rowKey] || index
}

// 获取列值
const getColumnValue = (row: any, key: string) => {
  return row[key] || '-'
}

// 处理滚动事件
const handleScroll = (e: any) => {
  const scrollLeft = e.detail.scrollLeft || 0
  showShadow.value = scrollLeft > 0
  
  emit('scroll', {
    scrollLeft,
    scrollTop: e.detail.scrollTop || 0,
    detail: e.detail
  })
}

// 处理选择变化
const handleSelectionChange = (e: any) => {
  emit('selection-change', e)
}

// 处理排序变化
const handleSortChange = (e: any) => {
  emit('sort-change', e)
}
</script>

<style lang="scss" scoped>
.uni-table-fixed-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.fixed-columns {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
}

.fixed-table-wrapper {
  width: 100%;
  overflow: hidden;
}

.scrollable-columns {
  width: 100%;
  overflow: hidden;
}

.scroll-container {
  width: 100%;
  height: 100%;
}

.scrollable-table-wrapper {
  width: 100%;
}

.fixed-shadow {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 6px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
  pointer-events: none;
  z-index: 9;
}

/* 隐藏固定列区域的滚动条 */
.fixed-columns :deep(.uni-table-scroll) {
  overflow: hidden !important;
}

/* 确保表格行高一致 */
.fixed-columns :deep(.uni-table-tr),
.scrollable-columns :deep(.uni-table-tr) {
  height: auto;
  min-height: 44px;
}

.fixed-columns :deep(.uni-table-th),
.scrollable-columns :deep(.uni-table-th) {
  height: 44px;
  line-height: 44px;
}

.fixed-columns :deep(.uni-table-td),
.scrollable-columns :deep(.uni-table-td) {
  height: 44px;
  line-height: 44px;
}
</style>
