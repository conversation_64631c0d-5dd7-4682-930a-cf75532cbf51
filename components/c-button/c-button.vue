<template>
	<view class="bottom">
		<slot>
			<text class="fr btn" :class="[disabled ? 'btn--disabled':'']" @click="onclick">{{ btnText }}</text>
		</slot>
	</view>
</template>

<script setup lang="ts">
	import { computed } from 'vue';
	const props = defineProps({
		btnText: {
			type: String,
			default: '确认提交'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		height: {
			type: Number,
			default: 120
		}
	})
	const emits = defineEmits(['click'])
	const safeInsetBottom = uni.getSystemInfoSync().safeAreaInsets.bottom || 34

	const bottomHeight = computed(() => uni.upx2px(props.height) + safeInsetBottom - uni.upx2px(18) + 'px')
	const onclick= () => {
		emits('click')
	}
	defineExpose({
		bottomHeight
	})
</script>

<style lang="scss" scoped>
	.bottom {
		position: fixed;
		bottom:0;
		left:0;
		right:0;
		z-index: 99;
		box-sizing: border-box;
		height: v-bind(bottomHeight);
		padding: 18rpx 48rpx;
		background-color: #fff;
		.btn{
			justify-content: center;
			width: 654rpx;
			height: 86rpx;
			font-size: 32rpx;
			color:#fff;
			background-color: $uni-color-primary;
			border-radius: 85rpx;
			
			&--disabled{
				background: #D8D8D8;
			}
		}
	}
</style>