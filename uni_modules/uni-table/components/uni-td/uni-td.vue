<template>
	<!-- #ifdef H5 -->
	<td class="uni-table-td" :rowspan="rowspan" :colspan="colspan" :class="{'table--border':border, 'fixed-column': fixed}" :style="tdStyle">
		<slot></slot>
	</td>
	<!-- #endif -->
	<!-- #ifndef H5 -->
	<!-- :class="{'table--border':border}"  -->
	<view class="uni-table-td" :class="{'table--border':border, 'fixed-column': fixed}" :style="tdStyle">
		<slot></slot>
	</view>
	<!-- #endif -->

</template>

<script>
	/**
	 * Td 单元格
	 * @description 表格中的标准单元格组件
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=3270
	 * @property {Number} 	align = [left|center|right]	单元格对齐方式
	 */
	export default {
		name: 'uniTd',
		options: {
			// #ifdef MP-TOUTIAO
			virtualHost: false,
			// #endif
			// #ifndef MP-TOUTIAO
			virtualHost: true
			// #endif
		},
		props: {
			width: {
				type: [String, Number],
				default: ''
			},
			align: {
				type: String,
				default: 'left'
			},
			rowspan: {
				type: [Number,String],
				default: 1
			},
			colspan: {
					type: [Number,String],
				default: 1
			},
			// 固定列相关属性
			fixed: {
				type: [Boolean, String],
				default: false
			},
			fixedLeft: {
				type: [String, Number],
				default: 0
			}
		},
		data() {
			return {
				border: false
			};
		},
		computed: {
			// 单元格样式，包含固定列样式
			tdStyle() {
				let style = {
					width: this.width + 'rpx',
					'text-align': this.align
				}

				// 如果是固定列，添加固定样式
				if (this.fixed) {
					style.position = 'sticky'
					style.left = this.fixedLeft + 'rpx'
					style.zIndex = 9
				}

				return style
			}
		},
		created() {
			this.root = this.getTable()
			this.border = this.root.border
		},
		methods: {
			/**
			 * 获取父元素实例
			 */
			getTable() {
				let parent = this.$parent;
				let parentName = parent.$options.name;
				while (parentName !== 'uniTable') {
					parent = parent.$parent;
					if (!parent) return false;
					parentName = parent.$options.name;
				}
				return parent;
			},
		}
	}
</script>

<style lang="scss">
	$border-color:#EBEEF5;

	.uni-table-td {
		display: table-cell;
		padding: 8px 10px;
		font-size: 14px;
		// border-bottom: 1px $border-color solid;
		font-weight: 400;
		color: #606266;
		line-height: 23px;
		box-sizing: border-box;
	}

	.table--border {
		border-right: 1px $border-color solid;
	}

	/* 固定列样式 */
	.fixed-column {
		position: sticky !important;
		z-index: 9 !important;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			width: 1px;
			background-color: $border-color;
			pointer-events: none;
		}
	}
</style>
