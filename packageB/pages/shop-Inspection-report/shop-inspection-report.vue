<template>
	<view class="w-container">
		<view class="bg">
			<commonBgVue />
		</view>
		<u-navbar title="巡店报告" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx;font-weight:700;color:#FFF;" @leftClick="customBack">
      <template #left>
        <template v-if="pages.length > 1">
          <u-icon name="arrow-left" size="20" color="#FFF"></u-icon>
        </template>
        <template v-else>
          <u-icon name="home" size="22" color="#FFF"></u-icon>
        </template>
      </template>
		</u-navbar>
		<view class="content">
			<image :src="reportInfo.picUrl" mode="widthFix" lazy-load class="pdf" @click="onPreviewImg"></image>
		</view>
		<fixedBtnLayoutVue ref="fixedBtnLayoutRef">
			<view class="fl container-footer">
				<view class="fr contract">
					<text>当前契约方式：</text>
					<text>{{ signingMethod }}</text>
				</view>
				<text v-if="config.text?.title" class="contract-title">{{ config.text?.title }}</text>
				<text class="contract-desc">{{ config.text?.subTitle || config.text }}</text>
				<view class="fr btn-groups" :class="{'btn-groups__reduce': config.arr?.length <= 3}">
					<view class="fl" v-for="(item, index) in config.arr" :key="index" @tap="onClick(item.key)">
						<view class="fr btn-image">
							<image :src="item.url" :style="{width: item.width + 'rpx', height: item.height + 'rpx'}" mode="aspectFill"
								lazy-load></image>
						</view>
						<text class="btn-title">{{ item.text }}</text>
					</view>
				</view>
			</view>
		</fixedBtnLayoutVue>
		<confirmModalVue ref="confirmModalRef" confirm-btn-text="确认变更" @confirm="onOk">
			<template v-slot="{ }">
				<text class="fr" style="justify-content: center;">{{ config.updateText }}</text>
			</template>
		</confirmModalVue>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onUnload } from '@dcloudio/uni-app'
  import { ref, computed, getCurrentInstance, ComponentInternalInstance } from 'vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import fixedBtnLayoutVue from '../../components/fixed-btn-layout/fixed-btn-layout.vue';
	import confirmModalVue from '../../components/confirm-modal/confirm-modal.vue';
	import { getConfig } from './enums'
	import { showToast, showLoading } from '@/utils/util'
	import { queryTripPdf as queryTripPdfApi, updateSign, offlineSignDone } from '../../api'
	import { useContentLayoutHeight } from '../../hooks'
	import { useCommon } from '@/hooks/useGlobalData';
	import { newPageInstance } from '@/types/index.d';
	type pageOptionType = {
		tripCode : string;
		[k : string] : any
	}
	const confirmModalRef = ref<InstanceType<typeof confirmModalVue>>(null)
	const config = ref<Record<string, any>>([])
	const pageOptions = ref<Partial<pageOptionType>>({})
	const reportInfo = ref<Partial<pageOptionType>>({})
	const { fixedBtnLayoutRef, getFixedBtnLayoutHeight, contentHeight, getContentLayoutHeight } = useContentLayoutHeight('.content')
  const instance = getCurrentInstance() as ComponentInternalInstance
	const { route_to_view, setRouteParams, customBack } = useCommon(getCurrentInstance())
  const pages = getCurrentPages()
	const signingMethod = computed(() => {
		const isOnline = reportInfo.value.isOnline
		let text = ''
		switch (isOnline) {
			case 1:
				text = '契约锁签章'
				break;
			case 0:
				text = '纸质版盖章'
				break;
			case 2:
				text = '无需签章'
				break;
		}
		return text
	})
	const onClick = (key : string) : void => {
		const fnMethod = {
			resendShortMessage() {
				showToast('请在企业微信联系王聪1进行短信重发操作')
			},
			sendReport() {
				route_to_view(`/packageB/pages/send-report/send-report?shopId=${reportInfo.value.shopId}&orderId=${reportInfo.value.orderId}&status=0`)
			},
			resendReport() {
				if(reportInfo.value.pdfResend <= 0){
					showToast('发送电子报告次数已用尽~')
					return
				}
				route_to_view(`/packageB/pages/send-report/send-report?shopId=${reportInfo.value.shopId}&orderId=${reportInfo.value.orderId}&status=1`)
			},
			signature() {
				if(reportInfo.value.pdfChange <= 0){
					showToast('签章类型变更次数已用尽~')
				} else {
					confirmModalRef.value.open(true)
				}
			},
			downloadImage() {
				if (reportInfo.value.picUrl) {
					showLoading('下载中')
					uni.downloadFile({
						url: reportInfo.value.picUrl,
						success: (res : UniApp.DownloadSuccessData) => {
							uni.saveImageToPhotosAlbum({
								filePath: res.tempFilePath,
								success() {
									showToast('保存成功')
								},
								fail: () => {
									showToast('保存失败')
								}
							})
						},
						fail: () => {
							showToast('保存失败')
						}
					})
				}
			},
			async signatureCompleted() {
				const { orderId, shopId, targetCode, picUrl} = reportInfo.value
				//修改状态
				await offlineSignDone({
					orderId,
					shopId,
				  targetCode,
					imagePath: picUrl
				})
				showToast('操作完成')
				await queryTripPdf()
			}
		}
		fnMethod[key]?.()
	}
	/**
	 * @description: 预览图片
	 */
	const onPreviewImg = () => {
		uni.previewImage({
			urls: [reportInfo.value.picUrl]
		})
	}
	/**
	 * @description: 获取行程pdf
	 */
	const queryTripPdf = async () : Promise<void> => {
		const res = await queryTripPdfApi({
			tripCode: pageOptions.value.tripCode
		})
		reportInfo.value = res.data
		config.value = getConfig(res.data)
	}
	/**
	 * @description: 更新签章
	 */
	const onOk = async () => {
		const { isOnline, shopId, orderId, targetCode } = reportInfo.value
		try {
			await updateSign({
				isOnlineSign: isOnline == 1 ? 0 : 1,
				shopId,
				orderId,
				targetCode
			})
			showToast('更新成功')
			confirmModalRef.value.open(false)
			await queryTripPdf()
		} catch (e) {
			console.log("🚀 ~ onOk ~ e:", e)
		}
	}
	onLoad(async (opts : { tripCode : string }) => {
		pageOptions.value = opts
    showLoading('加载中')
    instance.appContext.app.config.globalProperties.$unLaunched.then(async (res : any) => {
      if (res) {
        await queryTripPdf()
        await getFixedBtnLayoutHeight()
        await getContentLayoutHeight()
      }
    })
	})
	onShow(async () => {
		const pages = getCurrentPages()
		const currentPage : newPageInstance = pages[pages.length - 1]
		if (currentPage && currentPage.refresh) {
			delete currentPage.refresh
			await queryTripPdf()
		}
	})
	onUnload(() => {
		setRouteParams('packageB/pages/shop-inspection/shop-inspection-trip')
		setRouteParams('packageB/pages/shop-inspection/shop-inspection-report-list')
	})
</script>

<style lang="scss" scoped>
	.w-container {
		overflow: hidden;

		.bg {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			overflow: hidden;
			height: 206rpx;
		}

		.content {
			overflow-y: auto;
			display: flex;
			margin-top: 206rpx;
			height: v-bind(contentHeight);

			.pdf {
				margin: auto;
				max-width: 100%;
			}
		}

		:deep() {
			.btn-block {
				box-shadow: 0 -8rpx 20rpx 0 #c4c4c440;
			}
		}

		.container-footer {
			padding: 0 40rpx;
			width: 100%;
			background: #fff;
			color: #1F2428;
			border-radius: 20rpx 20rpx 0 0;

			.contract {
				justify-content: space-between;
				margin-bottom: 20rpx;
				font-weight: bold;

				&-title {
					font-size: 24rpx;
				}

				&-desc {
					margin-bottom: 40rpx;
					font-size: 24rpx;
				}
			}

			.btn-groups {
				justify-content: space-between;
				padding: 0 20rpx;
				height: 134rpx;
				font-size: 22rpx;
				background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
				box-shadow: 0 -8rpx 26rpx 0 #f75e3b21;
				border-radius: 20rpx;

				&__reduce {
					justify-content: space-around;
				}

				.btn-title {
					color: #fff;
				}

				.fl {
					align-items: center;
				}

				.btn-image {
					justify-content: center;
					width: 52rpx;
					height: 52rpx;
					margin-bottom: 20rpx;
				}
			}
		}
	}
</style>