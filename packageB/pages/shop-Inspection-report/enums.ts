export const getConfig = (data : { [k : string] : any }) => {
	const list = [
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733889060407.png',
			text: `重发电子报告(${data.pdfResend})`,
			key: 'resendReport',
			width: 52,
			height: 50
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733888989428.png',
			text: '已确认完成',
			key: 'confirmedCompleted',
			width: 52,
			height: 52
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733889097816.png',
			text: '我已完成签章',
			key: 'signatureCompleted',
			width: 52,
			height: 50
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733888972129.png',
			text: '发送电子报告',
			key: 'sendReport',
			width: 52,
			height: 50
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733888889620.png',
			text: '下载图片',
			key: 'downloadImage',
			width: 52,
			height: 46
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733888922453.png',
			text: '签章短信重发',
			key: 'resendShortMessage',
			width: 52,
			height: 44
		},
		{
			url: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-11/1733888943050.png',
			text: `签章类型变更(${data.pdfChange || 0})`,
			key: 'signature',
			width: 52,
			height: 50
		}
	]

	const configText = {
		'10': {
			title: '填写签章人信息时请保证信息准确;',
			subTitle: '手机号请保证与公司认证号码一致且必须是签章人自己身份证注册的手机号；签署人姓名/企业名，请注意错别字。'
		},
		'11': '契约锁签章短信已发送给业主用户，请跟进业主及时完成',
		'13': '业主已完成契约锁签章',
		'00': '如已下载图片打印并完成线下签章流程，请点击“我已完成签章”按钮进行确认；否则视为该表单签章未完成',
		'03': '您已完成线下纸质盖章，请及时将纸质报告寄回总部进行归档。',
		'24': '当前政策问题店表单无需回传',
	}

	const configBtn = {
		'03': 'confirmedCompleted',
		'00': 'signatureCompleted',
		'10': 'sendReport',
		'11': ['resendReport', 'resendShortMessage'],
		'default': 'downloadImage',
	}

	let arr = []
	for (let item in configBtn) {
		const key = data.isOnline + '' + data.pdfStatus
		if (key == item) {
			arr.push(...list.filter(r => typeof configBtn[key] == 'string' ? (r.key == configBtn[key]) : configBtn[key].includes(r.key)))
		}
		if (item == 'default') {
			arr.push(list.find(r => r.key == 'downloadImage'))
		}
	}
	if (data.pdfStatus != 3 && data.targetCode != 3) {
		arr.push(list.find(r => r.key == 'signature'))
	}
	return {
		arr,
		text: configText[data.isOnline + '' + data.pdfStatus],
		updateText: data.isOnline == 1 ? '巡店报告签章方式是否要变更为线下纸质版？' : '巡店报告签章方式是否变更为线上契约锁盖章？'
	}
}