// 计划信息
export interface PlanInfo {
  id: number;
  title: string;
  description: string;
  source: 1 | 2 | 3; // 1-集团下发、2-城区下发、3-大区下发
  type: 1 | 2 | 3 | 4; // 1-经营类、2-服务类、3-品质类、4-学习类
  project: string;
  refreshCycle: 1 | 2 | 3 | 4; // 1:日更新 2:周更新 3:月更新 4:固定任务
  dayBeginTime: string;
  dayEndTime: string;
  startDate: string;
  endDate: string;
  priority: 1 | 2 | 3; // 1-常规 2-重要 3-紧急
  requireReview: 0 | 1; // 0-不需审核,1-需要审核
  requireAssign: 0 | 1; // 0-不可,1-可以
  tags: string; // 标签：|tag1|tag2|
}

// 任务信息
export interface TaskInfo {
  title: string;
  id: number;
  taskPlanId: number;
  shopId: string;
  shopName: string;
  staffId: string;
  staffName: string;
  assignStaffId: string;
  assignStaffName: string;
  dateCode: string; // yyyyMMdd
  startTime: string;
  endTime: string;
  completionTime: string;
  reviewId: string;
  reviewName: string;
  reviewOpinion: string;
  reviewTime: string;
  reviewStatus: 0 | 1 | 2 | 3; // 0-不需审核,1-待审核,2-通过,3-驳回
  status: 0 | 1 | 2; // 0-未提交,1-已提交,2-已作废
  publishStatus: 1 | 2 | 3 | 4 | 5 | 6 | 7; // 发布状态枚举
  remark: string;
  lastModifiedTime: string;
}

// 步骤中的填写信息
export interface FillInfo {
  num: number;
  label: 1 | 2 | 3 | 4 | 5; // 1拍照上传 2单行文本 3多行文本 4单选 5多选
  name: string;
  remark: string;
  required: boolean;
  type: number;
  count?: number; // 仅label=1时有效
  demos?: string[]; // 仅label=1时有效
  pics?: Array<{ // 仅label=1时有效
    name: string;
    num: number;
    url: string;
    remark: string;
  }>;
  unit?: string; // 仅label=2时有效
  context?: string; // 仅label=2|3时有效
  options?: Array<{ // 仅label=4|5时有效
    num: number;
    checked: boolean;
    option: string;
  }>;
  singleChecked?: number; // 仅label=4时有效
  multiChecked?: number[]; // 仅label=5时有效
}

// 任务步骤
export interface TaskStep {
  num: number;
  name: string;
  description: string;
  completionCriteria: 1 | 2 | 3; // 1:填报资料 2:页面跳转 3:系统取数
  fillInfoList: FillInfo[];
  picCntRequired?: boolean; // 仅completionCriteria=1时有效
  jumpUrl?: string; // 仅completionCriteria=2时有效
  remark?: string
}

// 任务日志
export interface TaskLog {
  id: number;
  taskId: number;
  dateCode: string; // yyyyMMdd
  type: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
  remark: string;
  createdBy: string;
  createdByName: string;
  createdTime: string;
}


// 任务奖励
export interface TaskReward {
  rewardType: 1 | 2;
  rewardValue: number;
  extraReward: 0 | 1 | 2;
  rankList: {
    rank: number;
    rewardValue: number;
  }[]
}

// 完整的任务响应
export interface TaskResponse {
  planInfo: PlanInfo;
  taskInfo: TaskInfo;
  stepList: TaskStep[];
  logs: TaskLog[];
  rewardList: TaskReward[];
}