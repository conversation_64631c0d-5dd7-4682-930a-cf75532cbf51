<template>
  <view class="step">
    <view class="fr" style="justify-content: space-between">
      <view class="fr">
        步骤
        <text class="DIN-Bold" style="margin-left: 8rpx;">
          <text class="text">{{ currentStep }}</text>/{{ step }}
        </text>
      </view>
      <view class="DIN-Bold text">{{ currentStep === step ? 100 : (100 / step * currentStep).toFixed(1) }}%</view>
    </view>
    <view class="progress">
      <view class="progress-bar" :style="{width:(100 / step * currentStep).toFixed(1)+'%'}"></view>
    </view>
  </view>
</template>

<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      step: number,
      currentStep: number
    }>(),
    {
      step: () => 1,
      currentStep: () => 1
    }
  )
</script>

<style scoped lang="scss">
  .step {
    color: #222222;
    font-size: 26rpx;
    font-weight: bold;
    line-height: 26rpx;
    margin-bottom: 40rpx;

    .DIN-Bold {
      color: #999999;
      font-size: 32rpx;
      font-weight: 700;
      line-height: 32rpx;
    }

    .text {
      color: $uni-text-color;
    }

    .progress {
      margin-top: 18rpx;
      height: 4rpx;
      border-radius: 200rpx;
      background: #e5e5e599;

      &-bar {
        height: 100%;
        background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
        border-radius: 10px;
        transition: width 0.3s ease;
      }
    }
  }
</style>