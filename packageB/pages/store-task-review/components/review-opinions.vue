<template>
  <view class="review-opinions" v-if="taskInfo?.reviewStatus > 0">
    <template v-if="taskInfo?.reviewStatus === 1">
      <view class="title" style="margin-bottom: 28rpx;">审核意见（驳回必填）</view>
      <cTextareaVue
        v-model="remark" count maxlength="200" placeholder="请依据门店真实情况，填写真实的审核意见哦，最少输入10个字" />
      <fixed-btn-layout>
        <view class="custom-btn" @tap="feedback('reject')">审核驳回</view>
        <view class="custom-btn custom-btn-primary" @tap="feedback('pass')">审核通过</view>
      </fixed-btn-layout>
    </template>
    <template v-if="taskInfo?.reviewStatus === 2 || taskInfo?.reviewStatus === 3">
      <view class="t2">审核结果</view>
      <view>
        <view class="row">
          <text class="label">审核结果：</text>
          <text
            v-if="taskInfo?.reviewStatus" class="value"
            :class="[ taskInfo?.reviewStatus === 2  ? 'pass' : taskInfo?.reviewStatus === 3 ? 'rejected' : '']">
            {{ REVIEW_STATUS[taskInfo?.reviewStatus] }}
          </text>
        </view>
        <view class="row">
          <text class="label">审核意见：</text>
          <text class="value">{{ taskInfo?.reviewOpinion || '-' }}</text>
        </view>
        <view class="row">
          <text class="label">审核时间：</text>
          <text class="value">{{ dayjs(taskInfo?.reviewTime).format('YYYY.MM.DD HH:mm:ss') }}</text>
        </view>
        <view class="row">
          <text class="label">审核人：</text>
          <text class="value">{{ taskInfo?.reviewName }}</text>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
  import { ComponentInternalInstance, computed, getCurrentInstance, ref } from 'vue'
  import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
  import fixedBtnLayout from '../../../components/fixed-btn-layout/fixed-btn-layout.vue';
  import type { TaskInfo } from "../type";
  import { REVIEW_STATUS } from "../enmu";
  import dayjs from "dayjs";
  import { useCommon } from "@/hooks/useGlobalData";
  import { showToast } from "@/utils/util";
  import { taskReviewPass, taskReviewReject } from "@/packageB/api"

  const {globalData} = useCommon(getCurrentInstance() as ComponentInternalInstance)

  const props = withDefaults(
    defineProps<{
      taskInfo: Partial<TaskInfo>
    }>(),
    {
      taskInfo: () => ({})
    }
  )

  defineOptions({
    options: {
      styleIsolation: 'shared'
    }
  })

  const emit = defineEmits(['update'])

  const remark = ref<string>('')

  const margin_bottom = computed(() => {
    if (props?.taskInfo?.reviewStatus !== 1) {
      return uni.getSystemInfoSync().safeAreaInsets.bottom + 'rpx'
    } else {
      return uni.getSystemInfoSync().safeAreaInsets.bottom + 180 + 'rpx'
    }
  })

  const feedback = async (type: string) => {
    if (type === "reject") {
      if (remark.value.length < 1) {
        showToast('请填写审核意见')
        return
      }
      if (remark.value.length < 10) {
        showToast('最少输入10个字')
        return
      }
      if (remark.value.length > 200) {
        showToast('审核意见不能超过200字')
        return
      }
    }
    try {
      const API = type === 'pass' ? taskReviewPass : taskReviewReject
      const {code} = await API({
        taskId: props?.taskInfo.id,
        dateCode: props?.taskInfo.dateCode,
        userId: globalData.value?.shop?.staffId,
        remark: remark.value
      })
      if (code === 200) {
        emit('update', props?.taskInfo.id, props?.taskInfo.dateCode)
      }
    } catch (e) {
      console.error('任务审核详情 error', e)
    }
  }
</script>

<style scoped lang="scss">
  .review-opinions {
    padding: 40rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    margin-bottom: v-bind(margin_bottom);

    .title {
      color: #1f2428;
      font-size: 30rpx;
      font-weight: bold;
      line-height: 30rpx;
    }

    :deep(.custom-textarea) {
      .textarea {
        height: 200rpx;
      }
    }

    .t2 {
      color: #1f2428ff;
      font-size: 32rpx;
      line-height: 32rpx;
      text-align: center;
      margin-bottom: 40rpx;
    }

    .row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 32rpx;

      .label {
        opacity: 0.7;
        color: rgba(31, 36, 40, 0.7);
        font-size: 28rpx;
        line-height: 28rpx;
        flex-shrink: 0;
      }

      .value {
        color: rgb(31, 36, 40);
        font-size: 28rpx;
        font-weight: bold;
        line-height: 28rpx;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .pass {
      color: #43BF83 !important;
    }

    .rejected {
      color: $uni-text-color !important;
    }
  }
</style>