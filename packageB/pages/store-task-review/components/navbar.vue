<template>
  <commonBgVue height="560"/>
  <u-navbar
    placeholder title="门店任务审核" bg-color="transparent" leftIconColor="#FFF"
    titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" @leftClick="customBack">
    <template #left>
      <u-icon name="arrow-left" size="20" color="#FFF"></u-icon>
    </template>
  </u-navbar>
</template>

<script setup lang="ts">
  import { useCommon } from "@/hooks/useGlobalData";
  import { ComponentInternalInstance, getCurrentInstance } from "vue";
  import commonBgVue from '../../../components/common-bg/common-bg.vue';

  const {customBack} = useCommon(getCurrentInstance() as ComponentInternalInstance)
</script>

<style scoped lang="scss">
</style>