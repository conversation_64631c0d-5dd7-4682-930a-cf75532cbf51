<template>
  <view v-if="fillInfo?.name" class="title" :class="{'required' : fillInfo?.required}">{{ fillInfo?.name }}</view>
  <view v-if="fillInfo?.remark" class="remark">{{ fillInfo?.remark }}</view>
  <view v-if="fillInfo?.context" class="content">{{ fillInfo?.context }}</view>
</template>

<script setup lang="ts">
  import type { FillInfo } from "../type";

  withDefaults(
    defineProps<{
      fillInfo: Partial<FillInfo>
    }>(),
    {
      fillInfo: () => ({})
    }
  )
</script>

<style scoped lang="scss">
  @import '../store-task-review.scss';
</style>