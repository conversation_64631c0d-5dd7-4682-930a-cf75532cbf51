<template>
  <view v-if="fillInfo?.name" class="title" :class="{'required' : fillInfo?.required}">{{ fillInfo?.name }}</view>
  <view class="remark" style="margin-bottom: 32rpx;">{{ fillInfo?.remark }}</view>
  <view class="content">
    <radio-group class="c-radio-group fl">
      <label class="c-radio_label" v-for="(item, index) in fillInfo?.options" :key="index">
        <view class="c-radio fr">
          <radio :value="item.option" :checked="item.checked" :disabled="true"/>
          <view class="label">{{ item.option }}</view>
        </view>
      </label>
    </radio-group>
  </view>
</template>

<script setup lang="ts">
  import type { FillInfo } from "../type";

  withDefaults(
    defineProps<{
      fillInfo: Partial<FillInfo>
    }>(),
    {
      fillInfo: () => ({})
    }
  )

  defineOptions({
    options: {
      styleIsolation: 'shared'
    }
  })

  const radioGroup = [
    {label: '选项1', value: 1},
    {label: '选项2', value: 2},
    {label: '选项3', value: 3},
  ]
</script>

<style scoped lang="scss">
  @import '../store-task-review.scss';
</style>