<template>
  <view class="title" :class="{'required' : fillInfo?.required}">{{ fillInfo?.name }}({{ numberOfUploads
    }}/{{ fillInfo?.count }})
  </view>
  <view v-if="fillInfo?.remark" class="remark" style="margin-bottom: 20rpx;">{{ fillInfo?.remark }}</view>
  <view class="content">
    <view v-if="fillInfo?.demos && fillInfo?.demos.length > 0" style="margin-bottom: 24rpx;">
      <text>查看示例图：</text>
      <text class="href" v-for="(item,index) in fillInfo?.demos" :key="index" @tap="previewImage(item)">示例图
        {{ index + 1 }}
      </text>
    </view>
    <view class="pics" v-if="numberOfUploads > 0">
      <view
        v-for="(item,index) in fillInfo?.pics" :key="index" class="fl" style="align-items: center;">
        <view v-if="item?.name" class="name">{{ item.name }}</view>
        <image v-if="item?.url" class="img" :src="item.url" @tap="previewImage(item.url)"></image>
      </view>
    </view>
  </view>
  <view class="photo-remark" v-if="stepItem?.remark">
    <view class="photo-remark-tip">无法按需上传照片，原因如下：</view>
    <view class="photo-remark-content">
      {{ stepItem?.remark }}
    </view>
  </view>
</template>

<script setup lang="ts">
  import { FillInfo, TaskStep } from "../type";
  import { computed } from "vue";

  const props = withDefaults(
    defineProps<{
      fillInfo: Partial<FillInfo>
      stepItem: Partial<TaskStep>
    }>(),
    {
      fillInfo: () => ({}),
      stepItem: () => ({})
    }
  )

  const previewImage = (url: string) => {
    uni.previewImage({
      urls: [url]
    })
  }

  const numberOfUploads = computed(() => {
    return props?.fillInfo?.pics?.filter(pic => pic.url).length || 0;
  })
</script>

<style scoped lang="scss">
  @import '../store-task-review.scss';

  .href {
    margin-right: 32rpx;

    &:last-child {
      margin-right: 0;
    }
  }

  .photo-remark {
    &-tip {
      color: #666666;
      font-size: 24rpx;
      line-height: 24rpx;
      margin: 28rpx 0 24rpx;
      word-break: break-all;
    }

    &-content {
      border-radius: 20rpx;
      background: #f0f0f099;
      padding: 28rpx;
      word-break: break-all;
      font-size: 24rpx;
      line-height: 32rpx;
      color: #222222;
    }
  }
</style>