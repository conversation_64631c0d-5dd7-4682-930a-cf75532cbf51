<template>
  <view v-if="fillInfo?.name" class="title" :class="{'required' : fillInfo?.required}">{{ fillInfo?.name }}</view>
  <view class="remark" style="margin-bottom: 32rpx;">{{ fillInfo?.remark }}</view>
  <view class="content">
    <view class="radio-group">
      <up-checkbox-group>
        <up-checkbox
          v-for="(item,index) in fillInfo?.options" :key="index" :label="item.option" :checked="item.checked"
          :name="item.option" activeColor="#C35943" inactiveColor="#1f2428" labelSize="26rpx" labelColor="#1f2428"
          disabled />
      </up-checkbox-group>
    </view>
  </view>
</template>

<script setup lang="ts">
  import type { FillInfo } from "../type";
  import { ref } from "vue";

  withDefaults(
    defineProps<{
      fillInfo: Partial<FillInfo>
    }>(),
    {
      fillInfo: () => ({})
    }
  )

  defineOptions({
    options: {
      styleIsolation: 'shared'
    }
  })

</script>

<style scoped lang="scss">
  @import '../store-task-review.scss';

  :deep(.radio-group) {
    .u-checkbox__icon-wrap--disabled {
      //background-color: $uni-text-color !important;
      //border-color: $uni-text-color !important;
      //
      //.u-icon__icon {
      //  color: #FFFFFF !important;
      //}
      background: transparent !important;
    }

    .u-checkbox__icon-wrap--disabled--checked {
      background: $uni-text-color !important;

      .u-icon__icon {
        color: #FFFFFF !important;
      }
    }

    .u-checkbox__icon-wrap {
      border-color: $uni-text-color !important;
    }
  }
</style>