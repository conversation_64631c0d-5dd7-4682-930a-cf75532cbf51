<template>
  <view class="block">
    <view class="taskId fr">任务ID：{{ data?.planInfo?.id }}</view>
    <view class="title">{{ data?.planInfo?.title }}</view>
    <view class="desc">
      {{ data?.planInfo?.description }}
    </view>
    <view class="info">
      <view class="fr" style="margin-bottom: 16rpx;">
        <view class="label">起止时间：</view>
        <view class="value">
          {{ data?.taskInfo?.startTime }} -
          {{ data?.taskInfo?.endTime }}
        </view>
      </view>
      <view class="fr">
        <view class="fr">
          <view class="label">来源：</view>
          <view class="value">{{ TASK_SOURCE[data?.planInfo?.source] }}</view>
        </view>
        <view class="fr" style="margin-left: 48rpx;">
          <view class="label">类型：</view>
          <view class="value">{{ TASK_TYPE[data?.planInfo?.type] }}</view>
        </view>
      </view>
    </view>
    <view class="line" v-if="data?.rewardList && data?.rewardList[0]?.rewardType === 1 && data?.rewardList[0]?.rewardValue || timeShow"></view>
    <view class="fr experiencePoints">
      <view class="fr flex" v-if="data?.rewardList && data?.rewardList[0]?.rewardType === 1 && data?.rewardList[0]?.rewardValue">
        <image
          class="icon"
          src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-28/1745829172630.png"></image>
        <text class="text green">
          <text class="DIN-Bold green">{{ data?.rewardList[0]?.rewardValue }}</text>
          成长值
        </text>
      </view>
      <view class="fr flex" v-if="timeShow">
        <image
          class="icon"
          src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-28/1745829195651.png"></image>
        <text class="DIN-Bold text red">{{ remainingTime }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { TaskResponse } from "../type";
  import { TASK_SOURCE, TASK_TYPE } from "../enmu";
  import { computed, onMounted, onUnmounted, ref, watch } from "vue";
  import { getTimeRemaining, isTimeWithinHours } from "@/utils/util";
  import dayjs from "dayjs";

  const props = withDefaults(
    defineProps<{
      data: Partial<TaskResponse>
    }>(),
    {
      data: () => ({})
    }
  )

  watch(
    () => props.data,
    (newValue) => {
      if (Object.keys(newValue).length > 0) {
        if (timer) {
          clearInterval(timer);
        }
        updateCountdown(); // 立即执行一次
        timer = setInterval(updateCountdown, 1000); // 每秒更新
      }
    }
  )

  const timeShow = computed(() => {
    if (Object.keys(props.data).length <= 0) {
      return false
    } else {
      return (props.data?.taskInfo?.publishStatus === 2 || props.data?.taskInfo?.publishStatus === 3 || props.data?.taskInfo?.publishStatus === 4) && isTimeWithinHours(props.data?.taskInfo?.endTime, 4)
    }
  })


  const remainingTime = ref('00:00:00');
  let timer = null;

  // 格式化时间为 HH:MM:SS
  const formatTime = (hours, minutes, seconds) => {
    const pad = num => num.toString().padStart(2, '0');
    return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
  };

  // 更新倒计时
  const updateCountdown = () => {
    try {
      const now = new Date();
      const result = getTimeRemaining(now, props.data?.taskInfo?.endTime, 'object');

      // 如果是过去时间，可以显示00:00:00或者处理其他逻辑
      if (result.milliseconds <= 0) {
        remainingTime.value = '00:00:00';
        clearInterval(timer);
        return;
      }

      remainingTime.value = formatTime(
        result.timeParts.hours,
        result.timeParts.minutes,
        result.timeParts.seconds
      );
    } catch (error) {
      console.error('倒计时错误:', error);
      clearInterval(timer);
    }
  };

  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (timer) {
      clearInterval(timer);
    }
  });
</script>

<style scoped lang="scss">
  .block {
    width: 100%;
    border-radius: 20rpx;
    background: linear-gradient(180deg, rgba(247, 94, 59, 0.1) 0%, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 1) 100%);
    padding: 60rpx 40rpx 40rpx;
    position: relative;

    .taskId {
      position: absolute;
      top: 0;
      right: 0;
      width: 304rpx;
      height: 38rpx;
      border-radius: 0 20rpx 0 20rpx;
      background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
      color: #ffffff;
      font-size: 22rpx;
      line-height: 22rpx;
      justify-content: center;
    }

    .title {
      color: #1f2428;
      font-size: 32rpx;
      line-height: 32rpx;
    }

    .desc {
      margin-top: 24rpx;
      color: #1f2428;
      font-size: 26rpx;
      line-height: 42rpx;
      margin-bottom: 32rpx;
    }

    .info {
      .label {
        color: #999999;
        font-size: 24rpx;
        line-height: 24rpx;
      }

      .value {
        color: #1f2428;
        font-size: 24rpx;
        line-height: 24rpx;
      }
    }

    .line {
      margin: 40rpx 0;
      height: 2rpx;
      background: #f2f2f2;
    }

    .experiencePoints {
      .flex {
        flex: 1;
      }

      .icon {
        width: 40rpx;
        height: 40rpx;
        flex-shrink: 0;
        margin-right: 8rpx;
      }

      .text {
        font-size: 20rpx;
        line-height: 20rpx;
      }

      .DIN-Bold {
        font-size: 24rpx;
        font-weight: 700;
        line-height: 24rpx;
      }

      .green {
        color: #56cc93;
      }

      .red {
        color: $uni-text-color;
      }
    }
  }
</style>