<template>
  <view class="container" v-if="Object.keys(data).length > 0">
    <template v-if="data?.stepList?.length > 1">
      <step-component :current-step="step" :step="data?.stepList?.length" />
    </template>

    <template>
      <view class="block" v-if="data?.stepList[step - 1]?.name || data?.stepList[step - 1]?.description">
        <title-component :data="data?.stepList[step - 1]" />
      </view>
      <!--填报资料-->
      <template v-if="data?.stepList[step - 1]?.completionCriteria === 1">
        <view class="block" v-for="(item,index) in data?.stepList[step - 1]?.fillInfoList" :key="index">
          <photo-component :fill-info="item" :step-item="data?.stepList[index]" v-if="item.label === 1" />
          <single-line-text-component :fill-info="item" v-if="item.label === 2" />
          <multi-line-text-component :fill-info="item" v-if="item.label === 3" />
          <radio-component :fill-info="item" v-if="item.label === 4" />
          <checkbox-component :fill-info="item" v-if="item.label === 5" />
        </view>
      </template>
      <!--页面跳转-->
      <template v-if="data?.stepList[step - 1]?.completionCriteria === 2">
        <view class="block">
          <view class="check">用户已点击查看</view>
        </view>
      </template>
    </template>

    <view v-if="data?.stepList?.length > 1" class="fr" style="justify-content: flex-end">
      <view v-if="step > 1" class="custom-btn" @click="step--">上一步</view>
      <view
        v-if="step < data?.stepList?.length" class="custom-btn" :class="{'custom-btn-primary':step > 1}"
        @click="step++">下一步
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import PhotoComponent from "./photo-component.vue";
  import SingleLineTextComponent from "./single-line-text-component.vue";
  import MultiLineTextComponent from "./multi-line-text-component.vue";
  import RadioComponent from "./radio-component.vue";
  import CheckboxComponent from "./checkbox-component.vue";
  import StepComponent from "./step-component.vue";
  import TitleComponent from "./title-component.vue";
  import { TaskResponse } from "../type";

  const props = withDefaults(
    defineProps<{
      data: Partial<TaskResponse>
    }>(),
    {
      data: () => ({})
    }
  )

  const step = ref(1)
</script>

<style scoped lang="scss">
  .container {
    padding: 40rpx;
    border-radius: 20rpx;
    background: #ffffff;

    .block {
      margin-bottom: 60rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .check {
        color: $uni-text-color;
        font-size: 24rpx;
        line-height: 24rpx;
      }
    }
  }

  .custom-btn {
    width: 170rpx !important;
    padding: 20rpx 0 !important;
    font-size: 22rpx;
    line-height: 22rpx;
  }
</style>