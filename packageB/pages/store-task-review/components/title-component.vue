<template>
  <view v-if="data.name" class="title">{{ data.name }}</view>
  <view v-if="data.description" class="remark">{{ data.description }}</view>
</template>

<script setup lang="ts">
  import type { TaskStep } from "../type";

  withDefaults(
    defineProps<{
      data: Partial<TaskStep>
    }>(),
    {
      data: () => ({})
    }
  )
</script>

<style scoped lang="scss">
  @import "../store-task-review";
</style>