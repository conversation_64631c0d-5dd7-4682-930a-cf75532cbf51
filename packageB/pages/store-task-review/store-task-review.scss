.required::before {
  content: '*';
  color: $uni-text-color;
  margin-right: 8rpx;
}

.title {
  color: #1f2428;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 30rpx;
  margin-bottom: 28rpx;
}

.remark {
  color: #666666;
  font-size: 24rpx;
  line-height: 36rpx;
  margin-bottom: 20rpx;
}

.content {
  color: #1f2428;
  font-size: 24rpx;
  line-height: 40rpx;

  .href {
    color: #1e61fc;
    font-size: 24rpx;
    line-height: 24rpx;
    text-decoration: underline;
  }

  .pics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20rpx;
    margin-bottom: 24rpx;

    .name {
      color: #222222;
      font-size: 22rpx;
      line-height: 22rpx;
      margin-bottom: 16rpx;
    }

    .img {
      width: 140rpx;
      height: 140rpx;
      border-radius: 20rpx;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      flex-shrink: 0;
    }
  }
}


:deep() {
  .c-radio-group, .c-radio {
    display: flex;
    margin-bottom: 32rpx;
  }

  .c-radio_label {
    .c-radio {
      .label {
        color: #1f2428;
        font-size: 26rpx;
        line-height: 26rpx;
      }
    }
  }

  radio .wx-radio-input {
    border-radius: 50%;
    width: 28rpx;
    height: 28rpx;
    box-sizing: border-box;
    border: 2rpx #C35943 solid;
  }

  radio .wx-radio-input.wx-radio-input-checked {
    background-color: white !important;
    border: none !important;
  }

  radio .wx-radio-input.wx-radio-input-disabled {
    background-color: white !important;
  }

  radio .wx-radio-input.wx-radio-input-checked::before {
    border-radius: 50%;
    text-align: center;
    color: #FFFFFF;
    width: 28rpx;
    height: 28rpx;
    font-size: 20rpx;
    line-height: 28rpx;
    background: #C35943;
    box-sizing: border-box;
    transform: translate(-50%, -50%) scale(1);
    -webkit-transform: translate(-50%, -50%) scale(1);
  }
}

:deep() {
  .radio-group {
    .u-checkbox-group {
      flex-direction: column !important;
    }

    .u-checkbox {
      margin-bottom: 22rpx;
    }

    .u-checkbox__icon-wrap--square {
      width: 28rpx !important;
      height: 28rpx !important;
      border-radius: 8rpx !important;
    }
  }
}