<template>
  <view class="fl">
    <navbar />
    <view class="bg">
      <task-info :data="taskInfo" />
      <view style="margin-top: 20rpx;">
        <task-description-step :data="taskInfo" />
      </view>
      <view style="margin-top: 20rpx;">
        <review-opinions :task-info="taskInfo?.taskInfo" @update="toTaskReviewDetail" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { onLoad } from '@dcloudio/uni-app'
  import { useCommon } from "@/hooks/useGlobalData";
  import { ComponentInternalInstance, getCurrentInstance, ref } from "vue";
  import Navbar from "./components/navbar.vue";
  import TaskInfo from "./components/task-info.vue";
  import ReviewOpinions from "./components/review-opinions.vue";
  import TaskDescriptionStep from "./components/task-description-step.vue";
  import { taskReviewDetail } from "@/packageB/api";

  const {globalData, route_to_view, customBack} = useCommon(getCurrentInstance() as ComponentInternalInstance)
  const taskInfo = ref()

  onLoad((options: any) => {
    toTaskReviewDetail(options.taskId, options.dateCode)
  })

  // 任务审核详情
  const toTaskReviewDetail = async (taskId: string, dateCode: string) => {
    try {
      const {code, data} = await taskReviewDetail({
        taskId,
        dateCode
      })
      if (code === 200 && data) {
        taskInfo.value = data
      }
    } catch (e) {
      console.error('任务审核详情 error', e)
    }
  }

</script>


<style scoped lang="scss">
  .fl {
    overflow: hidden;
    width: 100%;
    height: 100%;
  }

  .bg {
    background: #efefef;
    margin-top: 20rpx;
    padding: 20rpx;
    flex: 1;
    overflow: auto;
  }
</style>
