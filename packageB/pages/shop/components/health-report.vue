<template>
	<view class="fl block">
		<view class="fr block-header">
			<text class="title">健康指数报告</text>
		</view>

		<view v-if="data?.data" class="fl block-body">
			<view class="fr canvas-wrap">
				<view class="fl">
					<text class="type">ADR</text>
					<text class="fr score">分数：<text class="num">{{ data.data?.adrPoint }}分</text></text>
				</view>
				<view class="fl">
					<text class="type">CRS直销</text>
					<text class="fr score">分数：<text class="num">{{ data.data?.directCrsPoint }}分</text></text>
				</view>
				<view class="fl">
					<text class="type">OCC</text>
					<text class="fr score">分数：<text class="num">{{ data.data?.occPoint }}分</text></text>
				</view>
				<view class="fl">
					<text class="type">CRS分销</text>
					<text class="fr score">分数：<text class="num">{{ data.data?.distributeCrsPoint }}分</text></text>
				</view>
				<view class="fl">
					<text class="type">售卡数</text>
					<text class="fr score">分数：<text class="num">{{ data.data?.cardSoldPoint }}分</text></text>
				</view>
				<view class="chart" :style="{display: show ? 'block' : 'none'}">
					<qiun-data-charts type="radar" :canvas2d="true" canvasId="JvlNlKhzClXbpuzblPLTqIZBWmjAVJuD" :opts="opts"
						:chart-data="chartData"></qiun-data-charts>
				</view>
			</view>
			<view class="fr legend">
				<text class="fr store">我的门店</text>
				<text class="fr store current-period">本期头部门店</text>
			</view>
			<view class="fl result">
				<view class="fl">
					<view class="fr title">
						<view class="icon"></view>
						<view class="text">诊断结论</view>
					</view>
					<view class="desc">
						{{handleDate()}}经营健康指数为<text class="theme-primary">{{ data.data?.totalPoint || 0}}分</text>，门店综合排名<text
							class="theme-primary">第{{ data.data?.generalRank || 0}}</text>
					</view>
				</view>
				<view class="fl">
					<view class="fr title">
						<view class="icon"></view>
						<view class="text">诊断建议</view>
					</view>
					<view class="desc">
						<text class="theme-primary">{{ source }}</text>是健康经营指数的主要影响因素，请点击查看完整报告
					</view>
				</view>
				<text class="fr btn"
					@tap="route_to_view(`/packageB/pages/business-diagnosis/business-diagnosis?shopId=${shopId}`)">查看完整报告</text>
			</view>
		</view>
		<view v-else class="fl block-body-empty">
			<emptyVue text="上周暂无评分数据" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, getCurrentInstance, onMounted, ref, unref, computed } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData'
	import { dataType, successCallbackResult } from '@/types/index.d';
	import { formatDate } from '@/utils/util';
	import emptyVue from '@/components/empty/empty.vue'
	import { queryShopHealthIndex } from '../../../api';

	const instance : ComponentInternalInstance = getCurrentInstance(), { route_to_view, globalData } = useCommon(instance),
		data = ref<dataType>({}), show = ref(true),
		opts = ref({}),
		chartData = ref({})
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL

	const props = defineProps({
		shopId: {
			type: String,
			default: ''
		}
	})

	const getDate = () => {
		const date = new Date()
		const h = date.getHours()
		const week = date.getDay()
		const day = date.getDate()
		let sv = 0, ev = 0, diff = week == 0 ? 7 : week
		if (week == 1) {
			if (h >= 9) {
				ev = day - 2
				sv = day - 8
			} else {
				ev = day - 9
				sv = day - 15
			}
		} else {
			sv = day - diff - 7
			ev = day - diff - 1
		}
		const startDate = formatDate(new Date(new Date().setDate(sv)))
		const endDate = formatDate(new Date(new Date().setDate(ev)))
		return {
			startDate,
			endDate
		}
	}
	const handleDate = () => {
		const { startDate, endDate } = getDate()
		return startDate.substring(5) + '~' + endDate.substring(5)
	}

	const zn = {
		adrPoint: {
			way: 'ADR提升攻略',
			name: 'ADR'
		},
		cardSoldPoint: {
			way: '售卡提升攻略',
			name: '售卡数'
		},
		directCrsPoint: {
			way: 'CRS直销提升攻略',
			name: 'CRS直销'
		},
		distributeCrsPoint: {
			way: 'CRS分销提升攻略',
			name: 'CRS分销'
		},
		occPoint: {
			way: 'OCC提升攻略',
			name: 'OCC'
		}
	}
	const source = computed(() => {
		if (!data.value?.data) return
		const arr = ['adrPoint', 'cardSoldPoint', 'directCrsPoint', 'distributeCrsPoint', 'occPoint'].map(key => {
			return {
				name: zn[key].name,
				way: zn[key].way,
				value: Number(data.value.data[key] == '-' ? 0 : data.value.data[key])
			}
		})
		const minValue = arr.reduce((min, current) => {
			return min === undefined || current.value < min ? current.value : min;
		}, undefined);
		const secondMinValue = arr.reduce((min, current) => {
			if (current.value !== minValue) {
				return min === undefined || current.value < min ? current.value : min;
			}
			return min;
		}, undefined);
		const result = arr.filter(item => item.value === minValue || item.value === secondMinValue);
		// 根据需要对结果进行排序
		result.sort((a, b) => a.value - b.value);
		const res = result.slice(0, 2)
		return res.map(item => item.name)?.join('和')
	})

	const toQueryShopHealthIndex = async () => {
		try {
			const res : successCallbackResult = await queryShopHealthIndex({
				shopId: props.shopId,
				queryType: 1,
				...getDate()
			})
			console.log("res", res)
			data.value = res.data
		} catch (e) {
			console.log('健康指数接口报错', e)
		}
	}
	const setChart = () => {
		const { firstAdrPoint, firstDirectCrsPoint, firstOccPoint, firstDistributeCrsPoint, firstCardSoldPoint, adrPointGap, directCrsPointGap, occPointGap, distributeCrsPointGap, cardSoldPointGap, adrPoint, occPoint, cardSoldPoint, distributeCrsPoint, directCrsPoint } = unref(data)?.data || {}
		const val = uni.upx2px(5)
		opts.value = {
			fontSize: 12,
			padding: [val, val, val, val],
			dataLabel: false,
			enableScroll: false,
			legend: {
				show: false,
			},
			extra: {
				radar: {
					gridType: "circle",
					gridColor: '#F0F1F5',
					labelPointShow: true,
					labelPointRadius: 1,
					labelPointColor: '#fff',
					opacity: 0.04,
					max: Math.max.apply(null, [adrPointGap, occPointGap, cardSoldPointGap, distributeCrsPointGap, directCrsPointGap, firstAdrPoint, firstOccPoint, firstCardSoldPoint, firstDistributeCrsPoint, firstDirectCrsPoint].map(Number).filter(value => !isNaN(value))),
					radius: uni.upx2px(400),
					borderWidth: 0,
					labelShow: false,
				}
			}
		}
		chartData.value = {
			categories: ["ADR", "OCC", "售卡数", "CRS分销", "CRS直销"],
			series: [
				{
					name: '本期头部门店',
					data: [firstAdrPoint, firstOccPoint, firstCardSoldPoint, firstDistributeCrsPoint, firstDirectCrsPoint],
					color: '#FF4D4D',
				},
				{
					name: '我的门店',
					data: [adrPoint == '-' ? 0 : adrPoint, occPoint == '-' ? 0 : occPoint, cardSoldPoint == '-' ? 0 : cardSoldPoint, distributeCrsPoint == '-' ? 0 : distributeCrsPoint, directCrsPoint == '-' ? 0 : directCrsPoint],
					color: '#43BF83'
				}
			],
		}
	}
	onMounted(async () => {
		await toQueryShopHealthIndex()
		if (data.value?.data) {
			setChart()
		}
	})
</script>

<style scoped lang="scss">
	.block {
		margin-bottom: 40rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&-header {
			justify-content: space-between;
			height: 92rpx;
			padding: 0 28rpx;

			.title {

				font-weight: bold;
			}

			.fr {
				font-size: 20rpx;
				color: #888;
			}

			.right-icon {
				margin-left: 9.11rpx;
			}
		}

		&-body {
			padding: 102rpx 40rpx 0;

			.canvas-wrap {
				position: relative;
				width: 292rpx;
				height: 292rpx;
				margin: 0 auto 94rpx;
				border-radius: 100%;

				.chart {
					width: 292rpx;
					height: 292rpx;
				}

				.fl {
					position: absolute;
					font-size: 24rpx;

					.type {
						margin-bottom: 4rpx;
						color: #1f2428;
						font-size: 24rpx;
						font-weight: bold;
						line-height: 24rpx;
					}

					.score {
						justify-content: center;
						color: #1f2428;
						font-size: 22rpx;
						line-height: 22rpx;

						.num {
							color: #ff4d4d;
							font-size: 24rpx;
							font-weight: 700;
							font-family: "Din Bold";
							line-height: 24rpx;
						}
					}

					&:nth-child(1) {
						top: -10rpx;
						left: 50%;
						transform: translate(-50%, -100%);
					}

					&:nth-child(2) {
						top: 50%;
						left: -10rpx;
						transform: translate(-100%, -100%);
					}

					&:nth-child(3) {
						top: 50%;
						right: -10rpx;
						transform: translate(100%, -100%);
					}

					&:nth-child(4) {
						top: 90%;
						left: 0;
						transform: translate(-50%, 0);
					}

					&:nth-child(5) {
						top: 90%;
						right: 0;
						transform: translate(50%, 0);
					}
				}


			}

			.legend {
				justify-content: center;
				margin-bottom: 48rpx;

				.store {

					font-size: 20rpx;
					color: #000;

					&::before {
						content: "";
						width: 16rpx;
						height: 16rpx;
						margin-right: 12rpx;
						background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
						border-radius: 100%;
					}

					&.current-period {
						margin-left: 48rpx;

						&::before {
							background: linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%);
						}
					}
				}
			}

			.result {
				padding: 20rpx 0 40rpx;
				border-radius: 12rpx;

				&>.fl {
					margin-bottom: 40rpx;

					.title {
						margin-bottom: 20rpx;
						font-weight: bold;
						color: #000;

						.icon {
							width: 60rpx;
							height: 60rpx;
							background-image: url('https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-28/1732780040621.png');
							background-repeat: no-repeat;
							background-size: 100% 100%;
							flex-shrink: 0;
						}

						.text {
							color: #1f2428;
							font-size: 28rpx;
							font-weight: bold;
							line-height: 28rpx;
							padding: 10rpx 32rpx 10rpx 68rpx;
							background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
							border-radius: 200rpx;
							margin-left: -52rpx;
						}

						.icon-result {
							width: 120.74rpx;
							height: 25.65rpx;
						}

						.icon-suggest {
							width: 112.11rpx;
							height: 23.92rpx;
						}
					}

					.desc {
						position: relative;
						padding: 26rpx 20rpx;
						font-size: 24rpx;
						line-height: 40rpx;
						color: #000;
						word-break: break-all;
						border-radius: 8rpx;
						background: linear-gradient(90deg, rgb(245, 245, 245) 0%, rgba(245, 245, 245, 0.5) 100%);
						margin-left: 40rpx;
					}
				}

				.btn {
					justify-content: center;
					height: 88rpx;
					margin-top: 20rpx;
					color: #ffffff;
					font-size: 28rpx;
					font-weight: bold;
					text-align: center;
					line-height: 28rpx;
					background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
					border-radius: 200rpx;
				}
			}
		}

		.block-body-empty {
			padding: 41.34rpx 0 40rpx;
			align-items: center;
		}

		.theme-primary {
			color: $uni-text-color;
			font-size: 24rpx;
			line-height: 24rpx;
		}

		.empty {
			width: 318rpx;
			height: 276.66rpx;
			margin-bottom: 60rpx;

			&-text {
				font-size: 26rpx;
				color: #777;
			}
		}
	}
</style>