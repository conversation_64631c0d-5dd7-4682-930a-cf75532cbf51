<template>
	<view class="shop fr">
		<image class="icon"
			src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733019856837.png"
			mode="aspectFill" lazy-load></image>
		<view class="shop-info">
			<view class="shop-name">{{ shopInfo.shopName }}</view>
			<view class="fr level">
				<!--<view class="shop-level fr">
					<image class="star"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733020006777.png"
						mode="aspectFill" lazy-load></image>
					<view>{{level}}</view>
				</view>-->
				<view class="shop-id">门店ID:{{ shopInfo.shopId }}</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	import { shopLevel } from '../../../api';
	const props = defineProps({
		shopInfo: {
			type: Object,
			default: () => { }
		}
	})

	watch(
		() => props.shopInfo,
		() => {
			getShopLevel()
		}
	)

	const level = ref('')

	const getShopLevel = async () => {
		try {
			const { data } = await shopLevel({ shopId: props.shopInfo.shopId })
			if (data) {
				level.value = data.shopLevel
			}
		} catch (e) {
			console.error('查询门店星级 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.shop {
		margin: 40rpx 0 40rpx 20rpx;

		.icon {
			width: 120rpx;
			height: 120rpx;
			flex-shrink: 0;
			margin-right: 28rpx;
		}

		&-info {

			.shop-name {
				color: #1f2428;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 36rpx;
				margin-bottom: 18rpx;
				word-break: break-all;
			}

			.level {
				background: #ffffff;
				border-radius: 200rpx;
				display: inline-flex;
			}

			.shop-level {
				color: #1f2428;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;
				background: linear-gradient(115.1deg, #ffdc5c 0%, #fcad0d 100%);
				border-radius: 200rpx;
				padding-right: 16rpx;


				.star {
					width: 38rpx;
					height: 38rpx;
					flex-shrink: 0;
					margin-right: 10rpx;
				}
			}

			.shop-id {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
				padding: 0 16rpx;
			}
		}
	}
</style>