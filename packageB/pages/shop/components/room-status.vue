<template>
	<view class="fl block">
		<view class="fr block-header" @tap="onTap">
			<text class="title">实时房态</text>
			<view class="fr block-header__date">
				<uni-icons type="arrowright" size="12" color="#999"></uni-icons>
			</view>
		</view>
		<view class="block-body">
			<view class="fr content">
				<view class="fl bg-1" @tap="onItemClick('rest')">
					<view class="icon icon-1"></view>
					<view class="fl">
						<text class="label">空闲数</text>
						<text class="DIN-Bold">{{ data.restRoomNum }}</text>
					</view>
				</view>
				<view class="fl bg-2" @tap="onItemClick('isIn')">
					<view class="icon icon-2"></view>
					<view class="fl">
						<text class="label">在住数</text>
						<text class="DIN-Bold">{{ data.stayingNum }}</text>
					</view>

				</view>
				<view class="fl bg-3" @tap="onItemClick('mayLeave')">
					<view class="icon icon-3"></view>
					<view class="fl">
						<text class="label">预离数</text>
						<text class="DIN-Bold">{{ data.leavingNum }}</text>
					</view>
				</view>
				<view class="fl bg-4" @tap="onItemClick('book')">
					<view class="icon icon-4"></view>
					<view class="fl">
						<text class="label">预抵数</text>
						<text class="DIN-Bold">{{ data.arrivingNum }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, getCurrentInstance, onMounted, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData'
	import { queryHomePageRoomStatus } from '../../../api'
	import { dataType, successCallbackResult } from '@/types/index.d';
	const { route_to_view, globalData, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance),
		data = ref<dataType>({})


	const props = defineProps({
		shopId: {
			type: String,
			default: ''
		}
	})

	const toQueryHomePageRoomStatus = async () => {
		try {
			const res : successCallbackResult = await queryHomePageRoomStatus({
				shopId: props.shopId
			})
			data.value = res.data
		} catch (e) {
			console.log('获取房态信息接口报错', e)
		}
	}

	const onTap = () => {
		route_to_view(`/packageB/pages/room-state/room-state?shopId=${props.shopId}`)
	}

	const onItemClick = (label : string) => {
		const url = `/packageB/pages/room-state/room-state?shopId=${props.shopId}&label=${label}`
		route_to_view(url)
	}

	onMounted(() => {
		toQueryHomePageRoomStatus()
	})
</script>

<style scoped lang="scss">
	$bgImg: (
		'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732593134512.png',
		'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732593177391.png',
		'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732593194785.png',
		'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732593206732.png',
	);
	// 定义一组无规律的渐变色
	$gradients: (
		linear-gradient(221.3deg, rgba(145, 186, 255, 0.05) 0%, rgba(20, 116, 250, 0.05) 100%),
		linear-gradient(45deg, rgba(255, 140, 26, 0.05) 0%, rgba(255, 207, 158, 0.05) 100%),
		linear-gradient(203.5deg, rgba(255, 178, 161, 0.05) 0%, rgba(247, 94, 59, 0.05) 100%),
		linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%),
	);

	.block {
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&-header {
			justify-content: space-between;
			height: 88rpx;
			padding: 0 40rpx;

			.title {
				font-weight: bold;
				font-size: 32rpx;
				line-height: 32rpx;
			}

			&__date {
				font-size: 22rpx;
				color: #888;

				&-text {
					margin-right: 8rpx;
				}
			}
		}

		&-body {
			padding: 14rpx 40rpx 0;

			.content {
				margin-bottom: 30rpx;

				@for $i from 1 through length($gradients) {
					.bg-#{$i} {
						background: nth($gradients, $i);
					}
				}

				&>.fl {
					flex: 1;
					align-items: center;
					padding: 34rpx 28rpx;
					margin-right: 20.68rpx;
					font-size: 22rpx;
					color: #666;
					border-radius: 12rpx;

					&:last-child {
						margin-right: 0;
					}

					.label {
						margin-bottom: 16rpx;
						font-size: 20rpx;
						line-height: 20rpx;
						color: #1f2428;
					}

					.DIN-Bold {
						font-size: 32rpx;
						color: #000;
						text-align: center;
					}
				}

				.icon {
					width: 60rpx;
					height: 60rpx;
					flex-shrink: 0;
					margin-bottom: 16rpx;
					background-size: 100% 100%;
					background-repeat: no-repeat;

					@for $i from 1 through 4 {
						&.icon-#{$i} {
							background-image: url(nth($bgImg, $i));
						}
					}

				}
			}
		}
	}
</style>