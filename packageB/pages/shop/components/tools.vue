<template>
	<view class="fl block" :class="{'ele-step': step}">
		<view class="fr block-header">
			<text class="title">常用工具</text>
		</view>
		<view class="block-body">
			<view class="fr content">
				<view class="fl" v-for="(item, index) in toolIconList" :key="index" @tap="onClick(item)">
					<view class="icon"
						:style="{height: item.height + 'rpx', width: item.width + 'rpx', 'background-image': 'url('+ item.imgUrl+')'}">
					</view>
					<text>{{ item.label }}</text>
				</view>
			</view>
		</view>
		<!-- <cGuideContentVue v-model="step" content="2.常用工具集合，报名营销活动，查
看日常任务，查看店长风云榜，查看
各渠道的点评分等" :current="2" :top="top" @complete="topInstance?.setStepValue()" /> -->
	</view>
	<!-- <cGuideMaskVue v-model="step" /> -->
</template>

<script setup lang="ts">
	import { dataType } from '@/types/index.d';
	import { useCommon } from '@/hooks/useGlobalData';
	import { ComponentInternalInstance, computed, getCurrentInstance, inject, unref } from 'vue';
	import cGuideContentVue from '@/components/c-guide/c-guide-content.vue';
	import cGuideMaskVue from '@/components/c-guide/c-guide-mask.vue';
	import { showModal } from '@/utils/util';
	import { toolIconList } from '../enums'
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL
	const { route_to_view, globalData, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance),
		topInstance : dataType = inject('topInstance'),
		step = computed(() => {
			return topInstance?.stepValue?.value == 2
		})
	const top = computed(() => {
		return globalData.value?.shop?.groupName == '业主' ? '440rpx' : '640rpx'
	})

	const props = defineProps({
		shopId: {
			type: String,
			default: ''
		}
	})

	const onClick = (data : dataType) => {
		switch (data.type) {
			case 'webview':
				route_to_view('/public/pages/webview/webview?url=' + webviewURL + data.url + '&shopId=' + props.shopId)
				break;
			case 'route':
				route_to_view(`${data.url}?shopId=${props.shopId}`)
				break;
			case 'modal':
				showModal({
					content: data.text,
					showCancel: false
				})
				break;
			default:
				break;
		}
	}
</script>

<style scoped lang="scss">
	.block {
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.ele-step {
			position: relative;
			z-index: 111;
			touch-action: none;

			.block-header,
			.block-body {
				pointer-events: none;
			}
		}

		&-header {
			justify-content: space-between;
			height: 92rpx;
			padding: 0 40rpx;

			.title {
				font-size: 32rpx;
				line-height: 32rpx;
				font-weight: bold;
			}
		}

		&-body {
			padding: 40rpx 0rpx 0;

			.content {
				flex-wrap: wrap;
				font-size: 22rpx;
				color: #1F2428;
				//justify-content: space-between;

				.fl {
					align-items: center;
          width: 25%;
					margin-bottom: 40rpx;
					// margin-right: 88rpx;
				}

				.hidden {
					display: none;
				}

				.icon {
					margin-bottom: 20rpx;
					background-size: 100% 100%;
					background-repeat: no-repeat;
				}
			}
		}
	}
</style>