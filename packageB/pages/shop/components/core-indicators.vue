<template>
	<view class="fl block">
		<view class="indicator-tab">
			<cSubsectionVue :list="coreIndicatorsTabs" v-model="coreIndicatorsIndex"></cSubsectionVue>
		</view>
		<view class="fr incicators real" style="margin-bottom: 20rpx;">
			<view class="fl">
				<view class="fr fr-title" @tap="showTips('OCC')">
					<text class="title">OCC</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<arcbarVue :value="data.occRate?.completionRate" width="120rpx" height="120rpx" centerWidth="46rpx"
					font-size="24rpx" tag-size="18rpx" />
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{data.occRate?.actual || '-'}}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.occRate?.lastActualRate || '-' }}%</text>
        <text class="tips" v-if="data.occRate?.actual < data.occRate?.lastActualRate && tabValue == coreIndicatorsTabs[0].value"
          @tap="toAi('如何提升OCC')">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr fr-title" @tap="showTips('ADR')">
					<text class="title">ADR</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<arcbarVue :value="data.adrData?.completionRate" width="120rpx" height="120rpx" centerWidth="46rpx"
					font-size="24rpx" tag-size="18rpx" />
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{data.adrData?.actual || '-'}}元</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.adrData?.lastActualData || '-'}}元</text>
        <text class="tips" v-if="data.adrData?.actual < data.adrData?.lastActualData && tabValue == coreIndicatorsTabs[0].value"
          @tap="toAi('如何提升ADR')">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr fr-title" @tap="showTips('RevPar')">
					<text class="title">RevPar</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<arcbarVue :value="data.revParData?.completionRate" width="120rpx" height="120rpx" centerWidth="46rpx"
					font-size="24rpx" tag-size="18rpx" />
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{data.revParData?.actual || '-'}}元</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.revParData?.lastActualData || '-' }}元</text>
			</view>
		</view>
		<view class="fr incicators real" style="margin-bottom: 20rpx;">
			<view class="fl">
				<view class="fr" @tap="showTips(tabValue == coreIndicatorsTabs[0].value ? '预计房费收入':'房费收入')">
					<text class="title">{{tabValue == coreIndicatorsTabs[0].value ? '预计房费收入':'房费收入' }}</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.roomFeeIncome?.actual || '-' }}元</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.roomFeeIncome?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.roomFeeIncome?.lastActualData || '-' }}元</text>
        <text
          v-if="data.roomFeeIncome?.actual < data.roomFeeIncome?.lastActualData && tabValue == coreIndicatorsTabs[0].value"
          @tap="toAi('如何提升房费收入')" class="theme-primary tips">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr" @tap="showTips('会员售卡数')">
					<text class="title">会员售卡数</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.memberCount?.actual || '-' }}张</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.memberCount?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.memberCount?.lastActualData || '-'}}张</text>
        <text v-if="data.memberCount?.actual < data.memberCount?.lastActualData && tabValue == coreIndicatorsTabs[0].value"
          @tap="toAi('如何提升售卡业绩')" class="theme-primary tips">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr" @tap="showTips('会员售卡金额')">
					<text class="title">会员售卡金额</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.memberAmount?.actual || '-' }}元</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.memberAmount?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.memberAmount?.lastActualData || '-' }}元</text>
			</view>
		</view>
		<view class="fr incicators real">
			<view class="fl">
				<view class="fr" @tap="showTips('CRS占比')">
					<text class="title">CRS占比</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.crsRate?.actual || '-' }}%</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.crsRate?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.crsRate?.lastActualRate || '-'}}%</text>
        <text v-if="data.crsRate?.actual < data.crsRate?.lastActualRate && tabValue == coreIndicatorsTabs[0].value" @tap="toAi('如何提升分销占比')"
          class="theme-primary tips">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr" @tap="showTips('直销占比')">
					<text class="title">直销占比</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.directSalesRate?.actual || '-' }}%</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.directSalesRate?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.directSalesRate?.lastActualRate || '-'}}%</text>
        <text v-if="data.directSalesRate?.actual < data.directSalesRate?.lastActualRate && tabValue == coreIndicatorsTabs[0].value"
          @tap="toAi('如何提升直销占比')" class="theme-primary tips">提升攻略</text>
			</view>
			<view class="fl">
				<view class="fr" @tap="showTips('会员占比')">
					<text class="title">会员占比</text>
					<image class="info"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
						mode="aspectFill"></image>
				</view>
				<view class="fr fr-actual">
					<text class="actual">实际：</text>
					<text class="">{{ data.memberRate?.actual || '-' }}%</text>
				</view>
				<view class="fr incicator-data">
					<text class="">完成率：</text>
					<text class="">{{ data.memberRate?.completionRate || '-' }}%</text>
				</view>
				<text class="desc">上周同期全天\n{{ data.memberRate?.lastActualRate || '-' }}%</text>
			</view>
		</view>
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts">
  import { ref, watch, unref, computed, onMounted, getCurrentInstance } from 'vue';
	import { dataType, successCallbackResult, tabItem } from '@/types/index.d';
	import { queryCoreIndicators } from '../../../api'
	import { formatDate, showModal } from '@/utils/util';
	import cSubsectionVue from '@/components/c-subsection/c-subsection.vue'
	import arcbarVue from '../../../components/arcbar/arcbar.vue';
	import cModal from '@/components/c-modal/index'
  import { useCommon } from "@/hooks/useGlobalData";

  const { route_to_view } = useCommon(getCurrentInstance())

	const coreIndicatorsTabs = [
		{
			name: '实时核心指标', value: formatDate(), map: {
        OCC: '出租率=(在住客房数+今日预抵数) / 该门店客房总数',
        ADR: '平均房价= (在住客房的当日房费+今日预抵订单的当日房费) / (在住客房数+今日预抵数)',
        RevPar: '每间可售房收入=OCC*ADR',
        'CRS占比': '今日在住+今日预抵订单中，(订单来源为CRS直销+分销数) / (在住客房数+今日预抵数)',
        '直销占比': '今日在住+今日预抵订单中，(订单来源为直销数) / (在住客房数+今日预抵数)',
        '会员占比': '今日在住+今日预抵订单中，(订单来源为会员身份的订单数) / (在住客房数+今日预抵数)',
        '会员售卡金额': '今日售卡总金额，指门店线上分销的会员卡金额',
        '会员售卡数': '今日售卡总数，指门店线上分销的会员卡数量',
        '预计房费收入': '今日成交的订单收入，包括预订单和线下直接入住的订单'
			}

		},
		{
			name: '昨日核心指标', value: (() => {
				const date = new Date()
				const newDate = date.setDate(date.getDate() - 1)
				return formatDate(new Date(newDate))
			})(), map: {
        OCC: '出租率=实际售出客房数量/可售房数量',
        ADR: '平均房价=客房当日费用/实际售出客房数量',
        RevPar: '每间可售房收入=OCC*ADR',
        'CRS占比': 'CRS(直销+分销)间夜/当日总出租间夜',
        '直销占比': '小程序、App等直销间夜/当日总出租间夜',
        '会员占比': '当日个人会员间夜(包括直销和线下会员)/当日总出租间夜',
        '会员售卡金额': '昨日售卡总金额，指门店线上分销的会员卡金额',
        '会员售卡数': '昨日售卡总数，指门店线上分销的会员卡数量',
        '房费收入': '当日房费收入'
			}

		}
	]

	const props = defineProps({
		shopId: {
			type: String,
			default: ''
		}
	})

	const coreIndicatorsIndex = ref(0),
		data = ref<dataType>({})
	const tabValue = computed(() => coreIndicatorsTabs[coreIndicatorsIndex.value].value)
	
	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')

	watch(() => tabValue.value, (v) => {
		toQueryCoreIndicators()
	})

	onMounted(() => {
		toQueryCoreIndicators()
	})

	/**
		* @description: 跳转ai发送对话
		* @param {*} text
		* @param {*} sv
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const toAi = (text : string) : void => {
    route_to_view(`/ai/pages/index/index?query=${encodeURIComponent(text)}`);
  }

	/**
		* @description: 小图标提示
		* @param {*} type
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const showTips = (type : string) : void => {
		const map = coreIndicatorsTabs.find(item => item.value == unref(tabValue))?.map
		// showModal({
		// 	content: map[type],
		// 	showCancel: false
		// })
		modalShow.value = true
		modalTitle.value = type
		modalContent.value = map[type]
	}

	/**
		* @description: 获取指标数据
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const toQueryCoreIndicators = async () : Promise<void> => {
		console.log(props.shopId, 'props.shopId1');
		try {
			const res : successCallbackResult = await queryCoreIndicators({
				shopId: props.shopId,
				queryDate: unref(tabValue)
			})
			data.value = res.data || {}
		} catch (e) {
			console.log('获取门店实时指标接口报错', e)
		}
	}
</script>

<style scoped lang="scss">
	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		.indicator-tab {
			margin-bottom: 40rpx;
		}

		.real {
			.fr-actual {
				margin-top: 20rpx;
			}

			.fr-budget,
			.fr-actual {
				line-height: 24rpx;
			}

			.fr-actual {
				margin-bottom: 16rpx;
			}

			.fr-budget {
				margin-bottom: 16rpx;
			}

			.fr-budget+.desc {
				// margin-bottom: 12rpx;
			}

			.actual,
			.budget,
			.actual+.theme-primary,
			.budget+.theme-primary {
				font-size: 22rpx;
			}

			.actual,
			.budget {
				flex-shrink: 0;
				// font-weight: bold;
			}

			.actual {
				color: #1F2428;
			}

			.actual+text {
				color: #1F2428;
				font-size: 22rpx;
			}

			.budget+text {
				color: #1F2428;
				font-size: 22rpx;
			}

			.desc+.tips {
				font-size: 20rpx;
				text-decoration: underline;
				color: #1e61fc;
				text-align: center;
				margin-top: 20rpx;
			}

		}

		.incicators {
			overflow: hidden;
			// margin: 0 28rpx;
			align-items: stretch;


			:deep(.info-title) {
				font-size: 24rpx;
			}

			&>.fl {
				width: calc(100% / 3);
				padding: 28rpx 16rpx;
				margin-right: 20rpx;
				border: 2rpx solid #f2f2f2;
				border-radius: 20rpx;

				&:nth-child(3n+3) {
					margin-right: 0;
				}
			}

			.fr-title {
				margin-bottom: 20rpx;
			}

			.fr {
				color: #000;

				.title {
					margin-right: 8rpx;
					font-size: 24rpx;
					font-weight: bold;
					line-height: 24rpx;
					color: #000;
					white-space: nowrap;
				}
			}

			.info {
				width: 22rpx;
				height: 22rpx;
				flex-shrink: 0;
			}

			.fee {
				margin: 28rpx 0 16rpx;
			}

			.desc {
				color: #1f2428b3;
				font-size: 20rpx;
				text-align: center;
				line-height: 28rpx;
				border-radius: 10rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				padding: 8rpx 20rpx;
			}

			.incicator-data {
				margin-bottom: 20rpx;
				font-size: 22rpx;
				line-height: 22rpx;

				& text:last-child {
					color: #ff4d4d;
					font-size: 24rpx;
					font-weight: 700;
					font-family: "Din Bold";
					line-height: 24rpx;
				}
			}
		}
	}
</style>