<template>
  <view class="fl container">
    <view class="shop-section">
      <shopInfoVue :shopInfo="shopInfo" />
    </view>
    <view class="position-offset">
      <coreIndicatorsVue :shopId="shopId" />
      <roomStatusVue :shopId="shopId" />
      <toolsVue :shopId="shopId" />
      <!--<healthReportVue :shopId="shopId" />-->
    </view>
  </view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
  import {onLoad} from '@dcloudio/uni-app'
  import coreIndicatorsVue from './components/core-indicators.vue';
  import roomStatusVue from './components/room-status.vue';
  import toolsVue from './components/tools.vue'
  import healthReportVue from './components/health-report.vue';
  import shopInfoVue from './components/shop-info.vue';
  import {ref} from 'vue';

  const shopId = ref<string>(''),
    shopInfo = ref({})
  onLoad((options) => {
    shopId.value = options.shopId
    if (options.shop) {
      try {
        const shop = JSON.parse(options.shop)
        shopInfo.value = shop
        uni.setNavigationBarTitle({
          title: shop.shopName
        })
      } catch (e) {
        console.error('解析JSON异常', e)
      }
    }
  })
</script>

<style scoped lang="scss">
  .shop-section {
    height: 392rpx;
    background: linear-gradient(180deg, #ffffff 0%, #fafafa00 100%);
    padding: 0 40rpx;
  }

  .position-offset {
    margin-top: -192rpx;
    padding: 0 20rpx;
  }
</style>