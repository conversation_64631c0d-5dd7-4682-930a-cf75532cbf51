import { tabItem } from "@/types/index.d"
type typeProp = 'webview' | 'route' | 'modal'
type roleType = 'store' | 'boss'
export type iconType = Pick<tabItem, 'value'> & {
	label : string | string[],
	url ?: string,
	url1 ?: string,
	type ?: typeProp,
	role ?: roleType,
	width ?: string | number,
	height ?: string | number,
	imgUrl ?: string,
	text ?: string
}
export const toolIconList : readonly iconType[] = [
	// {
	// 	label: '经营诊断',
	// 	value: 0,
	// 	imgUrl: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732600631721.png',
	// 	width: 90,
	// 	height: 90,
	// 	type: 'route',
	// 	url: '/packageB/pages/business-diagnosis/business-diagnosis'
	// },
	{
		label: '一张报表',
		value: 1,
		imgUrl: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732600651281.png',
		width: 90,
		height: 90,
		url: '/packageB/pages/sales-operation-report/sales-operation-report',
		type: 'route',
	},
	{
		label: '会员发展',
		value: 2,
		imgUrl: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732608670231.png',
		width: 90,
		height: 90,
		url: '/packageB/pages/member-development/member-development',
		type: 'route',
	},
	{
		label: '点评回顾',
		value: 3,
		imgUrl: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732608688389.png',
		width: 90,
		height: 90,
		type: 'webview',
		url: '/pages/comment/comment'
	},
]

export const reportIconList : readonly iconType[] = [
	{
		label: '业绩日报',
		value: 0,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-1.png',
		type: 'route',
		url: '/report/pages/performance-report/performance-report'
	},
	{
		label: ['月度经营', '分析报告'],
		value: 1,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-2.png',
		type: 'route',
		url: '/report/pages/monthly-business-analysis-report/monthly-business-analysis-report'
	},
	{
		label: '订单中心',
		value: 2,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-3.png',
		type: 'route',
		url: '/report/pages/order/order'
	},
	{
		label: '远期房价',
		value: 3,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-4.png',
		type: 'route',
		url: '/report/pages/long-room-price/long-room-price'
	},
	{
		label: '管理费账单',
		value: 4,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-5.png',
		type: 'webview',
		url: '/pages/bill/bill'
	},
	{
		label: '出租报表',
		value: 5,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-6.png',
		type: 'route',
		url: '/report/pages/rental-report/rental-report'
	},
	{

		label: ['门店服务', '数据报告'],
		value: 6,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-7.png',
		type: 'webview',
		url: '/pages/store-service-data-report/store-service-data-report'
	},
	{
		label: '客源结构',
		value: 7,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-07-25/report-8.png',
		type: 'route',
		url: '/report/pages/customer-analysis/customer-analysis'
	},
	{

		label: '远期房态',
		value: 8,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-08-07/room-9.png',
		type: 'route',
		url: '/report/pages/long-room-status/long-room-status'
	},
	{
		label: '扫码住统计',
		value: 9,
		imgUrl: 'https://tr-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-10-11/scan.png',
		type: 'route',
		url: '/report/pages/qrcode-check-in/qrcode-check-in'
	}
]
