export const manageDataHeaderFields = {
	totalRevenue: '总营收',
	rpData: 'RP',
	adrData: 'ADR',
	occData: 'OCC',
	memberCard: '会员售卡数量'
}
export const orderAnalyseHeaderFields = {
	individualTraveler: '散客',
	member: '会员',
	directSelling: '直销',
	distribution: '分销',
	intermediary: '中介',
	agreement: '协议'
}

export const manageDataHeader = [
	{ name: '项目', prop: 'dataName', width: 100 },
	{ name: '预算完成度', prop: 'rate', width: 140 },
	{ name: '本月', prop: 'monthData', width: 140 },
	{ name: '上月', prop: 'lastMonthData', width: 130 },
	{ name: '环比增长', prop: 'hb', width: 130 },
	{ name: '去年同期', prop: 'lastMonthData', width: 130 },
	{ name: '同比增长', prop: 'tb', width: 130 }
]
export const orderAnalyseHeader = [
	{ name: '项目', prop: 'dataName', width: 126 },
	{ name: '本月', prop: 'monthData', width: 100 },
	{ name: '上月', prop: 'lastMonthData', width: 100 },
	{ name: '环比增长', prop: 'hb', width: 130 },
	{ name: '去年同期', prop: 'lastYearData', width: 130 },
	{ name: '同比增长', prop: 'tb', width: 130 }
]

export const marketingSupportEnums = [
	{ label: '线上代运营', value: '0' },
	{ label: '线下营销指导', value: '1' },
	{ label: '营销礼品', value: '2' }
]

export const businessImprovementEnums = [
	{ label: 'RevPar', value: '0' },
	{ label: '会员卡', value: '1' },
	{ label: 'CRS', value: '2' },
	{ label: '网评', value: '3' },
]

export const qualityManagementEnums = [
	{ label: '内务管理', value: '0' },
	{ label: '员工操作技能提升', value: '1' },
	{ label: '成本控制', value: '2' }
]

export const otherEnums = [
	{ label: '约/升级改造', value: '0' },
]
/**
 * @description: 业主盈利
 */
export const PROFIT_FOR_HOMEOWNERS_CODE = '101501'
/**
 * @description: 员工热爱
 */
export const EMPLOYEES_LOVE_CODE = '101502'
/**
 * @description: 住客期待
 */
export const GUEST_EXPECTATIONS_CODE = '101503'
/**
 * @description: 培训&报销
 */
export const TRAINING_REIMBURSEMENT_CODE = '101504'

/**
 * @description: 谈话记录表
 */
export const CONVERSATION_RECORD_FORM_CODE = '100501'
/**
 * @description: 酒店营销
 */
export const HOTEL_MARKETING_CODE = '101301'
/**
 * @description: 公司新政策传递
 */
export const TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE = '100605'

/**
 * @description: 会议事项及完成事项
 */
export const MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE = '100703'
/**
 * @description: 运营经理10项检查
 */
export const CHECKS_BY_OPERATIONS_MANAGER_10_CODE = '100207'
/**
 * @description:筹建指导工作表
 */
export const CONSTRUCTION_GUIDANCE_WORKSHEET_CODE = '100801'
/**
 * @description:内控合规检查
 */
export const INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE = '100202'
/**
 * @description: 会议纪要
 */
export const MEETING_MINUTES_CODE = '100802'
/**
 * @description: 巡店事项及完成情况
 */
export const STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE = '100604'
/**
 * @description: 酒店培训
 */
export const HOTEL_TRAINING_CODE = '100203'
/**
 * @description: 骏怡筹建指导工作表
 */
export const JUNYI_CONSTRUCTION_GUIDANCE_WORKSHEET_CODE = '100901'
/**
 * @description: 品质检查
 */
export const QUALITY_CHECKS_CODE = '100701'
/**
 * @description: 公司活动及参与排名
 */
export const COMPANY_EVENTS_PARTICIPATION_RANKINGS_CODE = '100702'