<template>
	<view class="container">
		<commonBgVue height="530" />
		<u-navbar placeholder :title="(pageOptions?.shopId || '')+ '巡店表单'" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx;font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="tabs">
			<cTabsVue v-model="tabValue" :list="menuList" :isBig="false"
				itemStyle="height: 80rpx;padding: 0 20rpx;align-items:flex-start;" :activeFontSize="36"
				:inactiveFontSize="26" />
		</view>
		<view class="content">
			<profitForHomeownersVue ref="profitForHomeownersRef" :options="pageOptions" :menu-code="currentMenuCode"
				v-show="currentMenuCode == PROFIT_FOR_HOMEOWNERS_CODE" />
			<employeesLoveVue ref="employeesLoveRef" :menu-code="currentMenuCode" :options="pageOptions"
				v-show="currentMenuCode == EMPLOYEES_LOVE_CODE" />
			<guestExpectationsVue ref="guestExpectationsRef" :menu-code="currentMenuCode" :options="pageOptions"
				v-show="currentMenuCode == GUEST_EXPECTATIONS_CODE" />
			<trainingReimbursementVue ref="trainingReimbursementRef" :menu-code="currentMenuCode" :options="pageOptions"
				v-show="currentMenuCode == TRAINING_REIMBURSEMENT_CODE" />
			<constructionGuidanceWorksheetVue ref="constructionGuidanceWorksheetRef" :options="pageOptions"
				v-show="currentMenuCode == CONSTRUCTION_GUIDANCE_WORKSHEET_CODE" :menu-code="currentMenuCode" />
			<meetingMinutesVue ref="meetingMinutesRef" :options="pageOptions" v-show="currentMenuCode == MEETING_MINUTES_CODE"
				:menu-code="currentMenuCode" />
			<meetingMattersAndCompletionItemsVue ref="meetingMattersAndCompletionItemsRef" :options="pageOptions"
				:menu-code="currentMenuCode" v-show="currentMenuCode == MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE" />
			<checksOnOperationalQualityVue ref="checksOnOperationalQualityRef" :options="pageOptions"
				v-show="currentMenuCode == CHECKS_BY_OPERATIONS_MANAGER_10_CODE" :menu-code="currentMenuCode" />
			<storeInspectionItemsAndCompletionStatusVue ref="storeInspectionItemsAndCompletionStatusRef"
				:options="pageOptions" :menu-code="currentMenuCode"
				v-show="currentMenuCode == STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE" />
			<conversationRecordFormVue ref="conversationRecordFormRef" :options="pageOptions" :menu-code="currentMenuCode"
				v-show="currentMenuCode == CONVERSATION_RECORD_FORM_CODE" />
			<hotelTrainingVue ref="hotelTrainingRef" :options="pageOptions" v-show="currentMenuCode == HOTEL_TRAINING_CODE"
				:menu-code="currentMenuCode" />
			<internalControlComplianceInspectionVue ref="internalControlComplianceInspectionRef" :options="pageOptions"
				v-show="currentMenuCode == INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE" :menu-code="currentMenuCode" />
			<companyNewPolicyTransmissionVue ref="companyNewPolicyTransmissionRef" :options="pageOptions"
				:menu-code="currentMenuCode" v-show="currentMenuCode == TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE" />
			<hotelMarketingVue ref="hotelMarketingRef" :options="pageOptions" :menu-code="currentMenuCode"
				v-show="currentMenuCode == HOTEL_MARKETING_CODE" />
			<jyConstructionGuidanceWorksheetVue ref="jyConstructionGuidanceWorksheetRef" :options="pageOptions"
				:menu-code="currentMenuCode" v-show="currentMenuCode == JUNYI_CONSTRUCTION_GUIDANCE_WORKSHEET_CODE" />
			<qualityChecksVue ref="qualityChecksRef" :options="pageOptions" :menu-code="currentMenuCode"
				v-show="currentMenuCode == QUALITY_CHECKS_CODE" />
      <companyEventsParticipationRankingsVue ref="companyEventsParticipationRankingsRef" :options="pageOptions" :menu-code="currentMenuCode"
        v-show="currentMenuCode == COMPANY_EVENTS_PARTICIPATION_RANKINGS_CODE" />
		</view>
		<fixedBtnLayoutVue ref="fixedBtnLayoutRef">
			<view class="fr container-footer">
				<text class="fr btn btn-primary" @tap="submit(1)">确认提交</text>
				<text class="fr btn btn-success" @tap="submit(0)">保存草稿</text>
			</view>
		</fixedBtnLayoutVue>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { computed, ref, nextTick } from 'vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue'
	import profitForHomeownersVue from './components/profitForHomeowners.vue';
	import employeesLoveVue from './components/employeesLove.vue';
	import guestExpectationsVue from './components/guestExpectations.vue';
	import fixedBtnLayoutVue from '../../components/fixed-btn-layout/fixed-btn-layout.vue'
	import trainingReimbursementVue from './components/trainingReimbursement.vue';
	import constructionGuidanceWorksheetVue from './components/constructionGuidanceWorksheet.vue';
	import meetingMattersAndCompletionItemsVue from './components/meetingMattersAndCompletionItems.vue';
	import meetingMinutesVue from './components/meetingMinutes.vue';
	import checksOnOperationalQualityVue from './components/10ChecksOnOperationalQuality.vue'
	import storeInspectionItemsAndCompletionStatusVue from './components/storeInspectionItemsAndCompletionStatus.vue';
	import conversationRecordFormVue from './components/conversationRecordForm.vue';
	import hotelTrainingVue from './components/hotelTraining.vue';
	import internalControlComplianceInspectionVue from './components/internalControlComplianceInspection.vue';
	import companyNewPolicyTransmissionVue from './components/companyNewPolicyTransmission.vue';
	import hotelMarketingVue from './components/hotelMarketing.vue';
	import jyConstructionGuidanceWorksheetVue from './components/jyConstructionGuidanceWorksheet.vue';
  import qualityChecksVue from './components/qualityChecks.vue';
  import companyEventsParticipationRankingsVue from './components/companyEventsParticipationRankings.vue';
	import type { pageOptionsType, menuItemType } from './types'
	import { useContentLayoutHeight } from '../../hooks';
	import { getMenus as getMenusApi, createReportPdf as createReportPdfApi } from './api'
	import {
		HOTEL_MARKETING_CODE,
		PROFIT_FOR_HOMEOWNERS_CODE,
		EMPLOYEES_LOVE_CODE,
		GUEST_EXPECTATIONS_CODE,
		TRAINING_REIMBURSEMENT_CODE,
		CONVERSATION_RECORD_FORM_CODE,
		TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE,
		MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE,
		CONSTRUCTION_GUIDANCE_WORKSHEET_CODE,
		MEETING_MINUTES_CODE, CHECKS_BY_OPERATIONS_MANAGER_10_CODE,
		HOTEL_TRAINING_CODE,
		STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE,
		INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE,
		JUNYI_CONSTRUCTION_GUIDANCE_WORKSHEET_CODE,
		QUALITY_CHECKS_CODE,
		COMPANY_EVENTS_PARTICIPATION_RANKINGS_CODE
	} from './enums'
	import { showModal } from '@/utils/util';
	const { fixedBtnLayoutRef, getContentLayoutHeight, getFixedBtnLayoutHeight, contentHeight } = useContentLayoutHeight('.content')
	const profitForHomeownersRef = ref<InstanceType<typeof profitForHomeownersVue>>(null)
	const employeesLoveRef = ref<InstanceType<typeof employeesLoveVue>>(null)
	const guestExpectationsRef = ref<InstanceType<typeof guestExpectationsVue>>(null)
	const trainingReimbursementRef = ref<InstanceType<typeof trainingReimbursementVue>>(null)
	const conversationRecordFormRef = ref<InstanceType<typeof conversationRecordFormVue>>(null)
	const companyNewPolicyTransmissionRef = ref<InstanceType<typeof companyNewPolicyTransmissionVue>>(null)
	const hotelMarketingRef = ref<InstanceType<typeof hotelMarketingVue>>(null)
	const meetingMattersAndCompletionItemsRef = ref<InstanceType<typeof meetingMattersAndCompletionItemsVue>>(null)
	const checksOnOperationalQualityRef = ref<InstanceType<typeof checksOnOperationalQualityVue>>(null)
	const constructionGuidanceWorksheetRef = ref<InstanceType<typeof constructionGuidanceWorksheetVue>>(null)
	const meetingMinutesRef = ref<InstanceType<typeof meetingMinutesVue>>(null)
	const internalControlComplianceInspectionRef = ref<InstanceType<typeof internalControlComplianceInspectionVue>>(null)
	const storeInspectionItemsAndCompletionStatusRef = ref<InstanceType<typeof storeInspectionItemsAndCompletionStatusVue>>(null)
	const hotelTrainingRef = ref<InstanceType<typeof hotelTrainingVue>>(null)
	const jyConstructionGuidanceWorksheetRef = ref<InstanceType<typeof jyConstructionGuidanceWorksheetVue>>(null)
  const qualityChecksRef = ref<InstanceType<typeof qualityChecksVue>>(null)
  const companyEventsParticipationRankingsRef = ref<InstanceType<typeof companyEventsParticipationRankingsVue>>(null)
	const pageOptions = ref<Partial<pageOptionsType>>({})
	const menuList = ref<Partial<menuItemType>[]>([])
	const tabValue = ref(0)
	const currentMenuCode = computed(() => {
		return menuList.value?.[tabValue.value]?.value
	})
	const getSaveFn = (submissionStatus : number = 0) => {
		return {
			[CONVERSATION_RECORD_FORM_CODE]: () => ({
				validate: () => conversationRecordFormRef.value.validate(),
				submit: async () => await conversationRecordFormRef.value.saveTalkingContent(submissionStatus)
			}),
			[HOTEL_MARKETING_CODE]: () => ({
				validate: () => hotelMarketingRef.value.validate(),
				submit: async () => await hotelMarketingRef.value.saveArrearsSellRemark(submissionStatus),
			}),
			[TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE]: () => ({
				validate: () => companyNewPolicyTransmissionRef.value.validate(),
				submit: async () => await companyNewPolicyTransmissionRef.value.saveCompanyPolicy(submissionStatus)
			}),
			[MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE]: () => ({
				validate: () => meetingMattersAndCompletionItemsRef.value.validate(),
				submit: async () => await meetingMattersAndCompletionItemsRef.value.savePatrolMeetingCompletion(submissionStatus)
			}),
			[PROFIT_FOR_HOMEOWNERS_CODE]: () => ({
				validate: () => profitForHomeownersRef.value.validate(),
				submit: async () => await profitForHomeownersRef.value.saveOperatingProfitEvaluation(submissionStatus)
			}),
			[EMPLOYEES_LOVE_CODE]: () => ({
				validate: () => employeesLoveRef.value.validate(),
				submit: async () => await employeesLoveRef.value.saveEmployeeManagementInfo(submissionStatus)
			}),
			[GUEST_EXPECTATIONS_CODE]: () => ({
				validate: () => guestExpectationsRef.value.validate(),
				submit: async () => await guestExpectationsRef.value.saveSecurityQualityInfo(submissionStatus)
			}),
			[TRAINING_REIMBURSEMENT_CODE]: () => ({
				validate: () => trainingReimbursementRef.value.validate(),
				submit: async () => await trainingReimbursementRef.value.saveTrainReimbursableInfo(submissionStatus)
			}),
			[CHECKS_BY_OPERATIONS_MANAGER_10_CODE]: () => ({
				validate: () => checksOnOperationalQualityRef.value.validate(),
				submit: async () => await checksOnOperationalQualityRef.value.saveQualityOprationCheck(submissionStatus)
			}),
			[CONSTRUCTION_GUIDANCE_WORKSHEET_CODE]: () => ({
				validate: () => constructionGuidanceWorksheetRef.value.validate(),
				submit: async () => await constructionGuidanceWorksheetRef.value.saveItemsCompletionPrep(submissionStatus)
			}),
			[MEETING_MINUTES_CODE]: () => ({
				validate: () => meetingMinutesRef.value.validate(),
				submit: async () => await meetingMinutesRef.value.saveMeetingNotes(submissionStatus)
			}),
			[INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE]: () => ({
				validate: () => internalControlComplianceInspectionRef.value.validate(),
				submit: async () => await internalControlComplianceInspectionRef.value.saveInternalCheck(submissionStatus)
			}),
			[STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE]: () => ({
				validate: () => storeInspectionItemsAndCompletionStatusRef.value.validate(),
				submit: async () => await storeInspectionItemsAndCompletionStatusRef.value.savePatrolItemsAndCompletionForJY(submissionStatus)
			}),
			[HOTEL_TRAINING_CODE]: () => ({
				validate: () => hotelTrainingRef.value.validate(),
				submit: async () => await hotelTrainingRef.value.saveTrainingDetail(submissionStatus)
			}),
			[JUNYI_CONSTRUCTION_GUIDANCE_WORKSHEET_CODE]: () => ({
				validate: () => jyConstructionGuidanceWorksheetRef.value.validate(),
				submit: async () => await jyConstructionGuidanceWorksheetRef.value.saveItemsCompletionPrep(submissionStatus)
			}),
      [QUALITY_CHECKS_CODE]: () => ({
        validate: () => qualityChecksRef.value.validate(),
        submit: async () => await qualityChecksRef.value.toSaveQualityCheckByOnline(submissionStatus)
      }),
      [COMPANY_EVENTS_PARTICIPATION_RANKINGS_CODE]: () => ({
        validate: () => companyEventsParticipationRankingsRef.value.validate(),
        submit: async () => await companyEventsParticipationRankingsRef.value.toSavePatrolActiveTopInfo(submissionStatus)
      })
		}
	}
	/**
	 * @description: 提交
	 */
	const submit = async (submissionStatus : number) : Promise<void> => {

		try {
			const menuCodes = menuList.value.map(item => item.value)
			if (submissionStatus == 0) {
				getSaveFn(submissionStatus)[currentMenuCode.value]().submit()
			} else {
				const fns = menuCodes.map(item => {
					const savefn = getSaveFn(submissionStatus)
					console.log("🚀 ~ fns ~ savefn:", savefn, item)
					return { ...savefn[item](), menuCode: item }
				})
				console.log("🚀 ~ fns ~ fns:", fns)

				for (let i = 0, len = fns.length; i < len; i++) {
					if (!fns[i].validate()) {
						setTimeout(() => {
							tabValue.value = menuCodes.findIndex(item => item === fns[i].menuCode)
						}, 1500)
						return
					}
				}
				const res = await Promise.all(fns.map(item => item.submit()))
				const pass = res.every((item : any) => item.code == 200)
				if (pass) {
					showModal({
						title: '温馨提示',
						content: '巡店结束后将自动生成巡店报告，且无法修改巡店数据，确认结束巡店吗？',
						success: async (res) => {
							if (res.confirm) {
								if (pageOptions.value.targetCode == '3') {
									createReportPdf(2)
								} else {
									uni.showActionSheet({
										itemList: ['纸质版线下签章', '契约锁线上签章'],
										itemColor: '#000000',
										success: (result : UniApp.ShowActionSheetRes) => {
											createReportPdf(result.tapIndex)
										}
									})
								}
							}
						}
					})
				}
			}
		} catch (e) {
			console.log("🚀 ~ submit ~ e:", e)
		}
	}
	/**
	 * @description: 生成报告
	 */
	const createReportPdf = async (signatureType : number | string) => {
		try {
			const { brandType, orderId, shopId, targetCode, tripId, tripCode } = pageOptions.value
			await createReportPdfApi({
				brandType,
				dcmCode: 'pdf',
				shopid: shopId,
				orderId,
				targetCode,
				tripId,
				isOnlineSign: signatureType
			})
			if (signatureType == 1) {
				uni.redirectTo({
					url: '/packageB/pages/send-report/send-report?orderId=' + orderId + '&shopId=' + shopId + '&status=0'
				})
			} else {
				uni.redirectTo({
					url: '/packageB/pages/shop-inspection-report/shop-inspection-report?tripCode=' + tripCode
				})
			}
		} catch (e) {
			console.log("🚀 ~ createReportPdf ~ e:", e)
		}
	}
	/**
	 * @description : 获取菜单
	 */
	const getMenus = async () : Promise<void> => {
		try {
			const res = await getMenusApi({
				shopId: pageOptions.value.shopId,
				targetCode: pageOptions.value.targetCode
			})
			menuList.value = res.data?.map((item : menuItemType) => {
				return {
					name: item.value,
					value: item.code,
					brandType: item.brandType
				}
			}) || []
		} catch (e) {
			console.log("🚀 ~ getMenus ~ e:", e)
		}
	}
	onLoad(async (opts) => {
		pageOptions.value = { ...opts }
		await getMenus()
		setTimeout(async () => {
			await nextTick()
			await getFixedBtnLayoutHeight()
			await getContentLayoutHeight()
		}, 500)
	})
</script>

<style lang="scss" scoped>
	.container {
		.tabs {
			padding: 0 20rpx;
			padding-top: 30rpx;
			padding-bottom: 20rpx;
		}

		:deep() {
			.u-tabs__wrapper__nav__item__text {
				color: #fff !important;
			}

		}

		.content {
			overflow-y: auto;
			height: v-bind(contentHeight);
		}

		&-footer {
			flex: 1;
			padding: 0 40rpx;
			justify-content: space-between;

			.btn {
				justify-content: center;
				width: 314rpx;
				height: 80rpx;
				border: 2rpx solid #c35943;
				font-weight: bold;
				color: #C35943;
				border-radius: 200rpx;

				&-primary {
					border: none;
					color: #fff;
					background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				}
			}
		}

	}
</style>