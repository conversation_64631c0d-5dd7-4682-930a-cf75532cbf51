import http from '@/utils/http'
import { dataType, successCallbackResult } from '@/types/index.d'

/**
 * @description: 获取菜单
 */
export const getMenus = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getMenus',
		method: 'POST',
		data
	})
}
/**
 * @description: 获取谈话记录表
 */
export const getTalkingContent = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getTalkingContent',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存谈话记录表
 */
export const saveTalkingContent = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveTalkingContent',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取酒店营销
 */
export const getArrearsSellRemark = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getArrearsSellRemark',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存酒店营销
 */
export const saveArrearsSellRemark = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveArrearsSellRemark',
		method: 'POST',
		data
	})
}
/**
 * @description: 公司新政传递
 */
export const getCompanyPolicyInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getCompanyPolicyInfo',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存公司政策
 */
export const saveCompanyPolicy = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveCompanyPolicy',
		method: 'POST',
		data
	})
}
/**
 * @description: 生成报告pdf
 */
export const createReportPdf = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/report/createReportPdf',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取会议事项及完成事项（线上）
 */
export const getPatrolMeetingCompletion = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getPatrolMeetingCompletion',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存会议事项及完成事项（线上）
 */
export const savePatrolMeetingCompletion = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/savePatrolMeetingCompletion',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取业主/经营盈利数据
 */
export const getOperatingProfitData = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getOperatingProfitData',
		method: 'POST',
		data
	})
}
/**
 * @description: 获取业主/经营盈利评价
 */
export const getOperatingProfitEvaluation = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getOperatingProfitEvaluation',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取员工热爱管理详情
 */
export const getEmployeeManagementInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getEmployeeManagementInfo',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存业主/经营盈利评价
 */
export const saveOperatingProfitEvaluation = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveOperatingProfitEvaluation',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取员工热爱管理详情
 */
export const saveEmployeeManagementInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveEmployeeManagementInfo',
		method: 'POST',
		data
	})
}
/**
 * @description: 获取住客期待
 */
export const getSecurityQualityInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getSecurityQualityInfo',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存住客期待
 */
export const saveSecurityQualityInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveSecurityQualityInfo',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取培训&报销
 */
export const getTrainReimbursableInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getTrainReimbursableInfo',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存培训&报销
 */
export const saveTrainReimbursableInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveTrainReimbursableInfo',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取门店标题
 */
export const getPatrolItemsAndCompletionTitle = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getPatrolItemsAndCompletionTitle',
		method: 'POST',
		data
	})
}
/**
 * @description: 获取内控合规检查详情
 */
export const getInternalControlCheckInfo = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getInternalControlCheckInfo',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存内控合规检查
 */
export const saveInternalCheck = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveInternalCheck',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取品质运营检查详情
 */
export const getQualityOprationCheck = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getQualityOprationCheck',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存品质运营检查
 */
export const saveQualityOprationCheck = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveQualityOprationCheck',
		method: 'POST',
		data
	})
}
/**
 * @description : 获取酒店培训
 */
export const getTrainingDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getTrainingDetail',
		method: 'POST',
		data
	})
}
/**
 * @description : 保存酒店培训
 */
export const saveTrainingDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveTrainingDetail',
		method: 'POST',
		data
	})
}

/**
 * @description : 获取筹建指导工作表
 */
export const getItemsCompletionPrep = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getItemsCompletionPrep',
		method: 'POST',
		data
	})
}
/**
 * @description : 保存筹建指导工作表
 */
export const saveItemsCompletionPrep = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveItemsCompletionPrep',
		method: 'POST',
		data
	})
}
/**
 * @description : 获取会议纪要
 */
export const getMeetingNodesDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getMeetingNodesDetail',
		method: 'POST',
		data
	})
}

/**
 * @description : 保存会议纪要
 */
export const saveMeetingNotes = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveMeetingNotes',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取巡店事项及完成情况
 */
export const getPatrolItemsAndCompletionForJY = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getPatrolItemsAndCompletionForJY',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存巡店事项及完成情况
 */
export const savePatrolItemsAndCompletionForJY = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/savePatrolItemsAndCompletionForJY',
		method: 'POST',
		data
	})
}

/**
 * @description: 查看品质检查 (线上)
 */
export const getQualityCheckByOnline = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getQualityCheckByOnline',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存品质检查 (线上)
 */
export const saveQualityCheckByOnline = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveQualityCheckByOnline',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存公司活动及参与排名
 */
export const savePatrolActiveTopInfo = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/savePatrolActiveTopInfo',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取公司活动及参与排名
 */
export const getPatrolActiveTopInfo = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getPatrolActiveTopInfo',
		method: 'POST',
		data
	})
}