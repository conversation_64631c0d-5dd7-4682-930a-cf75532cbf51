<!--
 * @Description: 住客期待
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:56:30
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:57:26
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/guestExpectations.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fl block" v-for="(item, index) in dataList" :key="item.menuCode">
			<text class="block-title employee">{{ item.menuValue }}</text>
			<radioVue v-for="(r, i) in item.list" :key="r.detailMenuCode" :title="r.detailMenuValue" :radioGroup="[{
				label: '合格',
				value: 1
			}, { label: '不合格', value: 0 }]" :is-upload="r.qualifyStatus === 0" required v-model:desc="r.underproofDesc"
				v-model:value="r.qualifyStatus" v-model:images="r.picUrls" :query="useParams(r.detailMenuCode)"
				@change-image="e => onChangeImages(e, index, i)" />
		</view>
		<view class="fl block">
			<text class="block-title">跟进建议</text>
			<view class="suggest">
				<view class="fl first-fl">
					<text class="suggest-title">安全:</text>
					<cTextareaVue v-model="form.secureProposal" count maxlength="500" background="#fff" placeholder="请填写" />
				</view>
				<view class="fl">
					<text class="suggest-title">产品：</text>
					<cTextareaVue v-model="form.productProposal" count maxlength="500" background="#fff" placeholder="请填写" />
				</view>
				<view class="fl">
					<text class="suggest-title">品质：</text>
					<cTextareaVue v-model="form.qualityProposal" count maxlength="500" background="#fff" placeholder="请填写" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, onMounted, ref, watch } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import radioVue from './radio.vue';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { getSecurityQualityInfo as getSecurityQualityInfoApi, saveSecurityQualityInfo as saveSecurityQualityInfoApi } from '../api'
	import { GUEST_EXPECTATIONS_CODE } from '../enums';
	import { showToast } from '@/utils/util';
	type formType = {
		productProposal : string;
		qualityProposal : string;
		secureProposal : string;
	}
	type listItemType = {
		detailMenuCode : string;
		detailMenuValue : string;
		picUrl : string;
		qualifyStatus ?: number | undefined,
		underproofDesc : string;
		[k : string] : any
	}
	type dataListType = {
		list : Partial<listItemType>[],
		menuCode : string;
		menuValue : string;
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	const { useParams } = usePageParams(props)
	const form = ref<Partial<formType>>({})
	const dataList = ref<dataListType[]>([])
	watch(() => props.menuCode, (v : string) => {
		if (v == GUEST_EXPECTATIONS_CODE) {
			if (!dataList.value?.length) {
				getSecurityQualityInfo()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 图片回调
	 * @params pIndex: 父下标
	 * @params sIndex: 子下标
	 */
	const onChangeImages = (e : any, pIndex : number, sIndex : number) => {
		dataList.value[pIndex].list[sIndex].picUrls = e
	}
	/**
	 * @description: 获取数据
	 */
	const getSecurityQualityInfo = async () : Promise<void> => {
		try {
			const res = await getSecurityQualityInfoApi({
				code: GUEST_EXPECTATIONS_CODE,
				...useParams.value()
			})
			const data = res.data
			form.value = data.securityQualityProposal || {}
			dataList.value = data.list?.map((item : dataListType) => {
				item.list = item.list?.map((m : listItemType) => {
					return {
						...m,
						picUrls: m.picUrl ? m.picUrl.split(',').map((r : string) => {
							return {
								code: m.detailMenuCode,
								url: r
							}
						}) : []
					}
				}) || []
				return item
			}) || []
		} catch (error) {
			console.log("🚀 ~ getSecurityQualityInfo ~ error:", error)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		if (dataList.value.length <= 0) {
			showToast(`请填写住客期待`)
			return
		}
		for (let i = 0, pLen = dataList.value.length; i < pLen; i++) {
			const dataItem = dataList.value[i]
			for (let k = 0, sLen = dataItem.list.length; k < sLen; k++) {
				const sDataItem = dataItem.list[k]
				if (sDataItem.qualifyStatus != 0 && sDataItem.qualifyStatus != 1) {
					showToast(`请选择住客期待-${(dataItem.menuValue).slice(0, 2)}第${k + 1}项是否合格`)
					return
				}
				if (sDataItem.qualifyStatus == 0 && !sDataItem.underproofDesc) {
					showToast(`请填写住客期待-${(dataItem.menuValue).slice(0, 2)}第${k + 1}项是否合格`)
					return
				}
			}
		}
		return true
	}
	/**
	 * @description: 保存数据
	 */
	const saveSecurityQualityInfo = async (draftType : number = 0) : Promise<any> => {
		try {
			const params = {
				code: GUEST_EXPECTATIONS_CODE,
				...form.value,
				draftType,
				...useParams.value(),
			}
			const copyData : dataListType[] = JSON.parse(JSON.stringify(dataList.value))
			params.list = copyData.map((item : dataListType) => {
				return {
					...item,
					list: item.list.map((r : listItemType) => {
						r.picUrl = r.picUrls.map((u : any) => u.url)?.join(',')
						r.code = r.detailMenuCode
						delete r.picUrls
						return r
					})
				}
			}).reduce((prev, cur : any) => {
				return prev.concat(cur.list)
			}, [])
			const res = await saveSecurityQualityInfoApi(params)
			if (draftType == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ saveSecurityQualityInfo ~ error:", error)
		}
	}

	defineExpose({
		saveSecurityQualityInfo,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			padding-bottom: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&.employee {
				border-bottom: 2rpx solid #F2F2F2;
			}
		}

		&-content {
			padding: 20rpx 24rpx;
			border-radius: 15.38rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

			.boss-suggest {
				margin-top: 20rpx;
			}
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}
	}
</style>