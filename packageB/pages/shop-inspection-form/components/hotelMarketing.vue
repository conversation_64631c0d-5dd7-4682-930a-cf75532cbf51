<!--
 * @Description: 酒店营销
 * @Author: gu_shiyuan"
 * @Date: 2024-12-19 16:04:07
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:58:35
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/hotelMarketing.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="block">
			<text class="fr block-title">酒店营销</text>
			<cTextareaVue v-model="dataInfo.sellRemark" count maxlength="500" placeholder="请填写营销工作记录"
				background="linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%)" />
			<uploadImageGroupVue v-model:images="dataInfo.picUrls" imgLeftText="文件照片" :query="{dcmCode: '101301'}" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, watch, ref } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import uploadImageGroupVue from './uploadImageGroup.vue';
	import { pageOptionsType } from '../types';
	import { getArrearsSellRemark as getArrearsSellRemarkApi, saveArrearsSellRemark as saveArrearsSellRemarkApi } from '../api'
	import { usePageParams } from '../hooks/usePageParams';
	import { showToast } from '@/utils/util';
	import { HOTEL_MARKETING_CODE } from '../enums'
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	type dataInfoType = {
		sellRemark : string;
		image : string;
		picUrls : any[]
	}
	const dataInfo = ref<Partial<dataInfoType>>({})
	const { useParams } = usePageParams(props)
	/**
	 * @description: 获取数据
	 */
	const getArrearsSellRemark = async () => {
		const res = await getArrearsSellRemarkApi(useParams.value())
		const data = res.data
		if (data?.image) {
			data.picUrls = data.image.split(',').map((ig : string) => {
				return {
					code: HOTEL_MARKETING_CODE,
					url: ig
				}
			})
		}
		dataInfo.value = res.data || { picUrls: [] }
	}
	/**
	 * @description: 提交校验
	 */
	const validate = () => {
		if (!dataInfo.value.sellRemark) {
			showToast('酒店营销工作记录不能为空')
			return
		}
		return true
	}
	/**
	 * @description: 保存酒店营销
	 */
	const saveArrearsSellRemark = async (submissionStatus : number = 0) => {
		try {
			const params = {
				...useParams.value(),
			}
			if (dataInfo.value.picUrls?.length) {
				params.image = dataInfo.value.picUrls.map((item : any) => item.url).join(',')
			}
			if (dataInfo.value.sellRemark) {
				params.sellRemark = dataInfo.value.sellRemark
			}
			const res = await saveArrearsSellRemarkApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveArrearsSellRemark ~ e:", e)
		}
	}
	watch(() => props.menuCode, (v : string) => {
		if (v == HOTEL_MARKETING_CODE) {
			if (dataInfo.value && JSON.stringify(dataInfo.value) == '{}') {
				getArrearsSellRemark()
			}
		}
	}, { immediate: true })
	defineExpose({
		saveArrearsSellRemark,
		validate
	})
</script>

<style scoped lang="scss">
	.block {
		padding: 28rpx 24rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&-wrap {
			margin: 0 20rpx;
		}

		&-title {
			margin-bottom: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;
		}
	}
</style>