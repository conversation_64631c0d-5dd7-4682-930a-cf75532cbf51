<!--
 * @Description: 上传图片
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:57:39
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-19 18:12:11
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/uploadImageGroup.vue
-->

<template>
	<view class="fl upload-wrap">
		<view class="fr upload" v-if="showBtn">
			<text class="desc" :class="{'required': required}">{{ imgLeftText }}</text>
			<view class="fr btn-upload" v-if="!autoHideButton || images.length < maxLength" @tap="chooseImage">
				<image
					src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-12/1733971763935.png"
					mode="aspectFill" lazy-load class="btn-upload__icon"></image>
				<text class="btn-upload__text">图片</text>
			</view>
		</view>
		<view class="fr upload-list" v-if="images.length">
			<view class="upload-list__item" v-for="(item, index) in images" :key="index">
				<view class="icon-close" @tap="deleteImage(item, index)">
					<up-icon name="close-circle-fill" size="14" color="rgba(31, 36, 40, 0.7)"></up-icon>
				</view>
				<image :src="item.url" mode="aspectFill" lazy-load class="upload-img"></image>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { inject, computed } from 'vue';
	import { useUpload } from '../../../hooks/useUpload';
	import { showLoading, showToast } from '@/utils/util';
	type propType = {
		imgLeftText : string;
		required : boolean;
		specialUpload : boolean;
		images : any;
		isRegister : boolean;
		showBtn : boolean;
		maxLength : number;
    autoHideButton: boolean;
	};
	const props = withDefaults(defineProps<Partial<propType>>(), {
		imgLeftText: '请上传不合格情况照片',
		images: [],
		showBtn: true,
		query: Object,
		specialUpload: false,
		required: false,
		isRegister: false,
		maxLength: 9,
    autoHideButton: false
	})
	const emits = defineEmits(['update:images', 'change'])
	const { uploadImage, compressImage, upload, batchUpload } = useUpload()
	const chooseImage = async () => {
		console.log(props.images.length, props.maxLength,props.images.length >= props.maxLength,'props.images.length, props.maxLength');
		if (props.images.length >= props.maxLength) {
			showToast(`最多上传${props.maxLength}张`)
			return
		}
		if (props.specialUpload) {
			uni.$emit('uploadImage', {
				type: 'add',
				code: props.query?.dcmCode
			})
			return
		}
		batchUpload(Math.max(0, props.maxLength - props.images.length)).then(res => {
			const images = [...res.map((item : string) => {
				return {
					code: props.query?.dcmCode,
					url: item
				}
			})]
			emits('update:images', images)
			emits('change', images)
		})
	}

	const deleteImage = (data : any, index : number) => {
		if (props.specialUpload) {
			uni.$emit('uploadImage', {
				type: 'delete',
				code: props.query.dcmCode,
				data,
				index
			})
			return
		}
		props.images.splice(index, 1)
	}
	if (props.isRegister) {
		const provideInstance : any = inject('instance')
		provideInstance?.registerFn({ uploadImage, compressImage, upload, batchUpload })
	}
</script>

<style scoped lang="scss">
	.upload-wrap {
		margin-top: 20rpx;
		padding: 0 24rpx;
		background-color: #fff;
		border-radius: 15.38rpx;

		.required {
			&::before {
				content: "*";
				color: #C35943;
			}
		}

		.upload {
			justify-content: space-between;
			height: 80rpx;

			.desc {
				font-weight: bold;
			}

			.btn-upload {
				justify-content: center;
				height: 48rpx;
				padding: 0 24rpx;
				font-size: 24rpx;
				background: linear-gradient(203.5deg, rgba(#ffb2a1, .3) 0%, rgba(#f75e3b, .3) 100%);
				border-radius: 552rpx;

				&__icon {
					flex-shrink: 0;
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
					vertical-align: middle;
				}

				&__text {
					color: #C35943;
				}
			}
		}

		.upload-list {
			margin-top: 7px;
			flex-wrap: wrap;

			&__item {
				position: relative;
				margin-bottom: 28rpx;
				margin-right: 16rpx;

				&:nth-child(5n+5) {
					margin-right: 0;
				}

				.icon-close {
					position: absolute;
					top: -7px;
					left: 0;
				}

				.upload-img {
					width: 94rpx;
					height: 94rpx;
					border-radius: 12rpx;
					vertical-align: middle;
				}
			}
		}
	}
</style>