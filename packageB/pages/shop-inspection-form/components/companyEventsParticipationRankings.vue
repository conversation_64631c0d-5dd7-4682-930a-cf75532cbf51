<!--
 * @Description: 公司活动及参与排名
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/companyEventsParticipationRankings.vue
-->

<template>
	<view class="sheet" v-for="(item, index) in dataList" :key="item.trainId">
		<view class="fr block">
			<text class="block-title">活动名称：</text>
			<input v-model="item.activeName" type="text" placeholder="请输入活动名称" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
      <text class="block-title">是否参与：</text>
      <view class="radio-group">
        <up-radio-group v-model="item.hasJoin" shape="circle" usedAlone :size="size" activeColor="#C35943"
          inactiveColor="#C35943" labelColor="#1F2428">
          <up-radio label="是" name="是" :labelSize="size" />
          <up-radio label="否" name="否" :labelSize="size" />
        </up-radio-group>
      </view>
		</view>
    <view class="fr block">
      <text class="block-title">排名情况：</text>
      <input v-model="item.topInfo" type="text" placeholder="请输入活动排名" class="block-title__input"
        placeholder-style="font-size: 26rpx;color:#999;" />
    </view>
    <view class="fl block">
      <text class="block-title">分析及建议：</text>
      <cTextareaVue v-model="item.proposal" count maxlength="500" placeholder="请输入您的分析及建议" />
    </view>
    <view
      class="fr btn-row btn-row__del" v-if="dataList.length > 1 && index !== 0"
      @tap="deleteGuidanceProject(index)">
      <text class="fr symbol" style="color: #C35943;">-</text>
      删除项目
    </view>
	</view>
  <view class="fr btn-row btn-row__add" @tap="addKnowProject">
    <text class="fr symbol">+</text>
    新增项目
  </view>
</template>

<script setup lang="ts">
import { watch, ref, PropType } from 'vue'
import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
import { savePatrolActiveTopInfo, getPatrolActiveTopInfo } from '../api'
import { usePageParams } from '../hooks/usePageParams';
import { pageOptionsType } from '../types';
import { showToast } from '@/utils/util';
import { QUALITY_CHECKS_CODE } from '../enums'

type dataItemType = {
  activeName: string;
  hasJoin: string;
  proposal: string;
  topInfo: string;
  [k: string]: any
}
const props = defineProps({
  options: {
    type: Object as PropType<pageOptionsType>,
    default: () => {
    }
  },
  menuCode: {
    type: String,
    default: ''
  }
})
const dataList = ref<Partial<dataItemType[]>>([])
const size = uni.upx2px(28)
const { useParams } = usePageParams(props)
/**
 * @description: 获取数据
 */
const getData = async (): Promise<void> => {
  const res = await getPatrolActiveTopInfo(useParams.value())
  if (res.data?.activeTopInfoList?.length) {
    dataList.value = res.data.activeTopInfoList
  } else {
    dataList.value = [{ activeName: '', hasJoin: '', proposal: '', topInfo: '' }]
  }
}
/**
 * @description: 提交校验
 */
const validate = () => {
  return true
}
/**
 * @description: 保存公司活动及参与排名
 */
const toSavePatrolActiveTopInfo = async (submissionStatus: number = 0) => {
  try {
    const params = {
      ...useParams.value(),
      activeTopInfoList: dataList.value
    }
    if (submissionStatus == 1 && !validate()) return
    const res = await savePatrolActiveTopInfo(params)
    if (submissionStatus == 0) {
      showToast('保存成功')
      return
    }
    return res
  } catch (e) {
    console.log("🚀 ~ 保存公司活动及参与排名 ~ e:", e)
  }
}
/**
 * @description: 新增指导项目
 */
const addKnowProject = () => {
  dataList.value.push({} as any)
}
/**
 * @description: 删除指导项目
 */
const deleteGuidanceProject = (index: number) => {
  dataList.value.splice(index, 1)
}
watch(() => props.menuCode, (v: string | number) => {
  if (v == QUALITY_CHECKS_CODE) {
    if (!dataList.value?.length) {
      getData()
    }
  }
}, { immediate: true })
defineExpose({
  toSavePatrolActiveTopInfo,
  validate
})
</script>
<style scoped lang="scss">
	.sheet {
		padding: 0 24rpx;
		color: #1F2428;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		.feedback {
			padding: 0 24rpx;

			&-input {
				padding: 20rpx 0;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}

		.table {
			font-size: 22rpx;

			.thead {
				background: #ffb2a166;
				border-radius: 15.43rpx 15.43rpx 0 0;
			}

			.thead,
			.tbody {
				height: 84rpx;
			}

			.tbody-bg:nth-of-type(even) {
				background-color: #FFF9F7;
			}

			.tbody-bg:nth-of-type(odd) {
				background-color: #FFF3F0;
			}

			.td {
				width: 50%;
				text-align: center;

				&-left {
					padding: 0 20rpx;
				}

				.relative {
					.icon {
						position: absolute;
						right: -30rpx;
						top: 0;
						bottom: 0;
					}

					position: relative;
				}

				&.fr {
					justify-content: center;
				}

			}
		}

		.desc {
			margin-bottom: 20rpx;
		}

		.radio-group {
			justify-content: space-between;

			&:first-child {
				margin-bottom: 20rpx;
			}

			&__wrap {
				padding: 20rpx 40rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 20rpx;
			}
		}
	}
  .btn-row {
    justify-content: center;
    height: 80rpx;
    margin: 0 20rpx 40rpx;
    font-weight: bold;
    border-radius: 200rpx;
    background: #ffffff;

    .symbol {
      justify-content: center;
      width: 40rpx;
      height: 40rpx;
    }

    &__add {
      color: #1F2428;
    }

    &__del {
      color: #C35943;
      margin: 0 0 40rpx;

    }
  }
</style>