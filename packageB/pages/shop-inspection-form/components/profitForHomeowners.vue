<!--
 * @Description: 业主盈利
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:56:53
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 10:14:23
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/profitForHomeowners.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fl block">
			<text class="block-title">经营数据</text>
			<cTableVue :header="manageDataHeader" :list="manageData" />
		</view>
		<view class="fl block">
			<text class="block-title">渠道订单分析</text>
			<cTableVue :header="orderAnalyseHeader" :list="orderAnalyse" />
		</view>
		<view class="fl block">
			<text class="block-title">经营提升建议</text>
			<view class="suggest">
				<view class="fl first-fl">
					<text class="suggest-title">ADR提升：</text>
					<cTextareaVue v-model="form.adrProposal" count maxlength="500" background="#fff"
						placeholder="在预算未达成时，请进行重点指导" />
				</view>
				<view class="fl">
					<text class="suggest-title">OCC提升：</text>
					<cTextareaVue v-model="form.occProposal" count maxlength="500" background="#fff"
						placeholder="在预算未达成时，请进行重点指导" />
				</view>
				<view class="fl">
					<text class="suggest-title">会员占比：</text>
					<cTextareaVue v-model="form.memberPercentProposal" count maxlength="500" background="#fff"
						placeholder="若低于40%，需对门店会员发展情况进行梳理" />
				</view>
				<view class="fl">
					<text class="suggest-title">直连提升：</text>
					<cTextareaVue v-model="form.directProposal" count maxlength="500" background="#fff"
						placeholder="检查各渠道直连上线情况且价格无倒挂，保证直连房型展示" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, ref, watch } from 'vue'
	import cTableVue from '@/components/c-table/c-table.vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import { manageDataHeader, orderAnalyseHeader, orderAnalyseHeaderFields, manageDataHeaderFields, PROFIT_FOR_HOMEOWNERS_CODE } from '../enums'
	import { dataType } from '../../../../types';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams'
	import { getOperatingProfitData as getOperatingProfitDataApi, getOperatingProfitEvaluation as getOperatingProfitEvaluationApi, saveOperatingProfitEvaluation as saveOperatingProfitEvaluationApi } from '../api'
	import { showToast } from '@/utils/util';
	import { managerData } from '../../../api';
	type formType = {
		adrProposal : string;
		directProposal : string;
		memberPercentProposal : string;
		occProposal : string;
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	const { useParams } = usePageParams(props)
	const manageData = ref([])
	const orderAnalyse = ref([])
	const form = ref<Partial<formType>>({})
	watch(() => props.menuCode, (v : string) => {
		if (v == PROFIT_FOR_HOMEOWNERS_CODE) {
			if (!manageData.value?.length) {
				getOperatingProfitData()
				getOperatingProfitEvaluation()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 备注信息
	 */
	const getOperatingProfitEvaluation = async () : Promise<void> => {
		try {
			const res = await getOperatingProfitEvaluationApi(useParams.value())
			form.value = res.data || {}
		} catch (error) {
			console.log("🚀 ~ getOperatingProfitEvaluation ~ error:", error)
		}
	}
	/**
	 * @description: 门店经营数据
	 */
	const getOperatingProfitData = async () => {
		try {
			// const res = await getOperatingProfitDataApi({ shopId: useParams.value().shopId })
			const res = await managerData({ shopId: useParams.value().shopId })
			const data = res.data
			// manageData.value = handleResponseParams(manageDataHeaderFields, manageDataHeader, data)
			// orderAnalyse.value = handleResponseParams(orderAnalyseHeaderFields, orderAnalyseHeader, data)
			manageData.value = data.businessData
			orderAnalyse.value = data.channelData
		} catch (e) {
			console.log("🚀 ~ getOperatingProfitData ~ e:", e)
		}
	}

	/**
	 * @description: 处理表格返回值
	 */
	const handleResponseParams = (fields : Record<string, any>, headers : any[], data : Object) => {
		let arr = []
		for (const [key, value] of Object.entries(fields)) {
			const row = {
				dataName: value
			}
			headers.forEach((item : dataType) => {
				if (item.prop != 'dataName') {
					row[item.prop] = data[key]?.[item.prop]
				}
			})
			arr.push(row)
		}
		return arr
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		return true
	}
	/**
	 * @description: 保存业主盈利
	 */
	const saveOperatingProfitEvaluation = async (draftType : number = 0) : Promise<any> => {
		try {
			const res = await saveOperatingProfitEvaluationApi({
				...form.value,
				...useParams.value(),
				draftType,
			})
			if (draftType == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ saveOperatingProfitEvaluation ~ error:", error)
		}
	}
	defineExpose({
		saveOperatingProfitEvaluation,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			margin-bottom: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}
	}
</style>