<template>
	<view class="fl radio">
		<slot name="default">
			<text class="radio-title" v-if="title">{{ title }}</text>
		</slot>
		<view class="radio-content">
			<view class="fr radio-group" v-if="isSelected">
				<text class="desc">{{ leftText }}</text>
				<!--<view>-->
				<!--	<up-radio-group v-model="valueText" shape="circle" usedAlone :size="size" activeColor="#C35943"-->
				<!--		inactiveColor="#C35943" labelColor="#1F2428">-->
				<!--		<up-radio v-for="item in radioGroup" :key="item.value" :label="item.label" :name="item.value"-->
				<!--			:labelSize="size" />-->
				<!--	</up-radio-group>-->
				<!--</view>-->
        <radio-group @change="radioChange" class="c-radio-group">
          <label class="c-radio_label" v-for="(item, index) in radioGroup" :key="item.value">
            <view class="c-radio">
              <radio :value="item.value" :checked="item.value == valueText" />
              <view class="label">{{item.label}}</view>
            </view>
          </label>
        </radio-group>
			</view>
			<view class="fr-textarea" v-if="valueText == 0 && isItAssociatedWithInput">
				<cTextareaVue v-model="descText" count :maxlength="maxlength" :placehodler="placeholder"
					:background="background" />
			</view>
			<uploadImageGroupVue v-if="isUpload" @change="onChange" v-bind="$attrs" auto-hide-button />
			<template v-bind="$attrs"></template>
			<slot name="footer"></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed } from 'vue'
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import uploadImageGroupVue from './uploadImageGroup.vue';

	type radioType = { label : string, value : string | number }
	type propsType = {
		value : string | number;
		title : string;
		maxlength : string | number;
		placeholder : string;
		background : string;
		isSelected : boolean;
		radioGroup : radioType[] | any,
		leftText : string;
		isUpload : string | number | boolean;
		isItAssociatedWithInput : boolean;
		[k : string] : any;
	};

  defineOptions({
    options: {
      styleIsolation: 'shared'
    }
  })

	const props = withDefaults(defineProps<Partial<propsType>>(), {
		value: '',
		title: '',
		isItAssociatedWithInput: true,
		leftText: '是否合格',
		radioGroup: [{
			label: '合格',
			value: '1'
		}, { label: '不合格', value: '0' }],
		isSelected: true,
		maxlength: 500,
		isUpload: false,
		placeholder: '请列举不合格问题点…',
		background: '#fff',
	})
	const emits = defineEmits(['update:value', 'update:desc', 'changeImage'])
	const size = uni.upx2px(28)
	const valueText = computed({
		get() {
			return props.value
		},
		set(val) {
			emits('update:value', val)
		}
	})
	const descText = computed({
		get() {
			return props.desc
		},
		set(val) {
			emits('update:desc', val)
		}
	})
	/**
	 * @description: 中转 v-bind="$attrs" 不支持事件传递
	 */
	const onChange = (images : any[]) => {
		emits('changeImage', images)
	}
  const radioChange = (evt: any) => {
    for (let i = 0; i < props.radioGroup.length; i++) {
      if (props.radioGroup[i].value == evt.detail.value) {
        valueText.value = evt.detail.value
        break;
      }
    }
  }
</script>

<style lang="scss" scoped>
	.radio {
		padding-top: 40rpx;
		color: #1F2428;

		&-title {
			margin-bottom: 20rpx;
			color: #1F2428;
			font-weight: bold;
		}

		.desc {
			font-weight: bold;
		}

		.fr-textarea {
			margin-top: 20rpx;
		}

		&-group,
		.fr {
			justify-content: space-between;
		}

		&-group {
			padding: 0 16rpx;
		}

		&-content {
			padding: 20rpx 24rpx;
			border-radius: 15.38rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
		}
	}
  :deep() {
    .c-radio-group, .c-radio {
      display: flex;
      align-items: center;
    }
    .c-radio_label {
      &:first-child {
        margin-right: 40rpx;
      }

      .c-radio{
        .label{
          font-size: 28rpx;
          line-height: 40rpx;
          color: #1f2428;
        }
      }
    }
    radio .wx-radio-input {
      border-radius: 50%;
      width: 28rpx;
      height: 28rpx;
      box-sizing: border-box;
      bottom: 2rpx;
      border: 2rpx #C35943 solid;
    }
    radio .wx-radio-input.wx-radio-input-checked {
      background-color: white!important;
      border: none!important;
    }
    radio .wx-radio-input.wx-radio-input-checked::before {
      border-radius: 50%;
      text-align: center;
      color: #FFFFFF;
      width: 28rpx;
      height: 28rpx;
      font-size: 20rpx;
      line-height: 28rpx;
      background: #C35943;
      box-sizing: border-box;
      transform:translate(-50%, -50%) scale(1);
      -webkit-transform:translate(-50%, -50%) scale(1);
    }
  }
</style>