<!--
 * @Description: 培训&报销
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:55:11
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 10:15:08
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/trainingReimbursement.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fl block">
			<text class="block-title">培训</text>
			<view class="suggest">
				<view class="fl first-fl">
					<text class="suggest-title">业主:</text>
					<cTextareaVue v-model="form.ownerTrainContent" count maxlength="1000" background="#fff" placeholder="请输入培训内容..." />
				</view>
				<view class="fl">
					<text class="suggest-title">员工 (不少于2项)：</text>
					<cTextareaVue v-model="form.staffTrainContent" count maxlength="1000" background="#fff" placeholder="请输入培训内容..." />
					<uploadImageGroupVue imgLeftText="请上传培训照片" v-model:images="form.staffTrainPicUrl" required />
				</view>
			</view>
		</view>
		<view class="fl block">
			<text class="block-title">报销</text>
			<view class="fr radio-group">
				<text>是否业主报销</text>
				<view class="">
					<up-radio-group v-model="form.ownerReimbursableStatus" shape="circle" usedAlone :size="size"
						activeColor="#C35943" inactiveColor="#C35943" labelColor="#1F2428">
						<up-radio label="是" :name="1" :labelSize="size" />
						<up-radio label="否" :name="0" :labelSize="size" />
					</up-radio-group>
				</view>
			</view>
			<view class="fr fee-total" v-if="form.ownerReimbursableStatus == 1">
				<view>
					<text>合计</text>
					<text class="small">({{ totalText }})</text>
				</view>
				<text class="fee">{{ totalFee.toFixed(2) }}元</text>
			</view>
			<template v-if="form.ownerReimbursableStatus == 1">
				<view class="fee-item" v-for="item in dataList" :key="item.menuCode">
					<view class="fr">
						<text>{{item.menuValue}}</text>
						<view class="fr">
							<input v-model="item.reimbursableAmount" type="digit" placeholder="请输入金额(元)" class="input-fee"
								placeholder-style="font-size: 24rpx;color:979A9A;">
							<text class="unit" v-if="item.reimbursableAmount">元</text>
						</view>
					</view>
					<uploadImageGroupVue v-model:images="item.reimbursableVoucher" imgLeftText="请上传凭证照片"
						:required="item.reimbursableAmount > 0" />
				</view>
			</template>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed, PropType, watch } from 'vue'
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import uploadImageGroupVue from './uploadImageGroup.vue';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { getTrainReimbursableInfo as getTrainReimbursableInfoApi, saveTrainReimbursableInfo as saveTrainReimbursableInfoApi } from '../api'
	import { TRAINING_REIMBURSEMENT_CODE } from '../enums';
	import { showToast } from '@/utils/util';
	type formType = {
		ownerTrainContent : string;
		staffTrainContent : string;
		staffTrainPicUrl : string[];
		ownerReimbursableStatus : number;
	}
	type dataItemType = {
		menuCode : string;
		menuValue : string;
		reimbursableAmount ?: number;
		reimbursableVoucher ?: any
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: String
	})
	const { useParams } = usePageParams(props)
	const size = uni.upx2px(28)
	const form = ref<Partial<formType>>({})
	const dataList = ref<Partial<dataItemType[]>>([])
	const totalText = computed(() => dataList.value.map((item : dataItemType) => item.menuValue).join('+'))
	const totalFee = computed(() => dataList.value.map((item : dataItemType) => item.reimbursableAmount || 0).reduce((prev : number, cur : number) => prev + Number(cur), 0))
	watch(() => props.menuCode, (v : string) => {
		if (v == TRAINING_REIMBURSEMENT_CODE) {
			if (!dataList.value?.length) {
				getTrainReimbursableInfo()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 获取数据
	 */
	const getTrainReimbursableInfo = async () : Promise<void> => {
		try {
			const res = await getTrainReimbursableInfoApi({
				...useParams.value(),
				code: TRAINING_REIMBURSEMENT_CODE
			})
			const { list, trainReimbursableContent } = res.data
			const formValue = trainReimbursableContent || {}
			if (formValue.staffTrainPicUrl) {
				formValue.staffTrainPicUrl = formValue.staffTrainPicUrl.split(',').map((item : string) => {
					return {
						url: item
					}
				})
			} else {
				formValue.staffTrainPicUrl = []
			}
			form.value = { ...formValue }
			dataList.value = list.map((item : dataItemType) => {
				return {
					...item,
					reimbursableVoucher: item.reimbursableVoucher ? item.reimbursableVoucher.split(',').map((r : string) => {
						return {
							url: r,
							code: item.menuCode
						}
					}) : []
				}
			})
		} catch (error) {
			console.log("🚀 ~ getTrainReimbursableInfo ~ error:", error)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		if (!form.value.ownerTrainContent) {
			showToast('请填写培训&报销-业主培训内容')
			return
		}
		if (!form.value.staffTrainContent) {
			showToast('请填写培训&报销-员工培训内容')
			return
		}
		if (!form.value.staffTrainPicUrl.length) {
			showToast('请选择培训&报销-员工培训图片')
			return
		}
		if (form.value.ownerReimbursableStatus != 0 && form.value.ownerReimbursableStatus != 1) {
			showToast('请选择培训&报销-是否报销')
			return
		}
		if (form.value.ownerReimbursableStatus == 1) {
			for (let i = 0, len = dataList.value.length; i < len; i++) {
				const dataItem = dataList.value[i]
				if ((dataItem.reimbursableAmount && !dataItem.reimbursableVoucher.length) || (!dataItem.reimbursableAmount && dataItem.reimbursableVoucher.length)) {
					showToast(`培训&报销-${dataItem.menuValue}金额和图片必须同时包含`)
					return
				}
			}
		}
		return true
	}
	/**
	 * @description : 保存数据
	 */
	const saveTrainReimbursableInfo = async (draftType : number = 0) : Promise<any> => {
		try {
			const params : any & formType = {
				code: TRAINING_REIMBURSEMENT_CODE,
				...form.value,
				...useParams.value(),
				draftType,
			}
			params.staffTrainPicUrl = params.staffTrainPicUrl.map((item : any) => item.url)?.join(',')
			params.list = dataList.value.map((item : dataItemType) => {
				return {
					code: item.menuCode,
					reimbursableAmount: item.reimbursableAmount || undefined,
					reimbursableVoucher: item.reimbursableVoucher.map((item : any) => item.url)?.join(',')
				}
			})
			const res = await saveTrainReimbursableInfoApi(params)
			if (draftType == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ saveTrainReimbursableInfo ~ error:", error)
		}
	}

	defineExpose({
		saveTrainReimbursableInfo,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			padding-bottom: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;
		}

		&-content {
			padding: 20rpx 24rpx;
			border-radius: 15.38rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

			.boss-suggest {
				margin-top: 20rpx;
			}
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}

		.radio-group,
		.fee-total {
			margin-bottom: 20rpx;
			justify-content: space-between;
		}

		.fee-total {
			.small {
				font-size: 20rpx;
				color: #999;
			}

			.fee {
				color: #C35943;
			}
		}


		.fee-item {
			padding: 20rpx 24rpx;
			margin-bottom: 20rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 15.38rpx;

			&:last-child {
				margin-bottom: 0;
			}

			&>.fr {
				height: 80rpx;
				padding: 0 24rpx;
				justify-content: space-between;
				background-color: #fff;
				border-radius: 15.38rpx;
			}

			.unit {
				color: #C35943;
			}

			.input-fee {
				margin-right: 4rpx;
				text-align: right;
			}
		}
	}
</style>