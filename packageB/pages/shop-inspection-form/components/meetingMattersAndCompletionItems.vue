<!--
 * @Description: 会议事项及完成事项
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:45:10
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:59:28
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/meetingMattersAndCompletionItems.vue
-->

<template>
	<view class="sheet">
		<view class="fr block">
			<text class="block-title">门店名称：</text>
			<text class="shop">{{ shopInfo.shopId }} {{ shopInfo.shopName }}</text>
		</view>
		<view class="fr block">
			<text class="block-title">门店管理负责人：</text>
			<input v-model="form.shopManager" type="text" placeholder="请填写门店管理负责人" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">参会人员：</text>
			<input v-model="form.attendees" type="text" placeholder="请填写参会人员" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">参会日期：</text>
			<text class="shop" @tap="dateShow = true">{{form.meetingDate }}</text>
		</view>
		<view class="fr block">
			<text class="block-title">参会时间：</text>
			<text class="shop" @tap="timeShow = true">{{form.meetingTime }}</text>
		</view>
		<view class="fl block">
			<text class="block-title">门店经营指导：</text>
			<cTextareaVue v-model="form.guidance" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">公司新政传递：</text>
			<cTextareaVue v-model="form.newPolicies" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">本次会议重点事项：</text>
			<cTextareaVue v-model="form.curImportent" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">上次会议重点事项：</text>
			<cTextareaVue v-model="form.lastItem" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">下次会议重点事项：</text>
			<cTextareaVue v-model="form.nextImportent" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title border-bottom">门店待提升薄弱项：</text>
			<view class="fl block-item">
				<text class="block-item__title">经营提升</text>
				<view class="radio-group">
					<up-checkbox-group v-model="form.bussUp" shape="circle" :size="size" activeColor="#C35943"
						inactiveColor="#C35943" labelColor="#1F2428">
						<up-checkbox v-for="item in businessImprovementEnums" :key="item.value" :label="item.label"
							:name="item.value" :labelSize="size" />
					</up-checkbox-group>
				</view>
			</view>
			<view class="fl block-item">
				<text class="block-item__title">营销支持</text>
				<view class="radio-group">
					<up-checkbox-group v-model="form.marketSupport" shape="circle" :size="size" activeColor="#C35943"
						inactiveColor="#C35943" labelColor="#1F2428">
						<up-checkbox v-for="item in marketingSupportEnums" :key="item.value" :label="item.label" :name="item.value"
							:labelSize="size" />
					</up-checkbox-group>
				</view>
			</view>
			<view class="fl block-item">
				<text class="block-item__title">品质管理</text>
				<view class="radio-group">
					<up-checkbox-group v-model="form.qualityManage" shape="circle" :size="size" activeColor="#C35943"
						inactiveColor="#C35943" labelColor="#1F2428">
						<up-checkbox v-for="item in qualityManagementEnums" :key="item.value" :label="item.label" :name="item.value"
							:labelSize="size" />
					</up-checkbox-group>
				</view>
			</view>
			<view class="fl block-item">
				<text class="block-item__title">其他</text>
				<view class="radio-group">
					<up-checkbox-group v-model="form.other" shape="circle" :size="size" activeColor="#C35943"
						inactiveColor="#C35943" labelColor="#1F2428">
						<up-checkbox v-for="item in otherEnums" :key="item.value" :label="item.label" :name="item.value"
							:labelSize="size" />
					</up-checkbox-group>
				</view>
			</view>
			<view class="fl block-item">
				<text class="block-item__title">其他未尽事项</text>
				<cTextareaVue v-model="form.otherNotDo" count maxlength="500" placeholder="请填写内容" />
			</view>
		</view>
	</view>
	<up-datetime-picker v-model="form.meetingTime" :show="timeShow" mode="time" confirmColor="#C35943"
		@cancel="timeShow = false" @confirm="onChangeTime"></up-datetime-picker>
	<up-datetime-picker v-model="form.meetingDate" confirmColor="#C35943" @cancel="dateShow = false" @confirm="onChangeDate"
		:show="dateShow" mode="date"></up-datetime-picker>
</template>

<script setup lang="ts">
	import { PropType, ref, watch } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import { pageOptionsType } from '../types';
	import { getPatrolMeetingCompletion as getPatrolMeetingCompletionApi, savePatrolMeetingCompletion as savePatrolMeetingCompletionApi, getPatrolItemsAndCompletionTitle as getPatrolItemsAndCompletionTitleApi } from '../api'
	import { usePageParams } from '../hooks/usePageParams';
	import { qualityManagementEnums, marketingSupportEnums, businessImprovementEnums, otherEnums } from '../enums'
	import dayjs from 'dayjs'
	import { showToast } from '@/utils/util';
	import { MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE } from '../enums'
	type formType = {
		shopManager : string;
		attendees : string;
		meetingTime : string;
		guidance : string;
		newPolicies : string;
		curImportent : string;
		lastItem : string;
		nextImportent : string;
		bussUp : string;
		marketSupport : string[];
		qualityManage : string[];
		other : string[];
		otherNotDo : string;
		meetingDate : string;
		[k : string] : any
	}
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})

	const size = uni.upx2px(24)
	const { useParams } = usePageParams(props)
	const form = ref<Partial<formType>>({})
	const timeShow = ref(false)
	const dateShow = ref(false)
	const shopInfo = ref<any>({})
	const meetingDate = ref(dayjs().format('YYYY-MM-DD'))
	watch(() => props.menuCode, v => {
		if (v == MEETING_MATTERS_AND_COMPLETION_ITEMS_CODE) {
			getPatrolItemsAndCompletionTitle()
			if (form.value && JSON.stringify(form.value) == '{}') {
				getPatrolMeetingCompletion()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 选择日期
	 */
	const onChangeDate = (item : any) => {
		form.value.meetingDate = dayjs(item.value).format('YYYY-MM-DD')
		dateShow.value = false
	}
	/**
	 * @description: 选择时间
	 */
	const onChangeTime = (item : any) => {
		form.value.meetingTime = item.value
		timeShow.value = false
	}
	/**
	 * @description: 获取门店信息
	 */
	const getPatrolItemsAndCompletionTitle = async () => {
		try {
			const res = await getPatrolItemsAndCompletionTitleApi(useParams.value())
			shopInfo.value = res.data || {}
		} catch (error) {
			console.log("🚀 ~ getPatrolItemsAndCompletionTitle ~ error:", error)
		}
	}
	/**
	 * @description:获取会议事项及完成事项
	 */
	const getPatrolMeetingCompletion = async () => {
		try {
			const res = await getPatrolMeetingCompletionApi(useParams.value())
			if (res.data) {
				const fn = (str : any) => {
					return str?.split(',').map((item : string | number, index : number) => {
						if (item == 1) {
							return index.toString()
						}
					})?.filter((item : string | undefined) => item) || ''
				}
				const bussUp = fn(res.data.bussUp)
				const marketSupport = fn(res.data.marketSupport)
				const qualityManage = fn(res.data.qualityManage)
				const other = fn(res.data.other)
				let meetingTime = dayjs(res.data.meetingTime)?.format('YYYY-MM-DD HH:mm')
				let meetingDate = ''
				if (meetingTime) {
					const [date, time] = meetingTime.split(' ')
					meetingDate = date
					meetingTime = time
				}
				form.value = {
					...res.data,
					bussUp,
					marketSupport,
					qualityManage,
					other,
					meetingTime,
					meetingDate
				}
			} else {
				form.value = {
					meetingDate: dayjs().format('YYYY-MM-DD'),
					meetingTime: dayjs().format('HH:mm')
				}
			}
		} catch (error) {
			console.log("🚀 ~ getPatrolMeetingCompletion ~ error:", error)
		}
	}
	/**
	 * @description: 处理请求数组的参数
	 */
	const fns = (source : any, data : string[]) => {
		return source.map((item : any) => {
			return data.includes(item.value) ? 1 : 0
		})
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		return true
	}
	/**
	 * @description: 保存数据
	 */
	const savePatrolMeetingCompletion = async (submissionStatus : number = 0) => {
		try {
			const params : any & formType = {
				...useParams.value(),
				...form.value,
			}
			if (params.meetingDate && params.meetingTime) {
				params.meetingTime = params.meetingDate + ' ' + params.meetingTime
				delete params.meetingDate
			}
			if (params.bussUp?.length) {
				params.bussUp = fns(businessImprovementEnums, params.bussUp).join(',')
			}
			if (params.marketSupport?.length) {
				params.marketSupport = fns(marketingSupportEnums, params.marketSupport).join(',')
			}
			if (params.qualityManage?.length) {
				params.qualityManage = fns(qualityManagementEnums, params.qualityManage).join(',')
			}
			if (params.other?.length) {
				params.other = fns(otherEnums, params.other).join(',')
			}
			const res = await savePatrolMeetingCompletionApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ savePatrolMeetingCompletion ~ error:", error)
		}
	}
	defineExpose({
		savePatrolMeetingCompletion,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		padding: 0 24rpx;
		color: #1F2428;
	}

	:deep() {
		.u-checkbox-group {
			justify-content: space-between;
		}
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&.border-bottom {
				border-bottom: 2rpx solid #F2F2F2;
			}

			&__input {
				text-align: right;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				padding-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}

		.block-item {
			margin-top: 40rpx;

			&__title {
				margin-bottom: 20rpx;
			}

			.radio-group {
				padding: 20rpx 24rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 15.38rpx;
			}
		}

	}
</style>