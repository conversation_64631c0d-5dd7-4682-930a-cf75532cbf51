<!--
 * @Description: 筹建指导工作表
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:09:00
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:55:38
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/constructionGuidanceWorksheet.vue
-->

<template>
	<view class="sheet">
		<view class="fr block">
			<text class="block-title">门店名称：</text>
			<text class="shop">{{ shopInfo.shopName }}</text>
		</view>
		<view class="fr block">
			<text class="block-title">区域：</text>
			<input v-model="form.region" type="text" placeholder="请填写区域" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">区总：</text>
			<input v-model="form.regionManager" type="text" placeholder="请填写区总" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">巡店人：</text>
			<text class="shop">{{ shopInfo.patrolManName }}</text>
		</view>
		<view class="fr block">
			<text class="block-title">巡店日期：</text>
			<text class="shop">{{ form.patrolDate ||  currentDate}}</text>
		</view>
		<view class="fl block">
			<view class="fl table">
				<view class="fr thead">
					<text class="td">巡店事项</text>
					<text class="td">工作完成情况</text>
				</view>
				<view class="fl tbody-bg" v-for="(item, index) in storeInspectionMatters" :key="index">
					<view class="fr tbody">
						<text class="td td-left">{{ item.title }}</text>
						<view class="fr td">
							<view class="relative">
								<picker mode="selector" :range="completionStatusData" :value="form[item.prop]" range-key="label"
									@change="e => onChangePicker(e, index)">
									{{ getItemName(form[item.prop], index) || '选择完成情况'}}
								</picker>
								<view class="fr icon">
									<up-icon name="arrow-down" size="10" color="#1F2428"></up-icon>
								</view>
							</view>
						</view>
					</view>
					<view class="feedback-input">
						<input v-model="form[item.value]" type="text" placeholder="请输入反馈内容..." class="feedback">
					</view>
				</view>
			</view>
		</view>
		<view class="fl block">
			<text class="block-title">筹建工作进展：</text>
			<cTextareaVue v-model="form.preparationProgress" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">周边市场：</text>
			<cTextareaVue v-model="form.aroundMarket" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">经营建议：</text>
			<cTextareaVue v-model="form.businessAdvice" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">业主意见和建议：</text>
			<cTextareaVue v-model="form.ownerAdvice" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="desc">门店业主对本次指导生产的费用是否给予报销？</text>
			<view class="radio-group__wrap">
				<view class="fr radio-group">
					<text>差旅费</text>
					<view class="">
						<up-radio-group v-model="form.ifTravelFee" shape="circle" usedAlone :size="size" activeColor="#C35943"
							inactiveColor="#C35943" labelColor="#1F2428">
							<up-radio label="是" name="1" :labelSize="size" />
							<up-radio label="否" name="0" :labelSize="size" />
						</up-radio-group>
					</view>
				</view>
				<view class="fr radio-group">
					<text>食宿费</text>
					<view class="">
						<up-radio-group v-model="form.ifFoodFee" shape="circle" usedAlone :size="size" activeColor="#C35943"
							inactiveColor="#C35943" labelColor="#1F2428">
							<up-radio label="是" name="1" :labelSize="size" />
							<up-radio label="否" name="0" :labelSize="size" />
						</up-radio-group>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, ref, watch } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { CONSTRUCTION_GUIDANCE_WORKSHEET_CODE } from '../enums';
	import { getItemsCompletionPrep as getItemsCompletionPrepApi, saveItemsCompletionPrep as saveItemsCompletionPrepApi, getPatrolItemsAndCompletionTitle as getPatrolItemsAndCompletionTitleApi } from '../api'
	import dayjs from 'dayjs';
	import { showToast } from '@/utils/util';
	type formType = {
		shopName : string;
		region : string;
		regionManager : string;
		patrolManName : string;
		ifTravelFee : string;
		ifFoodFee : string;
		ownerAdvice : string;
		businessAdvice : string;
		aroundMarket : string;
		preparationProgress : string;
		patrolDate : string;
		[k : string] : any
	}
	type propsType = {
		options : Partial<pageOptionsType>;
		menuCode : string;
	}
	const props = withDefaults(defineProps<propsType>(), {})
	const completionStatusData = [
		{ label: '未完成', value: '0' },
		{ label: '已完成', value: '1' }
	]
	const storeInspectionMatters = [
		{ title: '门店证照办理情况', prop: 'licenseSituationIsOk', value: 'licenseSituationBack' },
		{ title: '三方转移协议"签订情况', prop: 'tripartiteTransferIsOk', value: 'tripartiteTransferBack' },
		{ title: '授权委托书是否已按公司要求签订', prop: 'attorneyPowerIsOk', value: 'attorneyPowerBack' },
		{ title: '开业质检材料', prop: 'openingInspectionIsOk', value: 'openingInspectionBack' },
		{ title: '店长排巡情况', prop: 'shopManagerPatrolIsOk', value: 'shopManagerPatrolBack' },
	]
	const size = uni.upx2px(28)
	const { useParams } = usePageParams(props)
	const form = ref<Partial<formType>>({})
	const currentDate = ref(dayjs().format('YYYY-MM-DD'))
	const shopInfo = ref<any>({})
	watch(() => props.menuCode, (v : string | number) => {
		if (v == CONSTRUCTION_GUIDANCE_WORKSHEET_CODE) {
			getPatrolItemsAndCompletionTitle()
			if(form.value && JSON.stringify(form.value) == '{}') {
				getItemsCompletionPrep()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 完成情况
	 */
	const onChangePicker = (e : any, index : number) => {
		form.value[storeInspectionMatters[index].prop] = e.detail.value
	}
	/**
	 * @description: 完成情况中文
	 */
	const getItemName = (value : string, index : number) => {
		return completionStatusData.find(item => item.value == value)?.label
	}
	/**
	 * @description: 获取门店信息
	 */
	const getPatrolItemsAndCompletionTitle = async () => {
		try {
			const res = await getPatrolItemsAndCompletionTitleApi(useParams.value())
			shopInfo.value = res.data || {}
		} catch (error) {
			console.log("🚀 ~ getPatrolItemsAndCompletionTitle ~ error:", error)
		}
	}
	/**
	 * @description: 获取筹建记录数据
	 */
	const getItemsCompletionPrep = async () => {
		try {
			const res = await getItemsCompletionPrepApi({
				...useParams.value(),
				brandType: props.options.brandType
			})
			form.value = res.data || {}
		} catch (e) {
			console.log("🚀 ~ getItemsCompletionPrep ~ e:", e)
		}
	}
	/**
	 * @description : 校验
	 */
	const validate = () => {
		const fileds = ['region', 'regionManager', 'licenseSituationIsOk', 'licenseSituationBack', 'tripartiteTransferIsOk', 'tripartiteTransferBack', 'attorneyPowerIsOk', 'attorneyPowerBack', 'openingInspectionIsOk', 'openingInspectionBack', 'shopManagerPatrolIsOk', 'shopManagerPatrolBack']
		for (let i = 0, len = fileds.length; i < len; i++) {
			const key = fileds[i]
			const laststr = key.substring(key.length - 2)
			if (laststr == 'Ok') {
				if (form.value[key] != '0' && form.value[key] != 1) {
					const row = storeInspectionMatters.findIndex(item => item.prop == key)
					showToast(`巡店事项第${row + 1}行未选择完成情况`)
					return
				}
			} else {
				if (!form.value[key]) {
					showToast('筹建指导工作表存在未填写的内容')
					return
				}
			}
		}
		return true
	}
	/**
	 * @description : 保存筹建工作表
	 */
	const saveItemsCompletionPrep = async (submissionStatus : number) => {
		try {
			const params = {
				...useParams.value(),
				...form.value
			}
			const res = await saveItemsCompletionPrepApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveItemsCompletionPrep ~ e:", e)
		}
	}
	defineExpose({
		saveItemsCompletionPrep,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		padding: 0 24rpx;
		color: #1F2428;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		.feedback {
			padding: 0 24rpx;

			&-input {
				padding: 20rpx 0;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}

		.table {
			font-size: 22rpx;

			.thead {
				background: #ffb2a166;
				border-radius: 15.43rpx 15.43rpx 0 0;
			}

			.thead,
			.tbody {
				height: 84rpx;
			}

			.tbody-bg:nth-of-type(even) {
				background-color: #FFF9F7;
			}

			.tbody-bg:nth-of-type(odd) {
				background-color: #FFF3F0;
			}

			.td {
				width: 50%;
				text-align: center;

				&-left {
					padding: 0 20rpx;
				}

				.relative {
					.icon {
						position: absolute;
						right: -30rpx;
						top: 0;
						bottom: 0;
					}

					position: relative;
				}

				&.fr {
					justify-content: center;
				}

			}
		}

		.desc {
			margin-bottom: 20rpx;
		}

		.radio-group {
			justify-content: space-between;

			&:first-child {
				margin-bottom: 20rpx;
			}

			&__wrap {
				padding: 20rpx 40rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 20rpx;
			}
		}
	}
</style>