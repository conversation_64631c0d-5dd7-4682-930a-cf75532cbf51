<!--
 * @Description: 品质检查
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/qualityChecks.vue
-->
<template>
  <view>
    <view v-for="(item, index) in dataList" :key="item.trainId">
      <view class="fl sheet">
        <text class="fr block sheet-title">项目</text>
        <view class="fl sheet-item">
          <view class="fl block">
            <text class="block-title">项目：</text>
            <cTextareaVue v-model="item.project" count maxlength="500" placeholder="请描述质检项目" />
          </view>
        </view>
      </view>
      <view class="fl sheet">
        <text class="fr block sheet-title">分析及建议</text>
        <view class="fl sheet-item">
          <view class="fl block">
            <text class="block-title">分析及建议：</text>
            <cTextareaVue v-model="item.proposal" count maxlength="500" placeholder="请输入您的分析及建议" />
          </view>
        </view>
      </view>
      <view
        class="fr btn-row btn-row__del" v-if="dataList.length > 1 && index !== 0"
        @tap="deleteGuidanceProject(index)">
        <text class="fr symbol" style="color: #C35943;">-</text>
        删除项目
      </view>
    </view>
    <view class="fr btn-row btn-row__add" @tap="addKnowProject">
      <text class="fr symbol">+</text>
      新增项目
    </view>
  </view>
</template>

<script setup lang="ts">
import { watch, ref, PropType } from 'vue'
import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
import { getQualityCheckByOnline, saveQualityCheckByOnline } from '../api'
import { usePageParams } from '../hooks/usePageParams';
import { pageOptionsType } from '../types';
import { showToast } from '@/utils/util';
import { QUALITY_CHECKS_CODE } from '../enums'

type dataItemType = {
  project: string;
  proposal: string;
  [k: string]: any
}
const props = defineProps({
  options: {
    type: Object as PropType<pageOptionsType>,
    default: () => {
    }
  },
  menuCode: {
    type: String,
    default: ''
  }
})
const dataList = ref<Partial<dataItemType[]>>([])
const size = uni.upx2px(28)
const { useParams } = usePageParams(props)
/**
 * @description: 获取数据
 */
const getData = async (): Promise<void> => {
  const res = await getQualityCheckByOnline(useParams.value())
  if (res.data?.qualityList?.length) {
    dataList.value = res.data.qualityList
  } else {
    dataList.value = [{ project: '', proposal: '' }]
  }
}
/**
 * @description: 提交校验
 */
const validate = () => {
  return true
}
/**
 * @description: 保存公司新政传递
 */
const toSaveQualityCheckByOnline = async (submissionStatus: number = 0) => {
  try {
    const params = {
      ...useParams.value(),
      qualityList: dataList.value
    }
    if (submissionStatus == 1 && !validate()) return
    const res = await saveQualityCheckByOnline(params)
    if (submissionStatus == 0) {
      showToast('保存成功')
      return
    }
    return res
  } catch (e) {
    console.log("🚀 ~ 保存品质检查 (线上) ~ e:", e)
  }
}
/**
 * @description: 新增指导项目
 */
const addKnowProject = () => {
  dataList.value.push({} as any)
}
/**
 * @description: 删除指导项目
 */
const deleteGuidanceProject = (index: number) => {
  dataList.value.splice(index, 1)
}
watch(() => props.menuCode, (v: string | number) => {
  if (v == QUALITY_CHECKS_CODE) {
    if (!dataList.value?.length) {
      getData()
    }
  }
}, { immediate: true })
defineExpose({
  toSaveQualityCheckByOnline,
  validate
})
</script>

<style scoped lang="scss">
.sheet {
  overflow: hidden;
  padding: 0 40rpx 40rpx;
  margin: 0 20rpx 40rpx;
  color: #1F2428;
  background-color: #fff;
  border-radius: 20rpx;

  &-title {
    padding: 40rpx 0 !important;
    margin-bottom: 0 !important;
    font-size: 32rpx;
    font-weight: bold;
  }

  &-item {
    padding: 40rpx 24rpx;
    background: linear-gradient(203.5deg, rgba(#ffb2a1, .1) 0%, rgba(#f75e3b, .1) 100%);
    border-radius: 20rpx;

    :deep() {
      .desc {
        font-size: 26rpx;
      }
    }
  }
}

.block {
  padding: 28rpx 24rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;

  &.fr {
    justify-content: space-between;
  }

  &-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #1F2428;

    &__input {
      text-align: right;
    }
  }

  .feedback {
    padding: 0 24rpx;

    &-input {
      padding: 20rpx 0;
    }
  }

  &.fl {
    .block-title {
      flex-shrink: 0;
      margin-right: 20rpx;
      margin-bottom: 28rpx;
    }
  }

  :deep() {
    .textarea {
      font-size: 24rpx;
    }

    .upload-wrap {
      .desc {
        font-size: 26rpx;
      }
    }
  }

  .radio-group {
    justify-content: space-between;
    margin-bottom: 20rpx;

    &__label {
      font-size: 26rpx;
      font-weight: bold;
      color: #1F2428;
    }
  }
}

.btn-row {
  justify-content: center;
  height: 80rpx;
  margin: 0 20rpx 40rpx;
  font-weight: bold;
  border-radius: 200rpx;
  background: #ffffff;

  .symbol {
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
  }

  &__add {
    color: #1F2428;
  }

  &__del {
    color: #C35943;
  }
}
</style>