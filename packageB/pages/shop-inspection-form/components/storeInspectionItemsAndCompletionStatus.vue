<!--
 * @Description: 巡店事项及完成情况
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:09:00
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:52:46
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/storeInspectionItemsAndCompletionStatus.vue
-->

<template>
	<view class="sheet">
		<view class="fr block">
			<text class="block-title">门店名称：</text>
			<text class="shop">{{shopInfo.shopId + shopInfo.shopName }}</text>
		</view>
		<view class="fr block">
			<text class="block-title">巡店日期：</text>
			<text class="shop">{{ form.patrolDate || currentDate }}</text>
		</view>
		<view class="fl block">
			<view class="fl table">
				<view class="fr thead">
					<text class="td">巡店事项</text>
					<text class="td">工作完成情况</text>
				</view>
				<view class="fl tbody-bg" v-for="(item, index) in storeInspectionMatters" :key="index">
					<view class="fr tbody">
						<text class="td td-left">{{ item.title }}</text>
						<view class="fr td">
							<view class="relative">
								<picker mode="selector" :range="completionStatusData" :value="form[item.prop]" range-key="label"
									@change="e => onChange(e, index)">
									{{ getItemName(form[item.prop], index) || '选择完成情况'}}
								</picker>
								<view class="fr icon">
									<up-icon name="arrow-down" size="10" color="#1F2428"></up-icon>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="fl block">
			<text class="block-title">本次巡店重点事项：</text>
			<cTextareaVue v-model="form.curImportentItem" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="block-title">下次巡店重点事项：</text>
			<cTextareaVue v-model="form.nextImportentItem" count maxlength="500" placeholder="请填写内容" />
		</view>
		<view class="fl block">
			<text class="desc">门店业主对本次指导生产的费用是否给予报销？</text>
			<view class="radio-group__wrap">
				<view class="fr radio-group">
					<text>差旅费</text>
					<view class="">
						<up-radio-group v-model="form.ifTravelFee" shape="circle" usedAlone :size="size" activeColor="#C35943"
							inactiveColor="#C35943" labelColor="#1F2428">
							<up-radio label="是" name="1" :labelSize="size" />
							<up-radio label="否" name="0" :labelSize="size" />
						</up-radio-group>
					</view>
				</view>
				<view class="fr radio-group">
					<text>食宿费</text>
					<view class="">
						<up-radio-group v-model="form.ifFoodFee" shape="circle" usedAlone :size="size" activeColor="#C35943"
							inactiveColor="#C35943" labelColor="#1F2428">
							<up-radio label="是" name="1" :labelSize="size" />
							<up-radio label="否" name="0" :labelSize="size" />
						</up-radio-group>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, ref, watch } from 'vue'
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE } from '../enums';
	import { getPatrolItemsAndCompletionTitle as getPatrolItemsAndCompletionTitleApi, getPatrolItemsAndCompletionForJY as getPatrolItemsAndCompletionForJYApi, savePatrolItemsAndCompletionForJY as savePatrolItemsAndCompletionForJYApi } from '../api'
	import { showToast } from '@/utils/util';
	import dayjs from 'dayjs';
	type formType = {
		curImportentItem : string;
		nextImportentItem : string;
		qoIsOk : string | number;
		iccIsOk : string | number;
		trainGuideIsOk : string | number;
		qredIsOk : string | number;
		newPolicies : string | number;
		ifTravelFee : string | number;
		ifFoodFee : string | number;
		[k : string] : any
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: String
	})
	const storeInspectionMatters = [
		{ title: '运营品质10项检查', prop: 'qoIsOk' },
		{ title: '内控合规检查', prop: 'iccIsOk' },
		{ title: '酒店培训', prop: 'trainGuideIsOk' },
		{ title: '谈话记录表', prop: 'qredIsOk' },
		{ title: '公司新政传递', prop: 'newPolicies' },
	]
	const completionStatusData = [
		{ label: '不合格', value: '0' },
		{ label: '合格', value: '1' },
	]
	const { useParams } = usePageParams(props)
	const size = uni.upx2px(28)
	const form = ref<Partial<formType>>({})
	const shopInfo = ref<any>({})
	const currentDate = ref(dayjs().format('YYYY-MM-DD'))
	const onChange = (e : any, index : number) => {
		form.value[storeInspectionMatters[index].prop] = e.detail.value
	}
	watch(() => props.menuCode, (v : string) => {
		if (v == STORE_INSPECTION_ITEMS_AND_COMPLETION_STATUS_CODE) {
			getPatrolItemsAndCompletionTitle()
			if (form.value && JSON.stringify(form.value) == '{}') {
				getPatrolItemsAndCompletionForJY()
			}
		}
	}, { immediate: true })
	/**
		 * @description: 完成情况中文
		 */
	const getItemName = (value : string, index : number) => {
		return completionStatusData.find(item => item.value == value)?.label
	}
	/**
	 * @description: 获取门店信息
	 */
	const getPatrolItemsAndCompletionTitle = async () => {
		try {
			const res = await getPatrolItemsAndCompletionTitleApi(useParams.value())
			shopInfo.value = res.data || {}
		} catch (error) {
			console.log("🚀 ~ getPatrolItemsAndCompletionTitle ~ error:", error)
		}
	}
	/**
	 * @description: 获取数据
	 */
	const getPatrolItemsAndCompletionForJY = async () : Promise<void> => {
		try {
			const res = await getPatrolItemsAndCompletionForJYApi({ ...useParams.value() })
			form.value = res.data || {}
		} catch (e) {
			console.log("🚀 ~ getPatrolItemsAndCompletionForJY ~ e:", e)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		const fields = ['curImportentItem', 'nextImportentItem', 'qoIsOk', 'iccIsOk', 'trainGuideIsOk', 'qredIsOk', 'newPolicies', 'ifTravelFee', 'ifFoodFee']
		for (let i = 0, len = fields.length; i < len; i++) {
			const key = fields[i]
			const laststr = key.substring(key.length - 2)
			if (laststr == 'Ok') {
				const data = storeInspectionMatters.find(item => item.prop == key)
				if (form.value[key] != 0 && form.value[key] != 1) {
					showToast(`巡店事项及完成情况: ${data?.title}未选择完成情况`)
					return
				}
			}
			if (key == 'newPolicies') {
				if (form.value[key] != 0 && form.value[key] != 1) {
					showToast(`巡店事项及完成情况：公司新政传递未选择完成情况`)
					return
				}
			}
			if (!form.value[key]) {
				console.log("🚀 ~ validate ~ key:", key)

				showToast(`巡店事项及完成情况存在未填写的项`)
				return
			}
		}
		return true
	}
	/**
	 * @description: 保存巡店事项及完成情况
	 */
	const savePatrolItemsAndCompletionForJY = async (submissionStatus : number) : Promise<any> => {
		try {
			const params = {
				...form.value,
				...useParams.value(),
			}
			const res = await savePatrolItemsAndCompletionForJYApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ savePatrolItemsAndCompletionForJY ~ e:", e)
		}
	}

	defineExpose({
		savePatrolItemsAndCompletionForJY,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		padding: 0 24rpx;
		color: #1F2428;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			flex-shrink: 0;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}

		.table {
			font-size: 22rpx;

			.thead {
				background: #ffb2a166;
				border-radius: 15.43rpx 15.43rpx 0 0;
			}

			.thead,
			.tbody {
				height: 84rpx;
			}

			.tbody-bg:nth-of-type(even) {
				background-color: #FFF9F7;
			}

			.tbody-bg:nth-of-type(odd) {
				background-color: #FFF3F0;
			}

			.td {
				width: 50%;
				text-align: center;

				&-left {
					padding: 0 20rpx;
				}

				.relative {
					.icon {
						position: absolute;
						right: -30rpx;
						top: 0;
						bottom: 0;
					}

					position: relative;
				}

				&.fr {
					justify-content: center;
				}

			}
		}

		.desc {
			margin-bottom: 20rpx;
		}

		.radio-group {
			justify-content: space-between;

			&:first-child {
				margin-bottom: 20rpx;
			}

			&__wrap {
				padding: 20rpx 40rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 20rpx;
			}
		}
	}
</style>