<!--
 * @Description: 内控合规检查
 * @Author: gu_shiyuan"
 * @Date: 2024-12-13 09:05:42
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:24:09
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/internalControlComplianceInspection.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fr tip">
			<up-icon name="info-circle" color="#C35943" size="12"></up-icon>
			<view class="fl">
				<text class="fr tip-text">提醒</text>
				<text class="tip-desc tip-desc__first">1、内容合规检查表单所有内容均做了必填项校验，请注意“是否合格选项、检查情况内容、照片”都需要填完整；</text>
				<text class="tip-desc">2、前厅管理-2：若不合格，需在”检查情况“位置，写明门店线下房间数、房间号并拍照取证。</text>
			</view>
		</view>
		<view class="fl block" v-for="(item, index) in tripList" :key="item.menuCode">
			<text class="block-title">{{ item.menuValue }}</text>
			<view v-for="(r, i) in item.menus" :key="r.code">
				<radioVue :title="r.value" v-model:value="r.isAllRight" :isItAssociatedWithInput="false"></radioVue>
				<view class="fr block-btn__group">
					<view class="fr block-btn" @tap="r.isOpen = !r.isOpen">
						<text class="block-btn__text">检查情况</text>
					</view>
					<view class="fr block-btn" v-if="r.picUrls.length < 9" @tap="onChooseImage(index, i)">
						<image
							src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-12/1733971763935.png"
							mode="aspectFill" lazy-load class="btn-upload__icon"></image>
						<text class="block-btn__text">图片</text>
					</view>
				</view>
				<view class="upload-img__text" v-if="r.isOpen">
					<cTextareaVue v-model="r.checkDetail" count maxlength="500" placeholder="请输入检查情况"
						background="linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);">
					</cTextareaVue>
				</view>
				<view class="upload-img__group" v-if="r.picUrls">
					<uploadImageGroupVue :show-btn="false" v-model:images="r.picUrls" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	import type { dataType } from '@/types/index.d'
	import radioVue from './radio.vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import uploadImageGroupVue from './uploadImageGroup.vue';
	import { getInternalControlCheckInfo as getInternalControlCheckInfoApi, saveInternalCheck as saveInternalCheckApi } from '../api'
	import { usePageParams } from '../hooks/usePageParams'
	import type { pageOptionsType } from '../types'
	import {
		INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE,
	} from '../enums';
	import { showToast } from '@/utils/util';
	import { useUpload } from '../../../hooks/useUpload';
	type propsType = {
		options : Partial<pageOptionsType>;
		menuCode : string;
	}
	const props = withDefaults(defineProps<propsType>(), {})
	const tripList = ref<dataType[]>([])
	const { useParams } = usePageParams(props)
	const { batchUpload } = useUpload()
	watch(() => props.menuCode, (v : string) => {
		if (v == INTERNAL_CONTROL_COMPLIANCE_INSPECTION_CODE) {
			if (!tripList.value?.length) {
				getInternalControlCheckInfo()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 点击上传图片
	 * @param {pIndex} 父下标
	 * @param {sIndex} 子下标   
	 */
	const onChooseImage = async (pIndex : number, sIndex : number) => {
		if (tripList.value[pIndex].menus[sIndex].picUrls.length >= 9) {
			showToast(`最多上传${9}张`)
			return
		}
		const filePaths = await batchUpload(9 - tripList.value[pIndex].menus[sIndex].picUrls.length)
		tripList.value[pIndex].menus[sIndex].picUrls.push(...filePaths.map((item : string) => {
			return {
				code: tripList.value[pIndex].menus[sIndex].code,
				url: item
			}
		}))
	}
	/**
	 * @description: 获取内控合规检查
	 */
	const getInternalControlCheckInfo = async () => {
		const res = await getInternalControlCheckInfoApi({
			...useParams.value(),
			code: props.options.brandType == '1006' ? '100602' : '1001',
		})
		tripList.value = res.data.map((item : dataType) => {
			return {
				...item,
				menus: item.menus.map((r : any) => {
					return {
						...r,
						isOpen: !!r.checkDetail,
						picUrls: r.picUrl ? r.picUrl.split(',').map((img : string) => {
							return {
								code: r.code,
								url: img
							}
						}) : []
					}
				})
			}
		})
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
    if (tripList.value.length <= 0) {
      showToast(`请填写内控合规检查`)
      return
    }
		for (let i = 0, len = tripList.value.length; i < len; i++) {
			const dataItem = tripList.value[i]
			for (let k = 0, sLen = dataItem.menus.length; k < sLen; k++) {
				const sDataItem = dataItem.menus[k]
				if (sDataItem.isAllRight != 0 && sDataItem.isAllRight != 1) {
					showToast(`内控合规检查-${dataItem.menuValue}第${k + 1}行存在未选择的项`)
					return
				}
				if (sDataItem.isAllRight == 0 && !sDataItem.checkDetail) {
					showToast(`内控合规检查-${dataItem.menuValue}第${k + 1}行存在未填写的项`)
					return
				}
				if (!sDataItem.picUrls.length) {
					showToast(`内控合规检查-${dataItem.menuValue}第${k + 1}行存在未上传的图片`)
					return
				}
			}
		}
		return true
	}
	/**
	 * @description : 保存内控合规检查
	 */
	const saveInternalCheck = async (draftType : number = 0) : Promise<any> => {
		const params = {
			code: props.options.brandType == '1006' ? '100602' : '1001',
			...useParams.value(),
		}
		params.infoList = tripList.value.reduce((prev, cur) => prev.concat(cur?.menus || []), []).map((item : any) => {
			return {
				dcmCode: item.code,
				picUrl: item.picUrls?.map((r : any) => r.url)?.join(','),
				isAllRight: item.isAllRight,
				checkDetail: item.checkDetail
			}
		})
		const res = await saveInternalCheckApi(params)
		if (draftType == 0) {
			showToast('保存成功')
			return
		}
		return res
	}
	defineExpose({
		saveInternalCheck,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.tip {
		align-items: baseline;
		padding: 16rpx 20rpx;
		margin-bottom: 20rpx;
		border-radius: 20rpx;
		background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

		&>.fl {
			margin-left: 8rpx;
		}

		&-text {
			margin-bottom: 16.44rpx;
			font-size: 22rpx;
			color: #C35943;
			font-weight: bold;
		}

		&-desc {
			font-size: 22rpx;
			color: #1f2428b3;

			&__first {
				margin-bottom: 40rpx;
			}
		}
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #F2F2F2;

			&__desc {
				margin-bottom: 40rpx;
				font-weight: bold;
			}
		}

		.upload-tip {
			display: block;
			margin-top: 20rpx;
			font-size: 24rpx;
			color: #999;
		}

		.block-btn {
			padding: 10rpx 20rpx;
			font-weight: bold;
			background: linear-gradient(203.5deg, rgba(#ffb2a1, .3) 0%, rgba(#f75e3b, .3) 100%);
			border-radius: 552rpx;

			&:first-child {
				margin-right: 28rpx;
			}

			&__text {
				color: #C35943;
			}

			&__group {
				margin-top: 20rpx;
				justify-content: flex-end;
			}

			.btn-upload__icon {
				flex-shrink: 0;
				width: 32rpx;
				height: 32rpx;
				margin-right: 8rpx;
				vertical-align: middle;
			}
		}

		.upload-img__group,
		.upload-img__text {
			margin-top: 20rpx;
		}
	}
</style>