<!--
 * @Description: 员工热爱ss
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:56:08
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:56:58
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/employeesLove.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fl block">
			<text class="block-title">管理层</text>
			<view class="block-content">
				<up-radio-group v-model="form.shopManagerRole" shape="circle" usedAlone :size="size" activeColor="#C35943"
					inactiveColor="#C35943" labelColor="#1F2428">
					<up-radio label="公司方店长" :name="1" :labelSize="size" />
					<up-radio label="业主方店长" :name="2" :labelSize="size" />
				</up-radio-group>
				<view class="boss-suggest" v-if="form.shopManagerRole == 2">
					<cTextareaVue v-model="form.ownerShopManagerComment" placeholder="工作评价记录（必填）" count maxlength="1000"
						background="#fff" />
				</view>
			</view>
		</view>
		<view class="fl block">
			<text class="block-title employee">员工</text>
			<radioVue v-for="(item, index) in dataList" :key="item.menuCode" :title="item.menuValue" :radioGroup="[{
			label: '合格',
			value: 1
		}, { label: '不合格', value: 0 }]" :is-upload="item.photoType == 1 && item.qualificationStatus == 0"
				v-model:value="item.qualificationStatus" :query="useParams(item.menuCode)" v-model:images="item.picUrls"
				v-model:desc="item.underproofDesc" @changeImage="e => onChangeImage(e, index)" />
		</view>
		<view class="fl block">
			<text class="block-title">跟进建议</text>
			<view class="suggest">
				<view class="fl first-fl">
					<text class="suggest-title">店长：</text>
					<cTextareaVue v-model="form.shopManagerProposal" count maxlength="500" background="#fff"
						placeholder="请基于实际情况进行填写" />
				</view>
				<view class="fl">
					<text class="suggest-title">员工：</text>
					<cTextareaVue v-model="form.staffProposal" count maxlength="500" background="#fff"
						placeholder="请基于实际情况进行填写（编制、食宿、工服）" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { PropType, ref, watch } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import radioVue from './radio.vue';
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { getEmployeeManagementInfo as getEmployeeManagementInfoApi, saveEmployeeManagementInfo as saveEmployeeManagementInfoApi } from '../api'
	import { showToast } from '@/utils/util';
	import { EMPLOYEES_LOVE_CODE } from '../enums'
	type formType = {
		shopManagerRole : string | number;
		ownerShopManagerComment : string;
		shopManagerProposal : string;
		staffProposal : string;
	}
	type listItemType = {
		menuCode : string;
		menuValue : string;
		photoType : number,
		underproofDesc : string;
		qualificationStatus ?: number,
		picUrl ?: string;
		[k : string] : any
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	const { useParams } = usePageParams(props)
	const size = uni.upx2px(28)
	const form = ref<Partial<formType>>({})
	const dataList = ref<listItemType[]>([])
	watch(() => props.menuCode, (v : string) => {
		if (v == EMPLOYEES_LOVE_CODE) {
			if (!dataList.value?.length) {
				getEmployeeManagementInfo()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 图片回调
	 */
	const onChangeImage = (e : any[], index : number) => {
		dataList.value[index].picUrls = e
	}
	/**
	 * @description: 获取数据
	 */
	const getEmployeeManagementInfo = async () : Promise<void> => {
		try {
			const res = await getEmployeeManagementInfoApi({ code: EMPLOYEES_LOVE_CODE, ...useParams.value() })
			const { employeeManagementProposal, list } = res.data
			form.value = employeeManagementProposal || {}
			dataList.value = list?.map((item : listItemType) => {
				return {
					...item,
					picUrls: item.picUrl ? item.picUrl.split(',').map((r : string) => {
						return {
							code: item.menuCode,
							url: r
						}
					}) : []
				}
			})
		} catch (error) {
			console.log("🚀 ~ getEmployeeManagementInfo ~ error:", error)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		if (!form.value.shopManagerRole) {
			showToast('请选择员工热爱-管理层店长')
			return
		}
		if (form.value.shopManagerRole == 2 && !form.value.ownerShopManagerComment) {
			showToast('请填写员工热爱-业主方评价')
			return
		}
		for (let i = 0, len = dataList.value.length; i < len; i++) {
			const dataItem = dataList.value[i]
			if (dataItem.qualificationStatus != 0 && dataItem.qualificationStatus != 1) {
				showToast(`请选择员工热爱-员工第${i + 1}项是否合格`)
				return
			}
			if (dataItem.qualificationStatus == 0 && !dataItem.underproofDesc) {
				showToast(`请填写员工热爱-员工第${i + 1}项不合格点`)
				return
			}
		}
		return true
	}
	/**
	 * @description: 保存数据
	 */
	const saveEmployeeManagementInfo = async (draftType : number = 0) : Promise<any> => {
		try {
			const params = {
				code: EMPLOYEES_LOVE_CODE,
				...form.value,
				...useParams.value(),
				draftType,
			}
			params.list = dataList.value.map((item : listItemType) => {
				return {
					code: item.menuCode,
					qualificationStatus: item.qualificationStatus,
					underproofDesc: item.qualificationStatus === 1 ? item.underproofDesc : '',
					picUrl: item.picUrls.map((r : any) => r.url)?.join(',')
				}
			})
			const res = await saveEmployeeManagementInfoApi(params)
			if (draftType == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ saveEmployeeManagementInfo ~ error:", error)
		}
	}
	defineExpose({
		saveEmployeeManagementInfo,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			padding-bottom: 40rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&.employee {
				border-bottom: 2rpx solid #F2F2F2;
			}
		}

		&-content {
			padding: 20rpx 24rpx;
			border-radius: 15.38rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

			.boss-suggest {
				margin-top: 20rpx;
			}
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}
	}
</style>