<!--
 * @Description: 酒店培训
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:09:00
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:46:33
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/hotelTraining.vue
-->

<template>
	<view>
		<view v-for="(item, index) in dataList" :key="item.trainId">
			<view class="fl sheet">
				<text class="fr block sheet-title">指导项目{{ index + 1}} </text>
				<view class="fl sheet-item">
					<view class="fr block">
						<text class="block-title">培训时间：</text>
						<picker mode="date" :value="item.time" @change="e => onChangeDate(e, index)">
							<text class="shop">{{ item.time }}</text>
						</picker>
					</view>
					<view class="fr block">
						<text class="block-title">培训指导人：</text>
						<input v-model="item.director" type="text" placeholder="请输入培训指导人" class="block-title__input"
							placeholder-style="font-size: 26rpx;color:#999;" />
					</view>
					<view class="fr block">
						<text class="block-title">培训项目名：</text>
						<input v-model="item.projectName" type="text" placeholder="请输入培训项目名" class="block-title__input"
							placeholder-style="font-size: 26rpx;color:#999;" />
					</view>
					<view class="fl block" style="margin-bottom: 0;">
						<text class="block-title">培训内容：</text>
						<cTextareaVue v-model="item.content" count maxlength="500" placeholder="请填写内容" />
					</view>
          <view v-if="options.brandType == '1006'">
            <uploadImageGroupVue imgLeftText="培训照片" v-model:images="item.picUrls" />
          </view>
					<view class="fr guidance-target__item" v-for="(r, i) in item.trainObjList" :key="i">
						<view class="fl guidance-target">
							<view class="fr guidance-target__row">
								<text class="guidance-target__label">指导对象：</text>
								<input v-model="r.guidanceObject" type="text" placeholder="对象姓名"
									placeholder-style="font-size: 26rpx;color:#999;" class="guidance-target__value">
							</view>
							<view class="fr">
								<text class="guidance-target__label">岗位：</text>
								<input v-model="r.position" type="text" placeholder="岗位"
									placeholder-style="font-size: 26rpx;color:#999;" class="guidance-target__value">
							</view>
						</view>
						<view class="fr icon-click__area">
							<image v-if="i == 0"
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-17/1734403298652.png"
								mode="aspectFill" lazy-load class="icon" @tap="addGuidanceObject(index)"></image>
							<image v-else
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-17/1734403361916.png"
								mode="aspectFill" lazy-load class="icon" @tap="deleteGuidanceObject(index, i)"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="fr btn-row btn-row__del" v-if="dataList.length > 1 && index !== 0"
				@tap="deleteGuidanceProject(index)">
				<text class="fr symbol" style="color: #C35943;">-</text>删除指导项目
			</view>
		</view>
		<view class="fr btn-row btn-row__add" @tap="addKnowProject">
			<text class="fr symbol">+</text>添加指导项目
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue'
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import uploadImageGroupVue from './uploadImageGroup.vue';
	import { pageOptionsType } from '../types';
	import { HOTEL_TRAINING_CODE } from '../enums'
	import { getTrainingDetail as getTrainingDetailApi, saveTrainingDetail as saveTrainingDetailApi } from '../api'
	import { usePageParams } from '../../../hooks/useUpload';
	import { showToast } from '@/utils/util';
	import dayjs from 'dayjs'
	type trainObjectItemType = {
		guidanceObject : string;
		position : string;
	}
	type dataItemType = {
		content : string;
		director : string;
		projectName : string;
		time : string;
		trainId : number;
		trainObjList : Partial<trainObjectItemType>[],
		trainTime : number;
		trainUrl : string;
		[k : string] : any
	}
	type propsType = {
		options : pageOptionsType,
		menuCode : string;
	}

	const props = withDefaults(defineProps<propsType>(), {})
	const dataList = ref<Partial<dataItemType>[]>([])
	const { useParams } = usePageParams(props)
	watch(() => props.menuCode, (v : string) => {
		if (v == HOTEL_TRAINING_CODE) {
			if (!dataList.value?.length) {
				getTrainingDetail()
			}
		}
	}, { immediate: true })
	/**
	 * @description:选择日期
	 */
	const onChangeDate = (e : any, index : number) => {
		dataList.value[index].time = e.detail.value
	}
	/**
	 * @description: 添加指导对象
	 */
	const addGuidanceObject = (index : number) => {
		dataList.value[index].trainObjList.push({})
	}
	/**
	 * @description: 删除指导对象
	 */
	const deleteGuidanceObject = (pIndex : number, sIndex : number) => {
		dataList.value[pIndex].trainObjList.splice(sIndex, 1)
	}
	/**
	 * @description: 新增指导项目
	 */
	const addKnowProject = () => {
		dataList.value.push({ picUrls: [], time: dayjs().format('YYYY-MM-DD'), trainObjList: [{}] } as any)
	}
	/**
	 * @description: 删除指导项目
	 */
	const deleteGuidanceProject = (index : number) => {
		dataList.value.splice(index, 1)
	}
	/**
	 * @description: 获取酒店培训s
	 */
	const getTrainingDetail = async () : Promise<void> => {
		try {
			const res = await getTrainingDetailApi(useParams.value())
			if (res.data?.length) {
				dataList.value = res.data.map((item : dataItemType) => {
					return {
						...item,
						trainObjList: item.trainObjList?.length ? item.trainObjList : [{}],
						picUrls: item.trainUrl ? item.trainUrl.split(',').map((r : string) => ({ url: r })) : []
					}
				})
			} else {
				dataList.value = [{ trainObjList: [{}], picUrls: [], time: dayjs().format('YYYY-MM-DD') }]
			}
		} catch (e) {
			console.log("🚀 ~ getTrainingDetail ~ e:", e)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
    if (dataList.value.length <= 0) {
      showToast(`请填写酒店培训`)
      return
    }
		for (let i = 0, len = dataList.value.length; i < len; i++) {
			const dataItem = dataList.value[i]
			if (!dataItem.director) {
				showToast(`请填写酒店培训-指导项目${i + 1}培训指导人`)
				return
			}
			if (!dataItem.projectName) {
				showToast(`请填写酒店培训-指导项目${i + 1}培训项目名`)
				return
			}
			if (!dataItem.content) {
				showToast(`请填写酒店培训-指导项目${i + 1}培训内容`)
				return
			}
			if (props.options.brandType == '1006' && !dataItem.picUrls.length) {
				showToast(`请选择酒店培训-指导项目${i + 1}培训照片`)
				return
			}
			for (let k = 0, sLen = dataItem.trainObjList.length; k < sLen; k++) {
				const sDataItem = dataItem.trainObjList[k]
				if (!sDataItem.guidanceObject || !sDataItem.position) {
					showToast(`请填写酒店培训-指导项目${i + 1}指导项目对象`)
					return
				}
			}
		}
		return true
	}
	/**
	 * @description: 保存酒店培训数据
	 */
	const saveTrainingDetail = async (submissionStatus : number) => {
		try {
			const params = {
				...useParams.value(),
				projectList: dataList.value.map((item : Partial<dataItemType>) => {
					return {
						trainId: item.trainId,
						projectName: item.projectName,
						time: item.time,
						content: item.content,
						director: item.director,
						trainUrl: item.picUrls?.map((r : any) => r.url)?.join(','),
						trainObjList: item.trainObjList
					}
				})
			}
			const res = await saveTrainingDetailApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return res
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveTrainingDetail ~ e:", e)
		}
	}

	defineExpose({
		saveTrainingDetail,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		overflow: hidden;
		padding: 0 40rpx 40rpx;
		margin: 0 20rpx 40rpx;
		color: #1F2428;
		background-color: #fff;
		border-radius: 20rpx;

		&-title {
			padding: 40rpx 0 !important;
			margin-bottom: 0 !important;
			font-size: 32rpx;
			font-weight: bold;
		}

		&-item {
			padding: 40rpx 24rpx;
			background: linear-gradient(203.5deg, rgba(#ffb2a1, .1) 0%, rgba(#f75e3b, .1) 100%);
			border-radius: 20rpx;

			:deep() {
				.desc {
					font-size: 26rpx;
				}
			}
		}
	}

	.block {
		padding: 28rpx 24rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 26rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		.feedback {
			padding: 0 24rpx;

			&-input {
				padding: 20rpx 0;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}

		.table {
			font-size: 22rpx;

			.thead {
				background: #ffb2a166;
				border-radius: 15.43rpx 15.43rpx 0 0;
			}

			.thead,
			.tbody {
				height: 84rpx;
			}

			.tbody-bg:nth-of-type(even) {
				background-color: #FFF9F7;
			}

			.tbody-bg:nth-of-type(odd) {
				background-color: #FFF3F0;
			}

			.td {
				width: 50%;
				text-align: center;

				&-left {
					padding: 0 20rpx;
				}

				.relative {
					.icon {
						position: absolute;
						right: -30rpx;
						top: 0;
						bottom: 0;
					}

					position: relative;
				}

				&.fr {
					justify-content: center;
				}

			}
		}

		.desc {
			margin-bottom: 20rpx;
		}

		.radio-group {
			justify-content: space-between;

			&:first-child {
				margin-bottom: 20rpx;
			}

			&__wrap {
				padding: 20rpx 40rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 20rpx;
			}
		}
	}

	.icon-click__area {
		justify-content: center;
		width: 120rpx;

		.icon {
			width: 40rpx;
			height: 40rpx;
		}
	}

	.guidance-target {
		flex: 1;
		padding: 28rpx 24rpx 40rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&__item {
			margin-top: 20rpx;
		}

		&>.fr {
			justify-content: space-between;
		}

		&__row {
			margin-bottom: 36rpx;
		}

		&__label,
		&__value {
			font-size: 26rpx;
		}

		&__label {
			flex-shrink: 0;
			font-weight: bold;
		}

		&__value {
			text-align: right;
		}
	}

	.btn-row {
		justify-content: center;
		height: 80rpx;
		margin: 0 20rpx 40rpx;
		font-weight: bold;
		border-radius: 200rpx;
		background: #ffffff;

		.symbol {
			justify-content: center;
			width: 40rpx;
			height: 40rpx;
		}

		&__add {
			color: #1F2428;
		}

		&__del {
			color: #C35943;
		}
	}
</style>