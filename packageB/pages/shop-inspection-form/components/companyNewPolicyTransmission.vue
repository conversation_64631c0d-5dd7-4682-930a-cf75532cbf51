<!--
 * @Description: 公司新政传递s
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:09:00
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:47:55
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/companyNewPolicyTransmission.vue
-->

<template>
	<view>
		<view v-for="(item, index) in dataList" :key="item.trainId">
			<view class="fl sheet">
				<text class="fr block sheet-title">政策{{ index + 1}} </text>
				<view class="fl sheet-item">
					<view class="fr block">
						<text class="block-title">政策名称：</text>
						<input v-model="item.policyName" type="text" placeholder="请输入政策名称" class="block-title__input"
							placeholder-style="font-size: 26rpx;color:#999;" />
					</view>
					<view class="fl block">
						<text class="block-title">政策内容：</text>
						<cTextareaVue v-model="item.policyContent" count maxlength="500" placeholder="请填写内容" />
					</view>
					<view class="fl block c-radio" style="margin-bottom: 0;">
						<view class="fr radio-group">
							<text class="radio-group__label">完成情况</text>
							<view>
								<up-radio-group v-model="item.isFulfill" shape="circle" usedAlone :size="size" activeColor="#C35943"
									inactiveColor="#C35943" labelColor="#1F2428">
									<up-radio label="已完成" name="1" :labelSize="size" />
									<up-radio label="未完成" name="0" :labelSize="size" />
								</up-radio-group>
							</view>
						</view>
						<cTextareaVue v-model="item.reason" count maxlength="500" placeholder="请填写未完成原因"
							background=" linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%)" />
					</view>
					<uploadImageGroupVue imgLeftText="文件照片" :images="item.picUrls" @change="e => onChangeImage(e, index)" auto-hide-button />
				</view>
			</view>
			<view class="fr btn-row btn-row__del" v-if="dataList.length > 1 && index !== 0"
				@tap="deleteGuidanceProject(index)">
				<text class="fr symbol" style="color: #C35943;">-</text>删除指导项目
			</view>
		</view>
		<view class="fr btn-row btn-row__add" @tap="addKnowProject">
			<text class="fr symbol">+</text>添加指导项目
		</view>
	</view>
</template>

<script setup lang="ts">
	import { watch, ref, PropType } from 'vue'
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import uploadImageGroupVue from './uploadImageGroup.vue';
	import { getCompanyPolicyInfo, saveCompanyPolicy as saveCompanyPolicyApi } from '../api'
	import { usePageParams } from '../hooks/usePageParams';
	import { pageOptionsType } from '../types';
	import { showToast } from '@/utils/util';
	import { TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE } from '../enums'
	type dataItemType = {
		policyName : string;
		policyContent : string;
		isFulfill : string;
		reason : string;
		picUrls : any[];
		policyUrl : string;
		[k : string] : any
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	const dataList = ref<Partial<dataItemType[]>>([])
	const size = uni.upx2px(28)
	const { useParams } = usePageParams(props)
	/**
	 * @description: 获取数据
	 */
	const getData = async () : Promise<void> => {
		const res = await getCompanyPolicyInfo(useParams.value())
		if (res.data?.length) {
			dataList.value = res.data.map((item : dataItemType) => {
				return {
					...item,
					picUrls: item.policyUrl ? item.policyUrl.split(',').map((r : string) => ({ url: r })) : []
				}
			})
		} else {
			dataList.value = [{ picUrls: [] } as any]
		}
	}
	/**
	 * @description: 提交校验
	 */
	const validate = () => {
		if (dataList.value.length) {
			const isExitEmptyValue = dataList.value.some(item => !item.policyName || !item.policyContent || (item.isFulfill !== '0' && item.isFulfill !== '1') || (item.isFulfill === '0' && !item.reason) || !item.picUrls.length)
			if (isExitEmptyValue) {
				showToast('公司新政传递存在未填写内容或上传图片')
				return
			}
		} else {
			showToast('公司新政传递存在未填写内容')
			return
		}
		return true
	}
	/**
	 * @description: 保存公司新政传递
	 */
	const saveCompanyPolicy = async (submissionStatus : number = 0) => {
		try {
			const params = {
				...useParams.value(),
				policyList: dataList.value.map(item => {
					return {
						...item,
						policyUrl: item.picUrls.length ? item.picUrls.map((r : any) => r.url).join(',') : ''
					}
				})
			}
			if (submissionStatus == 1 && !validate()) return
			const res = await saveCompanyPolicyApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveCompanyPolicy ~ e:", e)
		}
	}
	/**
	 * @description: 新增指导项目
	 */
	const addKnowProject = () => {
		dataList.value.push({ picUrls: [] } as any)
	}
	/**
	 * @description: 删除指导项目
	 */
	const deleteGuidanceProject = (index : number) => {
		dataList.value.splice(index, 1)
	}
	watch(() => props.menuCode, (v : string | number) => {
		if (v == TRANSMISSION_OF_NEW_COMPANY_POLICIES_CODE) {
			if (!dataList.value?.length) {
				getData()
			}
		}
	}, { immediate: true })

  const onChangeImage = (e : any[], index : number) => {
    dataList.value[index].picUrls.push(...e)
  }

	defineExpose({
		saveCompanyPolicy,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		overflow: hidden;
		padding: 0 40rpx 40rpx;
		margin: 0 20rpx 40rpx;
		color: #1F2428;
		background-color: #fff;
		border-radius: 20rpx;

		&-title {
			padding: 40rpx 0 !important;
			margin-bottom: 0 !important;
			font-size: 32rpx;
			font-weight: bold;
		}

		&-item {
			padding: 40rpx 24rpx;
			background: linear-gradient(203.5deg, rgba(#ffb2a1, .1) 0%, rgba(#f75e3b, .1) 100%);
			border-radius: 20rpx;

			:deep() {
				.desc {
					font-size: 26rpx;
				}
			}
		}
	}

	.block {
		padding: 28rpx 24rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 26rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		.feedback {
			padding: 0 24rpx;

			&-input {
				padding: 20rpx 0;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}

			.upload-wrap {
				.desc {
					font-size: 26rpx;
				}
			}
		}

		.radio-group {
			justify-content: space-between;
			margin-bottom: 20rpx;

			&__label {
				font-size: 26rpx;
				font-weight: bold;
				color: #1F2428;
			}
		}
	}

	.btn-row {
		justify-content: center;
		height: 80rpx;
		margin: 0 20rpx 40rpx;
		font-weight: bold;
		border-radius: 200rpx;
		background: #ffffff;

		.symbol {
			justify-content: center;
			width: 40rpx;
			height: 40rpx;
		}

		&__add {
			color: #1F2428;
		}

		&__del {
			color: #C35943;
		}
	}
</style>