<!--
 * @Description: 运营经理10项检查
 * @Author: gu_shiyuan"
 * @Date: 2024-12-13 09:05:42
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 09:24:18
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/10ChecksOnOperationalQuality.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fr tip">
			<up-icon name="info-circle" color="#C35943" size="12"></up-icon>
			<text class="fr tip-text">运营经理必填</text>
		</view>
		<view class="fl block" v-for="(item, index) in infoList" :key="item.menuCode">
			<text class="block-title">{{ item.menuValue }}</text>
			<radioVue :key="item.menuCode" :is-upload="item.isUpload == 1" :imgLeftText="item.images ? '请分角色上传' : '请至少上传一张图片'"
				placeholder="请输入不合格原因" v-model:value="item.isAllRight" v-model:desc="item.checkDetail"
				v-model:images="item.picUrls" :query="useParams?.(item.menuCode)" :specialUpload="!!item.images" is-register
				@change-image="e => onChangeImage(e, index)" :required="!!item.images && item.isUpload == 1">
				<template #default>
					<view class="fl block-title__desc">
						<text v-for="(menu, index) in item.menus" :key="menu.code">{{index + 1}}、 {{ menu.value}}</text>
					</view>
				</template>
				<template #footer>
					<text class="upload-tip" v-if="!!item.images">请上传工装照片，需区分前台、客房、后勤角色、后勤若没有可不传</text>
				</template>
			</radioVue>
		</view>
		<view class="fl block">
			<view class="suggest">
				<view class="fl first-fl">
					<text class="suggest-title">备注：</text>
					<cTextareaVue v-model="reasons" count maxlength="500" background="#fff" placeholder="请对不符合标准的项目情况进行描述说明" />
				</view>
			</view>
		</view>
		<up-modal :show="show" title="请选择工装图片角色类型">
			<template>
				<view class="fr role">
					<view class="fl role-item" v-for="(item, index) in roles" :key="index"
						:class="{'role-item--active': index == tabIndex}" @tap="tabIndex = index">
						<image :src="item.value" mode="aspectFill" lazy-load class="role-img"></image>
						<text class="role-title">{{ item.label }}</text>
					</view>
				</view>
			</template>
			<template #confirmButton>
				<view class="fr button">
					<text class="fr btn btn-cancel" @tap="show = false">取消</text>
					<text class="fr btn btn-primary" @tap="onConfirm">确认</text>
				</view>
			</template>
		</up-modal>
	</view>
</template>

<script setup lang="ts">
  import {onMounted, onUnmounted, provide, ref, watch} from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import radioVue from './radio.vue';
	import type { dataType } from '@/types/index.d'
	import { showToast } from '../../../../utils/util';
	import { usePageParams } from '../hooks/usePageParams'
	import type { pageOptionsType } from '../types'
	import { CHECKS_BY_OPERATIONS_MANAGER_10_CODE } from '../enums';
	import { saveQualityOprationCheck as saveQualityOprationCheckApi, getQualityOprationCheck as getQualityOprationCheckApi } from '../api'
	type propsType = {
		options : Partial<pageOptionsType>;
		menuCode : string;
	}
	const props = withDefaults(defineProps<propsType>(), {})
	const infoList = ref<dataType[]>([])
	const imageFn = ref(null);
	const show = ref(false)
	const tabIndex = ref(0)
	const menuCode = ref('') //前台/客房/后勤
	const reasons = ref('')
	const roles = [
		{
			label: '前台',
			value: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-13/1734065140501.png',
		},
		{
			label: '客房',
			value: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-13/1734065140501.png'
		},
		{
			label: '后勤',
			value: 'https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-13/1734065140501.png'
		}
	]
	const { useParams } = usePageParams(props)
	watch(() => props.menuCode, (v : string) => {
		if (v == CHECKS_BY_OPERATIONS_MANAGER_10_CODE) {
			if (!infoList.value?.length) {
				getQualityOprationCheck()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 图片回调
	 */
	const onChangeImage = (e : any, index : number) => {
    console.log(...e,'...e,onChangeImage>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>')
		infoList.value[index].picUrls.push(...e)
	}
	/**
	 * @description: 选择工装/客房/后勤
	 */
	const onConfirm = async () => {
		show.value = false
		const data = infoList.value.find(item => item.menuCode == menuCode.value)
		const currentImage = data.images[tabIndex.value]
		if (currentImage.picUrl && currentImage.picUrl != '') {
			showToast('该角色数量已达上线')
			return
		}
		const res = await imageFn.value.batchUpload(1, useParams.value(currentImage.code))
		currentImage.picUrl = res[0]
		data.picUrls.push({
			parentCode: data.menuCode,
			code: currentImage.code,
			url: res[0]
		})
	}
	/**
	 * @description: 获取数据
	 */
	const getQualityOprationCheck = async () => {
		try {
			const res = await getQualityOprationCheckApi({
				code: props.options.brandType == "1006" ? '100606' : '1004', // TODO 不知道原因
				...useParams.value(),
				brandType: props.options.brandType
			})
			reasons.value = res.data.reasons
			infoList.value = res.data.infoList.map((item : dataType) => {
				return {
					...item,
					picUrls: item.images ? item.images.filter((r : dataType) => r.picUrl).map((r : dataType) => {
						return {
							parentCode: item.menuCode,
							code: r.code,
							url: r.picUrl
						}
					}) : item.picUrl ? item.picUrl.split(',').map((r : string) => {
						return {
							code: item.menuCode,
							url: r
						}
					}) : []
				}
			})
		} catch (error) {
			console.log("🚀 ~ getQualityOprationCheck ~ error:", error)
		}
	}
	/**
	 * @description: 校验
	 */
	const validate = () => {
		for (let i = 0, len = infoList.value.length; i < len; i++) {
			const dataItem = infoList.value[i]
			if (dataItem.isAllRight != 0 && dataItem.isAllRight != 1) {
				showToast(`品质检查-${dataItem.menuValue}存在未选择的项`)
				return
			}
			if (dataItem.isAllRight == 0 && !dataItem.checkDetail) {
				showToast(`品质检查-${dataItem.menuValue}存在未填写的项`)
				return
			}
			if (!dataItem.images) {
				if (dataItem.isUpload == 1 && !dataItem.picUrls.length) {
					showToast(`品质检查-${dataItem.menuValue}存在未上传的图片`)
					return
				}
			} else {
				for (let j = 0, jLen = dataItem.images.length - 1; j < jLen; j++) {
					if (!dataItem.images[j].picUrl) {
						showToast(`品质检查-${dataItem.images[j].value}请上传图片`)
						return
					}
				}
			}
		}
		if (!reasons.value) {
			showToast('请填写备注')
			return
		}
		return true
	}
	/**
	 * @description: 保存数据
	 */
	const saveQualityOprationCheck = async (draftType : number = 0) : Promise<any> => {
		try {
			const params = {
				code: props.options.brandType == '1006' ? '100606' : '1004', // TODO 不知道原因
				...useParams.value(),
				reasons: reasons.value,
			}
			params.infoList = infoList.value.map((item : any) => {
				return {
					checkDetail: item.checkDetail,
					dcmCode: item.menuCode,
					isAllRight: item.isAllRight,
					picUrl: item.picUrls.map((r : any) => r.url)?.join(','),
					images: item.images?.map((r : any) => {
						return {
							dcmCode: r.code,
							picUrl: r.picUrl
						}
					})
				}
			})
			const res = await saveQualityOprationCheckApi(params)
			if (draftType == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (error) {
			console.log("🚀 ~ saveQualityOprationCheck ~ error:", error)
		}
	}
	/**
	 * @description: 注册子组件方法
	 */
	const registerFn = (params : { uploadImage : Function, upload : Function, compressImage : Function, batchUpload : Function }) => {
		if (!imageFn.value) {
			imageFn.value = params
		}
	}
	provide('instance', {
		registerFn
	})
	onMounted(async () => {
		uni.$on('uploadImage', ({ type, code, data, index }) => {
			menuCode.value = code
			if (type == 'add') {
				show.value = true
			} else {
				const currentData = infoList.value.find(item => item.menuCode == data.parentCode)
				const currentImage = currentData.images.find((item : dataType) => item.code == data.code)
				currentImage.picUrl = ''
				currentData.picUrls.splice(index, 1)
			}
		})
	})

  onUnmounted(()=>{
    uni.$off('uploadImage')
  })

	defineExpose({
		saveQualityOprationCheck,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.tip {
		padding: 16rpx 20rpx;
		margin-bottom: 20rpx;
		color: #C35943;
		border-radius: 200rpx;
		background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

		&-text {
			margin-left: 8rpx;
			font-size: 22rpx;
			font-weight: bold;
		}
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #F2F2F2;

			&__desc {
				margin-bottom: 40rpx;
				font-weight: bold;
			}
		}

		.upload-tip {
			display: block;
			margin-top: 20rpx;
			font-size: 24rpx;
			color: #999;
		}

		s &-content {
			padding: 20rpx 24rpx;
			border-radius: 15.38rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

			.boss-suggest {
				margin-top: 20rpx;
			}
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}
	}

	.fr.button {
		.btn {
			flex: 1;
			height: 80rpx;
			justify-content: center;
			color: #999;
			border-radius: 200rpx;
		}

		.btn-cancel {
			margin-right: 40rpx;
			color: #f75e3b;
			border: 2rpx solid #f75e3b;
		}

		.btn-primary {
			color: #fff;
			background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
		}
	}

	.role {
		margin-top: 20rpx;

		&-item {
			&:nth-child(2) {
				margin: 0 60rpx;
			}

			&--active {
				position: relative;

				&::after {
					content: "";
					position: absolute;
					bottom: -20rpx;
					left: 50%;
					display: block;
					height: 20rpx;
					width: 20rpx;
					transform: translateX(-50%);
					background-color: #f75e3b;
					border-radius: 10rpx;
				}
			}
		}

		&-img {
			width: 104rpx;
			height: 104rpx;
		}

		&-title {
			position: relative;
			top: -10rpx;
			width: 104rpx;
			height: 34rpx;
			line-height: 34rpx;
			font-weight: bold;
			font-size: 20rpx;
			text-align: center;
			border-radius: 17rpx;
			background: linear-gradient(to right, #E3C292, #F6D9B1);
		}
	}
</style>