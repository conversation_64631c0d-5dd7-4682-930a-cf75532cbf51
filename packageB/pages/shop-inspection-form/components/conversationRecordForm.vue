<!--
 * @Description: 谈话记录表
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 14:56:08
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2025-01-05 15:46:50
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/conversationRecordForm.vue
-->

<template>
	<view class="fl block-wrap">
		<view class="fl block">
			<view class="fr block-sub__title">
				<text class="">谈话对象</text>
				<picker class="picker" mode="selector" :value="sessionTargetValue" :range="range" range-key="label"
					@change="onChange">
					{{ getText }}
				</picker>
			</view>
			<view class="suggest">
				<view class="fl first-fl" v-for="item in dataList" :key="item.code">
					<text class="suggest-title">{{ item.explan }}：</text>
					<cTextareaVue v-model="item.value" count maxlength="1000" background="#fff" :placeholder="'请输入'+item.explan" />
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed, PropType, watch } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue'
	import { usePageParams } from '../hooks/usePageParams';
	import { pageOptionsType } from '../types';
	import { getTalkingContent as getTalkingContentApi, saveTalkingContent as saveTalkingContentApi } from '../api'
	import { showToast } from '@/utils/util';
	import { CONVERSATION_RECORD_FORM_CODE } from '../enums'
	type dataItemType = {
		code : string;
		value : string;
		explan : string;
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: {
			type: String,
			default: ''
		}
	})
	const { useParams } = usePageParams(props)
	const dataList = ref<Partial<dataItemType[]>>([])
	const sessionTargetValue = ref('')
	const range = [
		{ label: '店长', value: '0' },
		{ label: '业主', value: '1' },
		{ label: '店长和业主', value: '2' }
	]
	const getText = computed(() => {
		return range.find(item => item.value == sessionTargetValue.value)?.label || '请选择谈话对象'
	})
	const onChange = (e : any) => {
		sessionTargetValue.value = e.detail.value
	}
	/**
	 * @description: 获取数据
	 */
	const getTalkingContent = async () : Promise<void> => {
		const res = await getTalkingContentApi(useParams.value())
		const { content, sessionTarget } = res.data
		dataList.value = content
		sessionTargetValue.value = sessionTarget
	}
	/**
	 * @description: 提交校验
	 */
	const validate = () => {
		if (!sessionTargetValue.value) {
			showToast('请选择谈话记录表谈话对象')
			return
		}
		if (dataList.value.length) {
			const isEixtEmptyValue = dataList.value.some(item => !item.value)
			if (isEixtEmptyValue) {
				showToast('谈话记录表存在未填写的内容')
				return
			} sessionTargetValue
		} else {
			showToast('谈话记录表存在未填写的内容')
			return
		}
		return true
	}
	/**
	 * @description: 保存谈话记录表
	 */
	const saveTalkingContent = async (submissionStatus : number = 0) : Promise<any> => {
		try {
			let params = {
				...useParams.value(),
				sessionTarget: sessionTargetValue.value,
				talkInfoList: dataList.value
			}
			const res = await saveTalkingContentApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveTalkingContent ~ e:", e)
		}
	}
	watch(() => props.menuCode, (v : string | number) => {
		if (v == CONVERSATION_RECORD_FORM_CODE) {
			if (!dataList.value?.length) {
				getTalkingContent()
			}
		}
	}, { immediate: true })

	defineExpose({
		saveTalkingContent,
		validate
	})
</script>

<style scoped lang="scss">
	.block-wrap {
		padding: 0 20rpx;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #fff;
		border-radius: 20rpx;

		&-sub__title {
			justify-content: space-between;
			padding-bottom: 40rpx;
			color: #1F2428;
		}

		.picker {
			color: #C1BCB9;
		}

		.suggest {
			padding: 0 24rpx 24rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 20rpx;

			&-title {
				padding: 20rpx 0;
			}

			.first-fl {
				margin-bottom: 20rpx;
			}
		}
	}
</style>