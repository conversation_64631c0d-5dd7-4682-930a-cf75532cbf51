<!--
 * @Description: 会议纪要
 * @Author: gu_shiyuan"
 * @Date: 2024-12-12 16:42:56
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-12-27 10:15:26
 * @FilePath: /smart-operation/packageB/pages/shop-inspection-form/components/meetingMinutes.vue
-->

<template>
	<view class="sheet">
		<view class="fr block">
			<text class="block-title">会议时间：</text>
			<text class="shop" @tap="showDate = true">{{ form.time || currentDate}}</text>
		</view>
		<view class="fr block">
			<text class="block-title">会议地点：</text>
			<input v-model="form.place" type="text" placeholder="请输入会议地点" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">会议主持人：</text>
			<input v-model="form.supportor" type="text" placeholder="请输入主持人姓名" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">参会人员：</text>
			<input v-model="form.attendees" type="text" placeholder="请输入会议人员" class="block-title__input"
				placeholder-style="font-size: 26rpx;color:#999;" />
		</view>
		<view class="fr block">
			<text class="block-title">巡店日期：</text>
			<text class="shop">{{ currentDate }}</text>
		</view>
		<view class="fl block">
			<text class="block-title">会议主要内容：</text>
			<cTextareaVue v-model="form.primaryNotes" count maxlength="500" placeholder="请输入会议内容" />
		</view>
		<view class="fl block">
			<text class="block-title">需协调解决事宜：</text>
			<cTextareaVue v-model="form.needToDo" count maxlength="500" placeholder="请输入会议内容" />
		</view>
	</view>
	<up-datetime-picker mode="date" :show="showDate" v-model="form.time" confirmColor="#C35943" @cancel="showDate = false" @confirm="confirm"></up-datetime-picker>
</template>

<script setup lang="ts">
	import { PropType, watch, ref } from 'vue';
	import cTextareaVue from '../../../components/c-textarea/c-textarea.vue';
	import { getMeetingNodesDetail as getMeetingNodesDetailApi, saveMeetingNotes as saveMeetingNotesApi } from '../api'
	import { pageOptionsType } from '../types';
	import { usePageParams } from '../hooks/usePageParams';
	import { MEETING_MINUTES_CODE } from '../enums';
	import dayjs from 'dayjs';
	import { showToast } from '@/utils/util';
	type formType = {
		time : string;
		place : string;
		supportor : string;
		type : string;
		attendees : string;
		primaryNotes : string;
		needToDo : string;
		[k : string] : any
	}
	const props = defineProps({
		options: {
			type: Object as PropType<pageOptionsType>,
			default: () => { }
		},
		menuCode: String
	})
	const { useParams } = usePageParams(props)
	const showDate = ref(false)
	const currentDate = ref(dayjs().format('YYYY-MM-DD'))
	const form = ref<Partial<formType>>({time:currentDate.value})
	watch(() => props.menuCode, (v : string) => {
		if (v == MEETING_MINUTES_CODE) {
			if (form.value && JSON.stringify(form.value) == '{}') {
				getMeetingNodesDetail()
			}
		}
	}, { immediate: true })
	/**
	 * @description: 获取会议纪要
	 */
	const getMeetingNodesDetail = async () => {
		try {
			const res = await getMeetingNodesDetailApi(useParams.value())
			form.value = res.data || {}
		} catch (e) {
			console.log("🚀 ~ getMeetingNodesDetail ~ e:", e)
		}
	}
	/**
	 * @description; 校验
	 */
	const validate = () => {
		const fields = ['place', 'supportor', 'attendees', 'primaryNotes', 'needToDo']
		for (let i = 0, len = fields.length; i < len; i++) {
			if (!form.value[fields[i]]) {
				showToast('会议纪要存在未填写的项')
				return
			}
		}
		return true
	}
	/**
	 * @description: 保存会议纪要
	 */
	const saveMeetingNotes = async (submissionStatus : number) => {
		try {
			let params = {
				...useParams.value(),
				...form.value,
				time: form.value.time || currentDate.value
			}
			const res = await saveMeetingNotesApi(params)
			if (submissionStatus == 0) {
				showToast('保存成功')
				return
			}
			return res
		} catch (e) {
			console.log("🚀 ~ saveMeetingNotes ~ e:", e)
		}
	}

	const confirm = (e) => {
		form.value.time = dayjs(e.value).format('YYYY-MM-DD')
		showDate.value = false
	}

	defineExpose({
		saveMeetingNotes,
		validate
	})
</script>

<style scoped lang="scss">
	.sheet {
		padding: 0 24rpx;
		color: #1F2428;
	}

	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		&.fr {
			justify-content: space-between;
		}

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1F2428;

			&__input {
				text-align: right;
			}
		}

		&.fl {
			.block-title {
				flex-shrink: 0;
				margin-right: 20rpx;
				margin-bottom: 40rpx;
			}
		}

		:deep() {
			.textarea {
				font-size: 24rpx;
			}
		}
	}
</style>