<template>
  <view class="content">
    <view class="gradient">
      <view class="fl progress">
        <view class="fr tag">
          <image
            src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-10/1741571889635.png"
            mode="aspectFill" lazy-load class="tag-icon"></image>
          <text class="tag-title">{{ tabValue === 0 ? '售卡金额目标达成率' : '售卡数量目标达成率' }}</text>
        </view>
        <view class="fr arcbar">
          <arcbarVue
            :value="tabValue === 0 ? data.completedAmountRatio : data.completedNumberRatio" width="280rpx"
            height="280rpx" centerWidth="100rpx" font-size="52rpx" tag-size="28rpx" />
        </view>
      </view>
      <view class="card">
        <view
          class="card-item" v-for="(item, index) in tabValue === 0 ? THE_AMOUNT_OF_THE_CARD_SOLD : NUMBER_OF_CARDS_SOLD"
          :key="index">
          <view class="fr card-item__title">
            <text class="card-item__title-text">{{ item.title }}</text>
          </view>
          <view class="font-size">
            <text class="DIN-Bold">{{ data[item.prop] }}</text>
            <text class="unit">{{ item.unit }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="card-analyse" v-if="data.budgetAmount > 0 && data.completedAmountRatio <= 100">
      <view class="fr card-analyse__title">
        <image src="/static/tabbar/ai.png" mode="aspectFill" lazy-load class="logo"></image>
        <text class="card-analyse__title-text">售卡分析</text>
      </view>
      <view class="fl card-analyse__desc">
        <view class="card-analyse__desc-row">1.距离目标售卡数还差
          <text class="target">{{ data?.requireGoldCardNumber }}张88元</text>
          金卡新礼包
        </view>
        <text
          class="theme-primary"
          @click="route_to_view(`/ai/pages/index/index?query=${encodeURIComponent('如何提升售卡业绩')}`);">查收您的售卡攻略
        </text>
      </view>
    </view>
  </view>

</template>

<script setup lang="ts">
  import arcbarVue from '../../../components/arcbar/arcbar.vue';
  import { THE_AMOUNT_OF_THE_CARD_SOLD, NUMBER_OF_CARDS_SOLD } from "../enums";
  import { useCommon } from "@/hooks/useGlobalData";
  import { getCurrentInstance } from "vue";

  const {route_to_view} = useCommon(getCurrentInstance())

  defineProps({
    data: {
      type: Object,
      default: () => {
      }
    },
    tabValue: {
      type: Number,
    }
  })
</script>

<style scoped lang="scss">

  .content {
    border-radius: 20rpx;
  }

  .gradient {
    border-radius: 20rpx;
    background: linear-gradient(0deg, rgba(247, 94, 59, 0) 0%, rgba(247, 94, 59, 0) 65%, rgba(247, 94, 59, 0.05) 100%);
    padding-bottom: 20rpx;
  }

  .progress {
    position: relative;
    justify-content: space-between;
    padding-top: 92rpx;
    margin-bottom: 40rpx;

    .tag {
      position: absolute;
      top: 0;
      left: 0;
      padding: 10rpx 24rpx 10rpx 22rpx;
      font-weight: bold;
      line-height: 28rpx;
      background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
      border-radius: 20rpx 0 20rpx 0;

      &-title {
        margin: 0 8rpx;
        color: #fff;
      }

      &-icon {
        width: 30rpx;
        height: 26rpx;
      }
    }

    .arcbar {
      justify-content: center;
    }
  }

  .card {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;

    &-item {
      width: calc((100% - 40rpx) / 3);
      margin-bottom: 20rpx;
      margin-right: 20rpx;
      padding: 28rpx 0 12rpx 16rpx;
      border: 2rpx solid #f2f2f2;
      background: #ffffff;
      border-radius: 20rpx;

      &:nth-child(3n+3) {
        margin-right: 0;
      }

      &__title {
        margin-bottom: 28rpx;
        font-weight: bold;
        font-size: 24rpx;
        line-height: 24rpx;
        color: #1F2428;

        &-text {
          margin-right: 8rpx;
        }
      }

      .font-size {
        font-size: 40rpx;
        line-height: 40rpx;
        color: #1F2428;
        word-break: break-all;

        .unit {
          font-size: 22rpx;
          margin-left: 4rpx;
          line-height: 22rpx;
        }
      }
    }
  }


  .card-analyse {
    margin-top: 40rpx;

    .logo {
      width: 60rpx;
      height: 60rpx;
    }

    &__title {
      width: 212rpx;
      height: 52rpx;
      margin-bottom: 20rpx;
      background: linear-gradient(203.5deg, rgba(#ffb2a1, .1) 0%, rgba(#f75e3b, .1) 100%);
      border-radius: 52rpx;

      &-text {
        padding-left: 16rpx;
        font-weight: bold;
      }
    }

    &__desc {
      padding: 28rpx 28rpx 40rpx;
      font-size: 24rpx;
      font-weight: bold;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      border-radius: 16rpx;

      &-row {
        margin-bottom: 40rpx;
        color: #1F2428;

        &::last-child {
          margin-bottom: 0;
        }

        .target {
          color: #C35943;
        }
      }

      .theme-primary {
        text-align: center;
        text-decoration: underline;
      }

    }
  }
</style>