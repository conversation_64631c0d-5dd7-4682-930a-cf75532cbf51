<template>
  <view class="ring">
    <text class="title">员工售卡明细</text>
    <view style="margin-top: 40rpx;">
      <cTableVue :header="header" :list="data" height="514rpx">
        <template #default="scope">
          <view v-if="scope.prop === 'rank'" class="rank">
            <view v-if="scope.index <= 2">
              <image class="img"
                :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-02/${scope.index == 0 ? '1733120145481' : scope.index == 1 ? '1733120195739' : '1733120209949'}.png`"
                mode="aspectFill"></image>
            </view>
            <view v-else>
              {{scope.index+1}}
            </view>
          </view>
          <view v-if="scope.prop === 'staffName'" class="fl">
            <view style="text-align: left;margin-bottom: 12rpx;">
              {{scope.data.staffName}}
            </view>
            <view class="progress">
              <view class="progress-bar" :style="{width: ((scope.data.completedAmount / scope.data.topOneCompletedAmount) * 100).toFixed(2) + '%'}"></view>
            </view>
          </view>
        </template>
      </cTableVue>
    </view>
  </view>
</template>

<script setup lang="ts">
  import cTableVue from '@/components/c-table/c-table.vue'
  import { header } from '../enums'

  defineProps({
    data: {
      type: Object,
      default: () => {
      }
    }
  })
</script>

<style scoped lang="scss">
  .ring {
    padding: 40rpx;
    border-radius: 20rpx;
    background: #ffffff;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      line-height: 32rpx;
    }
  }

  .rank {
    display: flex;
    align-items: center;

    .img {
      width: 40rpx;
      height: 50rpx;
      flex-shrink: 0;
    }
  }

  .progress {
    width: 164rpx;
    height: 10rpx;
    background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.2) 0%, rgba(247, 94, 59, 0.2) 100%);
    border-radius: 94rpx;

    &-bar {
      width: 0;
      height: 10rpx;
      background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
      border-radius: 94rpx;
    }
  }
</style>