<template>
  <view class="container">
    <view class="section">
      <view class="section-header">
        <text class="section-header__title">会员发展</text>
        <cTabsVue
          :isBig="false" :list="topTabs" v-model="tabValue"
          itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;" />
      </view>
      <view class="subsection">
        <c-subsection-vue v-model="subsectionValue" :list="subsectionList" @change="updateData" />
      </view>
      <view class="calendar">
        <cCalendarVue :startDate="dayjs().subtract(1, 'year').add(1, 'day').format('YYYY-MM-DD')" :end-date="dayjs().format('YYYY-MM-DD')" v-model="calendarValue" @confirm="confirm" :showIcon="false" />
      </view>
      <marktingDataVue :data="cardSaleProgressData" :tabValue="tabValue"/>
    </view>
    <employeeCardRecordVue :data="cardStatisticsData"/>
  </view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
  import { onLoad } from '@dcloudio/uni-app'
  import { ref } from 'vue'
  import cTabsVue from '@/components/c-tabs/c-tabs.vue';
  import cSubsectionVue from '@/components/c-subsection/c-subsection.vue';
  import cCalendarVue from '@/components/c-calendar/c-calendar.vue'
  import marktingDataVue from './components/markting-data.vue';
  import employeeCardRecordVue from './components/empolyee-card-record.vue'
  import { topTabs, subsectionList } from './enums'
  import { successCallbackResult } from "@/types";
  import { membershipCardSaleProgress, staffSalesMembershipCardStatistics } from "@/packageB/api";
  import dayjs from "dayjs";

  const tabValue = ref(topTabs[0].value),
    subsectionValue = ref<number>(null),
    calendarValue = ref([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]),
    cardSaleProgressData = ref({}),
    cardStatisticsData = ref({}),
    shopId = ref<string>('')

  onLoad((options) => {
    shopId.value = options.shopId
    getData()
  })

  const getData = () => {
    toStaffSalesMembershipCardStatistics()
    toMembershipCardSaleProgress()
  }

  const updateData = (item: any) => {
    calendarValue.value = item.formt
    getData()
  }
  /**
   * @description: 获取门店会员卡售卖进展
   * @param {*} params
   * @return {*}
   */
  const toMembershipCardSaleProgress = async (): Promise<void> => {
    const res: successCallbackResult = await membershipCardSaleProgress({
      shopId: shopId.value,
      startDate: calendarValue.value[0],
      endDate: calendarValue.value[1]
    })
    cardSaleProgressData.value = res.data || {}
  }

  /**
   * @description: 员工售卖会员卡统计
   * @param {*} params
   * @return {*}
   */
  const toStaffSalesMembershipCardStatistics = async (): Promise<void> => {
    const res: successCallbackResult = await staffSalesMembershipCardStatistics({
      shopId: shopId.value,
      startDate: calendarValue.value[0],
      endDate: calendarValue.value[1]
    })
    cardStatisticsData.value = res.data.sort((a, b) => a.rank - b.rank) || []
  }

  const confirm = () => {
    subsectionValue.value = null
    getData()
  }
</script>

<style lang="scss" scoped>
  .container {
    padding: 20rpx 20rpx 60rpx;

    .section {
      padding: 0 40rpx 40rpx;
      margin-bottom: 20rpx;
      background: #ffffff;
      border-radius: 20rpx;

      &-header {
        display: flex;
        justify-content: space-between;
        padding: 40rpx 0;

        &__title {
          font-weight: bold;
          font-size: 32rpx;
          color: #1F2428;
          line-height: 32rpx;
        }
      }
    }

    .subsection, .calendar {
      margin-bottom: 40rpx;
    }
  }

  :deep(.calendar-picker) {
    background: #fff;
    height: 54rpx;
  }
</style>