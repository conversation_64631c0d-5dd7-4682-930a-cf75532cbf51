import { setDay } from "@/utils/util"

export const topTabs = [
	{name: '售卡金额', value: 0},
	{name: '售卡数量', value: 1}
]

export const subsectionList = [
	{
		name: '昨日',
		value: 1,
		formt: (() => {
			return [
				setDay(1, '-'),
				setDay(1, '-')
			]
		})(),
	},
	{
		name: '近3日',
		value: 2,
		formt: (() => {
			return [
				setDay(2, '-'),
				setDay(0, '-'),
			]
		})(),
	},
	{
		name: '近一周',
		value: 3,
		formt: (() => {
			return [
				setDay(6, '-'),
				setDay(0, '-'),
			]
		})(),
	},
	{
		name: '本月',
		value: 4,
		formt: (() => {
			const date = new Date()
			const y = date.getFullYear()
			const m =  (date.getMonth() + 1).toString().padStart(2, '0')
			return [
				y + '-' + m + '-01',
				setDay(0, '-'),
			]
		})(),
	}
]

export const header = [
	{name: '排名', prop: 'rank', slot:true},
	{name: '姓名', prop: 'staffName', slot:true},
	{name: '售卡金额', prop: 'completedAmount'},
	{name: '售卡量', prop: 'completedNumber'}
]

// 售卡金额
export const THE_AMOUNT_OF_THE_CARD_SOLD = [
	{
		title: '目标售卡金额',
		prop: 'budgetAmount',
		unit: '元'
	},
	{
    title: '完成售卡金额',
		prop: 'completedAmount',
		unit: '元'
	},
	{
    title: '金额目标达成率',
		prop: 'completedAmountRatio',
		unit: '%'
	},
	{
    title: '小美银卡金额',
		prop: 'completedSilverAmount',
		unit: '元'
	},
	{
    title: '小美金卡金额',
		prop: 'completedGoldAmount',
		unit: '元'
	},
	{
    title: '小美钻卡金额',
		prop: 'completedDrillAmount',
		unit: '元'
	}
]

// 售卡数量
export const NUMBER_OF_CARDS_SOLD = [
  {
    title: '目标售卡数量',
    prop: 'budgetNumber',
    unit: '张'
  },
  {
    title: '完成售卡数量',
    prop: 'completedNumber',
    unit: '张'
  },
  {
    title: '数量目标达成率',
    prop: 'completedNumberRatio',
    unit: '%'
  },
  {
    title: '小美银卡数量',
    prop: 'completedSilverNumber',
    unit: '张'
  },
	{
		title: '小美金卡数量',
		prop: 'completedGoldNumber',
		unit: '张'
	},
  {
    title: '小美钻卡数量',
    prop: 'completedDrillNumber',
    unit: '张'
  }
]