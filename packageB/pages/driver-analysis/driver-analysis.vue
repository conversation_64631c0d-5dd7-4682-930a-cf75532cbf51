<template>
	<view class="page">
		<view class="container">
			<view class="title fr">
				<text class="title-text">关键指标</text>
				<c-subsection-vue v-model="dateTypeIndex" :list="dateTypeList" @change="updateData" />
			</view>
			<!-- 渠道picker -->
			<view class="picker-section">
				<view class="picker fr" @click="show = true">
					<view class="">{{channelLable}}</view>
					<up-icon name="arrow-down" color="#1F2428" size="22rpx" />
				</view>
			</view>
			<view class="tabs">
				<cTabs :list="dataTypeList" v-model="dataTypeIndex" :isBig="false"
					itemStyle="min-width:108rpx;height: 60rpx;align-items:flex-start;padding:0" @change="handleDataType" />
			</view>
			<view class="key-indicators">
				<stackedLineChartVue ref="stackedLineChartRef" :data="shopMotivationAnalysisChartData" customFullScreenEvent
					@customFullScreenEvent="handleFullScreen" :full-screen-data="shopMotivationAnalysisFullScreenChartData" />
				<!-- <view class="key-indicators-chars"></view> -->
			</view>
			<template v-if="dataTypeIndex !==1 && dataTypeIndex!==4">
				<view class="title fr" style="margin-top: 40rpx;">
					<text class="title-text">关键驱动因素</text>
				</view>
				<view class="table">
					<view class="content">
						<view class="th fr">
							<view class="td">核心驱动因素</view>
							<view class="td">结果</view>
						</view>
						<view class="tbody">
							<view class="tr fr" v-if="dataTypeIndex === 2 || dataTypeIndex === 3">
								<view class="td">产品标准</view>
								<view class="td">{{shopMotivationAnalysisData?.productStandard || '-'}}</view>
							</view>
							<view class="tr fr" v-if="dataTypeIndex === 2 || dataTypeIndex === 3">
								<view class="td">物资采集率</view>
								<view class="td">{{shopMotivationAnalysisData?.materialCollectionRate || '-'}}</view>
							</view>
							<view class="tr fr" v-if="dataTypeIndex === 2 || dataTypeIndex === 3">
								<view class="td">是否覆盖早餐</view>
								<view class="td">
									{{shopMotivationAnalysisData?.breakfastCovered || '-'}}
								</view>
							</view>
							<view class="tr fr" v-if="dataTypeIndex === 0 || dataTypeIndex === 2">
								<view class="td">是否公司方店长</view>
								<view class="td">
									{{shopMotivationAnalysisData?.managerCovered || '-'}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>
	</view>
	<up-picker :show="show" :columns="channelList" confirmColor="#C35943" closeOnClickOverlay @confirm="confirm"
		@cancel="close" @close="close" keyName="name" />
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { computed, nextTick, onMounted, ref } from 'vue';
	import cTabs from '@/components/c-tabs/c-tabs.vue'
	import stackedLineChartVue from '@/components/stacked-line-chart/stacked-line-chart.vue';
	import cSubsectionVue from "@/components/c-subsection/c-subsection.vue";
	import { CHANNELLIST } from '@/packageB/constant';
	import { shopMotivationAnalysis } from '../../api';
	import dayjs from 'dayjs';

	const dateTypeList : { name : string, value : 'day' | 'month' }[] = [
		{ name: '日', value: 'day' },
		{ name: '月', value: 'month' },
	]

	const dataTypeList : { name : string, value : 1 | 2 | 3 | 4 | 5 }[] = [
		{ name: '卫生分', value: 1 },
		{ name: '位置分', value: 2 },
		{ name: '服务分', value: 3 },
		{ name: '设施分', value: 4 },
		{ name: '差评率', value: 5 },
	]


	// 渠道
	const channelIndex = ref<number>(0),
		show = ref<boolean>(false),
		channelList = ref([CHANNELLIST])
	const channelLable = computed(() => channelList.value[0][channelIndex.value].name)

	const dateTypeIndex = ref<number>(0),
		dataTypeIndex = ref<number>(0),
		shopId = ref<string>(),
		shopMotivationAnalysisData = ref(),
		shopMotivationAnalysisChartData = ref({}), // 关键指标图表数据
		shopMotivationAnalysisFullScreenChartData = ref({}), // 关键指标全屏图表数据
		stackedLineChartRef = ref(null)

	onLoad((options) => {
		shopId.value = options.shopId
	})

	onMounted(() => {
		toShopMotivationAnalysis()
	})

	// 门店动因分析
	const toShopMotivationAnalysis = async (isFullScreen : boolean = false) => {
		try {
			const params = {
				shopId: shopId.value,
				dateType: isFullScreen ? 'dayMonth' as 'dayMonth' : dateTypeList[dateTypeIndex.value].value,
				dataType: dataTypeList[dataTypeIndex.value].value,
				channelType: channelList.value[0][channelIndex.value].value
			}
			const { data } = await shopMotivationAnalysis(params)
			if (data && Object.keys(data).length > 0) {
				shopMotivationAnalysisData.value = data
				if (data.scoreList && data.scoreList.length > 0) {
					const res = {
						categories: data.scoreList.map(({ slotDate }) =>
							(dateTypeIndex.value == 0 ? (dayjs(slotDate).day() == 0 ? dayjs(slotDate).format('MM-DD') + '(周日)' : dayjs(slotDate).day() == 6 ? dayjs(slotDate).format('MM-DD') + '(周六)' : dayjs(slotDate).format('MM-DD')) : dayjs(slotDate).format('YYYY-MM').slice(2, 7))
						),
						value1: {
							name: dateTypeIndex.value == 0 ? '上周同期' : '去年同期',
							data: data.scoreList.map(({ termScore }) => termScore == '-' ? 0 : termScore)
						},
						value2: {
							name: dataTypeList[dataTypeIndex.value].name,
							data: data.scoreList.map(({ score }) => score == '-' ? 0 : score),
						}
					}
					if (isFullScreen) {
						shopMotivationAnalysisFullScreenChartData.value = res
					} else {
						shopMotivationAnalysisChartData.value = res
					}
				}
			} else {
				if (isFullScreen) {
					shopMotivationAnalysisFullScreenChartData.value = {}
				} else {
					shopMotivationAnalysisChartData.value = {}
				}
				shopMotivationAnalysisData.value = {}
			}
		} catch (e) {
			console.error('门店动因分析 error', e)
		}
	}

	const handleDataType = () => {
		updateData()
	}

	const confirm = (e : any) => {
		channelIndex.value = e.indexs[0]
		show.value = false
		updateData()
	}

	const close = () => {
		show.value = false
	}

	const updateData = () => {
		toShopMotivationAnalysis()
	}

	// 全屏显示数据
	const handleFullScreen = async () => {
		if (dateTypeIndex.value == 1) {
			shopMotivationAnalysisFullScreenChartData.value = shopMotivationAnalysisChartData.value
			setTimeout(() => { stackedLineChartRef.value.isFullScreen = true }, 500)
		} else {
			await toShopMotivationAnalysis(true)
			setTimeout(() => { stackedLineChartRef.value.isFullScreen = true }, 500)
		}
	}
</script>

<style scoped lang="scss">
	.page {
		box-sizing: border-box;
		padding: 20rpx 0;
	}

	.container {
		background: #fff;
		margin: 20rpx;
		margin-top: 0;
		border-radius: 20rpx;
		padding: 40rpx;
	}


	.title {
		justify-content: space-between;
		margin-bottom: 40rpx;

		&--tab {
			margin-bottom: 9rpx;
		}

		&-text {
			color: #1f2428;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 32rpx;
		}


		&-btn {
			height: 48rpx;
			border: 2rpx solid #f2f2f2;
			border-radius: 24rpx;

			.btn-item {
				padding: 0 24rpx;
				font-size: 22rpx;
				line-height: 44rpx;
				color: rgba(31, 36, 40, 0.7);
			}

			.active {
				border-radius: 22rpx;
				background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				font-weight: bold;
				color: #fff;
			}
		}
	}

	.picker-section {
		border-radius: 200rpx;
		border: 2rpx solid #f2f2f2;
		margin-bottom: 40rpx;

		.picker {
			justify-content: space-between;
			padding: 16rpx 40rpx;
			color: #1f2428;
			font-size: 22rpx;
			line-height: 22rpx;
		}
	}

	.tabs {
		margin-bottom: 20rpx;
	}

	.full-screen {
		justify-content: flex-end;
		margin-bottom: 40rpx;

		&-btn {
			border-radius: 200rpx;
			border: 1rpx solid #ffffff;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			padding: 8rpx 16rpx;

			.btn-text {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;

				&-primary {
					color: #c35943;
				}
			}
		}
	}

	.key-indicators {
		// margin-bottom: 40rpx;
	}

	.table {
		border-radius: 20rpx;

		.th {
			border-radius: 20rpx 20rpx 0 0;
		}

		.th,
		.tr {
			height: 84rpx;
		}

		.th {
			background: rgba(255, 178, 161, 0.4);

			.td {
				flex: 1;
				color: #1f2428;
				font-size: 22rpx;
				font-weight: bold;
				text-align: center;
				line-height: 22rpx;
			}

		}

		.tbody {
			border-radius: 0 0 20rpx 20rpx;

			.tr {
				&:nth-child(2n) {
					background: rgba(247, 94, 59, 0.08);
				}

				&:nth-child(2n+1) {
					background: rgba(247, 94, 59, 0.04);
				}

				&:last-child {
					border-radius: 0 0 20rpx 20rpx;
				}

				.td {
					flex: 1;
					color: #1f2428;
					font-size: 22rpx;
					text-align: center;
					line-height: 22rpx;
				}
			}
		}
	}
</style>