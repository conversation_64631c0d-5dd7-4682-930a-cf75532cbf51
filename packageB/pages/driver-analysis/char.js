import * as echarts from 'echarts';

export function stackedLineChart(res) {
	return {
		legend: {
			data: ['卫生分', '服务分'],
			icon: "circle",
			bottom: 0,
			itemWidth: 8,
			itemHeight: 8,
			itemGap: 20,
			textStyle: {
				color: "#1F2428",
				fontSize: 12
			}
		},
		grid: {
			top: "5%",
			left: '15%'
		},
		xAxis: {
			type: 'category',
			boundaryGap: true, 
			data: ['7/1', '7/1', '7/1', '7/1', '7/1', '7/1', '7/1'],
			axisPointer: {
				type: 'shadow'
			},
			axisLine: {
				lineStyle: {
					color: "#F2F2F2"
				}
			},
			axisTick: {
				show: false
			},
			axisLabel: {
				color: "rgba(31, 36, 40, 0.7)",
			}
		},
		yAxis: {
			type: 'value',
			min: 0,
			max: 5,
			axisLine: {
				show: true,
				lineStyle: {
					color: "#F2F2F2"
				}
			},
			splitLine: {
				show: true,
				lineStyle: {
					type: "dashed"
				}
			},
			axisLabel: {
				color: "rgba(31, 36, 40, 0.7)"
			}
		},
		series: [{
				name: '卫生分',
				type: 'line',
				data: [4.7, 1.3, 3.0, 2.1, 3.1, 2.4, 3.2],
				symbol: "circle",
				symbolSize: 0,
				label: {
					show: true,
					distance: 4,
					color: "#FF8C1A"
				},
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
							offset: 0,
							color: '#FF8C1A'
						},
						{
							offset: 1,
							color: '#FFCF9E'
						}
					]),
				},
				lineStyle: {
					width: 3
				}
			},
			{
				name: '服务分',
				type: 'line',
				data: [2.1, 4.7, 2.1, 5.0, 3.2, 2.1, 2.4],
				symbol: "circle",
				symbolSize: 0,
				label: {
					show: true,
					distance: 4,
					color: "#43BF83"
				},
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
							offset: 0,
							color: '#43BF83'
						},
						{
							offset: 1,
							color: '#BCE3D0'
						}
					]),
				},
				lineStyle: {
					width: 3
				}
			},
		]
	};
}