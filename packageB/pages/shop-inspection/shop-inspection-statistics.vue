<template>
	<view class="container">
		<view class="section">
			<view class="date fr">
				<c-subsection-vue v-model="subsectionValue" :list="subsectionList" @change="handleSubsection" />
				<view class="month fr">
					<cDate ref="cDateRef" dateType="month" :minData="dayjs().subtract(1, 'year').valueOf()" @setDate="setDate">
					</cDate>
				</view>
			</view>
			<view class="area-picker">
				<cAreaPicker :defaultName="areaData.orgName" @setArea="setArea">
					<template #right>
					</template>
				</cAreaPicker>
			</view>
			<view class="fr score-section">
				<view class="fl score-block" v-for="(item,index) in constant" :key="index"
					:style="{backgroundImage:item.background}">
					<view class="fr text" :style="{backgroundImage:item.titleBackground}"
						@click="showModal(item.text, item.desc, item)">
						<view class="name">{{item.text}}</view>
						<image class="tip"
							src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729678941228.png"
							mode=""></image>
					</view>
					<view class="score">
						<text class="num">{{patrolOverView[item.valueProp]}}</text>{{item.data !== '-' ? item.unit : ''}}
					</view>
					<view class="fr month-on-month">
						<view class="month-on-month-text">环比</view>
						<image v-if="patrolOverView[item.qoqProp] && patrolOverView[item.qoqProp] != '-'" class="img"
							:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${patrolOverView[item.qoqProp].indexOf('-') ==-1 ? '1729644716250' : '1729644734157'}.png`">
						</image>
						<view class="month-on-month-value"
							:class="[patrolOverView[item.qoqProp] == '-' ?  '' : patrolOverView[item.qoqProp]?.indexOf('-') == -1 ? 'up-text' : 'down-text']">
							{{patrolOverView[item.qoqProp] !== '-' ? patrolOverView[item.qoqProp]?.replace(/-/g, '') : patrolOverView[item.qoqProp]}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="section">
			<view class="tabs">
				<cTabs :list="tabs" v-model="dataTypeIndex" :isBig="false"
					itemStyle="height: 60rpx;align-items:flex-start;padding:0 20rpx" @change="handleDataType" />
			</view>
			<view class="char">
				<mixChartVue :data="patrolClassificationData" :pageScrollTop="pageScroll" />
			</view>
		</view>
		<view class="section">
			<view class="proportion-ratings">
				<ringChartVue :data="queryPatrolPurposeData" type="b"
					:bKeys="{labelKey:'dataName',valueKey:'shopCount',rateKey:'patrolRate'}" />
			</view>
		</view>
		<view class="section ranking-view">
			<view class="ranking-title">
				<view class="title-left">
					<text class="title-text">排行榜</text>
					<text class="sub-title-text">本月</text>
				</view>
			</view>

			<view class="ranking-btn">
				<cBtnGroupNoShop :level="areaData.level" v-model:tabType="tableType" @change="handleBtnGroupChange">
				</cBtnGroupNoShop>
			</view>

			<view class="target-table">
				<cTable :header="rankColumn" :list="rankList" height="660rpx" @updateList="sortList" @chooseItem="chooseItem"
					@scrolltolower="rankScrolltolower">
					<template #default="scope">
						<template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
					</template>
				</cTable>
			</view>
		</view>
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onPageScroll } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, ref } from 'vue';
	import dayjs from 'dayjs';
	import cSubsectionVue from "@/components/c-subsection/c-subsection.vue";
	import cDate from '@/components/c-date/index.vue'
	import cModal from '@/components/c-modal/index';
	import cAreaPicker from '@/components/c-area-picker/index.vue'
	import cTabs from '@/components/c-tabs/c-tabs.vue'
	import mixChartVue from '@/components/mix-chart/mix-chart.vue';
	import ringChartVue from '@/components/ring-chart/ring-chart.vue';
	import cBtnGroupNoShop from '@/components/c-btn-group-no-shop/index.vue'
	import cTable from '@/components/c-table/c-table.vue'
	import { useCommon } from '@/hooks/useGlobalData';
	import { queryMonthPatrolData, queryPatrolClassification, queryPatrolOverView, queryPatrolPurpose } from '../../api';
	import { column } from './enums';
	import { PaginationParams } from '@/types/index.d.ts';

	const instance = getCurrentInstance() as ComponentInternalInstance
	const { globalData } = useCommon(instance)
	const userInfo = ref(globalData.value.user)

	const subsectionList : { name : string, value : string }[] = [
		{ name: '本月', value: 'month' },
		{ name: '上月', value: 'last-month' },
	]

	const constant = [
		{
			text: '巡店率',
			desc: '已巡店/门店总数',
			titleBackground: 'linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 178, 161, 0.05) 0%, rgba(247, 94, 59, 0.05) 100%)',
			valueProp: 'patrolRate',
			qoqProp: 'patrolRateHb',
		},
		{
			text: '门店总数',
			desc: '所选权限下的门店总数，排除门店状态为作废、停业、解约的门店',
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%)',
			valueProp: 'totalShopCount',
			qoqProp: 'totalShopCountHb',
		},
		{
			text: '已巡店',
			desc: '已完成巡店的门店数',
			titleBackground: 'linear-gradient(25.9deg, #ff8c1a 0%, #ffcf9e 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 140, 26, 0.05) 0%, rgba(255, 140, 26, 0.05) 100%)',
			valueProp: 'patrolShopCount',
			qoqProp: 'patrolShopCountHb',
		},
		{
			text: '自动巡店目标数',
			desc: '命中巡店策略的门店数',
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%)',
			valueProp: 'autoTargetCount',
			qoqProp: 'autoTargetCountHb',
		},
		{
			text: '自动巡店完成数',
			desc: '命中巡店策略的门店中，已完成巡店的门店数',
			titleBackground: 'linear-gradient(25.9deg, #ff8c1a 0%, #ffcf9e 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 140, 26, 0.05) 0%, rgba(255, 140, 26, 0.05) 100%)',
			valueProp: 'autoCompleteCount',
			qoqProp: 'autoCompleteCountHb',
		},
		{
			text: '自动巡店完成率',
			desc: '自动巡店完成数/自动巡店目标数',
			titleBackground: 'linear-gradient(17.3deg, #6046f2 0%, #c7a2fc 100%)',
			background: 'linear-gradient(203.5deg, rgba(96, 70, 242, 0.05) 0%, rgba(199, 162, 252, 0.05) 100%)',
			valueProp: 'autoCompleteCount',
			qoqProp: 'autoCompleteCountHb',
		},
		{
			text: '手动巡店完成数',
			desc: '手动巡店完成数',
			titleBackground: 'linear-gradient(17.3deg, #6046f2 0%, #c7a2fc 100%)',
			background: 'linear-gradient(203.5deg, rgba(96, 70, 242, 0.05) 0%, rgba(199, 162, 252, 0.05) 100%)',
			valueProp: 'autoCompleteCount',
			qoqProp: 'autoCompleteCountHb',
		},
		{
			text: '新增网评分',
			desc: '新增网评分总分/总点评数',
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%)',
			valueProp: 'integratedScore',
			qoqProp: 'integratedScoreHb',
		},
		{
			text: '差评率',
			desc: '差评数/总点评数',
			titleBackground: 'linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 178, 161, 0.05) 0%, rgba(247, 94, 59, 0.05) 100%)',
			valueProp: 'negativeCommentRate',
			qoqProp: 'negativeCommentRateHb',
		}
	]


	const tabs = [
		{ name: '门店状态', value: 1 },
		{ name: '命中策略', value: 2 },
	]

	const subsectionValue = ref<number>(0), // 默认近一周
		monthDate = ref<string>(''), // 日期
		patrolOverView = ref({}), // 巡店概览数据
		dataTypeIndex = ref<number>(0),
		cDateRef = ref(null),
		pageScroll = ref<number>(), // 页面滚动高度
		patrolClassificationData = ref({}),
		queryPatrolPurposeData = ref({})

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')

	// 组织树相关
	const areaData = ref<{ orgId : number | null, level : string, orgName : string }>({
		orgId: userInfo.value.orgList[0].orgId,
		level: userInfo.value.orgList[0].level,
		orgName: userInfo.value.orgList[0].orgName,
	})

	const tableType = ref(areaData.value.level), // 10 总区；30 大区；40 城区；50 门店；
		rankColumn = ref([]),
		rankList = ref([]),
		rankParams = ref<{ [key : string] : any }>({}), // 下钻params
		sortParams = ref<{ orderBy : number, sortBy : string }>(), // 排序params
		loadMore = ref<boolean>(true),
		tableWidth = ref<string>(),
		pagination = ref<PaginationParams>({
			pageNum: 1,
			pageSize: 20
		})

	onLoad((opt:any) => {
    if(opt.level && opt.orgId && opt.orgName) {
      areaData.value.level = opt.level
      areaData.value.orgId = opt.orgId
      areaData.value.orgName = opt.orgName
    }
		handleDateFormat()
	})

	onShow(() => {
		reset()
		setHeader(tableType.value)
		updateData()
	})

	onMounted(() => {
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.target-table`).boundingClientRect((res : any) => {
			tableWidth.value = res.width + 'px !important'
		}).exec();
	})

	// 查询巡店概览数据
	const toQueryPatrolOverView = async () => {
		try {
			const { code, data } = await queryPatrolOverView({
				orgId: areaData.value.orgId,
				level: areaData.value.level,
				monthDate: monthDate.value
			})
			if (code == 200) {
				patrolOverView.value = data
			}
		} catch (e) {
			console.error('查询巡店概览数据 error', e)
		}
	}

	// 查询巡店分类数据
	const toQueryPatrolClassification = async () => {
		try {
			const { code, data } = await queryPatrolClassification({
				orgId: areaData.value.orgId,
				level: areaData.value.level,
				monthDate: monthDate.value,
				dataType: tabs[dataTypeIndex.value].value
			})
			if (code == 200) {
				if (data) {
					const res = {
						categories: data.map(({ dataName }) => dataName),
						values: [
							{
								name: '门店数(求和)',
								type: 'column',
								index: 0,
								data: data.map(({ shopCount }) => shopCount == '-' ? 0 : shopCount || 0),
							},
							{
								name: '巡店率(求和)',
								type: 'line',
								index: 0,
								textColor: '#43BF83',
								color: '#43BF83',
								linearColor: [[0, '#43BF83'], [1, '#BCE3D0']],
								data: data.map(({ patrolRate }) => patrolRate == '-' ? 0 : patrolRate || 0)
							}
						]
					}
					patrolClassificationData.value = res
				} else {
					patrolClassificationData.value = {}
				}
			}
		} catch (e) {
			console.error('查询巡店分类数据 error', e)
		}
	}

	const formatChartsData = (v : any) => {
		return v.map(({ dataName, shopCount }) => {
			return {
				name: dataName,
				value: Number(shopCount),
				labelShow: false
			}
		})
	}

	// 巡店目的统计数据
	const toQueryPatrolPurpose = async () => {
		try {
			const { code, data } = await queryPatrolPurpose({
				orgId: areaData.value.orgId,
				level: areaData.value.level,
				monthDate: monthDate.value,
			})
			if (code === 200) {
				if (data && data.length > 0) {
					queryPatrolPurposeData.value = { originData: data, data: formatChartsData(data) }
				} else {
					queryPatrolPurposeData.value = {}
				}
			}
		} catch (e) {
			console.error('巡店目的统计数据 error', e)
		}
	}

	// 月度区域巡店数据
	const toQueryMonthPatrolData = async () => {
		try {
			const params = {
				tableType: tableType.value,
				monthDate: monthDate.value,
				...areaData.value,
				...pagination.value,
				...sortParams.value,
				...rankParams.value
			}
			const { data } = await queryMonthPatrolData(params)
			if (data) {
				if (data.length < pagination.value.pageSize) loadMore.value = false
				nextTick(() => {
					if (rankList.value && rankList.value.length > 0)
						rankList.value.push(...data)
					else
						rankList.value = data || []
				})
			} else {
				rankList.value = []
			}
		} catch (e) {
			console.error('月度区域巡店数据 error', e)
		}
	}

	const updateData = () => {
		toQueryPatrolOverView()
		toQueryPatrolClassification()
		toQueryPatrolPurpose()
	}

	const handleSubsection = () => {
		handleDateFormat()
		updateData()
	}

	// 日期参数格式化
	const handleDateFormat = () => {
		const value = subsectionList[subsectionValue.value].value
		if (value === 'month') {
			monthDate.value = dayjs().format('YYYY-MM')
		} else if (value === 'last-month') {
			monthDate.value = dayjs().subtract(1, 'month').format('YYYY-MM');
		}
		if (cDateRef.value) {
			cDateRef.value.monthValue = monthDate.value
			cDateRef.value.dateShow = monthDate.value
		}
	}

	const setDate = (date : string) => {
		monthDate.value = date
		if (date == dayjs().format('YYYY-MM')) {
			subsectionValue.value = 0
		} else if (date == dayjs().subtract(1, 'month').format('YYYY-MM')) {
			subsectionValue.value = 1
		} else {
			subsectionValue.value = -1
		}
		updateData()
	}

	const showModal = (title : string, content : string, data : any) => {
		modalShow.value = true
		modalTitle.value = title
		modalContent.value = content
	}

	// 区域筛选
	const setArea = (id : number, level : string, name : string) => {
		areaData.value.orgId = id
		areaData.value.level = level
		areaData.value.orgName = name
		updateData()
		reset()
		nextTick(() => {
			toQueryMonthPatrolData()
		})
	}

	const handleDataType = () => {
		toQueryPatrolClassification()
	}

	// 区域维度切换
	const handleBtnGroupChange = (level : string) => {
		rankParams.value = {}
		sortParams.value = null
		reset()
		setHeader(level)
	}

	// 表格下钻
	const chooseItem = (data : any) => {
		if (!data.canClick) return
		sortParams.value = null
		tableType.value = null
		loadMore.value = true
		rankParams.value = {
			orgId: data.orgId,
			level: data.level,
			tableType: (data.level == 10 ? Number(data.level) + 20 : Number(data.level) + 10).toString()
		}
		reset()
		setHeader(rankParams.value.tableType)
	}


	// 表格排序
	const sortList = (item : any) => {
		const orderBy = item.orderByColumn == 'patrolRate'
			? 1
			: item.orderByColumn == 'integratedScore'
				? 2
				: item.orderByColumn == 'negativeCommentRate'
					? 3
					: item.orderByColumn == 'shopCount'
						? 4 : null
		sortParams.value = {
			orderBy,
			sortBy: item.sortType
		}
		reset()
		toQueryMonthPatrolData()
	}

	const reset = () => {
		loadMore.value = true
		rankList.value = []
		pagination.value = {
			pageNum: 1,
			pageSize: 20
		}
	}

	const rankScrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		toQueryMonthPatrolData()
	}

	const setHeader = (level : number | string) => {
		rankColumn.value = JSON.parse(JSON.stringify(column))
		switch (Number(level)) {
			case 10:
				rankColumn.value.splice(1, 2,
					{
						name: '地区总部',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '负责人',
						prop: 'chargeMan',
						width: '120'
					},
				);
				break;
			case 30:
				rankColumn.value.splice(1, 2,
					{
						name: '大区',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '大区总',
						prop: 'chargeMan',
						width: '120'
					},
				);
				break;
			case 40:
				rankColumn.value.splice(1, 2,
					{
						name: '城区',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '城区总',
						prop: 'chargeMan',
						width: '120'
					},
				);
				break;
			case 50:
				rankColumn.value = []
				break;
			default:
				break;
		}
		toQueryMonthPatrolData()
	}

	onPageScroll((e) => {
		pageScroll.value = e.scrollTop
	})
	const padding_bottom = (uni.getSystemInfoSync().safeAreaInsets.bottom || 10) + 'px'
</script>

<style scoped lang="scss">
	:deep() {
		.empty-img .empty {
			width: v-bind(tableWidth);
		}
	}

	:deep(.c-modal) {
		width: auto !important;
		height: auto !important;
	}

	.container {
		padding: 20rpx;
		padding-bottom: v-bind(padding_bottom);
	}

	.section {
		padding: 40rpx;
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
	}

	.date {
		justify-content: space-between;
		margin-bottom: 36rpx;
	}


	.area-picker {
		margin-bottom: 40rpx;

		:deep(.area-left) {
			background: #ffffff !important;
			border: 2rpx solid #f2f2f2;
		}
	}

	.score-section {
		flex-wrap: wrap;
		justify-content: space-around;
		gap: 20rpx;

		/* 垂直等间距 */
		.score-block {
			min-width: 196rpx;
			border-radius: 20rpx;
			align-items: center;
			padding-bottom: 28rpx;

			.text {
				text-align: left;
				justify-content: center;
				border-radius: 100rpx 100rpx 100rpx 0;
				padding: 12rpx 0;
				margin-bottom: 18rpx;
				width: 100%;

				.name {
					color: #ffffff;
					font-size: 24rpx;
					font-weight: bold;
					line-height: 24rpx;
				}

				.tip {
					width: 22rpx;
					height: 22rpx;
					flex-shrink: 0;
					margin-left: 8rpx;
				}
			}

			.score {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
				margin-bottom: 18rpx;

				.num {
					color: #1f2428;
					font-size: 42rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					line-height: 42rpx;
					margin-right: 12rpx;
				}
			}

			.month-on-month {
				&-text {
					color: rgba(31, 36, 40, 0.7);
					font-size: 22rpx;
					line-height: 22rpx;
				}

				.img {
					width: 16rpx;
					height: 20rpx;
					flex-shrink: 0;
					margin-left: 8rpx;
				}

				&-value {
					font-size: 24rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					line-height: 24rpx;
					margin-left: 8rpx;
				}
			}

			.up-text {
				color: #FF4D4D;
			}

			.down-text {
				color: #56CC93
			}
		}
	}

	.ranking-view {

		.ranking-title {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 40rpx;

			.title-left {
				display: flex;
				align-items: center;
				height: 32rpx;

				.title-text {
					font-size: 32rpx;
					font-weight: bold;
				}

				.sub-title-text {
					padding-left: 20rpx;
					font-size: 24rpx;
					color: #999999;
				}
			}
		}

		.ranking-btn {
			padding: 8rpx 0 20rpx 0;
		}

		.ranking-table,
		.brand-table {
			border-radius: 20rpx;
			overflow: hidden;

			.name-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.item-text {
					display: -webkit-box;
					max-width: 120rpx;
					overflow: hidden; //文本超出隐藏
					text-overflow: ellipsis;
					-webkit-box-orient: vertical; //文本显示方式，默认水平
					-webkit-line-clamp: 2; //设置显示多少行
				}

				.line-item {
					text-decoration: underline;
				}

				.item-info {
					padding-top: 12rpx;
					display: flex;
					align-items: center;

					.info-text {
						font-size: 18rpx;
						line-height: 20rpx;
						color: #999999;
					}

					.item-icon {
						width: 12rpx;
						height: 16rpx;
						margin: 0 8rpx;
					}

					.item-num {
						font-size: 20rpx;
						line-height: 20rpx;
						font-weight: bold;
						font-family: "DIN Bold";
					}

					.up-num {
						color: #FF4D4D;
					}

					.down-num {
						color: #56CC93;
					}
				}
			}
		}
	}

	:deep(.char) {
		.u-popup__content {
			border-radius: none;
		}
	}
</style>