<template>
	<view class="container">
		<commonBgVue height="414" />
		<u-navbar placeholder title="抄送人" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="custom-search">
			<up-search v-model="keyWords" show-action placeholder="请输入抄送人姓名/工号查询" @custom="search" action-text="查询" />
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" class="scroll-view" :scroll-anchoring="true">
				<view class="content">
					<view class="selected-CC-section">
						<view class="selected-CC">
							<view class="title">已选抄送人</view>
							<view class="CC-list" v-if="selectedCC.length > 0">
								<view class="CC-item" v-for="(item,index) in selectedCC" :key="index" @click="removeSelectedCC(item)">
									<cc :data="item" mode="check" />
								</view>
							</view>
							<view class="no-CC" v-else>还未选择抄送人哦～</view>
						</view>
					</view>
					<template>
						<view class="CC-item" v-if="oaList && oaList.length > 0 " v-for="(item,index) in oaList" :key="index"
							@click="selectCC(item)">
							<cc :data="item" />
						</view>
						<view class="empty"
							v-if="(!oaList || oaList.length <= 0)">
							<emptyVue />
						</view>
					</template>
				</view>
			</scroll-view>
		</view>
	</view>
	<fixed-btn-layout>
		<view class="custom-btn custom-btn-primary" @click="submit">确认</view>
	</fixed-btn-layout>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import fixedBtnLayout from '../../components/fixed-btn-layout/fixed-btn-layout.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import emptyVue from '../../../components/empty/empty.vue';
	import cc from './components/cc/cc.vue';
	import {  queryOaInfoByKeywords } from './api';
	import { showToast } from '@/utils/util';

	type OaList = {
		name : string
		userId : number
		departmentName : string
		selected ?: any
	}[]

	const { globalData, route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()

	const height = ref()
	const keyWords = ref<string>(), oaList = ref<OaList>()

	const selectedCC = ref([]);// 已选中抄送人

	const dataSet = ref({ department: [], userList: [] }), // 数据集合
		page = ref(0) // 页面层级

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	onLoad((options : any) => {
		if (options.cc) {
			options.cc.split(',').forEach(e => {
				toQueryOaInfoByKeywords(e)
			});
		}
	})

	watch(
		() => page.value,
		(v) => {
			const info = uni.getSystemInfoSync()
			const query = uni.createSelectorQuery().in(instance.proxy);
			if (v > 0) {
				query.select(`.list`).boundingClientRect((res : any) => {
					height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) - 80 - 26 + 'px'
				}).exec();
			} else {
				nextTick(() => {
					query.select(`.list`).boundingClientRect((res : any) => {
						height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) - 80 + 'px'
					}).exec();
				})
			}
		}
	)

	const search = () => {
		toQueryOaInfoByKeywords()
	}

	// 查询用户信息
	const toQueryOaInfoByKeywords = async (e ?: string) => {
		try {
			const { data } = await queryOaInfoByKeywords({ keyWords: e || keyWords.value })
			if (data) {
				console.log(selectedCC.value);
				if (!e) { // 通过搜索获取用户信息
					oaList.value = data?.map((item : any) => {
						return {
							...item,
							selected: selectedCC.value.some(v => v.userId === item.userId)
						}
					}) || []
				} else { // 查询已选
					selectedCC.value.push(data?.map((item : any) => {
						return {
							...item,
							selected: true
						}
					})[0])
				}
			} else {
				oaList.value = []
			}
		} catch (e) {
			console.error('查询用户信息 error', e)
		}
	}

	// 选中抄送人
	const selectCC = (item : OaList[number]) => {
		if (globalData.value.user?.userId === item.userId) {
			showToast('抄送人不能是巡店人')
			return
		}
		if (globalData.value.user?.managerId === item.userId) {
			showToast('抄送人不能是审核人')
			return
		}
		item.selected = !item.selected
		// 使用 findIndex 来查找具有相同 id 的对象
		const index = selectedCC.value.findIndex(i => i.userId === item.userId);
		if (item.selected) {
			// 如果选中并且不在列表中，将其添加
			if (index === -1) {
				selectedCC.value.push(item)
			}
		} else {
			// 如果未选中并且在列表中，将其移除
			if (index !== -1) {
				selectedCC.value.splice(index, 1);
			}
		}
	}

	// 移除门店
	const removeSelectedCC = (item : OaList[number]) => {
		const index = selectedCC.value.findIndex(i => i === item);
		if (index !== -1) {
			// 使用 splice 从 selectedCC 中移除指定的元素
			selectedCC.value.splice(index, 1);
			oaList.value.forEach(v => {
				if (v.userId === item.userId) v.selected = false
			})
		}
	}

	// 点击确认
	const submit = () => {
		const pages = getCurrentPages()
		const prevPage : any = pages[pages.length - 2]
		if (prevPage.route === 'packageB/pages/shop-inspection/shop-inspection-application') {
			prevPage.CC = {
				name: selectedCC.value.map(item => item.name).join(','),
				userId: selectedCC.value.map(item => item.userId).join(',')
			} || {};
			uni.navigateBack()
		}
	}
</script>

<style lang="scss" scoped>
	.container {}

	:deep() {
		.area-left {
			background: #ffffff !important;
		}
	}

	.custom-search {
		:deep() {
			.u-search__content {
				background-color: #ffffffe6 !important;
			}

			.u-search__content__input {
				background-color: transparent !important;
			}
		}
	}

	.back {
		margin-top: 20rpx;
		margin-left: 40rpx;

		.text {
			color: #ffffff;
			font-size: 28rpx;
			line-height: 28rpx;
			margin-left: 20rpx;
		}
	}

	.scroll-view {
		overflow: hidden;
		border-radius: 20rpx;
	}

	.list {
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0;

		.content {}

		:deep() {
			.CC {
				margin-bottom: 20rpx;
			}

			&:last-child {
				margin-bottom: 0;
			}
		}

		.department-item {
			justify-content: space-between;
			border-radius: 20rpx;
			background: #ffffff;
			padding: 28rpx 40rpx;
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.folder {
				margin-right: 20rpx;
				width: 70rpx;
				height: 70rpx;
				flex-shrink: 0;
			}

			.text {
				color: #1f2428;
				font-size: 28rpx;
				font-weight: bold;
				line-height: 28rpx;
			}
		}


		.CC-item {
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.selected-CC-section {
			position: sticky;
			top: 0;
			padding-bottom: 20rpx;
			z-index: 20000;

			.selected-CC {
				padding: 40rpx;
				background: #FFFFFF;
				border-radius: 20rpx;

				.title {
					color: #1f2428;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 32rpx;
					margin-bottom: 40rpx;
				}

				.no-CC {
					border-radius: 20rpx;
					opacity: 1;
					background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
					color: #999999;
					font-size: 24rpx;
					line-height: 24rpx;
					padding: 40rpx;
					text-align: center;
				}

				.CC-list {
					max-height: 300rpx;
					overflow-y: auto;
				}

				:deep() {
					.CC {
						background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
					}
				}
			}
		}
	}

	.empty {
		background: #ffffff;
		border-radius: 20rpx;
	}
</style>