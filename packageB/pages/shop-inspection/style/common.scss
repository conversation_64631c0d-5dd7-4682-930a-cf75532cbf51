	.space-between {
		justify-content: space-between;
	}

	.content {
		background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
		padding: 20rpx 24rpx 40rpx;
		border-radius: 20rpx;

		&-title {
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
	}
	
	.section {
		padding: 20rpx 40rpx;
		border-radius: 20rpx;
		background: #ffffffe6;
		margin-bottom: 20rpx;
	
		&:last-child {
			margin-bottom: 0;
		}
	
		.label {
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			line-height: 40rpx;
		}
		
		.images-tip {
			color: #999999;
			font-size: 24rpx;
			line-height: 36rpx;
		}
	}
	
	.tip {
		padding: 20rpx 24rpx;
	
		.text {
			color: #1f2428;
			font-size: 20rpx;
			font-weight: 400;
			line-height: 32rpx;
		}
	
		.href {
			color: #1e61fc;
			font-size: 24rpx;
			line-height: 24rpx;
			font-weight: bold;
			margin-top: 20rpx;
			text-decoration: underline;
		}
	}
	