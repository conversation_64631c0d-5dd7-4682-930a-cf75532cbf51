<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="278" />
		<u-navbar placeholder title="巡店申请" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" @leftClick="customBack">
      <template #left>
        <template v-if="pages.length > 1">
          <u-icon name="arrow-left" size="20" color="#FFF"></u-icon>
        </template>
        <template v-else>
          <u-icon name="home" size="22" color="#FFF"></u-icon>
        </template>
      </template>
		</u-navbar>
		<view class="content" :class="{'disabled':plannedState === 6}">
			<view class="section row" v-if="plannedState <= 6">
				<view class="label">计划当前状态：</view>
				<view class="value" :style="{'color': planStateColor[plannedState] + ' !important'}">
					{{planStateToStringEnums[plannedState]}}
				</view>
			</view>
			<view class="section row" v-if="plannedState === 2">
				<view class="label">原因：</view>
				<view class="value">{{form.backReason}}</view>
			</view>
			<view class="section row">
				<view class="label">计划类型:</view>
				<view class="value">{{form.tripType === 2 ? '自动生成' : '手动发起'}}</view>
			</view>
			<view class="section" style="padding-top: 0;">
				<view class="top fr">
					<view class="title fr" style="padding: 40rpx 0;">
						<view class="t1">计划门店：</view>
						<view class="fr" v-if="form.shopList?.length > 0">
							<text class="num">{{form.shopList?.length}}</text><text style="margin-top: 4rpx;">(家)</text>
						</view>
					</view>
					<!-- 草稿或申请 -->
					<view class="right fr" v-if="(plannedState == 0 || plannedState == 2 || plannedState > 6) && globalData?.user?.userId === form.patrolMan"
						@click="route_to_view(`/packageB/pages/shop-inspection/shop-search?shopIds=${JSON.stringify(shopIds)}`)">
						<text class="text">请选择计划门店</text>
						<up-icon name="arrow-right" color="#999999" size="22rpx" />
					</view>
					<!-- 已审核且是申请人 -->
					<view class="right fr" v-if="plannedState == 3 && globalData?.user?.userId === form.patrolMan"
						@click="route_to_view(`/packageB/pages/shop-inspection/shop-list?id=${form.orderId}&tripType=${form.tripType}`)">
						<text class="text">查看门店清单</text>
						<up-icon name="arrow-right" color="#999999" size="22rpx" />
					</view>
				</view>
				<view class="table-section" style="margin-top: 0;">
					<planned-stores :plannedState="plannedState" :tripType="form.tripType" :data="form.shopList" :all-data="form"
						@change="handlPlannedPurpose"
						:mode="(plannedState === 1 || plannedState === 3 || globalData?.user?.userId !== form.patrolMan) ? '' :plannedState === 6 ? 'disabled': 'edit'" />
				</view>
			</view>
			<view class="section">
				<view class="top fr">
					<view class="title fr">
						<view class="t1">起始时间：</view>
						<view class="fr" v-if="form.shopList.length > 0">
							<text
								class="num">{{form.tripType !== 2 ? days : (dayjs(form.endDate).diff(dayjs(form.beginDate), 'day') + 1)}}</text><text
								style="margin-top: 4rpx;">(天)</text>
							<text
								style="margin-left: 18rpx;margin-top: 4rpx;">{{form.tripType !== 2 ? beginTime : form.beginDate}}-{{form.tripType !== 2 ? overTime : form.endDate}}</text>
						</view>
					</view>
				</view>
				<view class="table-section">
					<starting-time :data="form.shopList" @change="handleStartingTime"
						:mode="(plannedState === 1 || plannedState === 3 || globalData?.user?.userId !== form.patrolMan) ? '' : plannedState === 6 ? 'disabled': 'edit'" />
				</view>
			</view>
			<view class="section row">
				<view class="label">巡店人：</view>
				<view class="value">{{form.patrolManName}}</view>
			</view>
			<view class="section row"
				v-if="form.copyManName || !(plannedState === 1 || (plannedState === 2 && globalData?.user?.userId === form.managerId) || plannedState === 3|| plannedState === 6)"
				@click="!(plannedState !== 1 && plannedState !== 3 && plannedState !== 6  && (plannedState !== 2 || globalData?.user?.userId === form.patrolMan)) ? null : route_to_view(`/packageB/pages/shop-inspection/carbon-copy?cc=${form.copyMan}`)">
				<view class="label">抄送人：</view>
				<view class="fr">
					<view class="value" v-if="form.copyManName">{{form.copyManName}}</view>
					<view class="placeholder" v-else>
						<view class="text">请选择计划抄送人</view>
					</view>
					<view style="margin-left: 16rpx;" v-if="plannedState !== 1 && plannedState !== 3 && plannedState !== 6  && (plannedState !== 2 || globalData?.user?.userId === form.patrolMan)">
						<up-icon name="arrow-right" color="#999999" size="22rpx" />
					</view>
				</view>
			</view>
			<view class="section row">
				<view class="label">审核人：</view>
				<view class="value">{{form.managerName}}</view>
			</view>
			<view class="section">
				<view class="label">巡店说明：</view>
				<view class="textarea">
					<view class="text" v-if="plannedState === 1 || plannedState === 3|| plannedState === 6">{{form.memo}}</view>
					<cTextareaVue v-else v-model="form.memo" count maxlength="500" />
				</view>
				<view
          v-if="plannedState === 3 && globalData?.user?.userId === form.managerId" class="href"
          @click="route_to_view(`/packageB/pages/shop-inspection/shop-inspection-statistics?orgId=${form.orgId}&orgName=${form.orgName}&level=${form.level}`)">查看巡店统计
        </view>
			</view>
		</view>
	</view>
	<template
		v-if="plannedState !== 6 && (plannedState !== 2 && plannedState !== 3 || globalData?.user?.userId === form.patrolMan) && globalData?.user?.userId !== form.copyMan">
		<fixed-btn-layout>
			<!-- 草稿 -->
			<template v-if="plannedState === 0">
				<view class="custom-btn" @click="modifyApplicationStatus(7)">取消</view>
				<view class="custom-btn" @click="toSavePatrolOrder(0)">保存</view>
				<view class="custom-btn custom-btn-primary" @click="toSavePatrolOrder(1)">提交</view>
			</template>
			<!-- 待审核 -->
			<template v-if="plannedState === 1">
				<!-- 审核人 -->
				<template v-if="globalData?.user?.userId === form.managerId">
					<view class="custom-btn" @click="operationModalShow = true">审核驳回</view>
					<view class="custom-btn" @click="toOrderAudit(3)">审核通过</view>
				</template>
				<!-- 申请人 -->
				<view v-else class="custom-btn custom-btn-primary" @click="modifyApplicationStatus(0)">撤销申请</view>
			</template>
			<!-- 被驳回 -->
			<template v-if="plannedState === 2">
				<view class="custom-btn" @click="modifyApplicationStatus(6)">计划作废</view>
				<view class="custom-btn" @click="toSavePatrolOrder(0)">保存</view>
				<view class="custom-btn custom-btn-primary" @click="toSavePatrolOrder(1)">提交</view>
			</template>
			<!-- 已审批 -->
			<template v-if="plannedState === 3">
				<view class="custom-btn" v-if="form.tripType !== 2" @click="modifyApplicationStatus(6)">计划作废</view>
				<view class="custom-btn" @click="handleModifyModal(1)">时间修改</view>
				<view class="custom-btn" @click="handleModifyModal(2)">目的修改</view>
			</template>
			<template v-if="plannedState > 6">
				<view class="custom-btn" @click="toSavePatrolOrder(0)">保存</view>
				<view class="custom-btn custom-btn-primary" @click="toSavePatrolOrder(1)">提交</view>
			</template>
		</fixed-btn-layout>
	</template>
	<template v-if="plannedState === 3 && modifyModalShow">
		<modify-modal :show="modifyModalShow" :data="form" :list="modifyModalList" :mode="modifyModalMode"
			@cancel="modifyModalShow=false" @confirm="toQueryPatrolOrderDetail"></modify-modal>
	</template>
	<template v-if="operationModalShow">
		<operationModalVue :show="operationModalShow" @confirm="handleOperationConfirm" @cancel="operationModalShow = false"
			placeholderText="请填写驳回原因" title="驳回申请" confirmButtonText="确定驳回申请" cancelButtonText="我再想想">
		</operationModalVue>
	</template>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, computed, getCurrentInstance, reactive, ref, toRefs} from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import fixedBtnLayout from '../../components/fixed-btn-layout/fixed-btn-layout.vue';
	import { planStateToStringEnums } from './enums';
	import dayjs from 'dayjs';
	import minMax from 'dayjs/plugin/minMax';
	import plannedStores from './components/planned-stores/planned-stores.vue';
	import startingTime from './components/starting-time/starting-time.vue';
	import cTextareaVue from '../../components/c-textarea/c-textarea.vue';
	import { orderAudit, queryPatrolOrderDetail, queryShopInfo, savePatrolOrder } from './api';
  import { hideLoading, showLoading, showToast } from '../../../utils/util';
	import modifyModal from './components/modify-modal/modify-modal.vue';
	import operationModalVue from './components/operation-modal/operation-modal.vue';
	dayjs.extend(minMax)
	const { globalData, route_to_view, customBack } = useCommon(getCurrentInstance() as ComponentInternalInstance)
  const instance = getCurrentInstance() as ComponentInternalInstance
  const pages = getCurrentPages()

	const planStateColor = { 0: '#1C7AC7', 1: '#E69850', 2: '#C35943', 3: '#56CC93', 6: '#1F2428' }
	const shopIds = ref<string[]>([]) // 已选门店编号
	const plannedState = ref() // 计划状态
	const form = reactive<any>({
		patrolManName: globalData.value?.user?.userName,
		patrolMan: globalData.value?.user?.userId,
		managerName: globalData.value?.user?.managerName,
		managerId: globalData.value?.user?.managerId,
		shopList: [], memo: ''
	})
	const modifyModalShow = ref<Boolean>(false), // 计划修改模态框显示
		modifyModalMode = ref<number>(), // 计划修改模态框模式
		modifyModalList = ref([]) // 计划修改门店list
	const orderId = ref('') //存储订单id
	const operationModalShow = ref<boolean>(false) // 驳回申请弹框
	const padding_bottom = computed(() => {
		if (!(plannedState.value !== 6 && (plannedState.value !== 2 && plannedState.value !== 3 || globalData.value?.user?.userId === form.patrolMan))) {
			return uni.getSystemInfoSync().safeAreaInsets.bottom + 'rpx'
		} else {
			return uni.getSystemInfoSync().safeAreaInsets.bottom + 180 + 'rpx'
		}
	})

	onLoad((options) => {
		console.log(options);
		orderId.value = options.id
		if (options.id) {
      showLoading('加载中')
      instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
        if (res) {
          toQueryPatrolOrderDetail(options.id)
        }
      })
		} else {
      plannedState.value = 999999
    }
	})

	onShow(() => {
		const pages = getCurrentPages();
		const currentPage : any = pages[pages.length - 1];
		const shopIdsFlag = Array.isArray(currentPage.shopIds) // 选择门店
		const CCFlag = currentPage.CC !== null && currentPage.CC !== undefined  // 选择抄送人
		if (shopIdsFlag) {
			handleShopIds(currentPage)
		}
		if (CCFlag) {
			handleCC(currentPage)
		}

		if (currentPage.refresh) {
			delete currentPage.refresh
			toQueryPatrolOrderDetail(orderId.value)
		}
	});

	// 选择后门店
	const handleShopIds = (currentPage : any) => {
		if (currentPage.shopIds.length === 0) {
			// 如果 shopIds 是空数组，清空 shopIds.value 和 form.shopList
			shopIds.value = [];
			form.shopList = []; // 清空数组
		} else {
			const newShopIds = currentPage.shopIds;
			const currentShopIds = Array.isArray(shopIds.value) ? shopIds.value : [];
			// 找到新增的 shopIds
			const addedShopIds = newShopIds.filter(shopId => !currentShopIds.includes(shopId));
			// 找到减少的 shopIds
			const removedShopIds = currentShopIds.filter(shopId => !newShopIds.includes(shopId));
			// 更新 shopIds.value
			shopIds.value = newShopIds;
      console.log(shopIds.value)
			// 处理新增的 shopIds
			if (addedShopIds.length > 0) {
				// 检查 addedShopIds 是否全部已存在于 form.shopList 中
				const allAddedExist = addedShopIds.every(shopId =>
					form.shopList.some(item => item.shopId === shopId)
				);

				// 仅在某些 addedShopIds 不存在于 form.shopList 时才请求接口
				if (!allAddedExist) {
					try {
						toQueryShopInfo(addedShopIds);
					} catch (e) {
						console.error('Error querying shop info for added shopIds', e);
					}
				}
			}
			// 处理减少的 shopIds
			if (removedShopIds.length > 0) {
				removedShopIds.forEach(shopId => {
					// 从 form.shopList 中删除 shopId
					const index = form.shopList.findIndex(item => item.shopId === shopId);
					if (index > -1) {
						form.shopList.splice(index, 1);
					}
				});
			}
		}

		// 重置 currentPage.shopIds
		currentPage.shopIds = null;
	}

	// 选择后抄送人
	const handleCC = (currentPage) => {
		form.copyMan = currentPage.CC.userId
		form.copyManName = currentPage.CC.name
		currentPage.CC = null;
	}

	// 查看门店信息
	const toQueryShopInfo = async (shopIds : string[]) => {
		try {
			const { data } = await queryShopInfo({ shopIdList: shopIds })
			const newArr = form.shopList.concat(data?.map(item => ({
				...item,
				ifShow: true, // 手动发起计划需要展示线上运营分析会
				beginTime: dayjs().format('YYYY-MM-DD'),
				overTime: dayjs().add(1, 'day').format('YYYY-MM-DD')
			})))
			form.shopList = newArr
		} catch (e) {
			console.error('查看门店信息 error', e)
		}
	}

	const beginTime = computed(() => {
		// 提取所有的 beginTime
		const beginTimes = form.shopList.map(item => dayjs(item.beginTime));
		const minStartDate = dayjs.min(...beginTimes);
		return minStartDate.format('YYYY-MM-DD')
	})

	const overTime = computed(() => {
		// 提取所有的overTime
		const overTimes = form.shopList.map(item => dayjs(item.overTime));
		// 找到最大的 overTime
		const maxEndDate = dayjs.max(...overTimes);
		return maxEndDate.format('YYYY-MM-DD')
	})

	// 计算起始时间差值（天）
	const days = computed(() => {
		// 计算两者之间的天数差
		const dayDifference = dayjs(overTime.value).diff(dayjs(beginTime.value), 'day');
		return dayDifference + 1
	})

	// 修改计划目的
	const handlPlannedPurpose = (value : any, index : number, prop : string) => {
		form.shopList[index].target = value.value[0].target
		form.shopList[index].targetCode = value.value[0].targetCode
	}

	// 修改起始时间
	const handleStartingTime = (value, index : number, prop : string) => {
		form.shopList[index][prop] = dayjs(value.value).format('YYYY-MM-DD')
	}

	// 保存巡店表单
	const toSavePatrolOrder = async (type) => {
		if (type === 1) {
			if (form.shopList.length <= 0) {
				showToast('请选择巡店门店')
				return
			}
			if (!form.memo) {
				showToast('请填写巡店说明')
				return
			}
		}
		try {
			const { patrolManName, patrolMan, managerName, managerId, copyMan, copyManName, shopList, memo, orderId, } = form
			const params = {
				patrolManName, patrolMan, managerName, managerId, copyMan, copyManName, shopList, memo, orderId, shopCount: shopList.length,
				isSubmit: type,
			}
			const { code } = await savePatrolOrder(params)
			if (code === 200) {
				showToast('保存成功')
				setTimeout(() => uni.navigateBack(), 1500)
			}
		} catch (e) {
			console.error('保存巡店表单 error', e)
		}
	}

	// 查询巡店计划详情
	const toQueryPatrolOrderDetail = async (orderId : string) => {
		try {
			const { data } = await queryPatrolOrderDetail({ orderId })
			if (data && Object.keys(data).length > 0) {
				plannedState.value = data.status
				for (const key in data) {
					form[key] = data[key]
				}
			}
      shopIds.value = data.shopIds ? data.shopIds.split(",") : []
		} catch (e) {
			console.error('查询巡店计划详情 error', e)
		}
	}

	// 撤销申请
	const modifyApplicationStatus = (type : number) => {
		// type 0:撤回申请 6:计划作废 7:取消计划
		// status 门店状态 4:巡店中 5:已完成
		const flag = form.shopList.some(item => {
			return item.status && (item.status == 4 || item.status == 5)
		})
		if (flag) { showToast('已有门店开始巡店,该计划无法作废'); return }
		uni.showModal({
			title: '温馨提示',
			content: `您确认${type == 0 ? '撤回申请' : type == 6 ? '计划作废' : type == 7 ? '取消计划' : '该操作'}吗？该操作无法逆转`,
			cancelText: '再想想',
			confirmText: '确定',
			confirmColor: '#C35943',
			success(res) {
				if (res.confirm) {
					toOrderAudit(type)
				}
			}
		})
	}

	// 修改计划状态
	const toOrderAudit = async (type : number, text : string = null) => {
		try {
			const params = {
				orderId: form.orderId,
				status: type,
				repair: globalData.value.user?.userId,
				backReason: text
			}
			const { code } = await orderAudit(params)
			if (code === 200) {
				showToast('操作成功')
				setTimeout(() => {
          if (pages.length > 1) {
            uni.navigateBack()
          } else {
            operationModalShow.value = false
            toQueryPatrolOrderDetail(orderId.value)
          }
        }, 1500)
			}
		} catch (e) {
			console.error('修改计划状态 error', e)
		}
	}

	// 修改计划时间/目的
	const handleModifyModal = (mode : number) => {
		modifyModalList.value = form.shopList.filter(item => {
			return item.status === 3
		})
		console.log(modifyModalList.value);
		if (modifyModalList.value.length <= 0) {
			showToast('仅限未开始状态门店可修改')
			return
		}
		modifyModalMode.value = mode
		modifyModalShow.value = true
	}

	// 审核驳回
	const handleOperationConfirm = (text : string) => {
		toOrderAudit(2, text)
	}
</script>

<style scoped lang="scss">
	.disabled {

		.label,
		.value,
		.textarea>.text {
			color: #1f24284d !important;
		}
	}

	.content {
		margin-top: 20rpx;
		padding: 0 20rpx;
		flex: 1;
		overflow-y: auto;
		padding-bottom: v-bind(padding_bottom);

		.section {
			padding: 35rpx 40rpx;
			border-radius: 20rpx;
			background: #ffffff;
			margin-top: 20rpx;

			&:first-child {
				margin-top: 0;
			}

			.label {
				color: #1f2428;
				font-size: 26rpx;
				font-weight: bold;
				line-height: 36rpx;
				white-space: nowrap;
				flex-shrink: 0;
			}

			.value {
				color: #1f2428;
				font-size: 26rpx;
				font-weight: bold;
				line-height: 36rpx;
				text-align: right;
				width: 530rpx;
				word-break: break-all;
			}

			.textarea {
				margin-top: 40rpx;
				background: #ffffff;
				color: #1f2428;
				font-size: 24rpx;
				line-height: 24rpx;

				:deep() {
					.custom-textarea {
						.textarea {
							height: 336rpx;
						}
					}
				}
			}

			.href {
				color: #1e61fc;
				font-size: 26rpx;
				line-height: 26rpx;
				font-weight: bold;
				margin-top: 20rpx;
				text-decoration: underline;
			}
		}

		.row {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;

			.placeholder {
				.text {
					font-size: 24rpx;
					line-height: 36rpx;
					color: #999999;
				}
			}
		}

		.top {
			justify-content: space-between;

			.title {
				.t1 {
					color: #1f2428;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 32rpx;
				}

				text {
					color: $uni-text-color;
					font-size: 24rpx;
					line-height: 24rpx;
					font-weight: bold;
				}

				.num {
					font-size: 32rpx;
					line-height: 32rpx;
					margin-right: 8rpx;
				}
			}

			.text {
				color: #999999;
				font-size: 24rpx;
				line-height: 104rpx;
				margin-right: 16rpx;
			}
		}

		.table-section {
			margin-top: 40rpx;
		}
	}

	.btn {
		flex: 1;
		margin: 0 20rpx;
	}
</style>