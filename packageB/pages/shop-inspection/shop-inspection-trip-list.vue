<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="560" />
		<u-navbar placeholder title="行程跟进" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="picker-section ">
			<view class="row fr">
				<cPickerVue :columns="tripTargetEnums" keyName="label" :value="indexObj.tripTargetIndex"
					@change="e => bindPickerChange(e, 'tripTargetIndex')">
					<view class="picker">{{tripTargetEnums[indexObj.tripTargetIndex].label}}</view>
				</cPickerVue>
				<cPickerVue :columns="tripStateEnums" keyName="label" :value="indexObj.tripStateIndex"
					@change="e => bindPickerChange(e, 'tripStateIndex')">
					<view class="picker">{{tripStateEnums[indexObj.tripStateIndex].label}}</view>
				</cPickerVue>
			</view>
			<view class="fr">
				<cPickerVue :columns="planTypeEnums" keyName="label" :value="indexObj.planTypeIndex"
					@change="e => bindPickerChange(e, 'planTypeIndex')">
					<view class="picker">{{planTypeEnums[indexObj.planTypeIndex].label}}</view>
				</cPickerVue>
				<cPickerVue :columns="tripBelongEnums" keyName="label" :value="indexObj.tripBelongIndex"
					@change="e => bindPickerChange(e, 'tripBelongIndex')">
					<view class="picker">{{tripBelongEnums[indexObj.tripBelongIndex].label}}</view>
				</cPickerVue>
			</view>
		</view>
		<view class="search">
			<input class="uni-input" v-model="queryCondition" placeholder="行程编号/计划编号/门店ID/邀请人姓名" shape="circle" border="none"
				@blur="handleBlur" placeholder-class="placeholderClass" @focus="focus = true" />
			<view class="button-group fr">
				<view class="custom-btn btn" @tap="reset">重置</view>
				<view class="custom-btn custom-btn-primary btn" @tap="search">查询</view>
			</view>
		</view>
		<view class="count">
			行程数：{{ pagination.total }}
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view">
				<view class="trip-block" v-for="(item, index) in dataList" :key="index"
					@tap="route_to_view(`./shop-inspection-trip?orderId=${item.orderId}&shopId=${item.shopId}`)">
					<view class="linear">
						<tripDescVue :data="item">
							<template v-if="item.tripStatus !== 1009">
								<view class="progress" v-if="item.tripStatus != 1000">
									<view class="top fr" v-if="item.tripTime">
										<view class="title">最新进度</view>
										<view class="time">{{ item.tripTime }}</view>
									</view>
									<view class="state" v-if="item.followStatus">{{ item.followTitle }}</view>
									<view class="state" v-else>{{ item.latestProgress }}</view>
									<view class="position fr" v-if="getFlag(item)">
										<image class="img"
											src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-17/1734413732822.png"
											mode="aspectFill"></image>
										<view class="text">{{ item.nowLocation }}</view>
									</view>
								</view>
							</template>
						</tripDescVue>
					</view>
				</view>
				<view class="fr empty" v-if="!pagination.loading && pagination.total">
					<text class="empty-text">到底了~</text>
				</view>
				<emptyVue v-if="!dataList.length" hasBackground />
			</scroll-view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import emptyVue from '@/components/empty/empty.vue';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import { tripStateEnums, tripBelongEnums, tripTargetEnums, planTypeEnums } from './enums';
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, reactive, ref, unref } from 'vue';
	import { useCommon } from '../../../hooks/useGlobalData';
	import tripDescVue from './components/trip-desc/trip-desc.vue';
	import { selectShopInspectionTripPage as selectShopInspectionTripPageApi } from './api'
	import { newPageInstance } from '../../../types';

	const instance = getCurrentInstance()
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const height = ref<string>(),
		queryCondition = ref<string>(),
		indexObj = reactive({
			tripStateIndex: 0,
			tripBelongIndex: 0,
			tripTargetIndex: 0,
			planTypeIndex: 0
		}),
		pagination = reactive({
			currentPage: 1,
			pageSize: 10,
			total: 0,
			loading: true
		}),
		dataList = ref([]),
		focus = ref<Boolean>(false)

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.scroll-view`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})
	/**
	 * @description: 筛选条件
	 */
	const bindPickerChange = (e : any, field : string) => {
		indexObj[field] = e.indexs[0]
		// search()
	}
	/**
	 * @description: 最新进度的显示状态
	 */
	const getFlag = (item : any) => {
		return (item.tripStatus == 1002 || item.tripStatus == 1007) && item.targetCode != 6 && !item.followStatus
	}
	/**
	 * @description : 上拉加载更多
	 */
	const scrolltolower = async () => {
		if (!pagination.loading) return
		pagination.currentPage += 1
		await selectShopInspectionTripPage()
	}
	/**
	 * @description: 查询
	 */
	const search = () => {
		pagination.loading = true
		pagination.total = 0
		pagination.currentPage = 1
		dataList.value = []
		selectShopInspectionTripPage()
	}

	const handleBlur = () => {
		if (!focus.value) {
			search()
		}
	}

	/**
	 * @description: 重置
	 */
	const reset = () => {
		focus.value = false
		indexObj.planTypeIndex = 0
		indexObj.tripBelongIndex = 0
		indexObj.tripStateIndex = 0
		indexObj.tripTargetIndex = 0
		queryCondition.value = ''
		search()
	}
	/**
	 * @description: 获取列表
	 */
	const selectShopInspectionTripPage = async () : Promise<void> => {
		const { currentPage, pageSize } = pagination
		const belong = tripBelongEnums[indexObj.tripBelongIndex].value
		const tripStatus = tripStateEnums[indexObj.tripStateIndex].value
		const targetCode = tripTargetEnums[indexObj.tripTargetIndex].value
		const tripType = planTypeEnums[indexObj.planTypeIndex].value
		const params : any = {
			currentPage,
			pageSize,
			queryCondition: unref(queryCondition)
		}
		belong != null && (params.belong = belong)
		tripStatus != null && (params.tripStatus = tripStatus)
		targetCode != null && (params.targetCode = targetCode)
		tripType != null && (params.tripType = tripType)

		const res = await selectShopInspectionTripPageApi(params)
		console.log("🚀 ~ selectShopInspectionTripPage ~ res:", res)
		const { records, total } = res.data
		pagination.total = total
		if (records?.length < pagination.pageSize) {
			pagination.loading = false
		}
		dataList.value = [...dataList.value, ...(records || [])]
	}

	onLoad((opts) => {
		if (opts.tripStatus) {
			indexObj.tripStateIndex = tripStateEnums.findIndex((item : any) => item.value == opts.tripStatus)
		}
		selectShopInspectionTripPage()
	})

	onShow(() => {
		const pages = getCurrentPages()
		const currentPage : newPageInstance = pages[pages.length - 1]
		if (currentPage.refresh) {
			delete currentPage.refresh
			search()
		}
	})
</script>

<style scoped lang="scss">
	.picker-section {
		width: 100%;
		padding: 20rpx 10rpx 20rpx;

		.row {
			margin-bottom: 20rpx;
		}

		c-picker-vue {
			width: 100%;
		}

		:deep() {
			.c-picker {
				margin: 0 10rpx;
			}
		}
	}

	.search {
		margin: 0 20rpx;
		background: #ffffffe6;
		padding: 8rpx 8rpx 8rpx 40rpx;
		border-radius: 200rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		:deep() {
			.placeholderClass {
				color: #999999;
				font-size: 24rpx;
				font-weight: 500;
				line-height: 24rpx;
			}

			.uni-input {
				width: 420rpx;
			}
		}

		.button-group {
			.btn {
				width: auto;
				padding: 12rpx 28rpx;
				margin: 0 8rpx 0 2rpx;
				font-size: 24rpx;
				font-weight: bold;
				line-height: 24rpx;
			}
		}
	}

	.count {
		margin: 20rpx 0;
		margin-left: 40rpx;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bpld;
		line-height: 32rpx;
	}

	.list {
		overflow: hidden;
		border-radius: 20rpx;
		margin: 0 20rpx;
		// backface-visibility: hidden;
		// transform: translate3d(0, 0, 0);
		// -webkit-backface-visibility: hidden;
		// -webkit-transform: translate3d(0, 0, 0);

		.scroll-view {

			.trip-block {
				margin-bottom: 20rpx;
				background: #ffffff;
				border-radius: 20rpx;

				.linear {
					background: linear-gradient(180deg, rgba(247, 94, 59, 0.1) 0%, rgba(255, 178, 161, 0) 102rpx), rgba(255, 255, 255, 1) 100%;
					border-radius: 20rpx;
				}

				&:last-child {
					margin-bottom: 0;
				}

				.progress {
					margin-top: 82rpx;
					font-size: 28rpx;
					font-weight: bold;
					line-height: 28rpx;
					position: relative;

					&::before {
						position: absolute;
						content: '';
						height: 2rpx;
						left: -20rpx;
						right: -20rpx;
						top: -40rpx;
						background: #f2f2f2;
					}

					.top {
						justify-content: space-between;
						margin-bottom: 20rpx;

						.title {
							color: $uni-text-color;
						}

						.time {}
					}

					.state {
						color: #1f2428;
						margin-bottom: 20rpx;
					}

					.position {
						.img {
							width: 28rpx;
							height: 28rpx;
							flex-shrink: 0;
							margin-right: 8rpx;
						}

						.text {
							color: #999999;
							font-size: 24rpx;
							line-height: 24rpx;
						}
					}
				}
			}
		}
	}

	.empty {
		justify-content: center;
		font-size: 22rpx;

		&-text {
			color: #999;
		}
	}
</style>