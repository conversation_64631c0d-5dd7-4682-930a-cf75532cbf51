import { GetUserDepartmentInfoParams, ListAuditOrderParams, ListReportParams, OrderAuditParams, QueryShopInfoParams, SavePatrolOrderParams, UpdatePatrolOrderParams, UpdateShopStatusParams } from '../type'
import { successCallbackResult } from '@/types/index.d.ts'
import http from '@/utils/http'


// 获取巡店目的code列表
export const listTargetCode = () : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listTargetCode',
		method: 'POST',
	})
}

// 保存巡店表单
export const savePatrolOrder = (data : SavePatrolOrderParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/savePatrolOrder',
		method: 'POST',
		data
	})
}

// 查看门店信息
export const queryShopInfo = (data : QueryShopInfoParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/queryShopInfo',
		method: 'POST',
		data
	})
}

// 查询用户部门信息
export const getUserDepartmentInfo = (data : GetUserDepartmentInfoParams) : Promise<successCallbackResult> => {
	return http({
		url: '/hrm/getUserDepartmentInfo',
		method: 'POST',
		data
	})
}

// 查询用户信息
export const queryOaInfoByKeywords = (data : { keyWords : string }) : Promise<successCallbackResult> => {
	return http({
		url: '/hrm/queryOaInfoByKeywords',
		method: 'POST',
		data
	})
}

/**
 * @description: 查询巡店计划行程列表
 */
export const listPatrolOrder = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listPatrolOrder',
		method: 'POST',
		data
	})
}

// 修改巡店计划
export const updatePatrolOrder = (data : UpdatePatrolOrderParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/updatePatrolOrder',
		method: 'POST',
		data
	})
}
/**
 * @description: 巡店报告列表
 */
export const listReport = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listReport',
		method: 'POST',
		data
	})
}

// 查询巡店计划详情
export const queryPatrolOrderDetail = (data : { orderId : string }) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/queryPatrolOrderDetail',
		method: 'POST',
		data
	})
}
/**
 * @description: 门店清单列表
 */
export const listPatrolShop = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listPatrolShop',
		method: 'POST',
		data
	})
}
/**
 * @description: 巡店行程列表
 */
export const selectShopInspectionTripPage = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/selectShopInspectionTripPage',
		method: 'POST',
		data
	})
}
// 巡店表单状态修改
export const orderAudit = (data : OrderAuditParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/orderAudit',
		method: 'POST',
		data
	})
}

// 待审核巡店列表
export const listAuditOrder = (data : ListAuditOrderParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listAuditOrder',
		method: 'POST',
		data
	})
}

// 修改巡店门店状态
export const updateShopStatus = (data : UpdateShopStatusParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/updateShopStatus',
		method: 'POST',
		data
	})
}

/**
 * @description: 行程详情
 */
export const getShopInspectionTripDetail = (data ?: any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/getShopInspectionTripDetail',
		method: 'POST',
		data
	})
}

/**
 * @description: 行程节点
 */
export const getShopInspectionTripNode = (data ?: any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/getShopInspectionTripNode',
		method: 'POST',
		data
	})
}
/**
 * @description: 行程进展
 */
export const getShopInspectionTripProgress = (data ?: any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/getShopInspectionTripProgress',
		method: 'POST',
		data
	})
}
/**
 * @description: 开始巡店打卡、离店打卡
 */
export const saveLocation = (data ?: any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/saveLocation',
		method: 'POST',
		data
	})
}
/**
 * @description: 查询稽核数据
 */
export const getRecords = (data ?: any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/getRecords',
		method: 'POST',
		data
	})
}
/**
 * @description: 提交欠费基本情况
 */
export const saveOweMessage = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveOweMessage',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存舞弊基本情况确定
 */
export const saveFraudDetermine = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveFraudDetermine',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存舞弊店取证节点
 */
export const saveForensics = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveForensics',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存舞弊店制定处理方案节点
 */
export const saveFraudMessage = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveFraudMessage',
		method: 'POST',
		data
	})
}

/**
 * @description: 保存解约情况记录
 */
export const saveRelieveCase = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveRelieveCase',
		method: 'POST',
		data
	})
}
/**
 * @description;保存解约取证资料
 */
export const saveRelieveForensics = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/plan/saveRelieveForensics',
		method: 'POST',
		data
	})
}

/**
 * @description;巡店计划已读
 */
export const orderRead = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/orderRead',
		method: 'POST',
		data
	})
}