<template>
	<view>
		<up-popup :show="show" mode="bottom" round="20" closeOnClickOverlay @close="$emit('close')"
			@touchmove.stop.prevent="false">
			<view class="popup-container">
				<view class="title">操作</view>
				<view class="fl option">
					<view class="option-item" v-if="data?.status === 3" @click="goTrip">开始巡店</view>
					<view class="option-item" v-if="data?.status === 4 || data?.status === 5 || data?.status === 6"
						@click="goTrip">
						{{ data?.status === 4 ? '行程跟进' : '查看行程'}}
					</view>
					<view class="option-item" v-if="data?.status === 3" @click="back">更改计划</view>
					<view class="option-item" v-if="data?.status === 3" @click="changeStatus(7)">不执行</view>
					<view class="option-item" v-if="data?.status === 4 || data?.status === 5" @click="changeStatus(6)">行程作废</view>
					<view class="option-item" @click="$emit('close')">取消</view>
				</view>
			</view>
		</up-popup>
	</view>
	<template v-if="operationModalShow">
		<operationModalVue :show="operationModalShow" @confirm="handleOperationConfirm" @cancel="operationModalShow = false"
			:placeholderText="operateCode === 6 ? '请填写作废原因' : '请填写不执行原因'"
			:confirmButtonText="operateCode === 6 ? '确认作废' : '确认不执行'" cancelButtonText="我再想想">
			<view class="tip">{{operateCode === 6 ? '是否作废该门店行程计划？' : '是否不执行该门店的巡店计划?'}}</view>
			<view class="tip" v-if="operateCode === 6">（门店行程计划作废后不可再次修改）</view>
		</operationModalVue>
	</template>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
	import operationModalVue from '../operation-modal/operation-modal.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { updateShopStatus } from '../../api';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		orderId: {
			type: [Number, String],
		},
		data: {
			type: Object,
			default: () => { }
		}
	})

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const emit = defineEmits(['refresh', 'close'])
	const operationModalShow = ref<boolean>(false),
		operateCode = ref<number>()

	const back = () => {
		uni.navigateBack()
	}

	const goTrip = () => {
		if (props.data?.status === 3) {
			operateCode.value = 4
			handleOperationConfirm()
		} else {
			emit('close')
			route_to_view(`/packageB/pages/shop-inspection/shop-inspection-trip?orderId=${props.orderId}&shopId=${props.data.shopId}`)
		}
	}

	const changeStatus = (type : number) => {
		operateCode.value = type
		operationModalShow.value = true
	}

	const handleOperationConfirm = async (text : string = '') => {
		try {
			const params = {
				orderId: props.orderId,
				shopId: props.data.shopId,
				status: operateCode.value,
				nonExecutionReason: text
			}
			const { code } = await updateShopStatus(params)
			if (code === 200) {
				operationModalShow.value == false
				emit('close')
				emit('refresh')
				if (operateCode.value === 4) {
					route_to_view(`/packageB/pages/shop-inspection/shop-inspection-trip?orderId=${props.orderId}&shopId=${props.data.shopId}`)
				}
			}
		} catch (e) {
			console.error('修改巡店门店状态 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.popup-container {}

	.title {
		color: #1f2428;
		font-size: 36rpx;
		text-align: center;
		line-height: 36rpx;
		margin: 40rpx 0;
		position: relative;

		&::before {
			content: '';
			display: block;
			height: 2px;
			background: #f2f2f2;
			position: absolute;
			bottom: -40rpx;
			left: 60rpx;
			right: 60rpx;
		}
	}

	.option {
		text-align: center;
		padding: 30rpx 0 0;
		align-items: center;
		justify-content: center;

		&-item {
			margin: 30rpx 0;
			color: #1F2428;
			font-size: 30rpx;
			line-height: 30rpx;

			&-active {
				font-size: 36rpx;
				line-height: 36rpx;
				background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
				text-fill-color: transparent;
			}
		}
	}


	.tip {
		color: #999999;
		font-size: 30rpx;
		text-align: center;
		line-height: 30rpx;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	:deep() {
		.textarea-section {
			margin-top: 60rpx;
		}
	}
</style>