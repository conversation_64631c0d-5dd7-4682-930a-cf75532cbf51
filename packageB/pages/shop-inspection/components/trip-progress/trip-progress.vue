<template>
	<view class="trip-progress">
		<view class="title">行程进度</view>
		<view class="end fr" v-if="tripInfo?.tripStatus == 1007">
			<up-icon name="checkmark" color="#FFFFFF" size="32rpx" />
			<text>行程已结束</text>
		</view>
		<!-- 位置信息 -->
    <!--<positionVue-->
    <!--  v-if="(tripInfo?.tripStatus == 1001 && tripInfo?.targetCode != 6) || (tripInfo?.tripStatus == 100401 && (tripInfo?.targetCode == 1 || tripInfo?.targetCode==2 || tripInfo?.targetCode == 3)) || (tripInfo?.tripStatus == 1005 && tripInfo?.targetCode == 4) || (tripInfo?.tripStatus == 1004 && tripInfo?.targetCode == 5) || (tripInfo?.tripStatus == 1006 && (tripInfo?.targetCode == 1||tripInfo?.targetCode == 2))"-->
    <!--  :data="tripInfo" />-->
		<!--<trip-form-->
		<!--	v-if="(tripInfo?.tripStatus==1002 && (tripInfo?.targetCode == 1 || tripInfo?.targetCode == 2 || tripInfo?.targetCode == 3 || tripInfo?.targetCode == 4 || tripInfo?.targetCode == 5 || tripInfo?.targetCode == 6)) || (tripInfo?.tripStatus==100301 && (tripInfo?.targetCode == 1 || tripInfo?.targetCode == 2)) || (tripInfo?.tripStatus == 1003 && (tripInfo?.targetCode == 1 || tripInfo?.targetCode == 2 || tripInfo?.targetCode == 3 || tripInfo?.targetCode == 4 || tripInfo?.targetCode == 5 || tripInfo?.targetCode == 6) || (tripInfo?.tripStatus==1004 && (tripInfo?.targetCode == 1 ||tripInfo?.targetCode == 4)) || ((tripInfo?.tripStatus == 1001 || tripInfo?.tripStatus == 1005 || tripInfo?.tripStatus == 100201 || tripInfo?.tripStatus == 100401) && tripInfo?.targetCode == 6))"-->
		<!--	:data="tripInfo" />-->
    <positionVue
      v-if="shouldShowComponent('positionVue', tripInfo)"
      :data="tripInfo"
    />
    <trip-form
      v-if="shouldShowComponent('tripForm', tripInfo)"
      :data="tripInfo"
    />
		<!-- 行程进度 -->
		<view v-if="tripPlanList && tripPlanList.length > 0">
			<!-- 进度信息 -->
			<progressItemVue :data="tripPlanList" />
		</view>
		<view v-else class="no-progress">
			无行程进度～
		</view>
	</view>
</template>

<script setup lang="ts">
import { Ref, provide, ref, watch, getCurrentInstance, ComponentInternalInstance } from 'vue';
	import positionVue from './position.vue';
	import progressItemVue from './progress-item.vue';
	import tripForm from './trip-form.vue';
	import { getShopInspectionTripDetail as getShopInspectionTripDetailApi, getShopInspectionTripNode as getShopInspectionTripNodeApi, getShopInspectionTripProgress as getShopInspectionTripProgressApi } from '../../api'
import { showLoading } from "@/utils/util";

  const instance = getCurrentInstance() as ComponentInternalInstance
	const props = defineProps({
		pageParams: {
			type: Object,
			default: () => { }
		}
	})
	const tripPlanList = ref([])

	const tripInfo : Ref = ref()
	const patrolPlan : Ref = ref()

	provide('tripInfo', tripInfo)
	provide('tripPlanList', tripPlanList)
	provide('patrolPlan', patrolPlan)

  const rules = {
    positionVue: [
      { tripStatus: "1001", targetCodes: (code) => code !== "6" },
      { tripStatus: "100401", targetCodes: ["1", "2", "3"] },
      { tripStatus: "1005", targetCodes: ["4"] },
      { tripStatus: "1004", targetCodes: ["5"] },
      { tripStatus: "1006", targetCodes: ["1", "2"] },
    ],
    tripForm: [
      { tripStatus: "1002", targetCodes: ["1", "2", "3", "4", "5", "6"] },
      { tripStatus: "100301", targetCodes: ["1", "2"] },
      { tripStatus: "1003", targetCodes: ["1", "2", "3", "4", "5", "6"] },
      { tripStatus: "1004", targetCodes: ["1", "2", "4"] },
      { tripStatus: ["1001", "1005", "100201", "100401"], targetCodes: ["6"] },
    ],
  };

  const shouldShowComponent = (component: string | number, tripInfo: any) => {
    const tripStatus = String(tripInfo?.tripStatus); // 统一转为字符串
    const targetCode = String(tripInfo?.targetCode); // 统一转为字符串

    return rules[component].some((rule) => {
      const statusMatch = Array.isArray(rule.tripStatus)
        ? rule.tripStatus.includes(tripStatus)
        : rule.tripStatus === tripStatus;

      const codeMatch =
        typeof rule.targetCodes === "function"
          ? rule.targetCodes(targetCode)
          : rule.targetCodes.includes(targetCode);

      return statusMatch && codeMatch;
    });
  };
	/**
		 * @description : 行程详情
		 */
	const getShopInspectionTripDetail = async () : Promise<void> => {
		try {
			const res = await getShopInspectionTripDetailApi(props.pageParams)
			res.data.shopType = res.data.shopStatus
			tripInfo.value = res.data
		} catch (e) {
			console.log("🚀 ~ getShopInspectionTripDetail ~ e:", e)
		}
	}
	/**
	 * @description: 获取行程节点
	 */
	const getShopInspectionTripNode = async () : Promise<void> => {
		try {
			const res = await getShopInspectionTripNodeApi({
				...props.pageParams,
				targetCode: tripInfo.value.targetCode,
				tripId: tripInfo.value.tripId || 581
			})
			tripPlanList.value = res.data
		} catch (e) {
			console.log("🚀 ~ getShopInspectionTripNode ~ e:", e)
		}
	}
	/**
	 * @description: 行程进展
	 */
	const getShopInspectionTripProgress = async () : Promise<void> => {
		try {
			const res = await getShopInspectionTripProgressApi({
				...props.pageParams,
				targetCode: tripInfo.value.targetCode,
				status: tripInfo.value.tripStatus
			})
			patrolPlan.value = res.data
		} catch (e) {
			console.log("🚀 ~ getShopInspectionTripProgress ~ e:", e)
		}
	}
	/**
	 * @description: 初始化请求
	 */
	const initial = async () => {
		await getShopInspectionTripDetail()
		await getShopInspectionTripNode()
		await getShopInspectionTripProgress()
	}
	provide('getTripDetail', initial)
	watch(() => props.pageParams, async (v : { orderId : string, shopId : string }) => {
		if (v) {
      showLoading('加载中')
      instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
        if (res) {
          initial()
        }
      })
		}
	}, { deep: true, immediate: true })
	defineExpose({
		tripInfo,
		initial
	})
</script>

<style scoped lang="scss">
	.trip-progress {
		padding: 40rpx;
		background: #FFFFFF;
		border-radius: 20rpx;

		.title {
			color: #1f2428;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 40rpx;
			margin-bottom: 40rpx;
		}
	}

	.end {
		padding: 20rpx 0;
		width: 100%;
		text-align: center;
		line-height: 40rpx;
		border-radius: 20rpx;
		background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
		justify-content: center;

		text {
			margin-left: 8rpx;
			color: #ffffff;
			font-size: 28rpx;
			font-weight: bold;
			margin-left: 8rpx;
		}
	}

	.no-progress {
		color: #999999;
		font-size: 28rpx;
		text-align: center;
		margin-top: 40rpx;
	}
</style>