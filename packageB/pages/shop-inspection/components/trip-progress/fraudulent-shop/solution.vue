<template>
	<!-- <view class="content">
		<view class="content-title">舞弊问题店制定处理方案节点</view>
		<view class="section tip">
			<view class="text">舞弊问题店制定处理方案节点</view>
		</view> -->
	<view class="section textarea">
		<view class="label">情况说明</view>
		<cTextareaVue v-model="form.message" count maxlength="300" placeholder="请填写处理方案"/>
	</view>
	<view class="section input fr space-between">
		<view class="label">罚款金额：</view>
		<view style="width: 180rpx;">
			<up-input placeholder="请填写舞弊罚款金额" border="none" v-model="form.payment" placeholderClass="placeholderClass"
				type="number" shape="circle" :customStyle="{height:'40rpx'}" fontSize="28rpx" inputAlign="right" />
		</view>
	</view>
	<view class="section fr space-between">
		<view class="label">预计闭环时间：</view>
		<view class="fr" @click="dateShow = true">
			<view class="placeholderClass" style="margin-right: 8rpx;">{{dateFormater || '请选择预计闭环时间'}}</view>
			<up-icon name="arrow-right" color="#999999" size="24rpx" />
		</view>
	</view>
	<view class="btn fr">
		<view class="custom-btn" @tap="onSave(1)">确认提交</view>
		<view class="custom-btn custom-btn-primary" @tap="onSave(0)">保存</view>
	</view>
	<!-- </view> -->
	<up-datetime-picker v-model="date" :show="dateShow" mode="date" :itemHeight="50" confirmColor="#C35943"
		@confirm.stop="handleDateConfirm" @cancel.stop="dateShow = false" :formatter="formatter"/>
</template>

<script setup lang="ts">
	import { ref, computed, getCurrentInstance, inject, watch } from 'vue';
	import cTextareaVue from '../../../../../components/c-textarea/c-textarea.vue';
	import dayjs from 'dayjs';
	import { useCommon } from '@/hooks/useGlobalData'
	import { showToast, formatter } from '@/utils/util';
	import { saveFraudMessage } from '../../../api';
import { format } from 'echarts';
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
	type formType = {
		message : string;
		payment : string;
		shutTime : string;
	}
	const tripInfo : any = inject('tripInfo')
	const getTripDetail: Function = inject('getTripDetail')
	const patrolPlan : any = inject('patrolPlan')
	const { globalData } = useCommon(getCurrentInstance())
	const dateShow = ref<Boolean>(false)
	const form = ref<Partial<formType>>({})
	const date = ref<string>(dayjs().format('YYYY-MM-DD'))
	
	watch(() => patrolPlan.value, (v: any) => {
		if(v?.fraudMessage){
			form.value = {
				...v.fraudMessage
			}
		}
	}, { immediate: true, deep: true})
	/**
		 * @description : 页面展示的日期
		 */
	const dateFormater = computed(() => {
		return form.value.shutTime ? dayjs(form.value.shutTime).format('YYYY年MM月DD日') : ''
	})
	/**
	 * @description :选择时间
	 */
	const handleDateConfirm = (item : any) => {
		dateShow.value = false
		date.value = item.value
		form.value.shutTime = dayjs(item.value).format('YYYY-MM-DD')
	}
	/**
	 * @description : 校验
	 */
	const validate = (isSubmit : number) => {
		const userId = globalData.value.user.userId
		if (userId != tripInfo.value.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (tripInfo.value.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		if (isSubmit) {
			if (!form.value.message) {
				showToast('请填入情况说明')
				return
			}
			if (!form.value.payment) {
				showToast('请填写罚款金额')
				return
			}
		}
		return true
	}
	/**
	 * @description: 保存
	 */
	const onSave = async (isSubmit : number) => {
		try {
			const { orderId, shopId, tripId } = tripInfo.value
			const params = {
				...form.value,
				isSubmit,
				orderId,
				shopId,
				tripId
			}
			if (!validate(isSubmit)) return
			await saveFraudMessage(params)
			showToast({
				title: '保存成功',
				success: () => {
					let timer = setTimeout(()=> {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000);
				}
			})
		} catch (e) {
			console.log("🚀 ~ onSave舞弊店处理方案节点 ~ e:", e)
		}
	}
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";

	.section {
		padding: 20rpx 24rpx !important;

		.label {
			line-height: 48rpx !important;
		}
	}

	.textarea {
		.label {
			margin-bottom: 20rpx;
		}

		:deep() {
			.custom-textarea {
				.textarea {
					height: 186rpx;
				}
			}
		}
	}

	:deep() {
		.placeholderClass {
			color: #999999 !important;
			font-size: 20rpx !important;
			text-align: right;
		}
	}


	.btn {
		justify-content: space-between;
		padding-top: 20rpx;

		.custom-btn {
			width: 270rpx;
			margin: 0;
			background: #fff;
		}
	}
</style>