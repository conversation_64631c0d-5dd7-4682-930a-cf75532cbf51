<template>
	<!-- 舞弊问题店情况判定 -->
	<template v-if="data.tripStatus == 1002">
		<situationDeterminationVue />
	</template>
	<!-- 舞弊问题店取证节点 -->
	<template v-if="data.tripStatus == 1003">
		<evidenceCollectionNodeVue />
	</template>
	<!-- 舞弊问题店制定处理方案节点 -->
	<template v-if="data.tripStatus == 1004">
		<solutionVue />
	</template>
</template>

<script setup lang="ts">
	import { inject } from 'vue';
	import evidenceCollectionNodeVue from './evidence-collection-node.vue';
	import situationDeterminationVue from './situation-determination.vue';
	import solutionVue from './solution.vue';
	const patrolPlan : any = inject('patrolPlan')

	defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";
</style>