<template>
	<view class="section fr space-between isFraudulent">
		<view class="label">是否存在舞弊现象：</view>
		<up-radio-group v-model="form.isFraud" placement="column" activeColor="#C35943" inactiveColor="#C35943">
			<up-radio v-for="(item, index) in radiolist1" :key="index" :label="item.name" :name="item.value"
				labelColor="#1F2428" size="16" labelSize="14">
			</up-radio>
		</up-radio-group>
	</view>
	<view class="section isFraudulent-type">
		<view class="label">舞弊店类型（请勾选舞弊店类型单选/多选）</view>
		<view class="fr" style="margin-bottom: 20rpx;">
			<up-checkbox label="房量舞弊" name="1" shape="circle" usedAlone v-model:checked="form.fraudType1" size="16"
				labelSize="14" activeColor="#C35943" inactiveColor="#C35943" labelColor="#1F2428"
				@change="e => onChange(e, 'fraudType1')" />
			<view class="input">
				<up-input placeholder="请填写实际房量数" border="none" v-model="form.actualNum" placeholderClass="placeholderClass"
					type="number" shape="circle" :customStyle="{background:'#F2F2F2',padding:'0 20rpx',height:'40rpx'}"
					fontSize="24rpx" />
			</view>
		</view>
		<up-checkbox v-model:checked="form.fraudType2" label="账房舞弊" name="2" shape="circle" usedAlone size="16"
			labelSize="14" activeColor="#C35943" inactiveColor="#C35943" labelColor="#1F2428"
			@change="e => onChange(e, 'fraudType2')" />
	</view>
	<view class="section explain">
		<view class="label">情况说明</view>
		<cTextareaVue v-model="form.showHow" count maxlength="300" />
	</view>
	<view class="btn fr">
		<view class="custom-btn" @tap="onSave(1)">确认提交</view>
		<view class="custom-btn custom-btn-primary" @tap="onSave(0)">保存</view>
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance, inject, ref, watch } from 'vue';
	import { radiolist1 } from '../../../enums';
	import cTextareaVue from '../../../../../components/c-textarea/c-textarea.vue';
	import { showToast } from '@/utils/util'
	import { useCommon } from '@/hooks/useGlobalData'
	import { saveFraudDetermine } from '../../../api'
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
	type formType = {
		isFraud : string | number;
		fraudType1 : boolean;
		fraudType2 : boolean;
		actualNum : string;
		showHow : string;
	}
	const tripInfo : any = inject('tripInfo')
	const getTripDetail : Function = inject('getTripDetail')
	const patrolPlan : any = inject('patrolPlan')
	const form = ref<Partial<formType>>({
		fraudType1: false,
		fraudType2: false
	})
	const { globalData } = useCommon(getCurrentInstance())
	watch(() => patrolPlan.value, (v : any) => {
		if (v?.fraudDetermine) {
			console.log(v?.fraudDetermine, 'v?.fraudDetermine');
			let fraudType1 = false
			let fraudType2 = false
			let { isFraud, fraudType, showHow, actualNum } = v.fraudDetermine
			if (isFraud == 0 || isFraud == 1) {
				isFraud = typeof isFraud == 'string' ? isFraud : isFraud.toString()
			} else {
				isFraud = '0'
			}
			if (fraudType) {
				fraudType = fraudType.toString()
				const fraudTypeArr = fraudType.split(',')
				if (fraudTypeArr.length == 1) {
					if (fraudTypeArr[0] == 2) {
						fraudType2 = true
					} else if (fraudTypeArr[0] == 1) {
						fraudType1 = true
					}
				} else {
					fraudType2 = true
					fraudType1 = true
				}
			} else {
				fraudType1 = true
			}
			form.value = {
				isFraud,
				fraudType1,
				fraudType2,
				showHow,
				actualNum
			}
		}
	}, { immediate: true, deep: true })
	/**
	 * @description: TODO checkbox双向绑定失败，通过事件赋值
	 */
	const onChange = (e : boolean, field : string) => {
		form.value[field] = e
	}
	/**
	 * @description: 校验
	 */
	const validate = (isSubmit : number) => {
		const userId = globalData.value.user?.userId
		if (userId != tripInfo.value.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (tripInfo.value.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		if (isSubmit) {
			if (form.value.isFraud != 0 && form.value.isFraud != 1) {
				showToast('是选择否存在舞弊现象')
				return
			}
			if (form.value.isFraud == 1 && !form.value.fraudType1 && !form.value.fraudType2) {
				showToast('请选择舞弊店类型')
				return
			}
			if (!form.value.actualNum && form.value.fraudType1) {
				showToast('请填写房量')
				return
			}
			if (!form.value.showHow) {
				showToast('请填写情况描述')
				return
			}
		}
		return true
	}
	/**
	 * @description: 提交
	 */
	const onSave = async (isSubmit : number) => {
		try {
			const { orderId, shopId, tripId } = tripInfo.value
			const params : any = {
				...form.value,
				fraudType: [],
				orderId,
				shopId,
				tripId,
				isSubmit
			}
			if (params.fraudType1) {
				params.fraudType.push(1)
			}
			if (params.fraudType2) {
				params.fraudType.push(2)
			}
			params.fraudType = params.fraudType.join(',')
			delete params.fraduType1
			delete params.fraudType2
			if (!validate(isSubmit)) return
			await saveFraudDetermine(params)
			showToast({
				title: '保存成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		} catch (e) {
			console.log("🚀 ~ onSave ~ e:", e)
		}
	}
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";

	.isFraudulent {
		:deep() {
			.u-radio-group {
				flex-direction: row;
				justify-content: flex-end;
			}

			.u-radio:first-child {
				margin-right: 40rpx;
			}
		}
	}

	.isFraudulent-type {
		.label {
			margin-bottom: 32rpx;
		}

		.input {
			width: 278rpx;
			margin-left: 20rpx;
		}
	}

	.explain {
		.label {
			margin-bottom: 20rpx;
		}

		:deep() {
			.custom-textarea {
				.textarea {
					height: 186rpx;
				}
			}
		}
	}

	:deep() {
		.u-radio {
			cursor: none;
		}

		.u-icon__icon {
			flex-shrink: 0;
		}

		.placeholderClass {
			color: #999999;
			font-size: 20rpx;
			line-height: 28rpx;
		}
	}

	.btn {
		justify-content: space-between;
		padding-top: 20rpx;

		.custom-btn {
			width: 270rpx;
			margin: 0;
			background: #fff;
		}
	}
</style>