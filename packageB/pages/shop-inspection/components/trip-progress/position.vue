<template>
	<view class="position">
		<view class="position-title">{{patrolPlan?.header}}</view>
		<template v-if="data.tripStatus !== 1008">
			<view class="tip">{{patrolPlan?.content}}
			</view>
			<view class="map-block">
				<view class="title">当前位置：</view>
				<view class="address">{{ location.address }}</view>
				<view class="map-comera fr">
					<view class="map-container">
						<map class="map" :latitude="location.lat" :longitude="location.lng" :markers="markers" :scale="18"></map>
					</view>
					<view class="camera fl" @tap="photo" v-if="!follow">
						<up-icon name="camera-fill" color="#F75E3B" size="32rpx" />
						<view class="text">拍照</view>
					</view>
					<view class="camera fl" v-else>
						<view class="icon-del" @tap="removeFollow">
							<up-icon name="close-circle-fill" size="12"></up-icon>
						</view>
						<image :src="follow" mode="aspectFill" lazy-load class="follow-img" @tap="previewImage([follow])"></image>
					</view>
				</view>
			</view>
			<view class="fr camera-tips">
				<view class="camera-tips__icon">
					<up-icon name="error-circle-fill" size="12" color="#C35943"></up-icon>
				</view>
				<view class="fl">
					<text class="camera-tips__primary">请保证照片中涵盖巡店人人像和门店前台/门头信息。</text>
					<text class="camera-tips__info">（照片信息仅供集团内部审计使用）</text>
				</view>
			</view>
			<view class="custom-btn custom-btn-primary" @tap="onConfirm">{{data.tripStatus == 1001 ? '确定' : '确认结束巡店'}}</view>
		</template>
		<template v-else>
			<view class="trip-cancel fl">
				<view class="fr">
					<up-icon name="close-circle-fill" color="#C35943" size="28rpx" />
					<view class="title">行程已作废</view>
				</view>
				<view class="text">
					{{ patrolPlan?.nonExecutionReason.canceledReason }}
				</view>
			</view>
		</template>
	</view>
</template>

<script setup lang="ts">
	import { inject, onMounted, ref, unref, getCurrentInstance } from 'vue';
	import { useUpload } from '../../../../hooks/useUpload'
	import BMapWX from '../../../../lib/bmap-wx.js'
	import { showToast } from '@/utils/util'
	import { saveLocation } from '../../api'
	import { useCommon } from '@/hooks/useGlobalData';
	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
	const BMap = new BMapWX({
		ak: 'rfIhkgeIsQh4JBHiZPMI1oYMPR3PPMGH'
	});

	const { globalData } = useCommon(getCurrentInstance())
	const { batchUpload, previewImage } = useUpload()
	const patrolPlan : any = inject('patrolPlan')
	const getTripDetail : Function = inject('getTripDetail')
	const follow = ref('')
	const location = ref({
		address: '',
		lat: '31.22024',
		lng: '121.42394'
	})
	const markers = ref(
		[
			{
				latitude: '31.22024',
				longitude: '121.42394',
				iconPath: "https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-18/1734491262759.png",
				width: '48rpx',
				height: '64rpx'
			}
		]
	)
	/**
	 * @description: 校验是否能操作
	 */
	const validate = () => {
		const userId = globalData.value.user.userId
		if (userId != props.data.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (props.data.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		return true
	}
	/**
	 * @description :开始巡店打卡、离店打卡
	 */
	const onConfirm = async () => {
		if (!validate()) return
		if (!follow.value) {
			showToast('请上传涵盖巡店人人像和门店前台/门头信息的图片')
			return
		}
		const { tripStatus, shopId, orderId, tripId, tripCode } = props.data
		const { lat, lng, address } = unref(location)
		const params = {
			longitude: lng,
			latitude: lat,
			location: address,
			status: tripStatus,
			shopId,
			orderId,
			tripId,
			follow: unref(follow)
		}
		if (tripCode == 6 && tripStatus == 1004) {
			params.location = null
		}
		try {
			await saveLocation(params)
			showToast({
				title: '操作成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		} catch (e) {
			console.log("?? ~ onConfirm ~ e:", e)
		}
	}
	/**
	 * @description: 上传图片
	 */
	const photo = async () => {
		const filePaths = await batchUpload(1, 0)
		follow.value = filePaths?.[0]
	}
	const getLocation = () => {
		BMap.regeocoding({
			success: (res : any) => {
				console.log("?? ~ onMounted ~ res:", res)
				if (res.originalData.status == 0) {
					const { formatted_address_poi, location: poi } = res.originalData.result
					location.value = {
						address: formatted_address_poi,
						lat: poi.lat,
						lng: poi.lng
					}
					markers.value[0].latitude = poi.lat
					markers.value[0].longitude = poi.lng
				}
			},
			fail: (e : any) => {
				console.log("?? ~ onMounted ~ e:", e)
				showToast('获取位置失败')
			}
		})
	}

	const removeFollow = () => {
		uni.showModal({
			cancelText: '否',
			confirmText: '是',
			content: '是否重拍照片',
			success: (res) => {
				if (res.confirm) {
					follow.value = ''
				}
			}
		})
	}
	onMounted(() => {
		getLocation()
	})
</script>

<style scoped lang="scss">
	.position {
		background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
		padding: 20rpx 24rpx 40rpx;
		border-radius: 20rpx;

		&-title {
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}

		.tip {
			padding: 12rpx 24rpx;
			border-radius: 20rpx;
			background: #ffffffe6;
			color: #1f2428;
			font-size: 20rpx;
			font-weight: 400;
			line-height: 40rpx;
		}
	}

	.map-block {
		margin-top: 20rpx;
		padding: 20rpx 24rpx;
		background: #ffffffe6;
		border-radius: 16.08rpx;

		.title {
			margin-bottom: 20rpx;
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
		}

		.address {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: 400;
		}

		.map-comera {
			justify-content: space-between;
			margin-top: 20rpx;

			.map-container {
				width: 258rpx;
				height: 354rpx;
				border-radius: 16rpx;
				overflow: hidden;

				.map {
					width: 100%;
					height: 100%;
				}
			}

			.camera {
				position: relative;
				width: 258rpx;
				height: 354rpx;
				border-radius: 20rpx;
				border: 2rpx dashed $uni-text-color;
				justify-content: center;
				align-items: center;

				.text {
					color: $uni-text-color;
					margin-top: 10rpx;
					font-size: 24rpx;
					font-weight: bold;
					line-height: 28rpx;
				}
			}
		}
	}

	.custom-btn {
		margin: 40rpx 0 0;
	}

	.trip-cancel {
		border-radius: 20rpx;
		background: #ffffffe6;
		padding: 40rpx 24rpx;
		align-items: center;

		.title {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 40rpx;
			margin-left: 10rpx;
		}

		.text {
			color: #1f2428;
			font-size: 22rpx;
			line-height: 32rpx;
			margin-top: 40rpx;
		}
	}

	.follow-img {
		width: 258rpx;
		height: 354rpx;
		border-radius: 20rpx;
	}

	.icon-del {
		position: absolute;
		top: -6px;
		left: -6px;
	}

	.camera-tips {
		align-items: flex-start;
		margin-top: 20rpx;

		&__icon {
			flex-shrink: 0;
			margin-right: 8rpx;
		}

		&__primary {
			margin-bottom: 4rpx;
			line-height: 24rpx;
			font-size: 24rpx;
			color: #1f2428;
		}

		&__info {
			line-height: 22rpx;
			font-size: 22rpx;
			color: #999;
		}
	}
</style>