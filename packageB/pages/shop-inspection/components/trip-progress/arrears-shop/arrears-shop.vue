<template>
	<!-- [已完成]欠费问题店基本情况填写 -->
	<template>
		<view class="section fr space-between">
			<view class="label">成因类型：</view>
			<view class="fr" @click="patrolDictionaryShow = true">
				<view class="placeholderClass" style="margin-right: 8rpx;">{{patrolDictionaryText || '请选择成因类型'}}</view>
				<up-icon name="arrow-right" color="#999999" size="24rpx" />
			</view>
		</view>
		<view class="section fr space-between">
			<view class="label">预计闭环时间：</view>
			<view class="fr" @click="dateShow = true">
				<view class="placeholderClass" style="margin-right: 8rpx;">{{ dateFormater || '请选择预计闭环时间'}}</view>
				<up-icon name="arrow-right" color="#999999" size="24rpx" />
			</view>
		</view>
		<view class="section textarea">
			<view class="label">成因描述：</view>
			<cTextareaVue v-model="oweMessage" count maxlength="100" placeholder="请描述具体原因"/>
		</view>
		<view class="section textarea">
			<view class="label">处理政策：</view>
			<cTextareaVue v-model="solution" count maxlength="100" placeholder="请填写处理政策"/>
		</view>
		<view class="btn fr">
			<view class="custom-btn" @tap="onSave(1)">确认提交</view>
			<view class="custom-btn custom-btn-primary" @tap="onSave(0)">保存</view>
		</view>
	</template>
	<up-datetime-picker v-model="date" :show="dateShow" mode="date" :itemHeight="50" confirmColor="#C35943"
		@confirm.stop="handleDateConfirm" @cancel.stop="dateShow = false" :formatter="formatter" />
	<up-picker ref="uPickerRef" :show="patrolDictionaryShow" :columns="columns" keyName="value" :loading="loading" confirmColor="#C35943"
		@change="ohangeHandler" @confirm="confirm" @cancel="patrolDictionaryShow = false" @close="patrolDictionaryShow = false"></up-picker>
</template>

<script setup lang="ts">
	import dayjs from 'dayjs';
	import { getCurrentInstance, inject, ref, watch, computed, unref, nextTick } from 'vue';
	import cTextareaVue from '../../../../../components/c-textarea/c-textarea.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { showToast, formatter } from '@/utils/util'
	import { saveOweMessage } from '../../../api'
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})

	const patrolPlan : any = inject('patrolPlan')
	const getTripDetail: Function = inject('getTripDetail')
	const loading = ref(false)
	const patrolDictionaryShow = ref(false)
	const columns = ref([])
	const columnIndexs = ref([])
	const uPickerRef = ref(null)
	const { globalData } = useCommon(getCurrentInstance())
	const dateShow = ref<Boolean>(false)
	const oweMessage = ref<string>(), dateValue = ref(''), date = ref<string>(dayjs().format('YYYY-MM-DD')), solution = ref('')
	/**
	 * @description ： 成因中文
	 */
	const patrolDictionaryText = computed(() => {
		console.log("🚀 ~ file: arrears-shop.vue:74 ~ patrolDictionaryText ~ columns.value?.[1]:", columns.value?.[1])
		return columns.value?.[1]?.[columnIndexs.value?.[1]]?.value
	})
	/**
	 * @description : 页面展示的日期
	 */
	const dateFormater = computed(() => {
		return dateValue.value? dayjs(dateValue.value).format('YYYY年MM月DD日') : ''
	})
	/**
	 * @description: 处理默认信息和成因类型
	 */
	watch(() => patrolPlan.value, (v) => {
		if (v?.patrolDictionaryList) {
			columns.value = [v.patrolDictionaryList, v.patrolDictionaryList[0].dictList]
		}
		if (v?.oweMessage && v?.patrolDictionaryList) {
			const causesOfCodeArr = v.oweMessage.causesOfCode?.split(',')
			if (causesOfCodeArr?.length) {
				columnIndexs.value = [causesOfCodeArr[2], causesOfCodeArr[3]]
			}
			if(v.patrolDictionaryList[columnIndexs.value[0]]){
				columns.value[1] = v.patrolDictionaryList[columnIndexs.value[0]].dictList
			}
			oweMessage.value = v.oweMessage.oweMessage
			solution.value = v.oweMessage.solution
			if (v.oweMessage.shutTime) {
				dateValue.value = date.value = dayjs(v.oweMessage.shutTime).format('YYYY-MM-DD')
			}
		}
	}, { deep: true, immediate: true })
	/**
	 * @description: 成因类型变化
	 */
	const ohangeHandler = async (e : { columnIndex : number, index : number, value : any }) => {
		const { columnIndex, value } = e
		if (columnIndex == 0) {
			loading.value = true
			let timer = setTimeout(() => {
				clearTimeout(timer)
				timer = null
				uPickerRef.value.setColumnValues(1, value[0].dictList)
				columns.value[1] = value[0].dictList
				loading.value = false
			}, 1000 / 60)
		}
	}
	/**
	 * @description: 选中成因类型
	 */
	const confirm = (e : any) => {
		columnIndexs.value = e.indexs
		patrolDictionaryShow.value = false
	}
	/**
	 * @description: 选择日期
	 */
	const handleDateConfirm = (item : any) => {
		dateValue.value = dayjs(item.value).format('YYYY-MM-DD')
		date.value = dateValue.value
		dateShow.value = false
	}
	/**
	 * @description: 校验是否能操作
	 */
	const validate = (isSubmit : number) => {
		const userId = globalData.value.user?.userId
		if (userId != props.data.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (props.data.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		if (isSubmit) {
			if (!columnIndexs.value.length) {
				showToast('请选择成因类型')
				return
			}
			if (!dateValue.value) {
				showToast('请选择预计闭环时间')
				return
			}
			if (!oweMessage.value) {
				showToast('请填写成因描述')
				return
			}
			if (!solution.value) {
				showToast('请填写处理政策')
				return
			}
		}
		return true
	}
	/**
	 * @description : 保存和提交  1:确认提交 0保存
	 */
	const onSave = async (isSubmit : number) => {
		if (!validate(isSubmit)) return
		try{
			const { orderId, shopId, tripId } = props.data
			const params : any = {
				orderId,
				shopId,
				tripId,
				isSubmit,
			}
			params.solution = unref(solution)
			params.oweMessage = unref(oweMessage)
			params.shutTime = unref(dateValue)
			if (columnIndexs.value.length) {
				params.causesOf = unref(patrolDictionaryText)
				params.causesOfCode = `${unref(columns)[0][unref(columnIndexs)[0]].code},${unref(columns)[1][unref(columnIndexs)[1]].code},${unref(columnIndexs).join(',')}`
			}
			await	saveOweMessage(params)
			showToast({
				title: '保存成功',
				success() {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		}catch(e){
			console.log("🚀 ~ onSave ~ e:", e)
		}
	}
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";

	.section {
		padding: 20rpx 24rpx !important;

		.label {
			line-height: 48rpx !important;
		}
	}


	.textarea {
		.label {
			margin-bottom: 20rpx;
		}

		:deep() {
			.custom-textarea {
				.textarea {
					height: 186rpx;
				}
			}
		}
	}

	:deep() {
		.placeholderClass {
			color: #999999 !important;
			font-size: 20rpx !important;
			text-align: right;
		}
	}

	.btn {
		justify-content: space-between;
		padding-top: 20rpx;

		.custom-btn {
			width: 270rpx;
			margin: 0;
			background: #fff;
		}
	}
</style>