<template>
	<view style="margin-top: 20rpx;">
		<view class="progress-item fr" v-for="(item,index) in data" :key="index">
			<template v-if="getInfo(item)?.title || (item.followTitle || item.follow)">
				<view class="spot-block fl">
					<view class="spot" :class="[{'spot-active': index < 2}]"></view>
					<view class="line" v-if="index !== data?.length - 1" :class="[{'line-active': index < 1}]"></view>
				</view>
				<view style="padding-bottom:  60rpx;">
					<template class="position-info" v-if="item?.tripStatus">
						<view class="title">{{replaceChar(getInfo(item)?.title)}}</view>
						<view class="tip content" v-if="getInfo(item)?.tip">{{getInfo(item)?.tip}}</view>
						<view class="href" v-if="getInfo(item)?.href" @tap="navigateTo(getInfo(item)?.href?.route)">
							{{replaceChar(getInfo(item)?.href?.title)}}
						</view>
						<view class="content">
							<!-- 舞弊问题店取证节点/解约取证资料节点图片列表 -->
							<template
								v-if="(tripInfo?.targetCode == 4 && item?.tripStatus == 1004) || (tripInfo?.targetCode == 5 && item?.tripStatus == 1004)">
								<view v-for="(v,idx) in tripInfo?.targetCode == 4 ? list : list1" :key="idx">
									<view>{{v.label}}：</view>
									<view class="img-list fr">
										<image v-if="tripInfo?.targetCode == 4" v-for="(url,i) in item.forensics?.[v.prop]" :key="i"
											class="img" :src="url" mode=""></image>
										<image v-else v-for="(url,i) in item.relieveForensics?.[v.prop]" :key="i" class="img" :src="url"
											mode="">
										</image>
									</view>
								</view>
							</template>
							<!-- 普通文字 -->
							<template v-else>
								<template v-if="isObject(getInfo(item)?.content)">
									<view v-for="(value,key) in getInfo(item)?.content" :key="key">
										{{ key }}： {{ getValueByPath(item,value) }}
									</view>
								</template>
								<template v-else>
									{{getInfo(item)?.content}}
								</template>
							</template>
							<!-- 解约情况记录图片 -->
							<template v-if="tripInfo?.targetCode == 5 && item?.tripStatus == 1003">
								<view>图片：</view>
								<view class="img-list fr">
									<image v-for="(url,i) in item.unwindingCase?.relieveImage" :key="i" class="img" :src="url" mode="">
									</image>
								</view>
							</template>
							<!-- 位置打卡图片 -->
							<template v-if="(item?.tripStatus == 1002 || item?.tripStatus == 1007) && tripInfo?.targetCode !=6">
								<view class="img-list">
									<image :src="item.follow" class="img" mode=""></image>
								</view>
							</template>
						</view>
						<view class="time">行程时间：{{item?.tripTime}}</view>
					</template>
					<template class="position-info" v-else>
						<view class="title">{{item.followTitle}} </view>
						<view class="content">{{item.follow}}</view>
						<view class="time">行程时间：{{item?.tripTime}}</view>
					</template>
				</view>
			</template>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { Ref, computed, inject } from 'vue';
	import { textEnums } from '../../enums';
	import { isObject } from '@/utils/util';

	defineProps({
		data: {
			type: Array as any,
			default: () => []
		}
	})
	const tripInfo : Ref = inject('tripInfo')

	const getInfo = computed(() => (item) => {
		const tripStatus = item?.tripStatus;
		const targetCode = tripInfo.value?.targetCode;
		return textEnums[tripStatus]?.[targetCode];
	})

	function getValueByPath(obj, path) {
		if (!path.includes('.')) {
			return obj[path]
		} else {
			const arr = path.split('.')
			let lastItem : any = null
			if (arr.length == 3) {
				lastItem = JSON.parse(arr.pop())
			}
			if (lastItem) {
				const res = arr.reduce((o : any, k : string) => o && o[k], obj)
				if (res && res.toString().indexOf(',') != -1) {
					return res.split(',').map((item : string) => lastItem[item])?.join(',')
				}
				return lastItem[res]
			}
			return arr.reduce((o : any, k : string) => o && o[k], obj);
		}
	}

	const list = [
		{ label: '门招', prop: 'doormanImage' },
		{ label: '房卡及门牌号', prop: 'roomCardImage' },
		{ label: 'PMS系统当天入住状态', prop: 'pmsImage' },
		{ label: '客房vi标识', prop: 'markingImage' },
		{ label: '住宿发票', prop: 'invoiceImage' },
	]

	const list1 = [
		{ label: '门头三招', doorFirstImage: [], prop: 'doorFirstImage' },
		{ label: '大堂', lobbyImage: [], prop: 'lobbyImage' },
		{ label: '吧台', barImage: [], prop: 'barImage' },
		{ label: '吧台背景墙', settingImage: [], prop: 'settingImage' },
		{ label: '走廊', corridorImage: [], prop: 'corridorImage' },
		{ label: '房间内部 ', roomImage: [], prop: 'roomImage' },
		{ label: '卫生间', toiletImage: [], prop: 'toiletImage' },
	]

	const navigateTo = (path : string) => {
		uni.navigateTo({
			url: path + '?targetCode=' + tripInfo.value.targetCode + '&shopId=' + tripInfo.value.shopId + '&tripCode=' + tripInfo.value.tripCode
		})
	}

	const replaceChar = computed(() => (text : string) => {
		if (!text) return
		if (text.includes('shopId')) {
			return text.replace('shopId', tripInfo.value.shopId ?? '')
		}
		return text
	})
</script>

<style scoped lang="scss">
	.progress-item {
		padding: 0 20rpx;
		align-items: stretch;

		.spot-block {
			position: relative;
			align-items: center;
			margin-right: 30rpx;
			padding-top: 12rpx;

			.spot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: #1f242826;
				flex-shrink: 0;
				z-index: 2;
				margin-bottom: 12rpx;

				&-active {
					background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
				}
			}

			.line {
				flex: 1;
				background: #1f242826;
				width: 2rpx;

				&-active {
					background: #C35943;
				}
			}
		}


		.title {
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			text-align: left;
			line-height: 40rpx;
			margin-bottom: 20rpx;
		}

		.href {
			color: #1e61fc;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 24rpx;
			text-decoration: underline;
			margin: 20rpx 0;
		}

		.content {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: 400;
			line-height: 36rpx;
			margin-bottom: 20rpx;
			word-break: break-all;

			view {
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}
			}
		}

		.time {
			color: #999999;
			font-size: 22rpx;
			font-weight: 400;
			line-height: 22rpx;
		}
	}

	.img-list {
		flex-wrap: wrap;
		gap: 14rpx;

		.img {
			width: 94rpx;
			height: 94rpx;
			border-radius: 12rpx;
			flex-shrink: 0;
		}
	}
</style>