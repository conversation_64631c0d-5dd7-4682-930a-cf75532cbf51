<template>
	<view class="section" v-for="(item,index) in list" :key="index">
		<view class="fr space-between">
			<view class="label">{{item.label}}：</view>
			<view class="camera fr" v-if="item[item.prop]?.length < 10" @tap="chooseImage(index,item.prop)">
				<image class="img"
					src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-12/1733971763935.png"
					mode=""></image>
				<view class="text">图片</view>
			</view>
		</view>
		<view class="tip" v-if="index ==0 || index == 2">
			<text class="text">（附带全国或当地主流媒体报纸、期刊，必要时可选用车票、发票等票据（彩票、购物小票除外）</text>
		</view>
		<view class="img-list fr" v-if="item[item.prop]?.length > 0">
			<view class="img-item" v-for="(url,idx) in item[item.prop]" :key="idx">
				<image class="img" :src="url" mode=""></image>
				<view @tap="removeImage(index,item.prop,idx)" class="remove">
					<up-icon name="close-circle-fill" size="14" color="rgba(31, 36, 40, 0.7)"></up-icon>
				</view>
			</view>
		</view>
	</view>
	<view class="btn fr">
		<view class="custom-btn" @tap="onSave(1)">确认提交</view>
		<view class="custom-btn custom-btn-primary" @tap="onSave(0)">保存</view>
	</view>
</template>


<script setup lang="ts">
	import { getCurrentInstance, inject, ref, watch } from 'vue';
	import { showToast } from '@/utils/util';
	import { useUpload } from '../../../../../hooks/useUpload';
	import { useCommon } from '@/hooks/useGlobalData';
	import { saveRelieveForensics } from '../../../api'
	const list = ref([
		{ label: '门头三招', doorFirstImage: [], prop: 'doorFirstImage' },
		{ label: '大堂', lobbyImage: [], prop: 'lobbyImage' },
		{ label: '吧台', barImage: [], prop: 'barImage' },
		{ label: '吧台背景墙', settingImage: [], prop: 'settingImage' },
		{ label: '走廊', corridorImage: [], prop: 'corridorImage' },
		{ label: '房间内部 ', roomImage: [], prop: 'roomImage' },
		{ label: '卫生间', toiletImage: [], prop: 'toiletImage' },
	])
	const { batchUpload } = useUpload()
	const tripInfo : any = inject('tripInfo')
	const getTripDetail : Function = inject('getTripDetail')
	const patrolPlan : any = inject('patrolPlan')
	const { globalData } = useCommon(getCurrentInstance())
	/**
	 * @description: 草稿复显
	 */
	watch(() => patrolPlan.value, (v : any) => {
		if (v?.relieveForensics) {
			for (let key in v.relieveForensics) {
				if (v.relieveForensics[key]) {
					const index = list.value.findIndex(item => item.prop == key)
					list.value[index][key] = v.relieveForensics[key]
				}
			}
		}
	}, { immediate: true, deep: true })
	/**
	 * @description: 上传图片
	 */
	const chooseImage = async (index : number, prop : string) => {
		try {
			const currentNum = list.value[index][prop].length
			const successPaths = await batchUpload(9 - currentNum)
			list.value[index][prop].push(...successPaths)
		} catch (e) {
			console.log("?? ~ chooseImage ~ e:", e)
		}
	}
	/**
	 * @description: 删除图片
	 */
	const removeImage = (index : number, prop : string, idx : number) => {
		list.value[index][prop].splice(idx, 1)
	}
	/**
	 * @description: 校验
	 */
	const validate = (isSubmit : number) => {
		const userId = globalData.value.user?.userId
		if (userId != tripInfo.value.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (tripInfo.value.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		if (isSubmit) {
			for (let i = 0, len = list.value.length; i < len; i++) {
				const item = list.value[i]
				if (!item[item.prop].length) {
					showToast(item.label + '图片不能为空')
					return
				}
			}
		}
		return true
	}
	/**
	 * @description: 保存
	 */
	const onSave = async (isSubmit : number) => {
		try {
			const { orderId, shopId, tripId } = tripInfo.value
			const obj = list.value.reduce((o : any, k : any) => {
				o[k.prop] = k[k.prop]
				return o
			}, {})
			const params = {
				orderId,
				shopId,
				tripId,
				isSubmit,
				...obj
			}
			if (!validate(isSubmit)) return
			await saveRelieveForensics(params)
			showToast({
				title: '保存成功',
				success() {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		} catch (error) {
			//TODO handle the exception
		}
	}
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";

	.section {
		padding: 20rpx 24rpx !important;

		.label {
			line-height: 48rpx !important;
		}
	}

	.tip {
		padding: 20rpx 0;
	}

	.camera {
		padding: 10rpx 20rpx;
		border-radius: 552rpx;
		background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);

		.img {
			width: 32rpx;
			height: 32rpx;
			margin-right: 10rpx;
			flex-shrink: 0;
		}

		.text {
			color: $uni-text-color;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 28rpx;
		}
	}

	.img-list {
		margin-top: 24rpx;
		flex-wrap: wrap;
		gap: 14rpx;

		.img-item {
			position: relative;

			.img {
				width: 94rpx;
				height: 94rpx;
				border-radius: 12rpx;
				flex-shrink: 0;
			}

			.remove {
				width: 28rpx;
				height: 28rpx;
				flex-shrink: 0;
				position: absolute;
				top: -8rpx;
				left: -8rpx;
				z-index: 1;
			}
		}
	}

	.btn {
		justify-content: space-between;
		padding-top: 20rpx;

		.custom-btn {
			width: 270rpx;
			margin: 0;
			background: #fff;
		}
	}
</style>