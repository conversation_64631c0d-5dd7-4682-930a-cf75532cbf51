<template>
	<!-- 欠费问题店基本情况填写 -->
	<template v-if="data.tripStatus == 1002">
		<basic-information :data="data" />
	</template>
	<!-- 解约取证资料节点 -->
	<template v-if="data.tripStatus == 1003">
		<obtain-evidence :data="data" />
	</template>
</template>

<script setup lang="ts">
	import basicInformation from './basic-information.vue';
	import obtainEvidence from './obtain-evidence.vue';

	defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
</script>

<style scoped lang="scss">
</style>