<template>
	<view class="section textarea">
		<view class="label">解约原因：</view>
		<cTextareaVue v-model="form.cause" count maxlength="300" placeholder="请填写情况说明" />
	</view>
	<view class="section">
		<view class="label">解约店类型（请勾选解约店类型单选/多选）：</view>
		<view class="flss space-between">
			<up-checkbox-group v-model="form.relieve" shape="circle" size="16" labelSize="14" activeColor="#C35943"
				inactiveColor="#C35943" labelColor="#1F2428">
				<up-checkbox v-for="item in types" :key="item.value" :label="item.label" :name="item.value" shape="circle"
					size="16" labelSize="14" activeColor="#C35943" inactiveColor="#C35943" labelColor="#1F2428" />
			</up-checkbox-group>
		</view>
	</view>
	<view class="section fr space-between">
		<view class="label">解约日期：</view>
		<view class="fr" @click="dateShow = true">
			<view class="placeholderClass" style="margin-right: 8rpx;">{{dateFormater || '解约时间'}}</view>
			<up-icon name="arrow-right" color="#999999" size="24rpx" />
		</view>
	</view>
	<view class="section textarea">
		<view class="label">解约方案：</view>
		<cTextareaVue v-model="form.relieveScheme" count maxlength="300" />
	</view>
	<view class="section textarea">
		<view class="fr space-between" style="margin-bottom: 20rpx;">
			<view class="label" style="margin-bottom: 0;">上传解约协议照片 ：</view>
			<view class="camera fr" v-if="form.relieveImage?.length < 5" @tap="chooseImage">
				<image class="img"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-12/1733971763935.png"
					mode=""></image>
				<view class="text">图片</view>
			</view>
		</view>
		<view class="images-tip">可上传扫描照片,请保证照片内容清楚</view>
		<view class="img-list fr" v-if="form.relieveImage?.length > 0">
			<view class="img-item" v-for="(url,idx) in form.relieveImage" :key="idx">
				<image class="img" :src="url" mode="aspectFill"></image>
				<view class="remove" @tap="removeImage(idx)">
					<up-icon name="close-circle-fill" size="14" color="rgba(31, 36, 40, 0.7)"></up-icon>
				</view>
			</view>
		</view>
	</view>
	<view class="btn fr">
		<view class="custom-btn" @tap="saveRelieveCase(1)">确认提交</view>
		<view class="custom-btn custom-btn-primary" @tap="saveRelieveCase(0)">保存</view>
	</view>

	<up-datetime-picker v-model="dateValue" :show="dateShow" mode="date" :itemHeight="50" confirmColor="#C35943"
		@confirm.stop="handleDateConfirm" @cancel.stop="dateShow = false" :formatter="formatter" />
</template>

<script setup lang="ts">
	import dayjs from 'dayjs';
	import { getCurrentInstance, inject, ref, watch, computed } from 'vue';
	import cTextareaVue from '../../../../../components/c-textarea/c-textarea.vue';
	import { formatter, setTime, showToast } from '@/utils/util'
	import { saveRelieveCase as saveRelieveCaseApi } from '../../../api'
	import { useCommon } from '@/hooks/useGlobalData';
	import { useUpload } from '../../../../../hooks/useUpload';
	import { format } from 'echarts';
	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
	type formType = {
		cause : string;
		relieve : string;
		relieveTime : string;
		relieveScheme : string;
		relieveImage : string[]
	}
	const types = [
		{ label: '单方解约', value: '1' },
		{ label: '双方解约', value: '2' },
		{ label: '公证解约', value: '3' }
	]
	const patrolPlan : any = inject('patrolPlan')
	const tripInfo : any = inject('tripInfo')
	const getTripDetail : Function = inject('getTripDetail')
	const { globalData } = useCommon(getCurrentInstance())
	const { batchUpload } = useUpload()
	const dateShow = ref<Boolean>(false), dateValue = ref(dayjs().format('YYYY-MM-DD'))
	const form = ref<Partial<formType>>({
		relieveImage: []
	})
	watch(() => patrolPlan.value, (v : any) => {
		if (v?.unwindingCase) {
			let relieve = v.unwindingCase.relieve
			if (relieve) {
				relieve = relieve.split(',')
			}
			form.value = {
				...v.unwindingCase,
				relieve,
				relieveImage: v.unwindingCase?.relieveImage || []
			}
		}
	}, { immediate: true, deep: true })
	/**
		 * @description : 页面展示的日期
		 */
	const dateFormater = computed(() => {
		return form.value.relieveTime ? dayjs(form.value.relieveTime).format('YYYY年MM月DD日') : ''
	})
	/**
	 * @description: 选择解约时间
	 */
	const handleDateConfirm = (item : any) => {
		form.value.relieveTime = item.value
		dateValue.value = item.value
		dateShow.value = false
	}
	/**
	 * @description: 上传图片
	 */
	const chooseImage = async () => {
		try {
			const currentNum = form.value.relieveImage?.length || 0
			const successPaths = await batchUpload(5 - currentNum)
			form.value.relieveImage.push(...successPaths)
		} catch (e) {
			console.log("?? ~ chooseImage ~ e:", e)
		}
	}
	/**
	 * @description: 删除图片
	 */
	const removeImage = (idx : number) => {
		form.value.relieveImage.splice(idx, 1)
	}
	/**
	 * @description: 校验
	 */
	const validate = (isSubmit : number) => {
		const userId = globalData.value.user.userId
		if (userId != tripInfo.value.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (tripInfo.value.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		if (isSubmit) {
			const rules = {
				cause: { type: 'string', message: '请填写解约原因' },
				relieve: { type: 'array', message: '请选择解约类型' },
				relieveTime: { type: 'string', message: '请选择解约日期' },
				relieveScheme: { type: 'string', message: '请填写解约方案' },
				relieveImage: { type: 'array', message: '请上传解约图片' }
			}
			for (const key in rules) {
				if (rules[key].type == 'string') {
					if (!form.value[key]) {
						showToast(rules[key].message)
						return
					}
				} else {
					if (!form.value[key]?.length) {
						showToast(rules[key].message)
						return
					}
				}
			}
		}
		return true
	}
	/**
	 * @description: 保存解约情况
	 */
	const saveRelieveCase = async (isSubmit : number) => {
		try {
			const { shopId, orderId, tripId } = tripInfo.value
			const params : any & formType = {
				isSubmit,
				shopId,
				orderId,
				tripId,
				...form.value
			}
			if (params.relieve) {
				params.relieve = params.relieve.join(',')
			}
			if (params.relieveTime) {
				params.relieveTime = dayjs(params.relieveTime).format('YYYY-MM-DD HH:mm:ss')
			}
			if (!validate(isSubmit)) return
			await saveRelieveCaseApi(params)
			showToast({
				title: '保存成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		} catch (e) {
			console.log("?? ~ saveRelieveCase ~ e:", e)
		}
	}
</script>

<style scoped lang="scss">
	@import "../../../style/common.scss";

	.section {
		padding: 20rpx 24rpx !important;

		.label {
			line-height: 48rpx !important;
		}
	}


	.textarea {
		.label {
			margin-bottom: 20rpx;
		}

		:deep() {
			.custom-textarea {
				.textarea {
					height: 186rpx;
				}
			}
		}
	}

	:deep() {
		.placeholderClass {
			color: #999999 !important;
			font-size: 20rpx !important;
			text-align: right;
		}

		.u-checkbox-group {
			justify-content: space-between;
		}
	}

	.btn {
		justify-content: space-between;
		padding-top: 20rpx;

		.custom-btn {
			width: 270rpx;
			margin: 0;
			background: #fff;
		}
	}

	.camera {
		padding: 10rpx 20rpx;
		border-radius: 552rpx;
		background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);

		.img {
			width: 33rpx;
			height: 33rpx;
			margin-right: 10rpx;
			flex-shrink: 0;
		}

		.text {
			color: $uni-text-color;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 28rpx;
		}
	}

	.img-list {
		margin-top: 24rpx;
		flex-wrap: wrap;
		gap: 14rpx;

		.img-item {
			position: relative;

			.img {
				width: 94rpx;
				height: 94rpx;
				border-radius: 12rpx;
				flex-shrink: 0;
			}

			.remove {
				width: 28rpx;
				height: 28rpx;
				flex-shrink: 0;
				position: absolute;
				top: -8rpx;
				left: -8rpx;
				z-index: 1;
			}
		}
	}
</style>