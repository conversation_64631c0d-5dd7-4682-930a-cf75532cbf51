<template>
	<view class="content">
		<view class="content-title" v-if="patrolPlan?.header">{{patrolPlan?.header}}</view>
		<view class="section tip" v-if="patrolPlan?.content || patrolPlan?.trail">
			<view class="text" v-if="patrolPlan?.content">{{patrolPlan?.content}}</view>
			<template v-if="patrolPlan?.trail">
				<view class="fr href" @tap="onTap" v-if="patrolShopStatus">
					<text class="href-title">
						{{ suffix && suffix !='填写' ? data.shopId : ''}}{{ suffix != '填写' ? '门店报告' : patrolPlan?.trail }}
					</text>
					<text class="href-btn">去{{suffix}}</text>
				</view>
				<view class="fr href" @tap="onView" v-if="arrearsStatus">
					<text class="href-title">
						{{ data.shopId }}{{patrolPlan?.trail}}
					</text>
					<text class="href-btn">查看</text>
				</view>
			</template>
		</view>
		<!-- 欠费问题店 -->
		<arrears-shop v-if="data?.targetCode == 3 && data?.tripStatus == 1002" :data="data" />
		<!-- 舞弊店表单 -->
		<fraudulent-shop
			v-if="data?.targetCode == 4 && (data?.tripStatus == 1002 || data?.tripStatus == 1003 || data?.tripStatus == 1004)"
			:data="data" />
		<termination-shop v-if="data?.targetCode == 5 && (data?.tripStatus == 1002 || data?.tripStatus == 1003)"
			:data="data" />
		<view class="custom-btn custom-btn-primary" v-if="data?.targetCode == 6 && (data?.tripStatus == 100401 || data?.tripStatus == 1005)" @tap="confirmTheEndOfTheStoreInspection">确认结束巡店</view>
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance, inject, computed, unref, ref, watch } from 'vue';
	import fraudulentShop from './fraudulent-shop/fraudulent-shop.vue'
	import arrearsShop from './arrears-shop/arrears-shop.vue';
	import terminationShop from './termination-shop/termination-shop.vue';
	import { useCommon } from '@/hooks/useGlobalData'
	import { showToast } from '@/utils/util';
	import BMapWX from '../../../../lib/bmap-wx.js'
	import { saveLocation } from '../../api'
	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
	const { route_to_view, globalData } = useCommon(getCurrentInstance())
	const patrolPlan : any = inject('patrolPlan')
	const getTripDetail : Function = inject('getTripDetail')
	const BMap = new BMapWX({
		ak: 'rfIhkgeIsQh4JBHiZPMI1oYMPR3PPMGH'
	});
	const location = ref({
		address: '',
		lat: '31.22024',
		lng: '121.42394'
	})
	const suffix = computed(() => {
		if (patrolPlan.value?.header) {
			return patrolPlan.value.header.substring(patrolPlan.value.header.length - 2)
		}
		return ''
	})
	/**
	 * @description: 巡店/表单状态
	 */
	const patrolShopStatus = computed(() => {
		const data = props.data
		return ((data.tripStatus == 1003 || data.tripStatus == 100301) && data.targetCode != 4 && data.targetCode != 5) || ((data.tripStatus == 1002 || data.tripStatus == 100201) && (data.targetCode == 1 || data.targetCode == 2 || data.targetCode == 6)) || ((data.tripStatus == 1001 || data.tripStatus == 100201) && data.targetCode == 6) || ((data.tripStatus == 1004 || data.tripStatus == 100301) && (data.targetCode == 2 || data.targetCode == 1))
	})
	/**
	 * @description: 欠费状态/舞弊
	 */
	const arrearsStatus = computed(() => {
		return props.data.tripStatus == 1002 && (props.data.targetCode == 3 || props.data.targetCode == 4)
	})
	/**
	 * @description: 校验是否能操作
	 */
	const validate = () => {
		const userId = globalData.value.user.userId
		if (userId != props.data.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (props.data.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		return true
	}
	/**
	 * @description: 表单和报告
	 */
	const onTap = () => {
		if (!validate()) return
		if (suffix.value) {
			if (suffix.value == '填写') {
				const { tripId, tripCode, brandType, orderId, targetCode, shopId, patrolUserId } = props.data
				route_to_view(`/packageB/pages/shop-inspection-form/shop-inspection-form?tripCode=${tripCode}&tripId=${tripId}&brandType=${brandType}&orderId=${orderId}&targetCode=${targetCode}&shopId=${shopId}&patrolMan=${patrolUserId}`)
			} else {
				route_to_view('/packageB/pages/shop-inspection-report/shop-inspection-report?tripCode=' + props.data.tripCode)
			}
		}
	}
	/**
	 * @description: 查看稽核数据
	 */
	const onView = () => {
		if (!validate()) return
		route_to_view('/packageB/pages/shop-inspection/audit-data?shopId=' + props.data.shopId + '&targetCode=' + props.data.targetCode)
	}
	/**
	 * @description: 确认结束巡店
	 */
	const confirmTheEndOfTheStoreInspection = async () => {
		const { tripStatus, shopId, orderId, tripId, tripCode } = props.data
		const { lat, lng, address } = unref(location)
		const params = {
			longitude: lng,
			latitude: lat,
			location: address,
			status: tripStatus,
			shopId,
			orderId,
			tripId,
		}
		if (tripCode == 6 && tripStatus == 1004) {
			params.location = null
		}
		try {
			await saveLocation(params)
			showToast({
				title: '操作成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						getTripDetail?.()
					}, 1000)
				}
			})
		} catch (e) {
			console.log("?? ~ onConfirm ~ e:", e)
		}
	}
	/**
	 * @description: 逆解析地址
	 */
	const getLocation = () => {
		BMap.regeocoding({
			success: (res : any) => {
				console.log("🚀 ~ getLocation ~ res:", res)

				if (res.originalData.status == 0) {
					const { formatted_address_poi, location: poi } = res.originalData.result
					location.value = {
						address: formatted_address_poi,
						lat: poi.lat,
						lng: poi.lng
					}
				} else {
					showToast('获取位置失败')
				}
			},
			fail: (e : any) => {
				console.log("?? ~ onMounted ~ e:", e)
				showToast('获取位置失败')
			}
		})
	}
	watch(() => props.data, (v: any) => {
		if(v?.targetCode == 6 && v?.tripStatus == 100401){
			getLocation()
		}
	}, {deep: true, immediate: true})
</script>

<style scoped lang="scss">
	@import "../../style/common.scss";

	.custom-btn {
		margin: 40rpx 0 0;
	}

	.href {
		justify-content: space-between;
		text-decoration: none !important;

		&-title {
			color: #1e61fc;
			text-decoration: underline;
		}

		&-btn {
			color: #1e61fc;
		}
	}
</style>