<template>
	<view :class="[{'table-disabled': mode === 'disabled'}]">
		<cTable :header="startingTimeEnums" :list="data" :height="460" emptyText="还未选择巡店时间哦～" ～>
			<template #default="scope">
				<view v-if="scope.prop === 'beginTime'" class="fr"
					@click="mode==='edit' ? handleDate(scope.data,scope.prop,scope.index) : null">
					<view class="time">{{scope.data.beginTime || '-'}}</view>
					<up-icon v-if="mode === 'edit'" name="arrow-down" color="#1F2428" size="22rpx" />
				</view>
				<view v-if="scope.prop === 'overTime'" class="fr"
					@click="mode==='edit' ? handleDate(scope.data,scope.prop,scope.index) : null">
					<view class="time">{{scope.data.overTime || '-'}}</view>
					<up-icon v-if="mode === 'edit'" name="arrow-down" color="#1F2428" size="22rpx" />
				</view>
			</template>
		</cTable>
	</view>
	<up-datetime-picker v-model="dateValue" :show="dateShow" mode="date" :itemHeight="50" confirmColor="#C35943"
		:minDate="minDate" :maxDate="maxDate" @confirm.stop="handleDateConfirm" @cancel.stop="dateShow = false" />
</template>

<script setup lang="ts">
	import { reactive, ref, watch } from 'vue';
	import cTable from '@/components/c-table/c-table.vue'
	import { startingTimeEnums } from '../../enums';
	import dayjs from 'dayjs';
	import { showToast } from '@/utils/util';

	type StartingTimeList = {
		shopId : string
		beginTime : string
		overTime : string
	}

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})


	const props = withDefaults(
		defineProps<{
			data : StartingTimeList[]
			mode : string
			tripType : Number | String
		}>(), {
		data: () => [],
		mode: () => '',
		tripType: () => 1 // 1手动发起 2自动生成
	}
	)
	const emit = defineEmits(['change'])

	const dateShow = ref<Boolean>(false), dateValue = ref('')
	const currentStartingTimeItemInfo = reactive<{ index : number, prop : string, dataItem : StartingTimeList }>({ index: null, prop: null, dataItem: null })

	const minDate = ref(dayjs().valueOf())
	const maxDate = ref(dayjs().add(10, 'year').endOf('year').valueOf())

	watch(
		() => props.tripType,
		() => {
			if (props.tripType == 2) {
				minDate.value = dayjs().valueOf()
				maxDate.value = dayjs().endOf('month').valueOf()
			}
		},
		{ immediate: true }
	)

	// 修改起始时间
	const handleDate = (item : StartingTimeList, prop : string, index : number) => {
		currentStartingTimeItemInfo.dataItem = item
		currentStartingTimeItemInfo.index = index
		currentStartingTimeItemInfo.prop = prop
		dateValue.value = item[prop]
		dateShow.value = true
	}

	// 确定时间
	const handleDateConfirm = (item) => {
		const { dataItem, index, prop } = currentStartingTimeItemInfo

		const dateValue = dayjs(item.value);
		const beginTime = dayjs(dataItem.beginTime);
		const overTime = dayjs(dataItem.overTime);

		let isValid = true;
		let errorMessage = '';

		if (prop === 'beginTime') {
			// 开始时间只能小于或等于结束时间
			if (dateValue.isAfter(overTime)) {
				isValid = false;
				errorMessage = '开始时间不能大于结束时间。';
			}
		} else if (prop === 'overTime') {
			// 结束时间只能大于或等于开始时间
			if (dateValue.isBefore(beginTime)) {
				isValid = false;
				errorMessage = '结束时间不能小于开始时间。';
			}
		}

		if (!isValid) {
			showToast(errorMessage);
			return;
		}

		dateShow.value = false
		emit('change', item, index, prop)
	}
</script>

<style scoped lang="scss">
	.time {
		margin-right: 7rpx;
		color: #1F2428
	}

	.table-disabled {
		.time {
			color: #1f24284d !important;
		}

		:deep() {
			.tbody {
				.td-text {
					color: #1f24284d !important;
				}
			}
		}
	}
</style>