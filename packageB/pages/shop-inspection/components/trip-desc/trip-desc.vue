<template>
	<view class="trip">
		<view class="info fr">
			<view class="no">行程编号：{{ data?.tripCode }}</view>
		</view>
		<view class="desc">
			<view class="row fr">
				<view class="label">计划门店：</view>
				<view class="value">{{ data?.shopId }}</view>
			</view>
			<view class="row fr">
				<view class="label">巡店人：</view>
				<view class="value">{{ data?.patrolUserName }}</view>
			</view>
			<view class="" v-show="isOpen">
				<view class="row fr">
					<view class="label">系统状态：</view>
					<view class="value">{{ data?.shopType }}</view>
				</view>
				<view class="row fr" v-if="data?.targetCode">
					<view class="label">巡店目的：</view>
					<view class="value">{{ tripTargetEnums.find(item => item.value == data.targetCode)?.label }}</view>
				</view>
				<view class="row fr" v-if="data?.orderCode">
					<view class="label">计划编号：</view>
					<view class="value">{{ data.orderCode }}</view>
				</view>
				<view class="row fr" v-if="data?.belong">
					<view class="label">计划归属：</view>
					<view class="value">{{ tripBelongEnums.find(item => item.value == data.belong)?.label }}</view>
				</view>
				<view class="row fr" v-if="data?.startTime && time == 1">
					<view class="label">巡店时间：</view>
					<view class="value">{{ data.startTime }}</view>
				</view>
				<view class="row fr" v-if="data?.startDate && time == 2">
					<view class="label">巡店时间：</view>
					<view class="value">{{ data.startDate }} ~ {{ data.endDate }}</view>
				</view>
				<view class="row fr" v-if="data?.tripType">
					<view class="label">行程类型：</view>
					<view class="value">{{ data.tripType ==1 ? '手动发起' : '自动生成'}}</view>
				</view>
				<view class="row fr" v-if="data?.tripType == 2">
					<view class="label">命中策略：</view>
					<view class="value fr" @click.stop="hide = !hide">
						<view class="">{{splitString(data.strategyName)}}</view>
						<view class="" style="margin-left: 8rpx;" v-if="data?.strategyName?.length > 15">
							<up-icon :name="!hide? 'arrow-up' :'arrow-down'" color="#1F2428" size="22rpx" bold />
						</view>
					</view>
				</view>
			</view>
			<view class="show fr">
				<view class="fr" @click.stop="isOpen = !isOpen">
					<view class="text">{{isOpen ? '收起' : '展开'}}</view>
					<up-icon :name="isOpen? 'arrow-up' :'arrow-down'" color="#999999" size="22rpx" />
				</view>
			</view>
		</view>
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	import { computed, ref } from 'vue';
	import { tripTargetEnums, tripBelongEnums } from '../../enums'
	const isOpen = ref<Boolean>(false)

	const hide = ref(true)
	const splitString = computed(() => (text : string) => {
		if (!text) return
		if (text.length > 15 && hide.value) {
			return text.slice(0, 15) + '...'
		} else {
			return text
		}
	})
	defineProps({
		data: {
			type: Object,
			default: () => { }
		},
		time: {
			type: Number,
			default: 1
		}
	})
</script>

<style scoped lang="scss">
	.trip {
		box-sizing: border-box;
		padding: 20rpx 40rpx;
		justify-content: space-between;
		border-radius: 20rpx;

		.info {
			position: relative;
			margin-bottom: 40rpx;
			justify-content: space-between;

			&::before {
				position: absolute;
				content: '';
				height: 2rpx;
				left: -20rpx;
				right: -20rpx;
				top: 60rpx;
				background: #f2f2f2;
			}

			.no {
				color: #1f2428;
				font-size: 28rpx;
				font-weight: 400;
			}

			.state {
				color: #56cc93;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;
				padding: 8rpx 46rpx 8rpx 20rpx;
				border-radius: 20rpx 0 0 0;
				margin-right: -40rpx;
			}
		}

		.desc {

			.row {
				justify-content: space-between;
				margin-bottom: 14rpx;
				align-items: baseline;

				&:last-child {
					margin-bottom: 0;
				}

				.label {
					 color: #1f2428b3;
					font-size: 28rpx;
					line-height: 38rpx;
					min-width: 140rpx;
					flex-shrink: 0;
				}

				.value {
					justify-content: flex-end;
					align-items: baseline;
					color: #1f2428;
					font-size: 28rpx;
					line-height: 38rpx;
					width: 411rpx;
					text-align: right;
					word-break: break-all;
				}
			}

			.show {
				justify-content: center;
				margin-top: 20rpx;

				&>view {
					padding: 0 40rpx;
				}

				.text {
					color: #999999;
					font-size: 24rpx;
					line-height: 44rpx;
					margin-right: 8rpx;
				}
			}
		}
	}
</style>