<template>
	<view :class="[{'table-disabled': mode === 'disabled'}]">
		<cTable :header="header" :list="data" :height="460" emptyText="还未选择门店哦～">
			<template #default="scope">
				<view v-if="scope.prop === 'target'" class="fr"
					@click="mode === 'edit' ? handlePurpose(scope.data,scope.prop,scope.index) : null">
					<view class="purpose">{{scope.data.target}}</view>
					<up-icon v-if="mode === 'edit'" name="arrow-down" color="#1F2428" size="22rpx" />
				</view>
				<view v-if="scope.prop === 'strategyName'" class="fr">
					<view class="strategyName"
						@click="scope.data.strategyName.length > 10 ? showText(scope.data.strategyName):''">
						{{omitText(scope.data.strategyName) || '-'}}
					</view>
				</view>
				<view v-if="scope.prop === 'status'" class="fr">
					<view class="status" style="font-weight: bold;" :style="{color:planStateColor[scope.data.status]}">
						{{shopInspectionToString[scope.data.status]}}
					</view>
				</view>
        <view v-if="scope.prop === 'lastTime'" class="fr">
          <view v-if="scope.data.lastTime" style="white-space: pre-wrap">
            {{scope.data.lastTime?.split(' ')[0]}}\n{{scope.data.lastTime?.split(' ')[1]}}
          </view>
          <view v-else>-</view>
        </view>
			</template>
		</cTable>
	</view>
	<up-picker :show="stateShow" :columns="[targetCode]" confirmColor="#C35943" closeOnClickOverlay
		:defaultIndex="defaultIndex" @confirm="handlesystemStateConfirm" @cancel="close" @close="close" keyName="target" />
	<template v-if="modalShow">
		<cModal v-model:show="modalShow" :content="modalContent"></cModal>
	</template>
</template>

<script setup lang="ts">
import {
  ComponentInternalInstance,
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  watch
} from 'vue';
	import { plannedStoresColumnEnums, shopInspectionToString } from '../../enums';
	import cTable from '@/components/c-table/c-table.vue'
	import cModal from '@/components/c-modal/index';
	import { listTargetCode } from '../../api';
  import { useCommon } from "@/hooks/useGlobalData";
  const instance = getCurrentInstance() as ComponentInternalInstance
  const { globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const planStateColor = {
		3: '#FF4D4D',
		4: '#56CC93',
	}

	const header = ref(JSON.parse(JSON.stringify(plannedStoresColumnEnums)))

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const props = defineProps({
		data: {
			type: Array,
			default: () => []
		},
		mode: {
			type: String,
			default: '' // 'edit | disabled'
		},
		plannedState: {
			type: [Number, String],
		},
		tripType: {
			type: [Number, String],
			default: 1 // 1手动发起 2自动生成
		},
    allData: {
      type: Object,
      default: () => {}
    }
	})
	const emit = defineEmits(['change'])

	type TargetCode = {
		target : string, targetCode : string
	}[]

	const stateShow = ref<Boolean>(false)
	const currentPlannedStoresItemInfo = reactive({ index: null, prop: null })
	const targetCodeList = ref<TargetCode>([]), targetCode = ref<TargetCode>([]), defaultIndex = ref([0])

	watch(
		() => props.plannedState,
		(v) => {
			header.value = JSON.parse(JSON.stringify(plannedStoresColumnEnums));
			if (props.tripType === 2) {
				header.value = header.value.map(item => ({
					...item,
					width: 130
				}))
				header.value.push({
					name: '命中策略',
					prop: 'strategyName',
					width: 200,
					slot: true,
				})
			}
			if (v === 3) { // 已审核
        if (globalData.value?.user?.userId === props.allData.managerId) { // 审核人查看下属巡店计划展示
          header.value = header.value.map(item => ({
            ...item,
            width: 130
          }))
          header.value.push({
            name: '上次巡店时间',
            prop: 'lastTime',
            width: 150,
            slot: true,
          },{
            name: '新增网评分',
            prop: 'comprehensiveScore',
            width: 130
          })
        } else {
          header.value.push({
            name: '行程状态',
            prop: 'status',
            slot: true,
            width: 130
          })
        }
			}
		}
	)

	onMounted(() => {
    instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
      if (res) {
        getListTargetCode()
      }
    })
	})

	// 修改巡店目的
	const handlePurpose = (item : any, prop : string, index : number) => {
		targetCode.value = JSON.parse(JSON.stringify(targetCodeList.value))
		if (!item.ifShow) {
			targetCode.value.pop()
		}
		nextTick(() => {
			defaultIndex.value = [targetCode.value.findIndex(v => v.targetCode == item.targetCode)]
			currentPlannedStoresItemInfo.index = index
			currentPlannedStoresItemInfo.prop = prop
			stateShow.value = true
		})
	}

	const close = () => {
		stateShow.value = false
	}

	// picker确定
	const handlesystemStateConfirm = (item : TargetCode[number]) => {
		const { index, prop } = currentPlannedStoresItemInfo
		stateShow.value = false
		emit('change', item, index, prop)
	}

	// 获取巡店目的code列表
	const getListTargetCode = async () => {
		try {
			const { data } = await listTargetCode()
			if (data) targetCodeList.value = data
		} catch (e) {
			console.error('获取巡店目的code列表 error', e)
		}
	}

	const omitText = computed(() => (text : string) => {
		if (text?.length > 10) {
			return text.slice(0, 10) + '...'
		} else {
			return text
		}
	})


	const modalContent = ref<string>(), modalShow = ref<Boolean>(false)
	const showText = (text : string) => {
		modalContent.value = text
		modalShow.value = true
	}
</script>

<style scoped lang="scss">
	.purpose {
		margin-right: 7rpx;
		color: #1F2428
	}

	.table-disabled {
		.purpose {
			color: #1f24284d !important;
		}

		:deep() {
			.tbody {
				.td-text {
					color: #1f24284d !important;
				}
			}
		}
	}
</style>