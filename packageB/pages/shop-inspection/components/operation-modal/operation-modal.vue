<template>
	<view class="modal">
		<up-modal :show="show" :zoom="false" :title="title" v-bind="$attrs" width="710rpx">
			<template>
				<view class="content">
					<slot></slot>
					<view class="textarea-section">
						<cTextareaVue v-model.trim="textarea" count maxlength="100" :placeholder="placeholderText" />
					</view>
				</view>
			</template>
			<template #confirmButton>
				<view class="footer">
					<view class="custom-btn custom-btn-primary" @click="cancel">{{cancelButtonText}}</view>
					<view class="btn" @click="confirm">{{confirmButtonText}}</view>
				</view>
			</template>
		</up-modal>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import cTextareaVue from '../../../../components/c-textarea/c-textarea.vue';
	import { showToast } from '@/utils/util';

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		placeholderText: {
			type: String,
			default: '请输入'
		},
		title: {
			type: String,
			default: '温馨提示'
		},
		confirmButtonText: {
			type: String,
			default: '确定'
		},
		cancelButtonText: {
			type: String,
			default: '取消'
		}
	})
	const emit = defineEmits(['cancel', 'confirm'])
	const textarea = ref<string>('')

	const confirm = () => {
		if (textarea.value.length <= 0) {
			showToast(`请填写${props.status == 6 ? '作废' : '不执行'}原因`)
			return
		}
		emit('confirm', textarea.value)
	}

	const cancel = () => {
		emit('cancel')
		textarea.value = ''
	}
</script>

<style scoped lang="scss">
	.modal {

		:deep() {
			.u-modal__title {
				color: #1f2428;
				font-size: 36rpx;
				text-align: center;
				line-height: 36rpx;
			}
		}
	}

	.content {
		width: 100%;

	}

	.footer {
		.custom-btn {
			margin: 0 0 40rpx;
		}

		.btn {
			color: #999999;
			font-size: 30rpx;
			font-weight: 500;
			line-height: 30rpx;
			text-align: center;
			margin-bottom: 10rpx;
		}
	}
</style>