<template>
	<view class="CC fr">
		<view class="fr">
			<image class="avatar"
				src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-05/1733379705136.png"
				mode="aspectFill" lazy-load></image>
			<view class="fl">
				<view class="name">{{data?.name}}</view>
				<view class="identity">{{data.departmentName}}</view>
			</view>
		</view>
		<image class="img"
			:src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/${mode == 'check' ? '1733282068789' : data.selected ? '1733282068789' : '1733282007313'}.png`"
			mode="aspectFill"></image>
	</view>
</template>

<script setup lang="ts">
	defineProps({
		data: {
			type: Object,
			default: () => { }
		},
		mode: {
			type: String,
			default: ''
		}
	})
</script>

<style lang="scss" scoped>
	.CC {
		margin-bottom: 20rpx;
		padding: 28rpx 40rpx;
		justify-content: space-between;
		border-radius: 20rpx;
		background: #ffffff;

		&:last-child {
			margin-bottom: 0;
		}

		.avatar {
			width: 70rpx;
			height: 70rpx;
			flex-shrink: 0;
			margin-right: 20rpx;
		}

		.name {
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			line-height: 28rpx;
			margin-bottom: 18rpx;
		}

		.identity {
			color: #999999;
			font-size: 28rpx;
			line-height: 28rpx;
		}

		.img {
			width: 24rpx;
			height: 24rpx;
			flex-shrink: 0;
		}
	}
</style>