<template>
	<view class="modal">
		<up-modal :show="show" :zoom="false" v-bind="$attrs" width="710rpx">
			<template>
				<starting-time v-if="mode === 1" :data="list" :tripType="data.tripType" @change="handleStartingTime"
					mode="edit" />
				<planned-stores v-if="mode === 2" :data="list" @change="handlPlannedPurpose" mode="edit" />
			</template>
			<template #confirmButton>
				<view class="footer fr">
					<view class="custom-btn" @click="$emit('cancel')">取消</view>
					<view class="custom-btn custom-btn-primary" @click="submit">保存修改</view>
				</view>
			</template>
		</up-modal>
	</view>
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	import plannedStores from '../planned-stores/planned-stores.vue';
	import startingTime from '../starting-time/starting-time.vue';
	import dayjs from 'dayjs';
	import { updatePatrolOrder } from '../../api';
	import { PatrolShop } from '../../type';
	import { showToast } from '../../../../../utils/util';

	const props = defineProps({
		show: {
			type: Boolean,
			default: false
		},
		data: {
			type: Object,
			default: () => { }
		},
		list: {
			type: Object,
			default: () => []
		},
		mode: {
			type: Number,
			default: 1
		}
	})
	const emit = defineEmits(['cancel', 'confirm'])
	const list = ref([])

	watch(() => props.data,
		(v) => {
			list.value = JSON.parse(JSON.stringify(props.list))
		},
		{ deep: true, immediate: true }
	)

	// 修改计划目的
	const handlPlannedPurpose = (value : any, index : number, prop : string) => {
		list.value[index].target = value.value[0].target
		list.value[index].targetCode = value.value[0].targetCode
	}

	// 修改起始时间
	const handleStartingTime = (value : any, index : number, prop : string) => {
		list.value[index][prop] = dayjs(value.value).format('YYYY-MM-DD')
	}

	// 修改巡店计划
	const submit = async () => {
		const firstIncompleteEntry = list.value.find(item => {
			const { beginTime, overTime } = item
			return (beginTime && !overTime) || (!beginTime && overTime);
		});
		if (firstIncompleteEntry) {
			showToast(`请选择${firstIncompleteEntry.shopId}门店巡店时间`)
			return
		}
		try {
			const params = {
				orderId: props.data.orderId as string,
				type: props.mode,
				shopList: list.value as PatrolShop[]
			}
			const { code } = await updatePatrolOrder(params)
			if (code === 200) {
				showToast('修改处理中，请稍后查看')
				emit('cancel')
				setTimeout(() => {
					emit('confirm', props.data.orderId)
				}, 1500)
			}
		} catch (e) {
			console.error('修改巡店计划 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.modal {
		width: 100%;

		planned-stores,
		starting-time {
			width: 100%;
		}

		:deep() {
			.u-modal__title {
				color: #1f2428;
				font-size: 36rpx;
				text-align: center;
				line-height: 36rpx;
			}
		}
	}


	.footer {
		.custom-btn {}
	}
</style>