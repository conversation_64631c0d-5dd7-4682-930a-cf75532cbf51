<template>
	<view class="container">
		<commonBgVue height="414" />
		<u-navbar placeholder title="门店搜索" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="custom-search">
			<up-search v-model="keyWord" show-action placeholder="请输入门店名字/门店ID搜索" @custom="search" action-text="查询" />
		</view>
		<view class="area-picker">
			<cAreaPicker @setArea="setArea">
				<template #right>
				</template>
			</cAreaPicker>
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view"
				:scroll-anchoring="true">
				<view class="content">
					<view class="selected-store-section">
						<view class="selected-store">
							<view class="title">已选酒店</view>
							<view class="store-list fr" v-if="selectedStoreList.length>0">
								<view class="store" v-for="(item,index) in selectedStoreList" :key="item"
									@click="removeSelectedStore(item)">
									<view class="">{{item}}</view>
									<image class="img"
										src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/1733284011578.png"
										mode=""></image>
								</view>
							</view>
							<view class="no-store" v-else>还未选择酒店哦～</view>
						</view>
					</view>
					<view class="store-item fr" v-if="list && list.length > 0 " v-for="(item,index) in list" :key="index"
						@click="selectStore(item)">
						<view class="fr">
							<image class="icon"
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/1733301372282.png"
								mode=""></image>
							<view class="shopId">{{item.shopId}}</view>
							<view class="shopName">{{item.shopName}}</view>
						</view>
						<image class="img"
							:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/${item.selected ? '1733282068789' : '1733282007313'}.png`"
							mode="aspectFill"></image>
					</view>
					<view class="empty" v-else>
						<emptyVue />
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
	<fixed-btn-layout>
		<view class="custom-btn custom-btn-primary" @click="submit">确认</view>
	</fixed-btn-layout>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, ref } from 'vue';
	import cAreaPicker from '@/components/c-area-picker/index.vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import fixedBtnLayout from '../../components/fixed-btn-layout/fixed-btn-layout.vue';
	import { OrgLevel } from '@/types/dataCockpit';
	import { useCommon } from '@/hooks/useGlobalData';
	import { listShop } from '../../api';
	import { PaginationParams } from '@/types/index.d.ts';
	import emptyVue from '../../../components/empty/empty.vue';

	type ListShop = {
		regionName : string
		areaName : number
		shopId : string
		shopName : string
		selected ?: any
	}[]

	const { globalData, route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()

	const height = ref()
	const keyWord = ref<string>(), list = ref<ListShop>()
	const { deptName, orgName, ...rest } = globalData.value.user.orgList[0];
	const areaData = ref<OrgLevel>({ // 区域数据
		...rest
	})

	const pagination = ref<PaginationParams>({
		pageNum: 1,
		pageSize: 20
	})

	const loadMore = ref<Boolean>(true)
	const selectedStoreList = ref([]) // 已选中门店

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	onLoad((options : any) => {
		const shopIds = JSON.parse(options.shopIds)
    console.log(shopIds)
		if (shopIds && shopIds.length > 0) {
			selectedStoreList.value = shopIds
		}
	})

	// 区域筛选
	const setArea = (id : number, level : string) => {
		areaData.value.orgId = id.toString()
		areaData.value.level = Number(level)
		reset()
		toListShop()
	}

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.list`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) - 80 + 'px'
		}).exec();
		toListShop()
	})

	// 获取门店列表
	const toListShop = async () => {
		try {
			const params = {
				...areaData.value,
				keyWord: keyWord.value,
				...pagination.value,
				queryType: 1
			}
			const { data } = await listShop(params)
			if (data.records && data.records.length > 0) {
				if (data.records.length < pagination.value.pageSize) loadMore.value = false
				const newData = data.records?.map((item : any) => {
					return {
						...item,
						selected: selectedStoreList.value.includes(item.shopId)
					}
				})
				nextTick(() => {
					if (list.value && list.value.length > 0)
						list.value.push(...newData)
					else
						list.value = newData
				})
			} else {
				list.value = []
			}
		} catch (e) {
			console.error('获取门店列表 error', e)
		}
	}

	const search = () => {
		reset()
		toListShop()
	}

	const scrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		toListShop()
	}

	const reset = () => {
		loadMore.value = true
		list.value = []
		pagination.value = {
			pageNum: 1,
			pageSize: 20
		}
	}

	// 选中门店
	const selectStore = (item : ListShop[number]) => {
		item.selected = !item.selected
		const index = selectedStoreList.value.indexOf(item.shopId);

		if (item.selected) {
			// 如果选中并且不在列表中，将其添加
			if (index === -1) {
				selectedStoreList.value.push(item.shopId);
			}
		} else {
			// 如果未选中并且在列表中，将其移除
			if (index !== -1) {
				selectedStoreList.value.splice(index, 1);
			}
		}
	}

	// 移除门店
	const removeSelectedStore = (item : string) => {
		const index = selectedStoreList.value.findIndex(i => i === item);
		if (index !== -1) {
			// 使用 splice 从 selectedStoreList 中移除指定的元素
			selectedStoreList.value.splice(index, 1);
			list.value.forEach(v => {
				if (v.shopId === item) v.selected = false
			})
		}
	}

	// 点击确认
	const submit = () => {
		const pages = getCurrentPages()
		const prevPage : any = pages[pages.length - 2]
		if (prevPage.route === 'packageB/pages/shop-inspection/shop-inspection-application') {
			prevPage.shopIds = selectedStoreList.value ?? []
			uni.navigateBack()
		}
	}
</script>

<style lang="scss" scoped>
	.container {}

	:deep() {
		.area-left {
			background: #ffffff !important;
		}
	}

	.custom-search {
		:deep() {
			.u-search__content {
				background-color: #ffffffe6 !important;
			}

			.u-search__content__input {
				background-color: transparent !important;
			}
		}
	}

	.area-picker {
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}

	.scroll-view {
		overflow: hidden;
		border-radius: 20rpx;
	}

	.list {
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0;

		.content {}

		.selected-store-section {
			position: sticky;
			top: 0;
			padding-bottom: 20rpx;
			background: #efefef;

			.selected-store {
				padding: 40rpx;
				background: #FFFFFF;
				border-radius: 20rpx;

				.title {
					color: #1f2428;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 32rpx;
					margin-bottom: 40rpx;
				}

				.no-store {
					border-radius: 20rpx;
					opacity: 1;
					background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
					color: #999999;
					font-size: 24rpx;
					line-height: 24rpx;
					padding: 40rpx;
					text-align: center;
				}

				.store-list {
					border-radius: 20rpx;
					background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
					padding: 28rpx 32rpx 0 40rpx;
					flex-wrap: wrap;

					.store {
						border-radius: 10rpx;
						border: 1rpx solid $uni-text-color;
						background: #ffffff;
						color: $uni-text-color;
						font-size: 26rpx;
						font-weight: bold;
						line-height: 26rpx;
						padding: 10rpx 18rpx;
						position: relative;
						margin-right: 26rpx;
						margin-bottom: 28rpx;
						box-sizing: border-box;

						view {
							color: $uni-text-color;
						}

						.img {
							width: 24rpx;
							height: 24rpx;
							flex-shrink: 0;
							position: absolute;
							top: -12rpx;
							left: -12rpx;
						}
					}
				}
			}
		}

		.store-item {
			justify-content: space-between;
			color: #1f2428;
			font-size: 28rpx;
			font-weight: bold;
			line-height: 38rpx;
			border-radius: 20rpx;
			background: #ffffff;
			padding: 28rpx 40rpx;
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.icon {
				width: 70rpx;
				height: 70rpx;
				flex-shrink: 0;
				margin-right: 20rpx;
			}

			.shopId {
				width: 120rpx;
			}

			.shopName {
				width: 308rpx;
			}

			.img {
				width: 28rpx;
				height: 28rpx;
				flex-shrink: 0;
			}
		}
	}

	.empty {
		background: #ffffff;
		border-radius: 20rpx;
	}
</style>