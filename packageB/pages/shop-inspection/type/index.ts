import { PaginationParams } from "@/types/index.d.ts"

export type Date = {
	beginDate : string
	endDate : string
}

export type PatrolShop = {
	beginTime : string
	overTime : string
	shopId : string
	shopType : string
	target : string
	targetCode : string
}

export type SavePatrolOrderParams = {
	orderId : string
	isSubmit : string
	memo : string
	patrolMan : string
	patrolManName : string
	managerId : string
	managerName : string
	copyMan ?: string
	copyManName ?: string
	shopCount ?: string
	shopList : PatrolShop[]
}

export type GetUserDepartmentInfoParams = {
	departmentId ?: string | number
}

export type QueryShopInfoParams = {
	shopIdList : string[]
}














































































export type UpdatePatrolOrderParams = {
	orderId : string
	type : number
	shopList : PatrolShop[]
}

export type OrderAuditParams = {
	orderId : string
	status : number
	backReason ?: string
	repair ?: string
}

export type ListAuditOrderParams = {
	keyword : string
} & PaginationParams

export type UpdateShopStatusParams = {
	orderId : number | string
	shopId : string
	status : number
	nonExecutionReason : string
}