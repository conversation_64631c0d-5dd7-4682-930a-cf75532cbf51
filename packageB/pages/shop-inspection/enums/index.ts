export const plannedStoresColumnEnums = [
	{
		name: '门店ID',
		prop: 'shopId',
	},
	{
		name: '系统状态',
		prop: 'shopType',
	},
	{
		name: '巡店目的',
		prop: 'target',
		slot: true,
	},
]

export const startingTimeEnums = [
	{
		name: '门店ID',
		prop: 'shopId',
	},
	{
		name: '开始时间',
		prop: 'beginTime',
		slot: true,
	},
	{
		name: '结束时间',
		prop: 'overTime',
		slot: true,
	},
]

export const systemStateEnums = [
	{ label: '全部', value: '' },
	{ label: '五星门店', value: 'A' },
	{ label: '四星门店', value: 'B' },
	{ label: '三星门店', value: 'C' },
	{ label: '二星门店', value: 'D' },
	{ label: '一星门店', value: 'E' },
	{ label: '无', value: 'F' },
]

export const planStateToStringEnums = {
	0: '草稿',
	1: '待审核',
	2: '被驳回',
	3: '已审核',
	6: '已作废'
}

export const shopInspectionToString = {
	3: '未开始',
	4: '巡店中',
	5: '已完成',
	6: '已作废',
	7: '取消',
	8: '已超时'
}

export const planStateEnums = [
	{ label: '全部状态', value: null },
	{ label: '草稿', value: 0 },
	{ label: '待审核', value: 1 },
	{ label: '被驳回', value: 2 },
	{ label: '已审核', value: 3 },
	{ label: '已作废', value: 6 }
]

export const planTypeEnums = [
	{ label: '全部类型', value: null },
	{ label: '手动发起', value: 1 },
	{ label: '自动生成', value: 2 }
]

export const planBelongEnums = [
	{ label: '全部归属', value: 0 },
	{ label: '个人自提', value: 1 },
	{ label: '他人抄送', value: 2 },
	{ label: '协助审核', value: 3 },
]

export const menu = [
	{ route: '/packageB/pages/write-follow-up/write-follow-up', icon: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-09/1733709624013.png', text: '写跟进', background: 'linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);' },
	{ route: '/packageB/pages/shop-inspection-report/shop-inspection-report', icon: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-09/1733709650248.png', text: '查报告', background: 'linear-gradient(197.7deg, #91baff 0%, #1474fa 100%);' },
	{ route: '/packageB/pages/store-data-report/store-data-report', icon: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-09/1733709679067.png', text: '门店数据', background: 'linear-gradient(17.3deg, #6046f2 0%, #c7a2fc 100%);' },
	{ route: '', icon: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-09/1733709691624.png', text: '行程作废', background: 'linear-gradient(25.9deg, #ff8c1a 0%, #ffcf9e 100%);' },
]

export const radiolist1 = [
	{ name: '是', value: '1' },
	{ name: '否', value: '0' },
]

export const tripStatusEnums = {
	1000: '未开始',
	1001: '巡店中',
	1002: '已确认',
	1003: '表单已完成',
	1004: '报告已发送',
	1005: '已签章',
	1006: '已归档',
	1007: '已确认离店',
	1008: '已作废'
}

export const targetCodeEnums = [
	{ label: '运营店巡店', value: 1 },
	{ label: '筹建巡店', value: 2 },
	{ label: '欠费问题巡店', value: 3 },
	{ label: '舞弊取证巡店', value: 4 },
	{ label: '解约取证巡店', value: 5 },
	{ label: '线上运营分析会', value: 6 }
]


export const reportTypeEnums = [
	{ label: '全部类型', value: null },
	{ label: '纸质版', value: 0 },
	{ label: '契约锁版', value: 1 },
	{ label: '无需签章版', value: 2 },
]
export const reportStateEnums = [
	{ label: '全部状态', value: null },
	{ label: '未签章', value: 0 },
	{ label: '签章中', value: 1 },
	{ label: '已签章', value: 3 },
]
export const reportStateEnumsToString = {
	0: '未签章',
	1: '签章中',
	3: '已签章',
	4: '无需签章'
}
export const textEnums = {
	1002: {
		1: {
			title: '[已确认]位置信息',
			content: {
				'当前位置': 'nowLocation'
			},
		},
		2: {
			title: '[已确认]位置信息',
			content: {
				'当前位置': 'nowLocation'
			},
		},
		3: {
			title: '[已确认]位置信息',
			content: {
				'当前位置': 'nowLocation'
			},
		},
		4: {
			title: '[已确认]位置信息',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		5: {
			title: '[已确认]位置信息',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		6: {
			title: '[已完成]线上运营分析会填写',
		}
	},
	1003: {
		1: {
			title: '[已完成]运营表单填写'
		},
		2:{
			title: '[已完成]表单填写'
		},
		3: {
			title: '[已完成]欠费问题店基本情况填写',
			href: {
				title: '《shopId稽核数据及历史处理结果》',
				route: '/packageB/pages/shop-inspection/audit-data'
			},
			content: {
				'问题店成因类型': 'oweMessage.causesOf',
				'预计闭环时间': 'oweMessage.shutTime',
				'成因描述': 'oweMessage.oweMessage',
				'处理政策': 'oweMessage.solution',
			}
		},
		4: {
			title: '[已完成]舞弊问题店情况判定',
			href: {
				title: '《稽核数据及历史处理结果》',
				route: '/packageB/pages/shop-inspection/audit-data',
			},
			content: {
				'是否存在舞弊现象': 'fraudDetermine.isFraud.["否", "是"]',
				'舞弊店类型': 'fraudDetermine.fraudType.[null, "房量舞弊","账房舞弊"]',
				'实际房量': 'fraudDetermine.actualNum',
				'情况说明': 'fraudDetermine.showHow',
			}
		},
		5: {
			title: '[已完成]解约情况记录',
			content: {
				'解约原因': 'unwindingCase.cause',
				'解约类型': 'unwindingCase.relieve.[null, "单方解约", "双方解约", "公证解约"]',
				'解约日期': 'unwindingCase.relieveTime',
				'解约方案': 'unwindingCase.relieveScheme',
			}
		},
		6: {
			title: '[已发送]门店报告已发送',
		}
	},
	1004: {
		1: {
			title: '[已发送]《shopId门店报告》向业主发送'
		},
		4: {
			title: '[已完成]舞弊问题店取证节点',
		},
		5: {
			title: '[已完成]解约取证资料节点',
		},
		2: {
			title: '[已发送]《shopId门店报告》向业主发送'
		},
		6: {
			title: '[已签章]表单已签章'
		}
	},
	1005: {
		4: {
			title: '[已完成]舞弊问题店制定处理方案节点',
			content: {
				'处理方案': 'fraudMessage.message',
				'罚款金额': 'fraudMessage.payment',
				'预计闭环时间': 'fraudMessage.shutTime',
			}
		},
		6: {
			title: '[已归档]线上运营分析会归档',
			href: {
				title: '《shopId门店报告》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report'
			}
		}
	},
	1006:{
		1:{
			title: '[已归档]运营表单归档',
			tip:'业主已签章完毕，电子档案已存至系统',
			href: {
				title: '《shopId门店报告》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report'
			}
		},
		2:{
			title: '[已归档]表单归档',
			tip:'业主已签章完毕，电子档案已存至系统',
			href: {
				title: '《shopId门店报告》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report'
			}
		}
	},
	1007: {
		1: {
			title: '[已确认]离店确认',
			tip: '您已结束当前巡店行程，祝您工作顺利',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		2: {
			title: '[已确认]离店确认',
			tip: '您已结束当前巡店行程，祝您工作顺利',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		3: {
			title: '[已确认]离店确认',
			tip: '您已结束当前巡店行程，祝您工作顺利',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		4: {
			title: '[已确认]离店确认',
			tip: '您已结束当前巡店行程，祝您工作顺利',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		5: {
			title: '[已确认]离店确认',
			tip: '您已结束当前巡店行程，祝您工作顺利',
			content: {
				'当前位置': 'nowLocation'
			}
		},
		6: {
			title: '[已确认]巡店结束',
		}
	},
	100201: {
		6: {
			title: '[已完成]线上运营分析会填写',
		},
	},
	100301: {
		1: {
			title: '[已完成]运营表单填写',
		},
		2: {
			title: '[已完成]表单填写'
		},
	},
	100401: {
		1: {
			title: '[已完成]表单签章',
			href: {
				title: '《shopId巡店表单》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report',
			}
		},
		2: {
			title: '[已完成]表单签章',
			href: {
				title: '《shopId巡店表单》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report'
			}
		},
		3: {
			title: '[已完成]欠费问题店表单填写（无需签章）',
			href: {
				title: '《欠费问题店巡店表单》',
				route: '/packageB/pages/shop-inspection-report/shop-inspection-report'
			}
		},
		6: {
			title: '[已签章]表单已签章',
		}
	}
}

export const shopStateEnumsToString = {
	3: '未开始',
	4: '巡店中',
	5: '已完成',
	6: '已作废',
	7: '不执行',
	8: '已超时'
}

export const shopStateEnums = [
	{ label: '全部状态', value: null },
	{ label: '未开始', value: 3 },
	{ label: '巡店中', value: 4 },
	{ label: '已完成', value: 5 },
	{ label: '已作废', value: 6 },
	{ label: '不执行', value: 7 }
]

export const tripTargetEnums = [
	{
		value: null,
		label: '全部目的'
	},
	{
		value: 1,
		label: '运营店巡店'
	},
	{
		value: 2,
		label: '筹建巡店'
	}, {
		value: 3,
		label: '欠费问题店巡店'
	}, {
		value: 4,
		label: '舞弊取证店巡店'
	}, {
		value: 5,
		label: '解约取证巡店'
	}, {
		value: 6,
		label: '线上运营分析会'
	}
]

export const tripStateEnums = [
	{
		value: null,
		label: '全部状态'
	},
	{
		value: 4,
		label: '巡店中'
	},
	{
		value: 5,
		label: '已完成'
	}, {
		value: 6,
		label: '已作废'
	}
]

export const tripBelongEnums = (() => {
	const data = JSON.parse(JSON.stringify(planBelongEnums))
	data[0].value = null
	return data
})()

export const auditStateEnums = [
	{
		value: 1,
		label: '待审核'
	},
	{
		value: 2,
		label: '被驳回'
	}, {
		value: 3,
		label: '已审核'
	}
]


export const column = [
	{
		name: '排名',
		prop: 'num',
		width: '76',
		slot: 'num',
	},
	{
		name: '地区总部',
		prop: 'orgName',
		width: '120',
	},
	{
		name: '负责人',
		prop: 'chargeMan',
		width: '90',
	},
	{
		name: '巡店率',
		prop: 'patrolRate',
		width: '120',
		sort: true,
		sortType: 'desc',
		orderByColumn: 'patrolRate',
	},
	{
		name: '新增网评分',
		prop: 'integratedScore',
		width: '146',
		sort: true,
		sortType: '',
		orderByColumn: 'integratedScore',
	},
	{
		name: '差评率',
		prop: 'negativeCommentRate',
		width: '120',
		sort: true,
		sortType: '',
		orderByColumn: 'negativeCommentRate',
	},
	{
		name: '门店数',
		prop: 'shopCount',
		width: '120',
		sort: true,
		sortType: '',
		orderByColumn: 'shopCount',
	},
	{
		name: '已巡店',
		prop: 'patrolShopCount',
		width: '120',
	},
	{
		name: '运营店巡店',
		prop: 'runningShopCount',
		width: '146',
	},
	{
		name: '筹建店',
		prop: 'preparedShopCount',
		width: '120',
	},
	{
		name: '欠费问题店巡店',
		prop: 'problemShopCount',
		width: '168',
	},
	{
		name: '舞弊取证店',
		prop: 'cheatingShopCount',
		width: '146',
	},
	{
		name: '解约取证巡店',
		prop: 'cancelledShopCount',
		width: '168',
	},
	{
		name: '线上运营分析会',
		prop: 'onlineShopCount',
		width: '190',
	}
]