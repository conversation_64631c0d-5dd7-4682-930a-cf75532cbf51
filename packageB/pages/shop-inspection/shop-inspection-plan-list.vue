<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="450" />
		<u-navbar placeholder title="巡店计划" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="picker-section fr">
			<cPickerVue :columns="planTypeEnums" keyName="label" :value="indexObj.planTypeIndex"
				@change="e => bindPickerChange(e, 'planTypeIndex')">
				<view class="picker">{{planTypeEnums[indexObj.planTypeIndex].label}}</view>
			</cPickerVue>
			<cPickerVue :columns="planStateEnums" keyName="label" :value="indexObj.planStateIndex"
				@change="e => bindPickerChange(e, 'planStateIndex')">
				<view class="picker">{{planStateEnums[indexObj.planStateIndex].label}}</view>
			</cPickerVue>
			<cPickerVue :columns="planBelongEnums" keyName="label" :value="indexObj.planBelongIndex"
				@change="e => bindPickerChange(e, 'planBelongIndex')">
				<view class="picker">{{planBelongEnums[indexObj.planBelongIndex].label}}</view>
			</cPickerVue>
		</view>
		<view class="custom-search">
			<up-search v-model="keyWord" show-action placeholder="请输入计划编号/巡店人姓名" @custom="search" action-text="查询" />
		</view>
		<view class="count">
			计划数：{{ pagination.total }}
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view">
				<view class="plan-block" v-if="dataList && dataList.length > 0" v-for="(item,index) in dataList" :key="index"
					@tap="toDetail(item)">
					<view class="info fr" :style="{background:planStateColor[item.status]?.infoBackground}">
						<view class="no">编号：{{ item.orderCode }}</view>
						<view class="state"
							:style="{'color': planStateColor[item.status]?.color,background:planStateColor[item.status]?.background}">
							状态：{{planStateToStringEnums[item.status]}}</view>
					</view>
					<view class="desc fr">
						<!-- <view class="icon">
							<image class="img"
								src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-08/1736320168474.png"
								mode="aspectFill" />
							<view class="spot" v-if="item.ifRead !==1"></view>
						</view> -->
						<view class="fl" style="flex: 1;">
							<template v-for="(r, i) in listFields()" :key="i">
								<view class="row fr" :class="{'row-bold':r.value==='patrolManName'}"
									v-if="r.value !== 'areaName' || item.tripType == 2">
									<view class="label">{{ r.label }}</view><text style="color: #999999;">：</text>
									<view class="value">{{ r.value ? item[r.value] : r.render?.(item)}}</view>
								</view>
							</template>
						</view>
					</view>
				</view>
				<view v-else>
					<emptyVue hasBackground />
				</view>
				<view class="fr empty" v-if="!pagination.loading && pagination.total">
					<text class="empty-text">到底了~</text>
				</view>
			</scroll-view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, onMounted, ref, reactive } from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { planStateEnums, planStateToStringEnums, planTypeEnums, planBelongEnums } from './enums';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import emptyVue from '@/components/empty/empty.vue';
	import { listPatrolOrder, orderRead } from './api'
	type R = Record<string, any>
	const planStateColor = {
		0: {
			color: '#1C7AC7',
			background: 'linear-gradient(203.5deg, #1c7ac70d 0%, #1c7ac733 100%)',
			infoBackground: 'linear-gradient(235.2deg, #1c7ac700 0%, #1c7ac733 100%);'
		},
		1: {
			color: '#6837DB',
			background: 'linear-gradient(203.5deg, #3e1cc70d 0%, #6f1cc733 100%)',
			infoBackground: 'linear-gradient(210.9deg, #3e1cc700 0%, #6f1cc733 100%);'
		},
		2: {
			color: '#C35943',
			background: 'linear-gradient(203.5deg, #ffb2a10d 0%, #f75e3b33 100%)',
			infoBackground: 'linear-gradient(203.5deg, #ffb2a100 0%, #f75e3b33 100%)'
		},
		3: {
			color: '#56CC93',
			background: 'linear-gradient(203.5deg, #04bd380d 0%, #04bd3833 100%)',
			infoBackground: 'linear-gradient(217.4deg, #04bd3800 0%, #04bd3833 100%);'
		},
		6: {
			color: '#1f242866',
			background: 'linear-gradient(203.5deg, #1f24280d 0%, #1f242833 100%)',
			infoBackground: 'linear-gradient(203.5deg, #1c7ac700 0%, #1f242833 100%)'
		},
	}
	const listFields = () => [
		{ label: '巡店人', value: 'patrolManName' },
		{

			label: '类型', render: (rowData : R) => {
				return planTypeEnums.find(item => item.value == rowData.tripType)?.label
			}

		},
		{
			label: '归属', render: (rowData : R) => {
				return planBelongEnums.find(item => item.value == rowData.belong)?.label
			}
		},
		{ label: '门店总数', value: 'shopCount' },
		{ label: '完成巡店', value: 'finishCount' },
		{ label: '城区', value: 'areaName' },
		{
			label: '周期', render: (rowData : R) => {
				return rowData.beginDate?.replace('-', '.') + "-" + rowData.endDate?.replace('-', '.')
			}
		}
	]
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()
	const keyWord = ref<string>()
	const indexObj = reactive({
		planTypeIndex: 0,
		planStateIndex: 0,
		planBelongIndex: 0
	}), height = ref<string>(),
		pagination = reactive({
			total: 0,
			pageNum: 1,
			pageSize: 10,
			loading: true
		})
	const dataList = ref([])
	/**
	 * @description : 获取列表
	 */
	const getListPatrolOrder = async () : Promise<void> => {
		const { pageNum, pageSize } = pagination
		const belong = planBelongEnums[indexObj.planBelongIndex].value
		const status = planStateEnums[indexObj.planStateIndex].value
		const tripType = planTypeEnums[indexObj.planTypeIndex].value
		const params : any = {
			keywords: keyWord.value,
			pageNum,
			pageSize,
			belong,
			status,
			tripType
		}
		if (status != null) {
			params.status = status
		}
		const res = await listPatrolOrder(params)
		const { records, total } = res.data
		pagination.total = total
		if (records?.length < pagination.pageSize) {
			pagination.loading = false
		}
		dataList.value = [...dataList.value, ...(records || [])]
	}

	onShow(() => {
		search()
	})

	onMounted(async () => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.scroll-view`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})

	function bindPickerChange(e : any, field : string) {
		indexObj[field] = e.indexs[0]
		search()
	}

	const search = async () : Promise<void> => {
		pagination.loading = true
		pagination.total = 0
		pagination.pageNum = 1
		dataList.value = []
		await getListPatrolOrder()
	}
	const scrolltolower = async () : Promise<void> => {
		if (!pagination.loading) return
		pagination.pageNum += 1
		await getListPatrolOrder()
	}

	const toDetail = (item : any) => {
		route_to_view('/packageB/pages/shop-inspection/shop-inspection-application?id=' + item.orderId)
		// if (item.ifRead == 1) return
		// try {
		// 	orderRead({
		// 		orderId: item.orderId,
		// 		ifRead: 1
		// 	})
		// } catch (e) {
		// 	console.error('巡店计划已读', e)
		// }
	}
</script>

<style scoped lang="scss">
	.picker-section {
		width: 100%;
		padding: 20rpx 10rpx 10rpx;

		c-picker-vue {
			width: 100%;
		}

		:deep() {
			.c-picker {
				margin: 0 10rpx;
			}
		}
	}

	.custom-search {
		:deep() {
			.u-search__content {
				background-color: #ffffffe6 !important;
			}

			.u-search__content__input {
				background-color: transparent !important;
			}
		}
	}

	.count {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 32rpx;
		padding: 0 20rpx;
	}

	.list {
		overflow: hidden;
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0;
		// backface-visibility: hidden;
		// transform: translate3d(0, 0, 0);
		// -webkit-backface-visibility: hidden;
		// -webkit-transform: translate3d(0, 0, 0);

		.scroll-view {

			.plan-block {
				box-sizing: border-box;
				justify-content: space-between;
				border-radius: 20rpx;
				background: #ffffff;
				margin-bottom: 20rpx;
				padding-bottom: 32rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info {
					margin-bottom: 26rpx;
					justify-content: space-between;
					border-radius: 20rpx 20rpx 0 0;
					padding: 20rpx 40rpx;

					.no {
						color: #404447;
						font-size: 28rpx;
						font-weight: bold;
						line-height: 28rpx;
					}

					.state {
						width: 198rpx;
						color: #56cc93;
						font-size: 22rpx;
						font-weight: bold;
						line-height: 22rpx;
						padding: 8rpx 0;
						text-align: center;
						border-radius: 20rpx 0 0 0;
						margin-right: -40rpx;
					}
				}

				.desc {
					padding: 0 40rpx;

					.icon {
						position: relative;

						.img {
							width: 90rpx;
							height: 90rpx;
							flex-shrink: 0;
							margin-right: 40rpx;
						}

						.spot {
							width: 16rpx;
							height: 16rpx;
							background: #c35943;
							border-radius: 50%;
							position: absolute;
							top: 6rpx;
							right: 46rpx;
						}
					}
				}

				.row {
					margin-bottom: 26rpx;
					align-items: center;

					&:last-child {
						margin-bottom: 0;
					}

					.label {
						color: #999999;
						font-size: 26rpx;
						line-height: 26rpx;
						height: 26rpx;
						flex-shrink: 0;
						width: 110rpx;
						text-align: justify;

						&:after {
							content: " ";
							display: inline-block;
							width: 100%;
						}
					}

					.value {
						color: #404447;
						font-size: 26rpx;
						line-height: 26rpx;
						margin-left: 40rpx;
					}
				}
			}
		}
	}

	.empty {
		justify-content: center;
		font-size: 22rpx;

		&-text {
			color: #999;
		}
	}
</style>