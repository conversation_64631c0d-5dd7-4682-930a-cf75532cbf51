<template>
	<view class="fl w-container" style="height: 100%;">
		<commonBgVue height="278" />
		<u-navbar placeholder :title="pageParams?.shopId + '巡店行程'" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" @leftClick="customBack">
      <template #left>
        <template v-if="pages.length > 1">
          <u-icon name="arrow-left" size="20" color="#FFF"></u-icon>
        </template>
        <template v-else>
          <u-icon name="home" size="22" color="#FFF"></u-icon>
        </template>
      </template>
		</u-navbar>
		<view class="content">
			<view class="trip-block">
				<tripDescVue :data="tripInfo" :time="2">
					<template>
						<view class="btn-block fr">
							<view class="btn fr" v-for="(item,index) in menu" :key="index" :style="{background:item.background}"
								@tap="onTabClick(item, index)">
								<image class="img" :src="item.icon" mode=""></image>
								<text class="text">{{item.text}}</text>
							</view>
						</view>
					</template>
				</tripDescVue>
			</view>
			<view class="trip-progress-block">
				<tripProgressVue :pageParams="pageParams" ref="tripProgressRef" />
			</view>
		</view>
		<confirmModalVue ref="confirmModalRef" @confirm="onConfirm">
			<template v-slot="{ show }">
				<view class="fl slot-content">
					<text class="fr">是否要作废该门店行程计划？</text>
					<text class="fr tip">（门店行程计划作废后不可再次修改）</text>
					<cTextareaVue v-if="show" v-model="nonExecutionReason" placeholder="请填写作废原因" count maxlength="100"
						background="linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%)" />
				</view>
			</template>
		</confirmModalVue>
	</view>
  <cWaterMark></cWaterMark>
</template>
<script setup lang="ts">
	import { onLoad, onShow, onUnload } from '@dcloudio/uni-app'
	import { ref, computed, getCurrentInstance, nextTick } from 'vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import tripDescVue from './components/trip-desc/trip-desc.vue';
	import tripProgressVue from './components/trip-progress/trip-progress.vue';
	import confirmModalVue from '../../components/confirm-modal/confirm-modal.vue'
	import cTextareaVue from '../../components/c-textarea/c-textarea.vue';
	import { menu } from './enums';
	import { useCommon } from '@/hooks/useGlobalData'
	import { showToast } from '@/utils/util'
	import { updateShopStatus } from './api'
	import { newPageInstance } from '../../../types';
	const padding_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 20 + 'rpx'
	const pageParams = ref(null)
	const tripProgressRef = ref<InstanceType<typeof tripProgressVue>>(null)
	const confirmModalRef = ref<InstanceType<typeof confirmModalVue>>(null)
	const { route_to_view, globalData, setRouteParams, customBack } = useCommon(getCurrentInstance())
  const pages = getCurrentPages()
	const nonExecutionReason = ref('')
	const tripInfo = computed(() => {
		return tripProgressRef.value?.tripInfo
	})
	/**
	 * @description: 校验是否能操作
	 */
	const validate = () => {
		const userId = globalData.value.user?.userId
		if (userId && userId != tripInfo.value.patrolUserId) {
			showToast('仅限巡店人本人可操作')
			return
		}
		if (tripInfo.value.belong != 1) {
			showToast('无权限进行该操作')
			return
		}
		return true
	}
	/**
	 * @description: 行程点击
	 */
	const onTabClick = (data : any, index : number) => {
		if (!validate()) return
		switch (index) {
			case 0:
				if ((tripInfo.value.tripStatus == 1001 && tripInfo.value.targetcode != 6) || tripInfo.value.tripStatus == 1007 || tripInfo.value.tripStatus == 1008) {
					showToast('该行程进度暂无法写跟进')
					return
				}
				route_to_view(data.route + '?tripId=' + tripInfo.value.tripId)
				break;
			case 1:
				if (((tripInfo.value.tripStatus == 1001 || tripInfo.value.tripStatus == 1002) && (tripInfo.value.targetCode == 1 || tripInfo.value.targetCode == 2)) || (tripInfo.value.targetCode == 6 && tripInfo.value.tripStatus == 1001) || tripInfo.value.targetCode == 4 || tripInfo.value.targetCode == 5 || (tripInfo.value.targetCode == 3 && (tripInfo.value.tripStatus == 1001 || tripInfo.value.tripStatus == 1002 || tripInfo.value.tripStatus == 1003))) {
					showToast('当前行程进度无法查看报告')
					return
				}
				route_to_view(data.route + '?tripCode=' + tripInfo.value.tripCode)
				break;
			case 2:
				route_to_view(data.route + '?id=' + tripInfo.value.shopId)
				break;
			case 3:
				if (tripInfo.value.tripStatus == 1008) {
					showToast('该行程已作废')
					return
				}
				confirmModalRef.value.open(true)
				break;
		}
	}
	/**
	 * @description: 确认作废
	 */
	const onConfirm = async () => {
		if (!nonExecutionReason.value) {
			showToast('请填写作废原因')
			return
		}
		try {
			await updateShopStatus({
				orderId: tripInfo.value.orderId,
				shopId: tripInfo.value.shopId,
				status: 6,
				nonExecutionReason: nonExecutionReason.value
			})
			showToast({
				title: '操作成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						confirmModalRef.value.open(false)
						tripProgressRef.value.initial()
					}, 1000)
				}
			})
		} catch (e) {
			console.log("🚀 ~ onConfirm ~ e:", e)
		}
	}
	onLoad((opts) => {
		pageParams.value = { ...opts }
	})

	onShow(async () => {
		const pages = getCurrentPages()
		const currentPage : newPageInstance = pages[pages.length - 1]
		if (currentPage.refresh) {
			await nextTick()
			delete currentPage.refresh
			tripProgressRef.value.initial()
		}
	})

	onUnload(() => {
		setRouteParams('packageB/pages/shop-inspection/shop-inspection-trip-list')
	})
</script>

<style scoped lang="scss">
	.content {
		margin: 0 20rpx;
		flex: 1;
		overflow-y: auto;
		padding-bottom: v-bind(padding_bottom);

		.trip-block {
			background: #ffffff;
			border-radius: 20rpx;

			.btn-block {
				justify-content: space-between;
				margin-top: 82rpx;
				position: relative;
				margin-bottom: 20rpx;

				&::before {
					position: absolute;
					content: '';
					height: 2rpx;
					left: -20rpx;
					right: -20rpx;
					top: -40rpx;
					background: #f2f2f2;
				}
			}

			.btn {
				width: 144rpx;
				border-radius: 378rpx;
				padding: 16rpx 0;
				text-align: center;
				justify-content: center;

				.img {
					width: 24rpx;
					height: 24rpx;
					flex-shrink: 0;
					margin-right: 6rpx;
				}

				.text {
					color: #ffffff;
					font-size: 24rpx;
					font-weight: 500;
					line-height: 24rpx;
				}
			}
		}

		.trip-progress-block {
			margin-top: 20rpx;
		}
	}

	:deep() {
		.slot-content {
			width: 100%;

			.fr {
				justify-content: center;
				margin-bottom: 20rpx;
				font-size: 30rpx;
				line-height: 30rpx;
				color: #999;

				&.tip {
					margin-bottom: 60rpx;
				}
			}

			.textarea {
				font-size: 24rpx;
			}
		}
	}
</style>