<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="486" />
		<u-navbar placeholder title="巡店报告" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="search">
			<view class="picker-section fr">
				<cPickerVue :columns="reportTypeEnums" keyName="label" :value="indexObj.reportTypeIndex"
					@change="e => bindPickerChange(e, 'reportTypeIndex')">
					<view class="picker">{{reportTypeEnums[indexObj.reportTypeIndex].label}}</view>
				</cPickerVue>
				<cPickerVue :columns="reportStateEnums" keyName="label" :value="indexObj.reportStateIndex"
					@change="e => bindPickerChange(e, 'reportStateIndex')"
					:class-name="{'point-event': indexObj.reportTypeIndex == 3}">
					<view class="picker">
						{{indexObj.reportTypeIndex == 3 ? '无需签章' : reportStateEnums[indexObj.reportStateIndex].label}}
					</view>
				</cPickerVue>
				<picker mode="date" class="c-picker__wrap" @change="onChangeDate" fields="month">
					<view class="c-picker">
						<view class="value">
							{{ queryDate || '全部月份' }}
						</view>
						<up-icon name="arrow-down" color="#999999" size="22rpx" />
					</view>
				</picker>

			</view>
			<view class="custom-search">
				<up-search v-model="keyword" show-action placeholder="请输入行程编号/巡店人姓名/门店id" @custom="search" action-text="查询" />
			</view>
		</view>
		<view class="count">
			报告数： {{ pagination.total }}
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view">
				<view class="shop-info" v-for="(item,index) in dataList" :key="index"
					@tap="route_to_view('/packageB/pages/shop-inspection-report/shop-inspection-report?tripCode=' + item.tripCode)">
					<view class="info fr">
						<view class="fr no" @click.stop="setClipboardData(item.tripCode)">报告编号：{{ item.tripCode }}
							<image
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-05/1736057482112.png"
								mode="aspectFill" lazy-load class="copy-icon"></image>
						</view>
						<view class="state"
							:style="{'color': reportStateColor[item.pdfStatus]?.color,background:reportStateColor[item.pdfStatus]?.background}">
							状态：{{reportStateEnumsToString[item.pdfStatus] || '无状态'}}</view>
					</view>
					<view class="desc fr">
						<image class="icon"
							src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/1733301372282.png"
							mode="aspectFill" />
						<view class="right">
							<view class="shop-name fr">
								<view class="">门店ID：{{ item.shopId }}</view>
								<view class="operation fr">
									<up-icon name="arrow-right" color="#999999" size="22rpx" />
								</view>
							</view>
							<view class="row fr">
								<view class="label">巡店人：</view>
								<view class="value">{{item.patrolManName}}</view>
							</view>
							<view class="row fr">
								<view class="label">生成时间：</view>
								<view class="value">{{item.createTime}}</view>
							</view>
							<view class="row fr">
								<view class="label">行程类型：</view>
								<view class="value">{{planTypeEnums.find((r: any) => r.value == item.tripType)?.label}}</view>
							</view>
							<view class="row fr" v-if="item.tripType != 1">
								<view class="label">命中策略：</view>
								<view class="value">{{item.strategy}}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="fr empty" v-if="!pagination.loading && pagination.total">
					<text>到底了~</text>
				</view>
				<emptyVue hasBackground v-if="!dataList.length" />
			</scroll-view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, onMounted, ref, reactive, nextTick } from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import { reportTypeEnums, reportStateEnums, reportStateEnumsToString, planTypeEnums } from './enums'
	import { listReport } from './api'
	import emptyVue from '@/components/empty/empty.vue';
import { newPageInstance } from '../../../types';
	const reportStateColor = {
		0: { color: '#C35943', background: 'linear-gradient(203.5deg, #ffb2a10d 0%, #f75e3b33 100%)' },
		1: { color: '#1C7AC7', background: 'linear-gradient(203.5deg, #1c7ac70d 0%, #1c7ac733 100%)' },
		3: { color: '#04BD38', background: 'linear-gradient(203.5deg, #04bd380d 0%, #04bd3833 100%)' },
	}

	const { route_to_view, setClipboardData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()
	const keyword = ref<string>()
	const queryDate = ref('')
	const indexObj = reactive({
		reportTypeIndex: 0,
		reportStateIndex: 0
	}), height = ref<string>(),
		pagination = reactive({
			total: 0,
			pageNum: 1,
			pageSize: 10,
			loading: true
		})
	const dataList = ref([])
	/**
	 * @description: 获取报告列表
	 */
	const getListReport = async () : Promise<void> => {
		const { pageNum, pageSize } = pagination
		const pdfStatus = reportStateEnums[indexObj.reportStateIndex].value
		const isOnlineSign = reportTypeEnums[indexObj.reportTypeIndex].value
		const params : any = {
			pageNum,
			pageSize,
		}
		if (pdfStatus != null) {
			params.pdfStatus = pdfStatus
		}
		if (isOnlineSign != null) {
			params.isOnlineSign = isOnlineSign
		}
		if (keyword.value) {
			params.keyword = keyword.value
		}
		if (queryDate.value) {
			params.queryDate = queryDate.value
		}
		const res = await listReport(params)
		const { records, total } = res.data
		pagination.total = total
		if (records?.length < pagination.pageSize) {
			pagination.loading = false
		}
		dataList.value = [...dataList.value, ...(records || [])]
	}
	onMounted(async () => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.scroll-view`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
		await getListReport()
	})

	function bindPickerChange(e : any, field : string) {
		indexObj[field] = e.indexs[0]
		search()
	}
	const onChangeDate = (e) => {
		queryDate.value = e.detail.value
		search()
	}
	const search = async () => {
		pagination.loading = true
		pagination.total = 0
		pagination.pageNum = 1
		dataList.value = []
		await getListReport()
	}
	const scrolltolower = async () => {
		if (!pagination.loading) return
		pagination.pageNum += 1
		await getListReport()
	}

	onShow(async () => {
		const pages = getCurrentPages()
		const currentPage : newPageInstance = pages[pages.length - 1]
		if (currentPage.refresh) {
			await nextTick()
			delete currentPage.refresh
			search()
		}
	})
</script>

<style scoped lang="scss">
	.search {
		padding-top: 20rpx;

		.picker-section {
			width: 100%;
			justify-content: space-between;
			padding: 0 10rpx;

			c-picker-vue {
				flex: 1;
				margin: 0 10rpx;
			}

			:deep() {
				.c-picker {
					background: #F5F5F5;
				}
			}
		}

		.custom-search {
			padding-bottom: 0;

			:deep() {
				.u-search__content {
					background-color: #ffffffe6 !important;
				}

				.u-search__content__input {
					background-color: transparent !important;
				}
			}
		}
	}

	.c-picker {
		flex-shrink: 0;
		display: flex;
		padding: 20rpx 40rpx;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 40rpx;
		background: #ffffffe6;
		border-radius: 200rpx;

		&__wrap {
			flex-shrink: 0;
			margin: 0 10rpx;
			flex: 1;
		}

		.value {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 24rpx;
		}
	}

	.count {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		line-height: 32rpx;
		padding: 40rpx 20rpx;
	}

	.list {
		overflow: hidden;
		border-radius: 20rpx;
		margin: 0 20rpx;
		// backface-visibility: hidden;
		// transform: translate3d(0, 0, 0);
		// -webkit-backface-visibility: hidden;
		// -webkit-transform: translate3d(0, 0, 0);

		.scroll-view {
			.shop-info {
				box-sizing: border-box;
				padding: 20rpx 40rpx;
				justify-content: space-between;
				border-radius: 20rpx;
				background: #ffffff;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info {
					position: relative;
					margin-bottom: 44rpx;
					justify-content: space-between;

					&::before {
						position: absolute;
						content: '';
						height: 2rpx;
						left: -20rpx;
						right: -20rpx;
						top: 60rpx;
						background: #f2f2f2;
					}

					.no {
						color: #999999;
						font-size: 28rpx;
					}

					.state {
						color: #56cc93;
						font-size: 22rpx;
						font-weight: bold;
						line-height: 22rpx;
						padding: 8rpx 46rpx 8rpx 20rpx;
						border-radius: 20rpx 0 0 0;
						margin-right: -40rpx;
					}
				}
			}

			.desc {
				align-items: flex-start;
				justify-content: space-between;

				&>view:first-child {
					align-items: flex-start;
				}

				.icon {
					width: 90rpx;
					height: 90rpx;
					flex-shrink: 0;
					margin-right: 40rpx;
				}

				.right {
					flex: 1;

					.shop-name {
						color: #1f2428;
						font-size: 28rpx;
						font-weight: bold;
						line-height: 28rpx;
						margin-bottom: 22rpx;
						align-items: flex-start;
						justify-content: space-between;

						.operation {
							flex-shrink: 0;

							text {
								color: #999999;
								font-size: 24rpx;
								line-height: 24rpx;
								margin-right: 16rpx;
							}
						}
					}

					.row {
						margin-bottom: 10rpx;
						align-items: baseline;

						&:last-child {
							margin-bottom: 0;
						}

						.label {
							color: #1f2428;
							font-size: 28rpx;
							line-height: 38rpx;
							flex-shrink: 0;
						}

						.value {
							color: #1f2428;
							font-size: 28rpx;
							line-height: 38rpx;
							word-break: break-all;
						}
					}
				}
			}
		}
	}

	.empty {
		justify-content: center;
		font-size: 22rpx;
		color: #999;
	}

	.copy-icon {
		width: 22.78rpx;
		height: 22.9rpx;
		margin-left: 12rpx;
	}
</style>