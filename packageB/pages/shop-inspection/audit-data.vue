<template>
	<view class="container" v-if="Object.keys(pageData).length>0">
		<view class="section">
			<view class="title">基本信息</view>
			<view class="content">
				<view class="row fr">
					<view class="label">财务号：</view>
					<view class="value">{{ pageData?.financeCode}}</view>
				</view>
				<view class="row fr">
					<view class="label">门店ID：</view>
					<view class="value">{{ pageData?.shopId}}</view>
				</view>
				<view class="row fr">
					<view class="label">门店名称：</view>
					<view class="value">{{ pageData?.shopName}}</view>
				</view>
				<template v-if="isOpen">
					<view class="row fr">
						<view class="label">酒店品牌：</view>
						<view class="value">{{ pageData?.brandName}}</view>
					</view>
					<view class="row fr">
						<view class="label">大区：</view>
						<view class="value">{{ pageData?.regionalName}}</view>
					</view>
					<view class="row fr">
						<view class="label">城区：</view>
						<view class="value">{{ pageData?.areaName}}</view>
					</view>
					<view class="row fr">
						<view class="label">运营负责人：</view>
						<view class="value">{{ pageData?.operateName}}</view>
					</view>
					<view class="row fr">
						<view class="label">房间数(房量)：</view>
						<view class="value">{{ pageData?.roomCount}}</view>
					</view>
					<view class="row fr">
						<view class="label">开业时间：</view>
						<view class="value">{{ pageData?.openTime ? dayjs(pageData.openTime).format('YYYY-MM-DD') : ''}}</view>
					</view>
					<view class="row fr">
						<view class="label">开系统时间：</view>
						<view class="value">{{ pageData?.systemTime ? dayjs(pageData.systemTime).format('YYYY-MM-DD') : ''}}</view>
					</view>
					<view class="row fr">
						<view class="label">门店类型(状态)：</view>
						<view class="value">{{ pageData?.systemStatus }}</view>
					</view>
				</template>
				<view class="show fr">
					<view class="fr" @click.stop="isOpen = !isOpen">
						<view class="text">{{isOpen ? '收起' : '展开'}}</view>
						<up-icon :name="isOpen? 'arrow-up' :'arrow-down'" color="#C35943" size="22rpx" />
					</view>
				</view>
			</view>
		</view>
		<view class="section">
			<view class="title">稽核数据</view>
			<view class="fr" style="flex-wrap: wrap;">
				<view class="fl score-block" v-for="(item, index) in data" :key="item.key"
					:style="{backgroundImage:constant[index]?.background}">
					<view class="fr text" :style="{backgroundImage:constant[index]?.titleBackground}">
						<view class="name">{{ item.name }}</view>
					</view>
					<view v-if="index==3" class="score fl" style="justify-content: center;">
						<view class="text">{{ pageData?.[item.key] || '-'}}</view>
					</view>
					<view class="score" v-else>
						<text class="num">{{ pageData?.[item.key] || 0}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="section" v-for="(item, index) in pageData?.auditList" :key="index">
			<view class="title" v-if="index == 0">历史处理记录</view>
			<view class="content">
				<template v-for="field in rowFields" :key="field.key">
					<view class="row fr" v-if="item[field.key] || field.targetCode == pageParams.targetCode">
						<view class="label">{{ field.name }}：</view>
						<view class="value">{{ item[field.key]}}</view>
					</view>
				</template>
			</view>
		</view>
	</view>
	<view style="height: 40%;" v-else>
		<empty-vue />
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ref } from 'vue';
	import { getRecords as getRecordsApi } from './api'
	import emptyVue from '@/components/empty/empty.vue';
	import dayjs from 'dayjs'
	type pageOptionsType = { shopId : string | number, targetCode : string }
	const isOpen = ref(false)
	const pageData = ref<any>({})
	const pageParams = ref<Partial<pageOptionsType>>({})
	const enums = {
		3: [
			{ name: '实收 (BR)', key: 'netReceipts' },
			{ name: '减免 (CB)', key: 'reduction' },
			{ name: '欠款 (CL)', key: 'arrears' },
			{ name: '累计回收率 (AL)', key: 'netReceipts' },
		],
		4: [
			{ name: '出租率', key: 'occupancyRate' },
			{ name: '房费合计', key: 'manualMoney' },
			{ name: '手工房金', key: 'roomMoney' },
			{ name: '舞弊情况', key: 'fraudRemarks' },
			{ name: '商标核定收取标准', key: 'collectStandard' },
			{ name: '商标核定应收金额', key: 'collectMoney' },
		]
	}
	const data = ref([])
	const rowFields = [
		{ name: '处理时间', key: 'endTime' },
		{ name: '处理人', key: 'patrolManName' },
		{ name: '是否存在舞弊现象', key: 'isFraud', targetCode: 4 },
		{ name: '舞弊店类型', key: 'fraudType', targetCode: 4 },
		{ name: '实际房量', key: 'actualNum', targetCode: 4 },
		{ name: '情况说明', key: 'showHow', targetCode: 4 },
		{ name: '处理方案', key: 'message', targetCode: 4 },
		{ name: '罚款金额', key: 'payment', targetCode: 4 },
		{ name: '问题店成因类型', key: 'causesOf', targetCode: 3 },
		{ name: '成因描述', key: 'oweMessage', targetCode: 3 },
		{ name: '处理政策', key: 'solution', targetCode: 3 },
		{ name: '预计闭环时间', key: 'shutTime' },
	]

	const constant = [
		{
			titleBackground: ' linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%)',
			background: 'linear-gradient(203.5deg,rgba(255, 178, 161,  0.05) 0%,rgba(247, 94, 59,  0.05) 100%)',
		},
		{
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%)',
		},
		{
			titleBackground: 'linear-gradient(25.9deg, #ff8c1a 0%, #ffcf9e 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 140, 26, 0.05) 0%, rgba(255, 140, 26, 0.05) 100%)',
		},
		{
			titleBackground: 'linear-gradient(17.3deg, #6046f2 0%, #c7a2fc 100%)',
			background: 'linear-gradient(203.5deg, rgba(96, 70, 242, 0.05) 0%, rgba(199, 162, 252, 0.05) 100%)',
		},
		{
			titleBackground: 'linear-gradient(197.7deg, #91baff 0%, #1474fb 100%)',
			background: 'linear-gradient(221.3deg, rgba(145, 186, 255, 0.05) 0%, rgba(145, 186, 255, 0.05) 100%)',
		},
		{
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(40.7deg, rgba(97, 197, 255, 0.05) 0%, rgba(189, 243, 255, 0.05)',
		}
	]
	const padding_bottom = (uni.getSystemInfoSync().safeAreaInsets.bottom || 10) + 'px'

	/**
	 * @description: 获取数据
	 */
	const getRecords = async (opts : pageOptionsType) => {
		try {
			const { data } = await getRecordsApi(opts)
			if (data) {
				pageData.value = data
			} else {

			}
		} catch (e) {
			console.log("🚀 ~ getRecords ~ e:", e)
		}
	}

	onLoad((opts : pageOptionsType) => {
		pageParams.value = opts
		getRecords(opts)
		data.value = enums[opts.targetCode]
	})
</script>

<style scoped lang="scss">
	.container {
		padding: 20rpx;
		padding-bottom: v-bind(padding_bottom);
	}

	.section {
		padding: 40rpx;
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.title {
			color: #1f2428;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 32rpx;
			margin-bottom: 40rpx;
		}

		.row {
			justify-content: space-between;
			margin-bottom: 12rpx;
			padding: 3rpx 0;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				flex-shrink: 0;
				align-self: baseline;
				opacity: 0.7;
				color: #1f2428;
				font-size: 28rpx;
			}

			.value {
				color: #1f2428;
				font-size: 28rpx;
				font-weight: bold;
				text-align: right;
			}
		}
	}


	.show {
		justify-content: center;
		margin-top: 20rpx;

		&>view {
			padding: 0 40rpx;
		}

		.text {
			color: #C35943;
			font-size: 24rpx;
			line-height: 44rpx;
			margin-right: 8rpx;
		}
	}

	.score-block {
		min-width: 196rpx;
		height: 184rpx;
		border-radius: 20rpx;
		align-items: center;
		margin-right: 20rpx;
		margin-bottom: 20rpx;

		&:nth-child(3),
		&:nth-child(6) {
			margin-right: 0;
		}


		&:nth-child(4),
		&:nth-child(5),
		&:nth-child(6) {
			margin-bottom: 0;
		}

		.text {
			text-align: left;
			justify-content: center;
			border-radius: 100rpx 100rpx 100rpx 0;
			padding: 12rpx 0;
			width: 100%;

			.name {
				color: #ffffff;
				font-size: 24rpx;
				font-weight: bold;
				line-height: 24rpx;
			}

			.tip {
				width: 22rpx;
				height: 22rpx;
				flex-shrink: 0;
				margin-left: 8rpx;
			}
		}

		.score {
			flex: 1;
			display: flex;
			align-items: center;
			color: #1f2428;
			font-size: 22rpx;
			line-height: 22rpx;
			margin-bottom: 18rpx;

			.num {
				color: #1f2428;
				font-size: 42rpx;
				font-weight: 700;
				font-family: "DIN Bold";
				line-height: 42rpx;
				margin-right: 12rpx;
			}

			.text {
				color: #1f2428b3;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;
			}


			.small-num {
				color: #1f2428;
				font-size: 36rpx;
				font-weight: 700;
				font-family: "Din Bold";
				line-height: 36rpx;
				margin: 0 8rpx;
			}

			.unit {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
			}
		}
	}
</style>