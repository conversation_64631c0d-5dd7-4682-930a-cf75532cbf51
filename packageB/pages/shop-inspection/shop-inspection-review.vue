<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="402" />
		<u-navbar placeholder title="巡店审核" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="picker-section fr">
			<cPickerVue :columns="auditStateEnums" keyName="label" :value="auditStateIndex" @change="bindPickerChange">
				<view class="picker">{{auditStateEnums[auditStateIndex].label}}</view>
			</cPickerVue>
			<view class="custom-search">
				<up-search v-model="keyword" show-action placeholder="搜索计划编号/巡店人姓名" @custom="search" action-text="查询" />
			</view>
		</view>
		<view class="count">
      {{auditStateEnums[auditStateIndex].label}}：{{pagination.total}}
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view">
				<view class="report-info" v-if="dataList && dataList.length > 0" v-for="(item,index) in dataList" :key="index"
					@tap="toDetail(item)">
					<view class="info fr">
						<view class="fr" @click.stop="setClipboardData(item.orderCode)">
							<view class="no">编号：{{ item.orderCode }}</view>
							<image class="copy"
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-02/1735811703078.png"
								mode=""></image>
						</view>
						<view class="state"
							:style="{'color': planStateColor[item.status]?.color,background:planStateColor[item.status]?.background}">
							状态：{{planStateToStringEnums[item.status]}}</view>
					</view>
					<view class="desc fr">
						<view class="icon">
							<image class="img"
								src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-08/1736320466110.png"
								mode="aspectFill" />
							<view class="spot" v-if="item.ifRead !==1"></view>
						</view>
						<view class="left">
							<view class="name">巡店人：{{ item.patrolManName }}</view>
							<view class="date">周期：{{item.beginDate?.replace(/-/g, '.')}}-{{item.endDate?.replace(/-/g, '.')}}</view>
						</view>
						<view class="check fr">
							<text>去查看</text>
							<up-icon name="arrow-right" color="#999999" size="22rpx" />
						</view>
					</view>
				</view>
				<view v-else>
					<emptyVue hasBackground />
				</view>
				<view class="fr empty" v-if="!pagination.loading && pagination.total">
					<text class="empty-text">到底了~</text>
				</view>
			</scroll-view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, onMounted, reactive, ref } from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { auditStateEnums, planStateToStringEnums } from './enums';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import { listAuditOrder, orderRead } from './api';
	import emptyVue from '@/components/empty/empty.vue';
	import { showToast } from '../../../utils/util';

	const planStateColor = {
		0: { color: '#1C7AC7', background: 'linear-gradient(203.5deg, #1c7ac70d 0%, #1c7ac733 100%)' },
		1: { color: '#6837DB', background: 'linear-gradient(203.5deg, #3e1cc70d 0%, #6f1cc733 100%)' },
		2: { color: '#C35943', background: 'linear-gradient(203.5deg, #ffb2a10d 0%, #f75e3b33 100%)' },
		3: { color: '#56CC93', background: 'linear-gradient(203.5deg, #04bd380d 0%, #04bd3833 100%)' },
		6: { color: '#1f242866', background: 'linear-gradient(203.5deg, #1f24280d 0%, #1f242833 100%)' },
	}

	const { route_to_view, setClipboardData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()
	const keyword = ref<string>()
	const auditStateIndex = ref(0),
		height = ref<string>(),
		pagination = reactive({
			total: 0,
			pageNum: 1,
			pageSize: 10,
			loading: true
		})

	const dataList = ref([])
	/**
	 * @description : 获取列表
	 */
	const getListAuditOrder = async () : Promise<void> => {
		const { pageNum, pageSize } = pagination
		const params : any = {
			status: auditStateEnums[auditStateIndex.value].value,
			keyword: keyword.value,
			pageNum,
			pageSize,
		}
		const res = await listAuditOrder(params)
		const { records, total } = res.data
		pagination.total = total
		if (records?.length < pagination.pageSize) {
			pagination.loading = false
		}
		dataList.value = [...dataList.value, ...(records || [])]
	}

	onShow(() => {
		search()
	})


	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.scroll-view`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})

	function bindPickerChange(e) {
		console.log('picker发送选择改变，携带值为', e)
		auditStateIndex.value = e.indexs[0]
		search()
	}

	const search = async () : Promise<void> => {
		pagination.loading = true
		pagination.total = 0
		pagination.pageNum = 1
		dataList.value = []
		await getListAuditOrder()
	}
	const scrolltolower = async () : Promise<void> => {
		if (!pagination.loading) return
		pagination.pageNum += 1
		await getListAuditOrder()
	}
	const toDetail = (item : any) => {
		route_to_view('/packageB/pages/shop-inspection/shop-inspection-application?id=' + item.orderId)
		if (item.ifRead == 1) return
		try {
			orderRead({
				orderId: item.orderId,
				ifRead: 1
			})
		} catch (e) {
			console.error('巡店审核已读', e)
		}
	}
</script>

<style scoped lang="scss">
	.picker-section {
		width: 100%;
		padding: 20rpx;
		justify-content: space-between;

		c-picker-vue {
			width: 224rpx;
			margin-right: 20rpx;
		}

		.custom-search {
			padding: 0;
		}
	}

	.count {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 32rpx;
		padding: 20rpx;
	}

	.list {
		overflow: hidden;
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0;
		// backface-visibility: hidden;
		// transform: translate3d(0, 0, 0);
		// -webkit-backface-visibility: hidden;
		// -webkit-transform: translate3d(0, 0, 0);

		.scroll-view {
			.report-info {
				box-sizing: border-box;
				padding: 20rpx 40rpx;
				justify-content: space-between;
				border-radius: 20rpx;
				background: #ffffff;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info {
					position: relative;
					margin-bottom: 44rpx;
					justify-content: space-between;

					&::before {
						position: absolute;
						content: '';
						height: 2rpx;
						left: -20rpx;
						right: -20rpx;
						top: 60rpx;
						background: #f2f2f2;
					}

					.no {
						color: #999999;
						font-size: 28rpx;
					}

					.copy {
						width: 32rpx;
						height: 32rpx;
						flex-shrink: 0;
						margin-left: 30rpx;
					}

					.state {
						color: #56cc93;
						font-size: 22rpx;
						font-weight: bold;
						line-height: 22rpx;
						padding: 8rpx 46rpx 8rpx 20rpx;
						border-radius: 20rpx 0 0 0;
						margin-right: -40rpx;
					}
				}
			}

			.desc {
				justify-content: space-between;
				align-items: flex-start;

				.icon {
					position: relative;

					.img {
						width: 90rpx;
						height: 90rpx;
						flex-shrink: 0;
						margin-right: 40rpx;
					}

					.spot {
						width: 16rpx;
						height: 16rpx;
						background: #c35943;
						border-radius: 50%;
						position: absolute;
						top: 6rpx;
						right: 46rpx;
					}
				}

				.left {
					.name {
						color: #1f2428;
						font-size: 28rpx;
						font-weight: bold;
						line-height: 28rpx;
						margin-bottom: 32rpx;
					}

					.date {
						color: #1f2428;
						font-size: 28rpx;
						line-height: 28rpx;
					}
				}

				.check {
					flex-shrink: 0;

					text {
						color: #999999;
						font-size: 24rpx;
						line-height: 28rpx;
						margin-right: 16rpx;
					}
				}
			}
		}
	}

	.empty {
		justify-content: center;
		font-size: 22rpx;

		&-text {
			color: #999;
		}
	}
</style>