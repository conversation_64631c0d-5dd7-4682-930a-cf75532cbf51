<template>
	<view class="fl" style="height: 100%;">
		<commonBgVue height="450" />
		<u-navbar placeholder title="门店清单" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" @leftClick="customBack">
      <template #left>
        <template v-if="pages.length > 1">
          <u-icon name="arrow-left" size="20" color="#FFF"></u-icon>
        </template>
        <template v-else>
          <u-icon name="home" size="22" color="#FFF"></u-icon>
        </template>
      </template>
		</u-navbar>
		<view class="picker-section fr">
			<cPickerVue :columns="column" keyName="label" :value="index" @change="bindPickerChange">
				<view class="picker">{{column[index]?.label}}</view>
			</cPickerVue>
			<view class="custom-search">
				<up-search v-model="keywords" show-action placeholder="请输入门店ID/门店名称" @custom="search" action-text="查询" />
			</view>
		</view>
		<view class="count">
			计划巡店总数 {{ dataList.length }}
		</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower" class="scroll-view">
				<view class="shop-info" v-for="(item,index) in dataList" :key="index"
					@tap="(item.status === 7 || item.status === 8) ? null: handleClick(item)">
					<view class="info fr">
						<view class="no">{{ item.shopId }}</view>
						<view class="state"
							:style="{'color': planStateColor[item.status]?.color,background:planStateColor[item.status]?.background}">
							状态：{{shopStateEnumsToString[item.status] || '无状态'}}</view>
					</view>
					<view class="desc fr">
						<image class="icon"
							src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-04/1733301372282.png"
							mode="aspectFill" />
						<view class="right">
							<view class="shop-name fr">
								<view style="width: 360rpx;">{{ item.shopName }}</view>
								<view class="operation fr" v-if="(item.status !== 7 && item.status !== 8)">
									<text>操作</text>
									<up-icon name="arrow-right" color="#999999" size="22rpx" />
								</view>
							</view>
							<view class="row fr">
								<view class="label">状态：</view>
								<view class="value">{{item.shopType}}</view>
							</view>
							<view class="row fr">
								<view class="label">目的：</view>
								<view class="value">{{item.target}}</view>
							</view>
							<view class="row fr">
								<view class="label">类型：</view>
								<view class="value">{{item.tripType == 1 ?'手动发起':'自动生成'}}</view>
							</view>
							<view class="row fr" v-if="item.tripType == 2">
								<view class="label">命中策略：</view>
								<view class="value">{{item.strategy}}</view>
							</view>
						</view>
					</view>
				</view>
				<emptyVue v-if="!dataList.length" hasBackground />
			</scroll-view>
		</view>
	</view>
	<template v-if="operationPopupShow">
		<operationPopupVue :orderId="orderId" :data="currentShop" :show="operationPopupShow"
			@close="operationPopupShow=false" @refresh="getListPatrolShop" />
	</template>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { getCurrentInstance, onMounted, ref } from 'vue';
	import { onLoad, onShow, onUnload } from '@dcloudio/uni-app'
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import { shopStateEnums, shopStateEnumsToString } from './enums';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import operationPopupVue from './components/operation-popup/operation-popup.vue';
	import { listPatrolShop } from './api';
	import { useCommon } from '@/hooks/useGlobalData';
	import emptyVue from '@/components/empty/empty.vue';
  import { showLoading } from "@/utils/util";
	type dataItemType = {
		status : string | number;
		target : string;
		shopType : number | string;
		shopId : string;
		shopName : string;
		[k : string] : any
	}
	const planStateColor = {
		3: { color: '#E69850', background: 'linear-gradient(203.5deg, #e6985000 0%, #ffe5ccff 100%)' },
		4: { color: '#56CC93', background: 'linear-gradient(203.5deg, #04bd380d 0%, #04bd3833 100%)' },
		5: { color: '#C35943', background: 'linear-gradient(203.5deg, #ffb2a10d 0%, #f75e3b33 100%)' },
		6: { color: '#1F242866', background: 'linear-gradient(203.5deg, #1f24280d 0%, #1f242833 100%)' },
		7: { color: '#1F2428', background: 'linear-gradient(203.5deg, #1f24280d 0%, #1f242833 100%)' },
		8: { color: '#6D2ED1', background: 'linear-gradient(203.5deg, #1c0aa60d 0%, #2a0e9933 100%)' },
	}
  const pages = getCurrentPages()
	const instance = getCurrentInstance()
	const keywords = ref<string>()
	const orderId = ref<string>('')
	const { setRouteParams, customBack } = useCommon(getCurrentInstance())
	const index = ref<number>(0),
		height = ref<string>(),
		operationPopupShow = ref<boolean>(false),
		dataList = ref<Partial<dataItemType[]>>([]),
		currentShop = ref<dataItemType>(),
		tripType = ref<number>(1),
		column = ref([])

	/**
	 * @description: 获取数据
	 */
	const getListPatrolShop = async () : Promise<void> => {
		const res = await listPatrolShop({
			orderId: orderId.value,
			status: column.value[index.value].value,
			keywords: keywords.value
		})
		dataList.value = res.data
	}

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.scroll-view`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})

	function bindPickerChange(e) {
		console.log('picker发送选择改变，携带值为', e)
		index.value = e.indexs[0]
		getListPatrolShop()
	}

	const search = () => {
		getListPatrolShop()
	}
	const scrolltolower = () => {
		console.log("🚀 ~ scrolltolower ~ scrolltolower:", '暂不分页')
	}

	const handleClick = (item) => {
		currentShop.value = item
		operationPopupShow.value = true
	}

	onLoad((opts) => {
		orderId.value = opts.id
		tripType.value = opts.tripType
		if (tripType.value == 2) {
			column.value = JSON.parse(JSON.stringify(shopStateEnums))
			column.value.push({ label: '已超时', value: 8 })
		} else {
			column.value = JSON.parse(JSON.stringify(shopStateEnums))
		}
	})

	onShow(() => {
    showLoading('加载中')
    instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
      if (res) {
        getListPatrolShop()
      }
    })
	})

	onUnload(() => {
		setRouteParams('packageB/pages/shop-inspection/shop-inspection-application')
	})
</script>

<style scoped lang="scss">
	.picker-section {
		width: 100%;
		padding: 20rpx;
		justify-content: space-between;

		c-picker-vue {
			width: 224rpx;
			margin-right: 20rpx;
		}

		.custom-search {
			padding: 0;

			:deep() {
				.u-search__content {
					background-color: #ffffffe6 !important;
				}

				.u-search__content__input {
					background-color: transparent !important;
				}
			}
		}
	}

	.count {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 32rpx;
		padding: 20rpx;
	}

	.list {
		overflow: hidden;
		border-radius: 20rpx;
		margin: 20rpx 20rpx 0;
		// backface-visibility: hidden;
		// transform: translate3d(0, 0, 0);
		// -webkit-backface-visibility: hidden;
		// -webkit-transform: translate3d(0, 0, 0);

		.scroll-view {
			.shop-info {
				box-sizing: border-box;
				padding: 20rpx 40rpx;
				justify-content: space-between;
				border-radius: 20rpx;
				background: #ffffff;
				margin-bottom: 20rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.info {
					position: relative;
					margin-bottom: 44rpx;
					justify-content: space-between;

					&::before {
						position: absolute;
						content: '';
						height: 2rpx;
						left: -20rpx;
						right: -20rpx;
						top: 60rpx;
						background: #f2f2f2;
					}

					.no {
						color: #999999;
						font-size: 28rpx;
					}

					.state {
						color: #56cc93;
						font-size: 22rpx;
						font-weight: bold;
						line-height: 22rpx;
						padding: 8rpx 46rpx 8rpx 20rpx;
						border-radius: 20rpx 0 0 0;
						margin-right: -40rpx;
					}
				}
			}

			.desc {
				align-items: flex-start;
				justify-content: space-between;

				&>view:first-child {
					align-items: flex-start;
				}

				.icon {
					width: 90rpx;
					height: 90rpx;
					flex-shrink: 0;
					margin-right: 40rpx;
				}

				.right {
					flex: 1;

					.shop-name {
						color: #1f2428;
						font-size: 28rpx;
						font-weight: bold;
						line-height: 36rpx;
						margin-bottom: 14rpx;
						align-items: baseline;
						justify-content: space-between;

						.operation {
							flex-shrink: 0;

							text {
								color: #999999;
								font-size: 24rpx;
								line-height: 24rpx;
								margin-right: 16rpx;
							}
						}
					}

					.row {
						margin-bottom: 10rpx;
						align-items: baseline;

						&:last-child {
							margin-bottom: 0;
						}

						.label {
							color: #1f2428;
							font-size: 28rpx;
							line-height: 38rpx;
							flex-shrink: 0;
						}

						.value {
							color: #1f2428;
							font-size: 28rpx;
							line-height: 38rpx;
							word-break: break-all;
						}
					}
				}
			}
		}
	}
</style>