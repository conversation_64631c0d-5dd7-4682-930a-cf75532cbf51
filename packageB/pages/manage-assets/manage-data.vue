<template>
	<view class="container">
		<commonBgVue height="442" />
		<u-navbar placeholder title="管辖数据" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="tabs">
			<cTabs v-model="currentTabIndex" :list="tabs" @change="toQueryList" />
		</view>
		<view class="picker-section">
			<view class="block fr" @click="show = true">
				<view class="text">{{currentPickerName[currentTabIndex]}}</view>
				<up-icon name="arrow-down" color="#999999" size="22rpx" />
			</view>
		</view>
		<view class="manage">
			<dataSectionVue :data="queryData" :currentTabIndex="currentTabIndex" />
		</view>
		<template v-if="columns[0].length > 0">
			<up-picker :show="show" :columns="columns" confirmColor="#C35943" closeOnClickOverlay @confirm="confirm"
				:defaultIndex="defaultIndex" @cancel="close" @close="close" keyName="label" />
		</template>
    <cWaterMark></cWaterMark>
	</view>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ref, reactive, watch, nextTick, unref } from 'vue';
	import commonBgVue from '@/packageB/components/common-bg/common-bg.vue';
	import cTabs from '@/components/c-tabs/c-tabs.vue';
	import dataSectionVue from './components/data-section/data-section.vue';
	import { queryManagerOperatingResources, queryReceptionOperatingResources, queryShopOperatingResources } from '@/packageB/api';

	const level = [
		[
			{ label: '全部', value: '' },
			{ label: '五星门店', value: 'A' },
			{ label: '四星门店', value: 'B' },
			{ label: '三星门店', value: 'C' },
			{ label: '二星门店', value: 'D' },
			{ label: '一星门店', value: 'E' },
			{ label: '无', value: 'F' },
		],
		[
			{ label: '全部', value: '' },
			{ label: '金枕头门店', value: '20' },
			{ label: '银枕头门店', value: '19' },
			{ label: '蓝枕头门店', value: '18' },
			{ label: '黄枕头门店', value: '17' },
			{ label: '白枕头门店', value: '16' }],
		[
			{ label: '全部', value: '' },
			{ label: '最强王者', value: 'king' },
			{ label: '至尊星耀', value: 'star' },
			{ label: '永恒钻石', value: 'diamond' },
			{ label: '荣耀黄金', value: 'gold' },
			{ label: '倔强青铜', value: 'bronze' },
		]
	]
	const currentTabIndex = ref(0)
	const tabs = [{ name: '门店星级' }, { name: '店长分布' }]
	const columns = reactive([level[0]])
	const currentPickerName = reactive(['全部', '全部', '全部'])
	const currentPickerValue = reactive(['', '', ''])
	const defaultIndex = ref([0])

	const show = ref(false)
	const queryData = ref({})

	onLoad(options => {
		currentTabIndex.value = Number(options.tabIndex)
		toQueryList()
	})

	watch(currentTabIndex,
		(v) => {
			columns[0] = []
			nextTick(() => {
				columns[0] = level[v]
				setDefaultIndex()
			})
		}
	)

	const close = () => {
		show.value = false
		setDefaultIndex()
	}

	const confirm = (e : any) => {
		currentPickerValue[unref(currentTabIndex)] = e.value[0].value
		currentPickerName[unref(currentTabIndex)] = e.value[0].label
		show.value = false;
		toQueryList()
	}

	const setDefaultIndex = () => {
		const index = columns[0].findIndex(item => item.value == currentPickerValue[currentTabIndex.value])
		defaultIndex.value = [index]
	}

	const toQueryList = () => {
		if (currentTabIndex.value == 0) {
			toQueryShopOperatingResources()
		} else if (currentTabIndex.value == 1) {
			toQueryManagerOperatingResources()
		} else if (currentTabIndex.value == 2) {
			toQueryReceptionOperatingResources()
		}
	}

	// 门店运营资产
	const toQueryShopOperatingResources = async () => {
		try {
			const { data } = await queryShopOperatingResources({
				shopRank: currentPickerValue[0]
			})
			if (data) queryData.value = data
			else queryData.value = {}
		} catch (e) {
			console.error('门店运营资产 error', e)
		}
	}

	// 店长运营资产
	const toQueryManagerOperatingResources = async () => {
		try {
			const { data } = await queryManagerOperatingResources({
				manageRank: currentPickerValue[1]
			})
			if (data) queryData.value = data
			else queryData.value = {}
		} catch (e) {
			console.error('店长运营资产 error', e)
		}
	}

	// 前台运营资产
	const toQueryReceptionOperatingResources = async () => {
		try {
			const { data } = await queryReceptionOperatingResources({
				receptionRank: currentPickerValue[2]
			})
			if (data) queryData.value = data
			else queryData.value = {}
		} catch (e) {
			console.error('前台运营资产 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.container {}

	.tabs {
		padding: 20rpx 20rpx;
	}

	.manage {
		padding: 0 20rpx;
	}

	.picker-section {
		padding: 0 20rpx;
		margin-bottom: 20rpx;

		.block {
			border-radius: 200rpx;
			background: #ffffffe6;
			justify-content: space-between;
			padding: 20rpx 40rpx;

			.text {
				color: #1f2428;
				font-size: 24rpx;
				font-weight: bold;
				line-height: 24rpx;
			}

			.img {
				transform: rotate(90deg);
				width: 12rpx;
				height: 21.6rpx;
			}
		}
	}
</style>