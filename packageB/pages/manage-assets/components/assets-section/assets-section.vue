<template>
	<view class="block">
		<view class="fr top" @click="route_to_view('/packageB/pages/manage-assets/manage-data?tabIndex=0')">
			<view class=" fr">
				<view class="title">辖区门店总数：</view>
				<view class="num">{{responseData.shopData?.totalCount}}</view>
			</view>
			<up-icon name="arrow-right" color="#999999" size="22rpx" />
		</view>
		<cTableVue :header="shopHeader" :list="shopDataList" :isPaginate="false" emptyText="暂无数据"/>
	</view>
	<view class="block">
		<view class="fr top" @click="route_to_view('/packageB/pages/manage-assets/manage-data?tabIndex=1')">
			<view class="fr">
				<view class="title">辖区店长总数：</view>
				<view class="num">{{responseData.managerData?.managerCount}}</view>
			</view>
			<up-icon name="arrow-right" color="#999999" size="22rpx" />
		</view>
		<cTableVue :header="managerHeader" :list="managerDataList" :isPaginate="false" emptyText="暂无数据"/>
	</view>
	<!--<view class="block">
		<view class="fr top" @click="route_to_view('/packageB/pages/manage-assets/manage-data?tabIndex=2')">
			<view class="fr">
				<view class="title">辖区前台总数：</view>
				<view class="num">{{responseData.receptionData?.receptionCount}}</view>
			</view>
			<up-icon name="arrow-right" color="#999999" size="22rpx" />
		</view>
		<cTableVue :header="receptionHeader" :list="receptionDataList" :isPaginate="false" emptyText="暂无数据"/>
	</view>-->
</template>

<script setup lang="ts">
	// import cTableVue from '@/components/c-table/c-table.vue'
	import cTableVue from '@/components/c-table/c-table.vue'
	import { getCurrentInstance, ComponentInternalInstance, onMounted, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { queryOperatingResourcesOverview } from '@/packageB/api';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const shopHeader = [
		{ name: '等级', prop: 'name' },
		{ name: '5星', prop: 'fiveRankCount' },
		{ name: '4星', prop: 'fourRankCount' },
		{ name: '3星', prop: 'threeRankCount' },
		{ name: '2星', prop: 'twoRankCount' },
		{ name: '1星', prop: 'oneRankCount' },
	]
	const managerHeader = [
		{ name: '等级', prop: 'name' },
		{ name: '金枕头', prop: 'goldRankCount' },
		{ name: '银枕头', prop: 'silverRankCount' },
		{ name: '蓝枕头', prop: 'blueRankCount' },
		{ name: '黄枕头', prop: 'yellowRankCount' },
		{ name: '白枕头', prop: 'whiteRankCount' },
	]
	const receptionHeader = [
		{ name: '等级', prop: 'name' },
		{ name: '最强王者', prop: 'kingCount' },
		{ name: '至尊星耀', prop: 'starCount' },
		{ name: '永恒钻石', prop: 'diamondCount' },
		{ name: '荣耀黄金', prop: 'goldCount' },
		{ name: '倔强青铜', prop: 'bronzeCount' },
	]
	const responseData = ref<{
		managerData ?: any,
		receptionData ?: any,
		shopData ?: any
	}>({})

	const shopDataList = ref([]), receptionDataList = ref([]), managerDataList = ref([])

	onMounted(() => {
		toQueryOperatingResourcesOverview()
	})

	// 运营资产概览
	const toQueryOperatingResourcesOverview = async () => {
		try {
			const { data } = await queryOperatingResourcesOverview({})
			if (data) {
				if (data.shopData?.totalCount > 0) {
					shopDataList.value = [{ name: '本月', ...data.shopData }]
				}
				if (data.managerData?.managerCount > 0) {
					managerDataList.value = [{ name: '本月', ...data.managerData }]
				}
				if (data.receptionData?.receptionCount > 0) {
					receptionDataList.value = [{ name: '本月', ...data.receptionData }]
				}
				if (data.lastReceptionData?.receptionCount > 0) {
					receptionDataList.value.push({ name: '上月', ...data.receptionData })
				}
				responseData.value = data
			}
		} catch (e) {
			console.error('运营资产概览 error', e)
		}
	}
	defineExpose({ toQueryOperatingResourcesOverview })
</script>

<style scoped lang="scss">
	.block {
		border-radius: 20rpx;
		background: #ffffff;
		padding: 40rpx;
		margin-bottom: 20rpx;

		.top {
			justify-content: space-between;
			margin-bottom: 40rpx;

			.title {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.num {
				color: #c35943;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.img {
				width: 12rpx;
				height: 21.6rpx;
				flex-shrink: 0;
			}
		}
	}
</style>