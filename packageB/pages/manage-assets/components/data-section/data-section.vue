<template>
	<view class="block" id="A">
		<view class="fr top">
			<view class="fr">
				<view class="title">{{temp[currentTabIndex].title}}：</view>
				<view class="num">{{data[temp[currentTabIndex].count] || '0' }}</view>
			</view>
		</view>
		<template v-if="data && JSON.stringify(data)!=='{}' && data[temp[currentTabIndex].list]?.length > 0">
			<cTableVue cellWidth="158" cellHeight="auto" :header="temp[currentTabIndex].header" :list="list"
				:height="scrollHeight" :isPaginate="false" @scrolltolower="scrolltolower" />
		</template>
		<template v-else>
			<emptyVue />
		</template>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, getCurrentInstance, ref, watch, nextTick } from 'vue';
	import cTableVue from '@/components/c-table/c-table.vue'
	import emptyVue from '@/components/empty/empty.vue';

	const instance = getCurrentInstance();
	const scrollHeight = ref(), list = ref([]), start = ref(0), end = ref(0), totalCount = ref(0), batchSize = 100

	const temp = [
		{
			title: '门店分布',
			count: 'totalCount',
			list: 'shopResourcesList',
			header: [
				{ name: '所属城区', prop: 'areaName' },
				{ name: '门店ID', prop: 'shopId' },
				{ name: '门店名称', prop: 'shopName' },
				{ name: '门店星级', prop: 'shopRank' },
			]

		},
		{
			title: '店长分布',
			count: 'managerCount',
			list: 'managerResourcesList',
			header: [
				{ name: '店长名称', prop: 'managerTypeName' },
				{ name: '门店ID', prop: 'shopId' },
				{ name: '归属门店', prop: 'shopName' },
				{ name: '店长等级', prop: 'level' },
			]
		},
		{
			title: '前台分布',
			count: 'receptionCount',
			list: 'receptionResourcesList',
			header: [
				{ name: '店长名称', prop: 'staffName' },
				{ name: '门店ID', prop: 'shopId' },
				{ name: '门店名称', prop: 'shopName' },
				{ name: '前台段位', prop: 'level' },
			]
		}
	]

	const props = defineProps({
		currentTabIndex: {
			type: [String, Number],
			default: 0
		},
		data: {
			type: Object,
			default: () => { }
		}
	})
	watch(() => props.data,
		(v) => {
			start.value = 0
			end.value = 0
			totalCount.value = 0
			list.value = []
			nextTick(() => {
				if (JSON.stringify(v) != '{}') {
					totalCount.value = props.data[temp[props.currentTabIndex].count]
					if (totalCount.value == 0) return
					end.value = totalCount.value > batchSize ? batchSize : totalCount.value
					list.value = props.data[temp[props.currentTabIndex].list].slice(start.value, end.value)
				}
			})
		}
	)

	onMounted(() => {
		let screenHeight = null
		let safeAreaBottom = null
		uni.getSystemInfo({
			success: function (res) {
				console.log(res);
				screenHeight = res.screenHeight
				safeAreaBottom = res.safeAreaInsets.bottom || 20;
			}
		})
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`#A`).boundingClientRect((res : any) => {
			const height = screenHeight - res.top - 40 - 36 - 42 - safeAreaBottom
			scrollHeight.value = height + 'px'
		}).exec();
	})

	const scrolltolower = () => {
		console.log(start.value, end.value);
		if (end.value >= totalCount.value) {
			return;
		}
		start.value = end.value
		const newEnd = Math.min(end.value + batchSize, totalCount.value);
		end.value = newEnd
		list.value.push(...props.data[temp[props.currentTabIndex].list].slice(start.value, end.value))
	}
</script>

<style scoped lang="scss">
	.block {
		border-radius: 21.11rpx;
		background: #ffffff;
		padding: 40rpx;

		.top {
			justify-content: space-between;
			margin-bottom: 40rpx;

			.title {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.num {
				color: #c35943;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.img {
				width: 12rpx;
				height: 21.6rpx;
				flex-shrink: 0;
			}
		}
	}
</style>