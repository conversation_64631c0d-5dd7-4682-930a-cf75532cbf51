<template>
	<view class="container">
		<commonBgVue />
		<u-navbar placeholder title="管辖资产" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="tabs">
			<cTabs v-model="currentTabIndex" :list="tabs" />
		</view>
		<view class="manage">
			<assetsSectionVue ref="assetsSectionRef" class="manage-block" :isPaginate="false" />
		</view>
    <cWaterMark></cWaterMark>
	</view>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app';
	import { ref } from 'vue';
	import commonBgVue from '@/packageB/components/common-bg/common-bg.vue';
	import cTabs from '@/components/c-tabs/c-tabs.vue';
	import assetsSectionVue from './components/assets-section/assets-section.vue';

	const currentTabIndex = ref(0), assetsSectionRef = ref(null)
	const tabs = [{ name: '管辖数据' }]

	onShow(() => {
		if (assetsSectionRef.value)
			assetsSectionRef.value.toQueryOperatingResourcesOverview()
	})
</script>

<style scoped lang="scss">
	.container {
		padding-bottom: 40rpx;
	}

	.tabs {
		padding: 20rpx 42rpx;
	}

	.bg {
		width: 100%;
		height: calc(100% - 374rpx);
		border-radius: 0 120rpx 0 0;
		background: #efefef;
		position: absolute;
		top: 374rpx;
		left: 0;
		z-index: -10;
	}

	.manage {
		padding: 0 20rpx;
	}
</style>