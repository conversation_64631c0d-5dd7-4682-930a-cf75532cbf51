<template>
  <layout>
    <view class="container">
      <view class="calendar">
        <view class="fr picker-date">
          <view class="icon" @tap="onPrevMonth" v-if="!isEqualMonth">
            <uni-icons type="arrowleft" size="12" color="#000"></uni-icons>
          </view>
          <picker class="picker" mode="date" fields="month" :value="currentMonth" @change="onPickerChangeDate" disabled>
            <view class="fr current-date">
              <text class="DIN-Bold">{{ formatYear }}</text>
              年
              <text class="DIN-Bold">{{ formatMonth }}</text>
              月
            </view>
          </picker>
          <view class="icon" @tap="onNextMonth">
            <uni-icons type="arrowright" size="12" color="#000"></uni-icons>
          </view>
        </view>
        <calendarVue
          ref="calendarRef" v-model:value="currentDate" v-model:month="currentMonth"
          @change-month="loadCalendar" :allowTouchLeft="!isEqualMonth"></calendarVue>
        <calendarIntroductionVue />
      </view>
      <baseInfoVue v-if="shopId" :date="currentDate" :currentDate="currentDate" :shopId="shopId"/>
      <roomTypePriceAdjustmentVue v-if="shopId" v-model:date="currentDate" :shopId="shopId"/>
    </view>
  </layout>
  <cWaterMark></cWaterMark>
</template>

<script lang="ts" setup>
  import { onLoad } from '@dcloudio/uni-app'
  import { computed, onMounted, ref, nextTick, toRaw } from 'vue'
  import dayjs from 'dayjs';
  import calendarVue from './components/calendar/calendar.vue'
  import calendarIntroductionVue from './components/calendar-introduction.vue';
  import roomTypePriceAdjustmentVue from './components/roomTypePriceAdjustment.vue';
  import baseInfoVue from './components/base-info.vue';
  import type { listItem } from './components/calendar/type'
  import { dataType, successCallbackResult } from "@/types";
  import { calendarDayData } from "../../api";

  const calendarRef = ref<InstanceType<typeof calendarVue>>(null)
  const currentMonth = ref(dayjs().format('YYYY-MM'))
  const currentDate = ref(dayjs().format('YYYY-MM-DD'))
  const formatYear = computed(() => dayjs(currentMonth.value).year())
  const formatMonth = computed(() => dayjs(currentMonth.value).month() + 1)
  const setStyle = {
    leftRadius(color: string = 'rgba(242, 242, 242, 0.5)') {
      return {
        backgroundColor: color,
        borderRadius: '10rpx 0 0 10rpx'
      }
    },
    rightRadius(color: string = 'rgba(242, 242, 242, 0.5)') {
      return {
        backgroundColor: color,
        borderRadius: '0 10rpx 10rpx 0'
      }
    },
    radius(color: string = 'rgba(242, 242, 242, 0.5)') {
      return {
        backgroundColor: color,
        borderRadius: '10rpx'
      }
    },
    bg(color: string = 'rgba(242, 242, 242, 0.5)') {
      return {
        backgroundColor: color,
      }
    }
  }
  const shopId = ref<String>('')
  /**
   * @description ： 是否当月
   */
  const isEqualMonth = computed(() => dayjs().isSame(dayjs(currentMonth.value), 'month'))
  /**
   * @description: 选择日期
   */
  const onPickerChangeDate = async (e: any) => {
    currentMonth.value = e.detail.value
    calendarRef.value.setDateList(calendarRef.value.createCalendar(new Date(e.detail.value)))
    await nextTick()
    await toCalendarDayData()
    setDisabled()
  }
  /**
   * @description : 上个月
   */
  const onPrevMonth = async () => {
    calendarRef.value.onPrevMonth(currentMonth.value)
    await nextTick()
    await toCalendarDayData()
    setDisabled()
  }
  /**
   * @description: 下个月
   */
  const onNextMonth = async () => {
    calendarRef.value.onNextMonth(currentMonth.value)
    await nextTick()
    await toCalendarDayData()
    setDisabled()
  }
  /**
   * @description: 查找最后的下标
   */
  const findLastIndex = (arr: listItem[]) => {
    for (let i = arr.length - 1; i >= 0; i--) {
      if (arr[i]?.disabled) {
        return i
      }
    }
    return -1
  }
  /**
   * @description: 设置选中区间
   */
  const setRange = (dateList: listItem[], firstDisabledDataIndex: number, lastDisabledDataIndex: number, color: string = 'rgba(242, 242, 242, 0.5)') => {
    let arr = []
    for (let i = firstDisabledDataIndex; i <= lastDisabledDataIndex; i++) {
      const inter = Math.floor(i / 7)
      if (inter >= 0) {
        arr[inter] = arr[inter] || []
        arr[inter].push(i)
      }
    }
    arr = arr.filter(item => item?.length)
    console.log(arr,'arr')
    for (let i = 0, iLen = arr.length; i < iLen; i++) {
      for (let j = 0, jLen = arr[i].length; j < jLen; j++) {
        const currentIndex = arr[i][j];
        const date = new Date(dateList[currentIndex].label);
        const dayOfWeek = date.getDay(); // 获取星期几，0=周日，1=周一

        const isSingleInRow = jLen === 1; // 当前行只有一个禁用项

        if (isSingleInRow) {
          // 一行仅一个禁用项，设置完整圆角，同时加左右边距
          dateList[currentIndex].children = null;
          dateList[currentIndex].parent = setStyle.radius(color);
          // 如果是周日或周一，增加左右边距
          if (dayOfWeek === 0 || dayOfWeek === 1) {
            dateList[currentIndex].parent.marginLeft = '10rpx'; // 周日加左边距
            dateList[currentIndex].parent.marginRight = '10rpx'; // 周一加右边距
          }
        } else {
          if (j === 0) {
            // 行首元素，设置左圆角和左边距
            dateList[currentIndex].children = null;
            dateList[currentIndex].parent = setStyle.leftRadius(color);
            dateList[currentIndex].parent.marginLeft = '10rpx'; // 周一，添加左边距
          } else if (j === jLen - 1) {
            // 行尾元素，设置右圆角和右边距
            dateList[currentIndex].children = null;
            dateList[currentIndex].parent = setStyle.rightRadius(color);
            dateList[currentIndex].parent.marginRight = '10rpx'; // 周日，添加右边距
          } else {
            // 中间元素，仅设置背景色
            dateList[currentIndex].children = null;
            dateList[currentIndex].parent = setStyle.bg(color);
          }
        }
      }
    }
    return dateList
  }
  /**
   * @description: 设置不可选日期
   */
  const setDisabled = () => {
    try {
      const timestamp = new Date(dayjs().format('YYYY-MM-DD')).getTime()
      const maxTimestap = new Date(dayjs().add(90, 'day').format('YYYY-MM-DD')).getTime()
      let dateList = calendarRef.value.dateList?.map((item: listItem) => {
        if (item.label) {
          const diff = new Date(item.label).getTime() - timestamp
          const maxDiff = new Date(item.label).getTime() - maxTimestap
          if (diff < 0 || maxDiff > 0) {
            item.disabled = true
          }
        }
        return item
      })
      const firstDisabledDataIndex = dateList.findIndex(item => item.disabled)
      const lastDisabledDataIndex = findLastIndex(dateList)
      if (firstDisabledDataIndex > -1 && lastDisabledDataIndex > -1) {
        dateList = setRange(dateList, firstDisabledDataIndex, lastDisabledDataIndex)
      }
      calendarRef.value.setDateList(dateList)
    } catch (err) {
      console.log("🚀 ~ file: scheduled-price-adjustment.vue:86 ~ setDisabled ~ err:", err)
    }
  }

  /**
   * @description: 获取日历数据
   */
  const toCalendarDayData = async () => {
    try {
      const res: successCallbackResult = await calendarDayData({
        monthDate: currentMonth.value,
        shopId: shopId.value
      })
      if (res.code === 200) {
        if (res.data.length > 0) {
          calendarRef.value.setDateEvents(res.data.map((item: { date: any; event: any; }) => ({
            ...item,
            label: item.date,
            events: item.event || []
          })))
          calendarRef.value.setDateColor(updatedData(res.data.map((item: { date: any; event: any; }) => ({
            ...item,
            label: item.date,
            events: item.event || []
          }))), 'parent')
        }
      }
    } catch (e) {

    }
  }

  const updatedData = (data: { occRank: any; date: string | number | Date; }[]) => {
    return data.map((item: { occRank: any; date: string | number | Date; }, index: number, array: string | any[]) => {
      let background: string;
      switch (item.occRank) {
        case 1:
          background = 'rgba(20, 116, 251, 0.1)';
          break;
        case 2:
          background = 'rgba(255, 141, 26, 0.1)';
          break;
        case 3:
          background = 'rgba(247, 94, 59, 0.1)';
          break;
        case 4:
          background = 'rgba(67, 191, 131, 0.1)';
          break;
        default:
          background = 'transparent';
      }

      // 初始化style
      let style: dataType = {background};

      // 判断日期是否是周一或周日
      const date = new Date(item.date);
      const dayOfWeek = date.getDay();  // 获取日期对应的星期几，0为周日，1为周一

      // 判断是否为连续相同的日期段的第一个和最后一个
      const isFirstInGroup = (index === 0 || array[index - 1].occRank !== item.occRank);
      const isLastInGroup = (index === array.length - 1 || array[index + 1].occRank !== item.occRank);

      // // 不加边距的条件：周一不加左边距，周日不加右边距
      // if (![1].includes(dayOfWeek) && isFirstInGroup) {
      //   style.marginLeft = '10rpx';  // 非周一且为第一个连续日期段时加左边距
      // }
      // if (![0].includes(dayOfWeek) && isLastInGroup) {
      //   style.marginRight = '10rpx'; // 非周日且为最后一个连续日期段时加右边距
      // }

      // 圆角处理：周一和第一个相同occRank的日期
      if (dayOfWeek === 1 || isFirstInGroup) {
        style.marginLeft = '10rpx';  // 周一或为第一个连续日期段时加左边距
        if (style.borderRadius) {
          style.borderRadius = '10rpx 0 0 10rpx';  // 设置左上和左下圆角
        } else {
          style.borderRadius = '10rpx 0 0 10rpx'; // 初始设置
        }
      }

      // 圆角处理：周日和最后一个相同occRank的日期
      if (dayOfWeek === 0 || isLastInGroup) {
        style.marginRight = '10rpx';  // 周日或为最后一个连续日期段时加右边距
        if (style.borderRadius) {
          style.borderRadius = style.borderRadius.split(' ').map((radius, index) => {
            // 保证右边的圆角只加一次，左边的圆角保留
            if (index === 1 || index === 2) return '10rpx';
            return radius;
          }).join(' ');
        } else {
          style.borderRadius = '0 10rpx 10rpx 0';  // 初始设置右上右下圆角
        }
      }

      return {
        ...item,
        style
      };
    });
  };

  const loadCalendar = async () => {
    await nextTick()
    await toCalendarDayData()
    setDisabled()
  }

  onLoad(async (options) => {
    shopId.value = options.shopId
    await loadCalendar()
  })
</script>

<style lang="scss" scoped>
  .container {
    padding: 20rpx 20rpx 0;
  }

  .calendar {
    padding-bottom: 40rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;

    .picker-date {
      justify-content: center;
      padding-top: 40rpx;
      padding-bottom: 22rpx;

      .current-date {
        font-size: 36rpx;
        font-weight: bold;
        color: #1F2428;

        .DIN-Bold {
          font-size: 40rpx;
          line-height: 40rpx;
        }
      }

      .icon {
        width: 40rpx;
        text-align: center;
      }

      .picker {
        margin: 0 16rpx;
      }
    }
  }
</style>