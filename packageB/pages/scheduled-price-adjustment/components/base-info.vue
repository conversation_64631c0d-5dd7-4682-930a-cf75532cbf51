<template>
  <view class="container">
    <view style="margin: 0 20rpx">
      <cTabs
        :list="tabList" v-model="tabIndex" :isBig="false"
        itemStyle="height: 60rpx;align-items:flex-start;padding:0 20rpx"/>
    </view>
    <baseInfoVue v-if="tabValue == 1 && hasItExceeded30Days" :data="source?.baseData" />
    <hotEventsVue v-if="tabValue == 2" :data="source?.hotEvents" />
    <!-- <activityVue v-if="tabValue == 3"/> -->
    <trendVue v-if="tabValue == 4" :data="source?.motivationList" />
  </view>
</template>

<script setup lang="ts">
  import { computed, ref, unref, watch } from 'vue';
  import dayjs from 'dayjs'
  import { calendarDetail } from '../../../api'
  import baseInfoVue from './base-info/index.vue';
  import hotEventsVue from './base-info/hotEvents.vue';
  import activityVue from './base-info/activity.vue';
  import trendVue from './base-info/trend.vue';
  import cTabs from '@/components/c-tabs/c-tabs.vue'

  const props = defineProps({
    date: {
      type: String,
      default: () => dayjs().format('YYYY-MM-DD')
    },
    currentDate: {
      type: String
    },
    shopId: {
      type: String
    }
  })
  type sourceType = {
    baseData?: any;
    hotEvents?: any;
    motivationList?: any
  }

  const source = ref<sourceType>({})
  /**
   * @description: 是否未超出30天
   */
  const hasItExceeded30Days = computed(() => dayjs(props.currentDate).diff(dayjs(), 'day') < 30)

  const tabList = computed(() => {
    const list = [
      {name: '基础信息', value: 1, key: 1},
      {name: '周边热点事件', value: 2, key: 2},
      // { label: '营销活动', value: 3, key: 3 },
      // { label: '趋势分析', value: 4, key: 4 },
    ]
    return unref(hasItExceeded30Days) ? list : list.slice(1)
  })
  const tabIndex = ref<number>(0)
  const tabValue = computed(()=>{
    return tabList.value[tabIndex.value].value
  })

  watch(() => tabList.value.length, v => {
    if (v == 1) {
      tabIndex.value = 0
    }
  })

  const getCalendarDetail = async () => {
    try {
      const res = await calendarDetail({
        queryDate: props.date,
        shopId: props.shopId
      })
      source.value = res.data
    } catch (err) {
    }
  }

  watch(() => props.date, v => {
    if (v) {
      getCalendarDetail()
    }
  }, {immediate: true})
</script>

<style lang="scss" scoped>
  .container {
    padding: 49rpx 0;
    margin-bottom: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;

    .rank {
      white-space: nowrap;
      margin-bottom: 32rpx;
    }

    .rank-item {
      display: inline-flex;
      padding: 20rpx 32rpx;
      font-size: 26rpx;
      color: #000;
      border-radius: 200rpx;

      &:first-child {
        margin-left: 28rpx;
      }

      &:last-child {
        margin-right: 28rpx;
      }

      &--active {
        color: #fff;
        font-weight: bold;
        background-color: $uni-color-primary;
      }
    }

  }
</style>