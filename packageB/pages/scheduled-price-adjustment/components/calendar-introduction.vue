<template>
	<view class="calendar">
		<view class="fr calendar-status">
			<view v-for="item in dateStatus" :key="item.value" class="fr">
				<text class="circle" :style="{'backgroundColor': item.bgColor}"></text>
				<text class="calendar-status__text">{{ item.label }}</text>
			</view>
		</view>
		<view class="fr calendar-type">
			<view class="fr" v-for="item in activityType" :key="item.value">
				<text class="circle" :style="{'backgroundColor': item.bgColor}"></text>
				<text class="calendar-type__text">{{ item.label }}</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	const dateStatus = Object.freeze([
		{ label: 'OCC < 40%', bgColor: 'rgba(20, 116, 251, 0.2)', value: 1 },
		{ label: '40% ≤ OCC < 60%', bgColor: 'rgba(255, 141, 26, 0.2)', value: 2 },
		{ label: '60% ≤ OCC < 80%', bgColor: 'rgba(247, 94, 59, 0.2)', value: 3 },
		{ label: 'OCC ≥ 80%', bgColor: 'rgba(67, 191, 131, 0.2)', value: 4 },
		{ label: '不可选日期', bgColor: 'rgba(242, 242, 242, 0.5)', value: 5 }
	])
	const activityType = Object.freeze([
		// { label: '营销活动提醒', bgColor: '#FFC300', value: 1 },
		{ label: '法定节假日', bgColor: '#F75E3B', value: 2 },
		{ label: '周边热点事件', bgColor: '#1474FB', value: 3 }
	])
</script>

<style scoped lang="scss">
	.circle {
		flex-shrink: 0;
		width: 16rpx;
		height: 16rpx;
		margin-right: 16rpx;
		aspect-ratio: 1;
		border-radius: 50%;
	}

	.calendar {
		margin-top: 36rpx;

		&>.fr {
			padding: 0 40rpx;
		}
	}

	.calendar-status {
		flex-wrap: wrap;

		&>.fr {
			width: 50%;
			margin-bottom: 29rpx;
		}

		&__text {
			font-size: 24rpx;
			color: #000;
		}
	}

	.calendar-type {
		margin: 0 28rpx;
		padding: 20rpx 32rpx !important;
		//justify-content: space-between;
		background: rgba(242, 242, 242, 0.5);
		border-radius: 200rpx;

    &>.fr {
      margin-right: 50rpx;
    }

		&__text {
			font-size: 24rpx;
			color: #000;
		}
	}
</style>