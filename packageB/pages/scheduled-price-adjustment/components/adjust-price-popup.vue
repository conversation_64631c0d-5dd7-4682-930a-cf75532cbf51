<template>
  <view sclass="popup-container">
    <view class="overlay" :class="{'overlay-show': show}" @tap="close"></view>
    <view class="fl slot-popup" :class="{'slot-popup__show': show}">
      <view class="fr slot-popup__head">
        <text class="fr btn btn-cancel" @tap="close">取消</text>
        <text class="title">调价</text>
        <text class="fr btn btn-confirm" @tap="submit">提交</text>
      </view>
      <view class="fl line popup-content">
        <view class="fr">
          <text class="label fr-label required">房型</text>
          <view class="fr right-rect">
            <cPickerVue
              :columns="listData" keyName="roomTypeName" :value="roomTypeIndex"
              @change="onSelectRoomType">
              <view class="picker">{{ roomTypeName }}</view>
            </cPickerVue>
          </view>
        </view>
        <view class="fr">
          <text class="label fr-label required">日期</text>
          <view class="fr date-select">
            <picker
              mode="date" fields="day" class="picker" :value="data.startDate" :start="limitDate.startDate"
              :end="data.endDate || limitDate.endDate" @change="e => onChangeDate(e, 'startDate')">
              <text class="picker-date">{{ data.startDate }}</text>
            </picker>
            <text class="divider">~</text>
            <picker
              mode="date" fields="day" class="picker" :value="data.endDate"
              :start="data.startDate || limitDate.startDate" :end="limitDate.endDate"
              @change="e => onChangeDate(e, 'endDate')">
              <text class="picker-date">{{ data.endDate }}</text>
            </picker>
            <u-icon name="calendar" size="14" color="#000"></u-icon>
          </view>
        </view>
        <view class="fr" style="align-items: flex-start;">
          <view class="fr fr-label" @tap="onShowModal('week')">
            <text class="label required">适用星期</text>
            <image
              class="tip"
              src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-26/1740560975172.png"
              mode=""></image>
          </view>
          <view class="fr week">
            <text
              class="fr week-item" v-for="item in weeks" :key="item.value"
              :class="{'week-item--active': data.weeks?.includes(item.value)}"
              @tap="onTapWeek(item)">{{ item.label }}
            </text>
          </view>
        </view>
        <view class="fr">
          <view class="fr">
            <text class="label fr-label required">调整后门市价</text>
          </view>
          <view class="fr" style="width: 100%">
            <input
              v-model="data.rackRate" type="digit" placeholder="请输入" class="input"
              placeholder-class="input-placeholder" :cursor-spacing="50"
              @input="e => onInput(e, 'rackRate')">
            <text class="input-unit">元</text>
          </view>
        </view>
        <view class="fr">
          <view class="fr fr-label" @tap="onShowModal('breakfast')">
            <text class="label">设置含早价格</text>
            <image
              class="tip"
              src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-26/1740560975172.png"
              mode=""></image>
          </view>
          <view class="fr btn-group">
            <view
              class="fr btn-group__item"
              :class="{'btn-group__item--active': item.value == data.breakfastSetting}"
              v-for="item in settingGroup" :key="item.value"
              @tap="isSetBreakfastPrice(item.value)">{{ item.label }}
            </view>
          </view>
        </view>
        <view class="fl" v-if="data.breakfastSetting == 1">
          <view class="fr">
            <view class="fr fr-label" @tap="onShowModal('one')">
              <text class="label">门市价一份早餐</text>
              <image
                class="tip"
                src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-26/1740560975172.png"
                mode=""></image>
            </view>
            <view class="fr" style="width: 100%">
              <input
                v-model="data.breakfastOneRate" @input="e => onInput(e, 'breakfastOneRate')"
                type="digit" placeholder="请输入" placeholder-class="input-placeholder" class="input"
                :cursor-spacing="50">
              <text class="input-unit">元</text>
            </view>
          </view>
          <view class="fr">
            <view class="fr fr-label" @tap="onShowModal('one')">
              <text class="label">门市价二份早餐</text>
              <image
                class="tip"
                src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-26/1740560975172.png"
                mode=""></image>
            </view>
            <view class="fr" style="width: 100%">
              <input
                v-model="data.breakfastTwoRate" @input="e => onInput(e, 'breakfastTwoRate')"
                type="digit" placeholder="请输入" placeholder-class="input-placeholder" class="input"
                :cursor-spacing="50">
              <text class="input-unit">元</text>
            </view>
          </view>
          <view class="fr">
            <view class="fr fr-label" @tap="onShowModal('one')">
              <text class="label">门市价三份早餐</text>
              <image
                class="tip"
                src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-02-26/1740560975172.png"
                mode=""></image>
            </view>
            <view class="fr" style="width: 100%">
              <input
                v-model="data.breakfastThreeRate" @input="e => onInput(e, 'breakfastThreeRate')"
                type="digit" placeholder="请输入" placeholder-class="input-placeholder" class="input"
                :cursor-spacing="50">
              <text class="input-unit">元</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import { computed, nextTick, reactive, readonly, ref } from 'vue'
  import { dataType } from '@/types';
  import { showModal, showToast, formatDate } from '@/utils/util';
  import { manualSubmission } from '../../../api'
  import cPickerVue from '../../../components/c-picker/c-picker.vue';

  const props = defineProps({
    show: {
      type: Boolean,
      default: false
    },
    curData: {
      type: Object,
      default: null
    },
    listData: {
      type: Array<dataType>,
      default: () => []
    },
    shopId: {
      type: String,
    }
  })
  const emits = defineEmits(['update:show', 'afterClose'])

  const weeks = readonly([
      {label: '周一', value: 1},
      {label: '周二', value: 2},
      {label: '周三', value: 3},
      {label: '周四', value: 4},
      {label: '周五', value: 5},
      {label: '周六', value: 6},
      {label: '周日', value: 0}
    ]),
    settingGroup = readonly([
      {label: '不设置', value: 0},
      {label: '设置', value: 1},
    ]),
    showModalObj = readonly({
      week: '红色表示选中，灰色未选中',
      breakfast: '早餐价格及份数请以门店的实际情况来填写，其他房价码价格由门市价无早餐或含早餐价格自动计算得出',
      one: '非必填：填写后自动计算其他房价码价格，且作用于客人预订、开房'
    }),
    limitDate = reactive({
      startDate: formatDate(),
      endDate: (() => {
        const date = new Date()
        date.setFullYear(date.getFullYear() + 1)
        return formatDate(date)
      })()
    })

  const roomTypeIndex = ref<number>(props.listData.findIndex((item: dataType) => item.roomTypeCode == props.curData?.roomTypeCode)),
    data = ref<dataType>({
      startDate: props.curData.businessDate,
      endDate: props.curData.businessDate,
      weeks: weeks.map(item => item.value),
      breakfastSetting: 0
    })

  const roomTypeName = computed(() => {
    return props.listData?.[roomTypeIndex.value]?.roomTypeName
  })

  /**
   * @description: 选择房型
   * @param {*} e
   * @return {*}
   * @author: gu_shiyuan"
   */
  const onSelectRoomType = (e: any): void => {
    if (roomTypeIndex.value != e.indexs[0]) {
      data.value = {
        startDate: props.curData.businessDate,
        endDate: props.curData.businessDate,
        weeks: weeks.map(item => item.value),
        breakfastSetting: 0,
        breakfastThreeRate: undefined,
        breakfastOneRate: undefined,
        breakfastTwoRate: undefined
      }
    }
    roomTypeIndex.value = e.indexs[0]
  }

  /**
   * @description: 选择日期
   * @return {*}
   * @author: gu_shiyuan"
   */
  const onChangeDate = (e: any, field: string): void => {
    if (field == 'endDate') {
      if (new Date(e.detail.value) > new Date(limitDate.endDate)) {
        return
      }
      if (new Date(e.detail.value) < new Date(data.value.startDate)) {
        showToast('结束日期不能小于开始日期')
        return
      }
    } else if (field == 'startDate') {
      if (new Date(e.detail.value) < new Date(limitDate.startDate)) {
        return
      }
      if (new Date(e.detail.value) > new Date(data.value.endDate)) {
        showToast('开始日期不能大于结束日期')
        return
      }
    }
    data.value[field] = e.detail.value
  }

  /**
   * @description: 输入值保留两位小数
   * @param {*} e
   * @param {*} field
   * @return {*}
   * @author: gu_shiyuan"
   */
  const onInput = (e: any, field: string): void => {
    const value = e.detail.value
    const index = value.indexOf('.')
    if (index > -1) {
      if (value.length > index + 3) {
        nextTick(() => {
          data.value[field] = Number(value.substring(0, index + 3))
        })
        return
      }
    }
    nextTick(() => {
      data.value[field] = value
    })
  }

  /**
   * @description: 图标提示
   * @param {*} key
   * @return {*}
   * @author: gu_shiyuan"
   */
  const onShowModal = (key: string): void => {
    showModal({
      content: showModalObj[key],
      showCancel: false
    })
  }


  /**
   * @description: 点击设置含早价格按钮
   * @param {*} val
   * @return {*}
   * @author: gu_shiyuan"
   */
  const isSetBreakfastPrice = (val: number): void => {
    data.value.breakfastSetting = val
  }

  /**
   * @description: 选择适用星期
   * @param {*} item
   * @return {*}
   * @author: gu_shiyuan"
   */
  const onTapWeek = (item: dataType): void => {
    if (data.value.weeks.includes(item.value)) {
      data.value.weeks = data.value.weeks.filter((a: number) => a != item.value)
    } else {
      data.value.weeks.push(item.value)
    }
  }

  /**
   * @description: 关闭弹窗
   * @return {*}
   * @author: gu_shiyuan"
   */
  const close = (): void => {
    emits('update:show', false)
  }

  /**
   * @description: 处理请求参数
   * @return {*}
   * @author: gu_shiyuan"
   */
  const handleReqParams = (): dataType => {
    const tpl = JSON.parse(JSON.stringify(data.value))
    tpl.breakfastSetting = Boolean(tpl.breakfastSetting)
    if (!tpl.breakfastSetting) {
      tpl.clearBreakfast = true
    }
    tpl.weeks = tpl.weeks.join(',')
    tpl.roomTypeCode = props.listData[roomTypeIndex.value].roomTypeCode
    return tpl
  }

  /**
   * @description: 提交校验
   * @return {*}
   * @author: gu_shiyuan"
   */
  const validate = (): any => {
    let valid = true
    const fieldValidator = {
      startDate: [{required: true, message: '开始日期不能为空'}],
      endDate: [{required: true, message: '结束日期不能为空'}],
      weeks: [{type: 'array', required: true, message: '生效星期不能为空'}],
      breakfastSetting: [{required: handleReqParams()['breakfastSetting'], message: '是否设置含早价格不能为空'}],
      rackRate: [{required: true, message: '门市价不能为空'}]
    }
    for (let key in fieldValidator) {
      for (let item of fieldValidator[key]) {
        if (item.required) {
          if (item.type == 'array') {
            if (!handleReqParams()[key]?.length) {
              valid = false
              showToast(item.message)
            }
          } else {
            if (!handleReqParams()[key]) {
              valid = false
              showToast(item.message)
            }
          }
        }
      }
    }
    return valid
  }

  /**
   * @description: 调价提交校验
   * @return {*}
   * @author: gu_shiyuan"
   */
  const submit = (): any => {
    if (!validate()) return
    if (data.value.breakfastSetting == 0) {
      showModal({
        content: '是否清除原来的早餐价格',
        showCancel: true,
        confirmText: "清除",
        cancelText: '不清除',
        success: async (res) => {
          if (res.confirm) {
            submitReq(true)
          }
          if (res.cancel) {
            submitReq(false)
          }
        }
      })
    } else {
      submitReq(true)
    }
  }

  /**
   * @description: 调价提交
   * @return {*}
   * @author: gu_shiyuan"
   */
  const submitReq = async (clearBreakfast: boolean): Promise<void> => {
    try {
      const res = await manualSubmission({
        shopId: props.shopId,
        ...handleReqParams(),
        clearBreakfast
      })
      if (res.code == 200) {
        showToast({
          title: res.data,
          success() {
            let timer = setTimeout(() => {
              clearTimeout(timer)
              timer = null
              close()
              emits('afterClose')
            }, 1500)
          }
        })
      } else {
        showToast(res.data)
      }
    } catch (e) {
      console.log('调价接口报错', e)
    }
  }

  defineOptions({
    options: {
      styleIsolation: 'shared'
    }
  })
</script>

<style scoped lang="scss">
  .popup-container {
    position: relative;
  }

  .overlay {
    position: fixed;
    inset: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, .6);

    &-show {
      animation: opacity .3s ease-in forwards;
    }
  }

  @keyframes opacity {
    to {
      opacity: 1;
    }
  }

  @keyframes transform {
    to {
      transform: translateY(0);
    }
  }

  .slot-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    transform: translateY(100%);
    border-radius: 10rpx 10rpx 0 0;
    z-index: 1000000000;

    &__show {
      animation: transform .3s ease-in forwards;
    }

    &__head {
      height: 92rpx;
      line-height: 92rpx;
      justify-content: space-between;
      padding: 0 40rpx;

      .title {
        color: #1f2428;
        font-size: 36rpx;
        text-align: center;
        line-height: 36rpx;
      }

      .btn {
        font-size: 30rpx;
        line-height: 30rpx;

        &-cancel {
          color: #999999;
        }

        &-confirm {
          color: $uni-text-color;
        }
      }
    }

    .popup-content {
      overflow: hidden;
      padding: 40rpx 60rpx 120rpx;

      & > .fr {
        margin-bottom: 40rpx;
        width: 100%;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .week {
        display: flex;
        flex-wrap: wrap;
        max-width: 400rpx;

        .week-item {
          justify-content: center;
          width: 88rpx;
          height: 48rpx;
          margin-right: 16rpx;
          margin-bottom: 16rpx;
          background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
          color: #999999;
          font-size: 22rpx;
          border-radius: 177.78rpx;

          &:nth-child(4) {
            margin-right: 0;
          }

          &--active {
            color: $uni-text-color;
            background: linear-gradient(198.3deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
          }
        }
      }

      .label {
        color: #707070;
        line-height: 32rpx;
        font-size: 26rpx;

        &.required {
          position: relative;

          &::before {
            position: absolute;
            left: -24rpx;
            top: 5rpx;
            content: "*";
            font-size: 28rpx;
            color: #FE5031;
          }
        }
      }

      .fr-label {
        flex-shrink: 0;
        width: 230rpx;
      }

      .right-rect {
        width: 100%;
        height: 54rpx;
        border: 2rpx solid #f2f2f2;
        border-radius: 200rpx;

        c-picker-vue {
          width: 100%;
        }

        :deep() {
          .c-picker {
            background: none;
            padding: 16rpx 18rpx;
          }
        }

        .picker {
          color: #1f2428;
          font-size: 22rpx;
          line-height: 22rpx;
        }
      }

      .input {
        width: 100%;
        height: 54rpx;
        padding: 0 20rpx;
        margin-right: 16rpx;
        font-size: 22rpx;
        border: 2rpx solid #f0f1f5;
        border-radius: 200rpx;
        box-sizing: border-box;
      }

      .input-placeholder {
        font-size: 22rpx;
        color: #D6D6D6;
      }

      .input-unit {
        color: #707070;
      }

      & > .fl {
        & > .fr {
          margin-bottom: 40rpx;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .btn-group {
        border: 2rpx solid #f2f2f2;
        border-radius: 200rpx;
        height: 48rpx;
        font-size: 22rpx;
        line-height: 22rpx;

        &__item {
          justify-content: center;
          width: 122rpx;
          height: 100%;
          color: #1f2428b3;
          border-radius: 200rpx;

          &--active {
            color: #fff;
            background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          }
        }
      }

      .date-select {
        width: 406rpx;
        height: 54rpx;
        line-height: 54rpx;
        padding-right: 10rpx;
        border: 2rpx solid #f0f1f5;
        color: #000;
        border-radius: 200rpx;

        .slot-picker {
          display: flex;
          justify-content: space-between;
        }

        .picker-date {
          font-size: 22rpx;
        }

        .picker {
          flex: 1;
          text-align: center;
        }
      }

      .tip {
        width: 22rpx;
        height: 22rpx;
        margin-left: 8rpx;
        flex-shrink: 0;
      }
    }
  }
</style>