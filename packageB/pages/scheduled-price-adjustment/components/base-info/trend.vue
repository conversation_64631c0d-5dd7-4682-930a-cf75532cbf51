<template>
	<view class="fl trend">
		<view class="fr custom-tabs">
			<view class="fr" :class="{'fr--active': tabValue == item.value }" v-for="item in tabs" :key="item.value"
				@tap="onTabItemClick(item.value)">
				<text>{{ item.label }}</text>
			</view>
		</view>
		<view class="chart">
			<qiun-data-charts :loading-type="0" :canvas2d="canvas2d" type="mix" canvas-id="totalanalyse__3" :opts="opts"
				:chart-data="chartData" :tapLegend="false" :ontouch="true" tooltip-format="jshaha"></qiun-data-charts>
			<view class="fr legend" v-if="chartData.series?.length">
				<view class="fr legend-item" v-for="(item, index) in chartData.series" :key="index">
					<text class="circle" :style="{'backgroundColor': item.color}"></text>
					<text>{{ item.name }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed, unref } from 'vue';
	import { canvas2d } from '@/utils/util'

	const props = defineProps({
		data: {
			type: [Object, Array],
			default: null
		}
	})

	const tabs = Object.freeze([
		{ label: 'OCC趋势', value: 1, field: 'occData', lastField: 'lastOccData' },
		{ label: 'ADR趋势', value: 2, field: 'adrData', lastField: 'lastAdrData' },
	])
	const tabValue = ref<string | number>(tabs[0].value)
	const opts = ref({
		color: ["#1E61FC", "#C71616", "#27E657"],
		padding: [10, 5, 5, 5],
		enableScroll: true,
		fontSize: uni.upx2px(20),
		legend: {
			show: false,
		},
		dataLabel: false,
		pixelRatio: uni.getSystemInfoSync().devicePixelRatio,
		xAxis: {
			gridType: "dash",
			disableGrid: true,
			itemCount: 4,
			axisLineColor: '#F0F1F5',
			fontSize: uni.upx2px(20),
			fontColor: '#000',
			scrollAlign: 'right'
		},
		yAxis: {
			gridType: "dash",
			dashLength: 2,
			showTitle: false,
			data: [{
				fontColor: '#707070',
				fontSize: uni.upx2px(20),
				axisLine: false,
			}, {
				position: 'right',
				disabled: true
			}]
		},
		extra: {
			mix: {
				line: {
					width: uni.upx2px(2)
				}
			}
		}
	})
	const chartData = computed(() => {
		const { label, field, lastField} =  tabs.find(item => item.value == unref(tabValue))
		return {
			categories: props.data?.map(item => item.date ),
			series: [
				{
					name: label,
					type: 'line',
					index: 0,
					color: opts.value.color[0],
					data: props.data?.map(item => item[field])
				},
				{
					name: '去年同期',
					type: 'line',
					index: 0,
					color: opts.value.color[1],
					data: props.data?.map(item => item[lastField])
				}
			]
		}
	})

	/**
	 * @description: 切换指标
	 */
	const onTabItemClick = (value : string | number) => {
		tabValue.value = value
	}
</script>

<style lang="scss" scoped>
	.trend {
		overflow: hidden;
		margin: 0 28rpx;

		.custom-tabs {
			justify-content: center;
			margin-bottom: 40rpx;

			.fr {
				padding: 12rpx 25rpx;
				font-size: 24rpx;
				color: $uni-color-primary;
				border: 2rpx solid $uni-color-primary;

				&:nth-child(2) {
					border-left: none;
				}

				&--active {
					color: #fff;
					background-color: $uni-color-primary;
				}
			}
		}

		.legend {
			margin-top: 20rpx;
			justify-content: center;
			color: #444;

			&-item {
				margin-right: 60rpx;

				&:last-child {
					margin-right: 0;
				}
			}

			.circle {
				width: 12rpx;
				height: 12rpx;
				margin-right: 12rpx;
				aspect-ratio: 1;
				border-radius: 50%;
			}
		}
	}
</style>