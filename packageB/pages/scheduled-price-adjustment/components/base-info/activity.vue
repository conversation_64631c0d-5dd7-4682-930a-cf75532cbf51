<template>
	<view class="fl block">
		<view class="fr thead">
			<text class="fr-item" v-for="(item, index) in thead" :key="index">{{ item }}</text>
		</view>
		<view class="fr" v-for="item in 3">
			<text class="fr-item">直销</text>
			<text class="fr-item">新人立减</text>
			<text class="fr-item">立减</text>
			<text class="fr-item">50</text>
		</view>
	</view>
</template>

<script setup lang="ts">
	const thead = ['类型', '活动名称', '活动形式', '趋势分析']
</script>

<style lang="scss" scoped>
	.block {
		overflow: hidden;
		margin: 0 28rpx;
		border: 2rpx solid #f7f7f7;
		border-radius: 12rpx;

		.fr {
			border-bottom: 2rpx solid #F7F7F7;

			&::last-child {
				border-bottom: none;
			}

			&-item {
				width: 25%;
				padding: 16rpx 8rpx;
				font-size: 20rpx;
				text-align: center;
			}
		}

		.thead {
			font-weight: bold;
			color: #000;
			background-color: #F0F1F5;

			.fr-item {
				font-size: 22rpx;
				padding: 28rpx 0;
			}
		}
	}
</style>