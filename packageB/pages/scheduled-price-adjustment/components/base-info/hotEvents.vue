<template>
	<view class="block">
		<view class="desc-box" v-if="showContent">{{showContent}}
			<view class="label-btn theme-primary" v-if="content.length > 200" @tap="onTap">
				{{ showBtn == 'arrowdown' ? '展开' : '收起' }}<uni-icons :type="showBtn" size="12" color="#1E61FC"></uni-icons>
			</view>
		</view>
		<view class="desc-box align" v-else>
			暂无数据
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed } from 'vue'
	const props = defineProps({
		data: {
			type: [Object, Array],
			default: null
		}
	})
	const content = computed(() => props.data)
	const showBtn = ref('arrowdown')

	const onTap = () => {
		if (showBtn.value == 'arrowdown') {
			showBtn.value = 'arrowup'
		} else {
			showBtn.value = 'arrowdown'
		}
	}
	const showContent = computed(() => {
		if (content.value?.length > 200) {
			if (showBtn.value == 'arrowdown') {
				return content.value.substring(0, 200) + '...'
			} else {
				return content.value
			}
		} else {
			return content.value
		}
	})
</script>

<style lang="scss" scoped>
	.block {
		margin: 0 28rpx;
		padding: 42rpx 28rpx;
		font-size: 24rpx;
		color: #1F2428;
		border-radius: 20rpx;
    background: #f75e3b14;

		.label-btn {
			display: inline-block;
			font-size: 24rpx;
		}

		.desc-box {
			line-height: 1.5;
			
			&.align{
				text-align: center;
			}
		}
	}
</style>