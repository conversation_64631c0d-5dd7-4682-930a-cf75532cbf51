<template>
	<view class="fr block">
		<view class="fr">
			<uni-icons type="help" color="#707070" size="14" @tap="onShowModal('出租率=当日预订占房数 / 该门店客房总数')"></uni-icons>
			<view class="fr">
				<text>OCC：</text>
				<text class="font-weight">{{ data?.occData }}%</text>
			</view>
		</view>
		<view class="fr">
			<uni-icons type="help" color="#707070" size="14" @tap="onShowModal('平均房价=当日预订占房的房费 / 当日预订占房数')"></uni-icons>
			<view class="fr">
				<text>ADR：</text>
				<text class="font-weight">{{ data?.adrData }}元</text>
			</view>
		</view>
		<!-- <view class="fr">
			<uni-icons type="help" color="#707070" size="14"></uni-icons>
			<view class="fr">
				<text>分销占比：</text>
				<text class="font-weight">10.11%</text>
			</view>
		</view>
		<view class="fr">
			<uni-icons type="help" color="#707070" size="14"></uni-icons>
			<view class="fr">
				<text>直销占比：</text>
				<text class="font-weight">10.11%</text>
			</view>
		</view> -->
	</view>
</template>

<script setup lang="ts">
	import { showModal } from '@/utils/util';
	defineProps({
		data: {
			type: Object,
			default: null
		}
	})
	
	const onShowModal = (text: string) => {
		showModal({
			content: text,
			showCancel: false
		})
	}
</script>

<style lang="scss" scoped>
	.block {
		flex-wrap: wrap;
		margin: 0 40rpx;
		padding: 42rpx 28rpx 10rpx;
		font-size: 24rpx;
		color: #000;
		border-radius: 20rpx;
    background: #f75e3b14;

		&>.fr {
			width: 50%;
			margin-bottom: 32rpx;

			&>.fr {
				margin-left: 8rpx;

				.font-weight {
					font-weight: bold;
				}
			}
		}
	}
</style>