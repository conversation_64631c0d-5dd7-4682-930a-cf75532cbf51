<template>
	<view class="fl container">
		<view class="title">按房型调价</view>
    <view class="fl picker-section" v-if="listData.length > 0">
      <cPickerVue
        :columns="pickerData" keyName="roomTypeName" :value="roomTypeIndex"
        @change="onChange">
        <view class="picker picker-room-type">{{ roomTypeName }}</view>
      </cPickerVue>
    </view>
		<view class="fl list">
			<view class="list-item" v-for="item in showListData" :key="item.roomTypeCode">
				<view class="fr list-item__row room-type">
					<text class="room-type__name">{{ item.roomTypeName }}</text>
					<text class="room-type__date">{{ formatDate(item.businessDate )}}</text>
				</view>
				<view class="fr list-item__row market-price">
					<view class="fr">
						<text class="label">当前门市价：</text>
						<text class="price DIN-Bold">{{ item.rackRate }}</text>
					</view>
					<view class="fr">
						<text class="label">预订量/总房量：</text>
						<text class="number">{{ item.bookedNumber }}/{{ item.allNumber }}</text>
					</view>
				</view>
				<view class="fr list-item__row retail-price">
					<view class="fr">
						<text class="label">建议门市价：</text>
						<text class="price DIN-Bold">{{ item.suggestedRackRate || '-' }}</text>
					</view>
				</view>
				<view class="fr list-item__row btn">
					<text class="fr manual-price-adjustment" @tap="manualPriceAdjustmentEvent(item)">手动调价</text>
				</view>
			</view>
		</view>
		<emptyVue v-if="!listData.length"></emptyVue>
	</view>
	<adjustPricePopupVue v-if="show" v-model:show="show" :curData="curData" :listData="listData"
		@after-close="getValenceAnalysis" :shopId="shopId"/>
</template>

<script setup lang="ts">
	import { computed, onMounted, ref, watch } from 'vue'
	import adjustPricePopupVue from './adjust-price-popup.vue'
	import emptyVue from '@/components/empty/empty'
	import { invalidOffsetRateCode, valenceAnalysis } from '../../../api'
	import dayjs from 'dayjs'
	import { showModal } from '@/utils/util'
  import cPickerVue from '../../../components/c-picker/c-picker.vue';

	type propType = {
		date : String
    shopId : String
	}
	type listDataItem = {
		roomTypeName : string;
		roomTypeCode : string | number;
		rackRate : string | number;
		suggestedRackRate : string | number;
		bookedNumber : number;
		allNumber : number;
		businessDate : string;
		[k : string] : any
	}
	const props = withDefaults(defineProps<Partial<propType>>(), {})

	const listData = ref<Partial<listDataItem[]>>([])
	const roomTypeIndex = ref(0)
	const show = ref(false)
	const curData = ref<Partial<listDataItem>>({})

	const pickerData = computed(() => {
		return [{ roomTypeName: '全部房型', roomTypeCode: 'ALL' }, ...listData.value]
	})
	/**
	 * @description :显示的房型名称
	 */
	const roomTypeName = computed(() => {
		return pickerData.value?.[roomTypeIndex.value]?.roomTypeName
	})
	/**
	 * @description: 显示的房型列表
	 */
	const showListData = computed(() => {
		const currentRoomTypeCode = pickerData.value?.[roomTypeIndex.value]?.roomTypeCode
		return currentRoomTypeCode != 'ALL' ? listData.value?.filter(item => item.roomTypeCode == currentRoomTypeCode) : listData.value
	})
	/**
	 * @description : 格式化日期格式
	 */
	const formatDate = (date : string) => {
		return date?.replace(/\-/g, '.')
	}
	/**
		* @description: 获取门店房型价量分析列表
		*/
	const getValenceAnalysis = async (date ?: string) : Promise<void> => {
		try {
			const res = await valenceAnalysis({
				businessDate: date || props.date,
        shopId: props.shopId
			})
			listData.value = res.data || []
		} catch (e) {
			console.log('门店房型价量分析列表')
		}
	}
	/**
	 * @description : 选择房型
	 */
	const onChange = (e : any) => {
    console.log(e)
		roomTypeIndex.value = e.indexs[0]
	}
	/**
	 * @description: 手动调价
	 */
	const manualPriceAdjustmentEvent = async (item : any) => {
		try {
			const res = await invalidOffsetRateCode({shopId: props.shopId})
			if (res.data.length == 0) {
				curData.value = { ...item }
				show.value = true
			} else {
				showModal({
					content: `系统未获取到【${res.data.join(',')}】价格码的偏移值，继续操作则默认将门市价价格写入未设置偏移值的价格码中；取消操作请前往PMS房价代码设置菜单设置偏移值`,
					success(res) {
						if (res.confirm) {
							curData.value = { ...item }
							show.value = true
						}
					}
				})
			}
		} catch (e) {
			console.log('门店是否有未设置偏移值的价格码接口报错', e)
		}
	}

	watch(() => props.date, (v : string) => {
		if (v) {
			getValenceAnalysis()
		}
	}, { immediate: true })

	defineExpose({
		getValenceAnalysis
	})
</script>

<style scoped lang="scss">
	.container {
		padding: 40rpx 0;
		background-color: #fff;
    border-radius: 20rpx;

		.title {
			margin-bottom: 40rpx;
			margin-left: 40rpx;
			margin-right: 40rpx;
			font-weight: bold;
			color: #1F2428;
		}

		.picker-section {
      border-radius: 200rpx;
      border: 2rpx solid #f2f2f2;
      margin: 0 40rpx 20rpx;

      .picker-room-type {
        color: #1f2428;
        font-size: 22rpx;
        line-height: 22rpx;
      }
		}

		.list {
			margin: 0 40rpx;

			&-item {
				padding: 28rpx;
				margin-bottom: 20rpx;
        background: #f75e3b14;
				border-radius: 18.31rpx;

				&:last-child {
					margin-bottom: 0;
				}

				&__row {
					justify-content: space-between;

					.label {
						font-size: 24rpx;
						color: #000;
					}

					.number {
						font-size: 24rpx;
						font-weight: bold;
					}

					.price {
						font-size: 26rpx;
						color: #000;

						&::before {
							content: "￥";
							font-size: 16rpx;
						}
					}

					&.btn {
						justify-content: flex-end;
					}
				}

				.room-type {
					align-items: baseline;
					margin-bottom: 32rpx;
				}

				.market-price {
					margin-bottom: 24rpx;
				}

				.retail-price {
					margin-bottom: 30rpx;
				}
			}

			.manual-price-adjustment {
				justify-content: center;
				align-self: flex-end;
				width: 170rpx;
				height: 60rpx;
				font-size: 24rpx;
				color: #fff;
				border-radius: 177.78rpx;
        background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
			}

			.room-type__name {
				font-size: 34rpx;
				font-weight: bold;
			}

			.room-type__date {
				font-size: 24rpx;
				font-weight: bold;
			}
		}
	}
</style>