<!--
 * @Description: 日历组件
 * @Author: gu_shiyuan"
 * @Date: 2025-02-08 10:53:03
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2025-02-08 10:53:03
 * @FilePath: /smart-marketing-manager/report/pages/scheduled-price-adjustment/components/calendar/calendar.vue
-->

<template>
	<view class="content" @touchstart="ontouchstart" @touchmove="ontouchmove" @touchend="ontouchend">
		<view class="calendar">
			<view class="week">
				<view class="week-item" v-for="item in weeks" :key="item.value">
					<text>{{ item.label }}</text>
				</view>
			</view>
			<view class="date">
				<slot :data="dateList">
					<view class="date-item" :class="{'date-item--active': item.label == currentDate}" v-for="item in dateList"
						:key="item.label"  @tap="onClick(item)">
						<view class="date-item__cell" :style="item.parent">
              <view class="date-item__wrap" :style="item.children">
                <text class="date-text">{{ item.value }}</text>
                <text class="first-few">{{ item.firstFew}}</text>
                <text class="is-work" v-if="item.isWork != undefined">{{ item.isWork ? '班':'休'}}</text>
                <template v-if="Object.keys(item).length">
                  <view class="dot-group" v-if="item.events?.length">
                    <template v-for="(r, i) in events" :key="i">
                      <text v-if="item.events?.includes(r.value)" class="dot" :style="{background: r.bgColor}"></text>
                    </template>
                  </view>
                </template>
              </view>
            </view>
					</view>
				</slot>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	/**
	 * @param
	 * 	shujiu: 日期是否数九
	 *  events： 事件
	 *  value： 当前日期
	 *  month： 当前月
	 *  allowTouch： 允许左右滑动
	 *  allowTouchLeft： 允许左滑动
	 *  allowTouchRight： 允许右滑动
	 */
	import dayjs from 'dayjs';
	import { Solar, HolidayUtil, Lunar, LunarYear } from 'lunar-typescript'
	import { onMounted, ref, watch } from 'vue'
	import type { eventType, propType, listItem} from './type'

	const props = withDefaults(defineProps<propType>(), {
		events: () => [
			{ label: '营销活动提醒', bgColor: '#FFC300', value: 'activity' },
			{ label: '法定节假日', bgColor: '#F75E3B', value: 'holiday' },
			{ label: '周边热点事件', bgColor: '#1474FB', value: 'event' }
		],
		shujiu: true,
		value: dayjs().format('YYYY-MM-DD'),
		month: dayjs().format('YYYY-MM'),
		allowTouch: true,
		allowTouchLeft: true,
		allowTouchRight: true
	})

	const emits = defineEmits<{
		(e : 'update:value', value ?: any) : void,
		(e : 'change:value', value ?: any) : void,
		(e : 'update:month', value ?: any) : void,
		(e : 'change:month', value ?: any) : void
	}>()

	const weeks = Object.freeze([
		{ label: '一', value: 1 },
		{ label: '二', value: 2 },
		{ label: '三', value: 3 },
		{ label: '四', value: 4 },
		{ label: '五', value: 5 },
		{ label: '六', value: 6 },
		{ label: '日', value: 0 },
	])
	const numberMap = new Map([[1, '一'], [2, '二'], [3, '三'], [4, '四'], [5, '五'], [6, '六'], [7, '七'], [8, '八'], [9, '九'], [10, '十'], [11, '十一'], [12, '十二']])
	const dateList = ref<listItem[]>([])
	const currentDate = ref(props.value)
	const currentMonth = ref(props.month)
	/**
	 * @description: 获取闰月
	 */
	const getLeapMonth = (year : number) => {
		return LunarYear.fromYear(year).getLeapMonth()
	}
	/**
	 * @description: 月初是周几
	 */
	const whatDayIsTheBeginningOfTheMonth = (date : Date = new Date()) => {
		return dayjs(date).startOf('month').day()
	}
	/**
	 * @description: 当前月多少天
	 */
	const howManyDaysInAMonth = (date : Date = new Date()) => {
		return dayjs(date).daysInMonth()
	}
	/**
	 * @description: 当前日期初几
	 */
	const currentInitialDate = (date : Date = new Date(), label : string) => {
		const lunar = Lunar.fromDate(date);
		const leapMonth = getLeapMonth(lunar.getYear())
		if (leapMonth > 0) {
			const name = numberMap.get(leapMonth) + '月初一'
			if (lunar.toString().indexOf(name) > -1) {
				return '闰' + numberMap.get(leapMonth) + '月'
			}
		}
		if (lunarSpecialFestivals(lunar.toString())) return lunarSpecialFestivals(lunar.toString())
		if (specialFestivalsInTheGregorianCalendar(label)) return specialFestivalsInTheGregorianCalendar(label)
		return Lunar.fromDate(date).getDayInChinese()
	}
	/**
	 * @description: 获取当月所有节假日信息
	 */
	const obtainInformationOnStatutoryHolidaysAndDates = (date : Date = new Date()) => {
		try {
			const d = Solar.fromDate(date)
			return HolidayUtil.getHolidays(d.getYear(), d.getMonth())
		} catch (err) {
			console.log("🚀 ~ file: calendar.vue:90 ~ obtainInformationOnStatutoryHolidaysAndDates ~ err:", err)
		}
	}
	/**
	 * @description: 获取节气
	 */
	const obtainSolarTerms = (date : Date = new Date()) => {
		try {
			const d = Lunar.fromDate(date);
			return d.getJieQi()
		} catch (err) {
			console.log("🚀 ~ obtainSolarTerms ~ err:", err)
		}
	}
	/**
	 * @description: 日期相差天数
	 */
	const days = (startDate : string, endDate : string) => {
		return dayjs(endDate).diff(dayjs(startDate)) / (1000 * 60 * 60 * 24)
	}
	/**
	 * @description: 详细节假日信息
	 */
	const getDateAndHolidayInformation = (date : string) => {
		try {
			const d = HolidayUtil.getHoliday(date)
			const lunar = Lunar.fromDate(new Date(date));
			const lunarSpecialFestival = lunarSpecialFestivals(lunar.toString())
			const Calendar = specialFestivalsInTheGregorianCalendar(date)
			return d ? {
				date: d.getDay(),
				isWork: d.isWork(),
				name: lunarSpecialFestival ? lunarSpecialFestival : Calendar ? Calendar : d.getName() == '春节' ? (days(d.getDay(), d.getTarget()) == 1 ? '除夕' : d.getName()) : d.getName(),
				target: d.getTarget()
			} : d
		} catch (err) {
			console.log("🚀 ~ getDateAndHolidayInformation ~ err:", err)
		}
	}
	/**
	 * @description: 设置日期颜色
	 */
	const setDateColor = (list : any[] = [], level : string) => {
		dateList.value = dateList.value.map(item => {
			return {
				...item,
				[level]: list.find(r => r.label == item.label)?.style
			}
		})
	}
	/**
	 * @description: 设置日期事件
	 */
	const setDateEvents = (list : any[] = []) => {
		dateList.value = dateList.value.map(item => {
			return {
				...item,
				events: list.find(e => e.label == item.label)?.events.concat(item.events) || []
			}
		})
	}
	/**
	 * @description: 覆盖dateList
	 */
	const setDateList = (list : any[]) => {
		dateList.value = [...list]
	}
	/**
	 * @description: 找出春节日期
	 */
	const findTheSpringFestivalDate = (date : Date = new Date()) => {
		const holidays = obtainInformationOnStatutoryHolidaysAndDates(date)
		const holiday = holidays.find(item => item.getDay() == item.getTarget() && item.getName() == '春节')
		return holiday?.getDay()
	}
	/**
	 * @description: 找出南小年日期
	 */
	const findTheDateOfNanxiaonian = (date : Date = new Date()) => {
		// const springFestival = findTheSpringFestivalDate(date)
		// if (springFestival) {
		// 	const d = Solar.fromDate(new Date(springFestival))
		// 	return dayjs(new Date(d.getYear() + '-' + d.getMonth() + '-' + '23')).format('YYYY-MM-DD')
		// }
    const d = Lunar.fromDate(date);
    // 转阳历
    const lunar = Lunar.fromYmd(d.getYear(), 12, 24);
    return lunar.getSolar().toString()
	}
	/**
	 * @description: 找出北小年日期
	 */
	const findTheDateOfBeixiaonian = (date : Date = new Date()) => {
		// const springFestival = findTheSpringFestivalDate(date)
		// if (springFestival) {
		// 	const d = Solar.fromDate(new Date(springFestival))
		// 	return dayjs(new Date(d.getYear() + '-' + d.getMonth() + '-' + '22')).format('YYYY-MM-DD')
		// }
    const d = Lunar.fromDate(date);
    // 转阳历
    const lunar = Lunar.fromYmd(d.getYear(), 12, 23);
    return lunar.getSolar().toString()
	}
	/**
	 * @description: 农历特殊节日
	 */
	const lunarSpecialFestivals = (dateStr : string) => {
		if (dateStr.indexOf('正月十五') > -1) {
			return '元宵节'
		}
		if (dateStr.indexOf('八月十五') > -1) {
			return '中秋节'
		}
		if (dateStr.indexOf('九月初九') > -1) {
			return '重阳节'
		}
		if (dateStr.indexOf('腊月初一') > -1) {
			return '腊月'
		}
	}
	/**
	 * @description: 阳历特殊节日
	 */
	const specialFestivalsInTheGregorianCalendar = (dateStr : string) => {
		if (dateStr.indexOf('03-08') > -1) {
			return '妇女节'
		}
		if (dateStr.indexOf('10-01') > -1) {
			return '国庆节'
		}
		if (dateStr.indexOf('12-25') > -1) {
			return '圣诞节'
		}
	}

	/**
	 * @description: 创建日历
	 */
	const createCalendar = (date : Date = new Date()) => {
		const list = []
		const days = howManyDaysInAMonth(date)
		const d = Solar.fromDate(date)
		const nanXiaonian = findTheDateOfNanxiaonian(date)
		const beiXiaonian = findTheDateOfBeixiaonian(date)
		for (let i = 1; i <= days; i++) {
			const label = dayjs(d.getYear() + '-' + d.getMonth() + '-' + i).format('YYYY-MM-DD')
			const firstFew = currentInitialDate(new Date(label), label)
			const data : any = getDateAndHolidayInformation(label)
			const solarTerms = obtainSolarTerms(new Date(label))
			list.push({
				value: i,
				label: label,
				firstFew: solarTerms ? solarTerms : (data ? (data.date == data.target || data.name == '除夕' ? data.name : (nanXiaonian && nanXiaonian == label ? '南小年' : beiXiaonian && beiXiaonian == label ? '北小年' : firstFew)) : (nanXiaonian && nanXiaonian == label ? '南小年' : beiXiaonian && beiXiaonian == label ? '北小年' : firstFew)),
				isWork: data?.isWork,
				whatDayIsToday: weeks.find(item => item.value == new Date(label).getDay())?.label,
        events: data?.isWork === false ? ['holiday'] : [],
			})
		}
		const fillIndex = weeks.findIndex(item => item.value == whatDayIsTheBeginningOfTheMonth(date))
		let i = 0
		while (i < fillIndex) {
			list.unshift({})
			i++
		}
		console.log("🚀 ~ createCalendar ~ list:", list)
		return list
	}
	/**
	 * @description 点击日期
	 */
	const onClick = (item : any) => {
		if (!item.label || item.disabled) return
		currentDate.value = item.label
		emits('update:value', item.label)
		emits('change:value', item.label)
	}
	let startX = 0, distance = 100
	/**
	 * @description: 滑动开始
	 */
	const ontouchstart = (e : any) => {
		if (!props.allowTouch) return
		startX = e.changedTouches[0].pageX
	}
	/**
	 * @description: 滑动中
	 */
	const ontouchmove = (e : any) => {
		if (!props.allowTouch) return
	}
	/**
	 * @description: 滑动结束
	 */
	const ontouchend = (e : any) => {
		if (!props.allowTouch) return
		const endX = e.changedTouches[0].pageX
		if (endX > startX) { //向右
			if(!props.allowTouchLeft) return
			if (endX - startX > distance) {
        onPrevMonth(currentMonth.value)
			}
		} else { //向左
			if(!props.allowTouchRight) return
			if (startX - endX > distance) {
        onNextMonth(currentMonth.value)
			}
		}
	}
	/**
	 * @description: 上个月
	 */
	const onPrevMonth = (date : string) => {
		const prevMonth = dayjs(date).subtract(1, 'month').format('YYYY-MM')
		dateList.value = createCalendar(new Date(prevMonth))
		currentMonth.value = prevMonth
		emits('update:month', prevMonth)
		emits('change:month', prevMonth)
	}
	/**
	 * @description : 下个月
	 */
	const onNextMonth = (date : string) => {
		const nextMonth = dayjs(date).add(1, 'month').format('YYYY-MM')
		dateList.value = createCalendar(new Date(nextMonth))
		currentMonth.value = nextMonth
		emits('update:month', nextMonth)
		emits('change:month', nextMonth)
	}
	watch(() => props.month, v => {
		if (v) {
			currentMonth.value = v
		}
	}, { immediate: true })

	watch(() => props.value, v => {
		if (v) {
			currentDate.value = v
		}
	}, { immediate: true })

	dateList.value = createCalendar()


	defineExpose({
		dateList,
		createCalendar,
		setDateList,
		setDateEvents,
		setDateColor,
		onPrevMonth,
		onNextMonth
	})
</script>

<style scoped lang="scss">
	.calendar {
	}

	.week {
		display: flex;
		flex-wrap: wrap;
    padding: 0 32rpx;

		&-item {
			display: flex;
			justify-content: center;
			align-items: center;
			width: calc(100% / 7);
			height: 62rpx;
			font-size: 26rpx;
			color: #1F2428;
		}
	}

	.date {
		display: flex;
		flex-wrap: wrap;
    padding: 0 32rpx;

		.date-item {
			display: flex;
			justify-content: center;
			align-items: center;
			width: calc(100% / 7);
			font-size: 26rpx;
			color: #1F2428;
			margin-bottom: 10rpx;
      position: relative;
      height: 110rpx;
		}

		.date-item__wrap {
			position: absolute;
			display: flex;
      width: 72rpx;
      height: 100%;
			align-items: center;
			flex-direction: column;
			padding-top: 20rpx;
      left: 50%;
      transform: translate(-50%);

			.date-text {
				margin-bottom: 4rpx;
				font-size: 36rpx;
        line-height: 36rpx;
				font-weight: bold;
				color: #1F2428;
				font-family: 'DIN Bold';
			}

			.first-few {
				margin-bottom: 12rpx;
				font-size: 20rpx;
        line-height: 20rpx;
				color: #1F2428;
			}

			.is-work {
				position: absolute;
				right: 2rpx;
				top: 8rpx;
				font-size: 18rpx;
        line-height: 18rpx;
				color: #1F2428;
			}
		}

    .date-item__cell{
      height: 110rpx;
      width: 100%;
    }

		.date-item--active {

			.date-item__wrap {
				background-color: #F75E3B;
				border-radius: 10rpx;
			}

			.first-few {
				color: #fff;
			}

			.date-text {
				color: #fff;
			}

			.dot {
				border: 2rpx solid #fff!important;
        width: 12rpx!important;
        height: 12rpx!important;
			}

			.is-work {
				color: #fff;
			}
		}

		.dot-group {
			flex: 1;
			display: flex;
			justify-content: center;

			.dot {
				width: 8rpx;
				height: 8rpx;
				margin-right: 8rpx;
				border: 2rpx solid transparent;
				border-radius: 50%;

				&:last-child {
					margin-right: 0;
				}
			}
		}
	}
</style>