export type eventType = {
	label : string;
	bgColor : string;
	value : string;
}
export type propType = {
	events : eventType[],
	shujiu : boolean,
	value : string;
	month : string;
	allowTouch : boolean,
	allowTouchLeft: boolean,
	allowTouchRight: boolean,
	[k : string] : any
}

export type listItem = {
	value : any;
	label : string;
	firstFew : string;
	isWork : boolean | undefined;
	events : string[],
	parent ?: any;
	children ?: any;
	disabled ?: boolean;
	whatDayIsToday ?: string;
}