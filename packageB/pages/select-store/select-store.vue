<template>
	<view class="container">
		<view class="search">
      <view class="search-section">
        <input class="uni-input" v-model="keyWord" placeholder="请输入门店名字/门店ID搜索" shape="circle" border="none"
          placeholder-class="placeholderClass" />
        <view class="button-group fr">
          <view class="custom-btn custom-btn-primary btn" @tap="search">搜索</view>
        </view>
      </view>
		</view>
		<view class="area-picker">
			<cAreaPicker :defaultName="areaData.orgName" @setArea="setArea">
				<template #right>
				</template>
			</cAreaPicker>
		</view>
		<view class="total" v-if="from === 'shop'">共{{total}}个门店</view>
		<view class="list">
			<scroll-view :style="{height:height}" scroll-y="true" @scrolltolower="scrolltolower">
				<view class="item" v-if="list && list.length > 0 " v-for="(item,index) in list" :key="index"
					@click="navigatorTo(item)">
					<view style="margin-bottom: 20rpx;text-align: right;">
						<view class="shop-id">门店ID：{{item.shopId}}</view>
					</view>
					<view class="info fr">
						<view class="">
							<view class="region fr">
								<view style="flex-shrink:0;color:rgba(31, 36, 40, 0.7);">大区：</view>
								<view class="text">{{item.regionName}}</view>
							</view>
							<view class="area fr">
								<view style="flex-shrink:0;color:rgba(31, 36, 40, 0.7);">城区：</view>
								<view class="text">{{item.areaName}}</view>
							</view>
							<view class="shop-name fr">
								<view style="flex-shrink:0;color:rgba(31, 36, 40, 0.7);">门店：</view>
								<view class="text">{{item.shopName}}</view>
							</view>
						</view>
						<up-icon name="arrow-right" color="#999999" size="22rpx" />
					</view>
				</view>
				<view class="empty" v-else>
					<emptyVue />
				</view>
			</scroll-view>
		</view>
    <cWaterMark></cWaterMark>
	</view>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, ref } from 'vue';
	import cAreaPicker from '@/components/c-area-picker/index.vue'
	import { OrgLevel } from '@/types/dataCockpit';
	import { useCommon } from '@/hooks/useGlobalData';
	import { listShop } from '../../api';
	import emptyVue from '@/components/empty/empty.vue';
	import { PaginationParams } from '@/types/index.d.ts';

	type ListShop = {
		regionName : string
		areaName : number
		shopId : string
		shopName : string
	}[]

	const { globalData, route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance()

	const height = ref()
	const keyWord = ref<string>(), list = ref<ListShop>()
	const { deptName, orgName, ...rest } = globalData.value.user.orgList[0]; // 用户最大权限第一条
	const areaData = ref<OrgLevel>({ // 区域数据
		...rest
	})
	const from = ref<string>() // 来源(页面)
	const maxDate = ref<string>() // 财务的最大时间

	const pagination = ref<PaginationParams>({
		pageNum: 1,
		pageSize: 20
	})

	const loadMore = ref<Boolean>(true)
	const total = ref<number>() // 门店数

	onLoad((options : any) => {
		from.value = options.from ?? ''
		maxDate.value = options.maxDate ?? ''
		if (options.orgId) {
			setArea(options.orgId, options.level, options.orgName)
		} else {
			toListShop()
		}
	})

	// 区域筛选
	const setArea = (id : number, level : string, name?: string) => {
		areaData.value.orgId = id.toString()
		areaData.value.level = Number(level)
		areaData.value.orgName = name
		reset()
		toListShop()
	}

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.list`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})

	// 获取门店列表
	const toListShop = async (keyWord = '') => {
		try {
			const params = {
				...areaData.value,
				keyWord: keyWord,
				...pagination.value
			}
			const { data } = await listShop(params)
			if (data.records && data.records.length > 0) {
				total.value = data.total
				if (data.records.length < pagination.value.pageSize) loadMore.value = false
				nextTick(() => {
					if (list.value && list.value.length > 0)
						list.value.push(...data.records)
					else
						list.value = data.records
				})
			} else {
				list.value = []
				total.value = 0
			}
		} catch (e) {
			console.error('获取门店列表 error', e)
		}
	}

	const search = () => {
		reset()
		toListShop(keyWord.value)
	}

	const scrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		toListShop()
	}

	const reset = () => {
		loadMore.value = true
		list.value = []
		pagination.value = {
			pageNum: 1,
			pageSize: 20
		}
	}

	const navigatorTo = (item : ListShop[number]) => {
		if (from.value === 'public-sentiment') { // 舆情->单点舆情
			route_to_view(`/packageB/pages/public-opinion-analysis/public-opinion-analysis?shopId=${item.shopId}`)
		} else if (from.value === 'store-data-report' || from.value === 'seivice-data-report') { // 单店运营报表 | 门店服务数据报告
			const pages = getCurrentPages()
			const prevPage : any = pages[pages.length - 2]
			prevPage.shop = item
			uni.navigateBack()
		} else if (from.value === 'shop') { // 门店监控
			route_to_view(`/packageB/pages/shop/shop?shopId=${item.shopId}&shop=${JSON.stringify(item)}`)
		} else if (from.value === 'file') { // 门店档案
			route_to_view(`/packageA/pages/file/index?shopId=${item.shopId}`)
		} else if (from.value === 'finance') { // 财务单店
			route_to_view(`/packageA/pages/finance/shop?shopId=${item.shopId}&shopName=${item.shopName}&maxDate=${maxDate.value}`)
		} else if (from.value === 'scheduled-price-adjustment') { // 营销日历
      route_to_view(`/packageB/pages/scheduled-price-adjustment/scheduled-price-adjustment?shopId=${item.shopId}`)
		} else if (from.value === 'operations' || from.value === 'member') { // 会员/运行单店
			route_to_view(`/packageB/pages/sales-operation-report/sales-operation-report?shopId=${item.shopId}&tabValue=${from.value === 'operations' ? 0 : 1}`)
		}
	}
</script>

<style lang="scss" scoped>
	.container {}

	:deep() {
		.area-left {
			background: #ffffff !important;
		}
	}

	.area-picker {
		padding: 0 20rpx;
		margin-bottom: 40rpx;
	}

	.search {
		background: #ffffff;
		padding: 20rpx;
		margin-bottom: 40rpx;
	}

  .search-section {
    background: #f2f2f2;
    padding: 8rpx 8rpx 8rpx 50rpx;
    border-radius: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    :deep() {
      .placeholderClass {
        color: #999999;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 24rpx;
      }

      .uni-input {
        width: 500rpx;
      }
    }

    .button-group {
      .btn {
        width: auto;
        padding: 12rpx 28rpx;
        margin: 0 0 0 2rpx;
        font-size: 24rpx;
        font-weight: bold;
        line-height: 24rpx;
      }
    }
  }

	.total {
		padding: 0 20rpx;
		margin-left: 40rpx;
		color: #1f2428b3;
		font-size: 24rpx;
		font-weight: bold;
		line-height: 24rpx;
		margin-bottom: 20rpx;
	}

	.list {
		padding: 0 20rpx 40rpx;

		.item {
			border-radius: 20rpx;
			background: #ffffff;
			justify-content: space-between;
			margin-bottom: 20rpx;
			padding-bottom: 40rpx;

			.shop-id {
				display: inline-block;
				background: linear-gradient(203.5deg, #ffb2a10d 0%, #f75e3b33 100%);
				border-radius: 100rpx 100rpx 100rpx 0;
				color: #c35943;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;
				padding: 8rpx 20rpx;
				margin-top: 20rpx;
			}

			.info {
				justify-content: space-between;
				padding: 0 40rpx;

				.region,
				.area {
					margin-bottom: 22rpx;
				}

				.region,
				.area,
				.shop-name {
					color: #1f2428;
					font-size: 28rpx;
					line-height: 38rpx;
					align-items: flex-start;
				}

				.text {
					color: #1F2428 !important;
					font-size: 28rpx;
					font-weight: bold;
					line-height: 38rpx;
					max-height: 490rpx;
					word-break: break-all;
				}
			}
		}
	}

	.empty {
		background: #ffffff;
		border-radius: 20rpx;
	}
</style>