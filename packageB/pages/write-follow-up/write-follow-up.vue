<template>
	<view class="w-container">
		<commonBgVue height="304" />
		<u-navbar placeholder title="写跟进" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx;font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="content">
			<view class="fr block">
				<text class="block-title">内容标题：</text>
				<input v-model="form.followTitle" type="text" placeholder="请填写跟进内容标题(仅限10个字)" class="block-title__input"
					placeholder-style="font-size: 26rpx;color:#999;" maxlength="10" style="width: 380rpx;" />
			</view>
			<view class="fl block">
				<text class="block-title">跟进内容：</text>
				<cTextareaVue v-model="form.follow" placeholder='请填写跟进内容描述' count maxlength="100"
					background="linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%)" />
			</view>
		</view>
		<fixedBtnLayoutVue>
			<view class="fr btn" @tap="submit">确认</view>
		</fixedBtnLayoutVue>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { getCurrentInstance, ref } from 'vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue'
	import fixedBtnLayoutVue from '../../components/fixed-btn-layout/fixed-btn-layout.vue'
	import cTextareaVue from '../../components/c-textarea/c-textarea.vue';
	import { showToast } from '@/utils/util'
	import { recordFollow } from '../../api'
	import { useCommon } from '@/hooks/useGlobalData';
	const { setRouteParams } = useCommon(getCurrentInstance())

	type formType = {
		followTitle : string;
		follow : string;
	}
	const tripId = ref('')
	const shopId = ref('')
	const form = ref<Partial<formType>>({})
	/**
	 * @description: 校验
	 */
	const validate = async () => {
		let success = true
		const validateRules = {
			followTitle: { required: true, message: '请填写内容标题' },
			follow: { required: true, message: '请填写跟进内容' }
		}
		for (let [key, value] of Object.entries(validateRules)) {
			if (!form.value[key] && value.required) {
				showToast(value.message)
				success = false
				break;
			}
		}
		return success
	}
	/**
	 * @description: 提交跟进
	 */
	const submit = async () : Promise<void> => {
		const res = await validate()
		if (res) {
			await recordFollow({
				...form.value,
				tripId: tripId.value
			})
			showToast({
				title: '添加成功',
				success: () => {
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						setRouteParams('packageB/pages/shop-inspection/shop-inspection-trip')
						uni.navigateBack({
              fail: (err: any) => {
                console.error(err)
              }
            })
					}, 1000)
				}
			})
		}
	}
	onLoad((opts) => {
		tripId.value = opts.tripId
	})
</script>

<style lang="scss" scoped>
	.w-container {
		.content {
			padding-top: 44rpx;
			margin: 0 20rpx;

			.block {
				padding: 40rpx;
				margin-bottom: 20rpx;
				background-color: #fff;
				border-radius: 20rpx;

				&.fr {
					justify-content: space-between;
				}

				&-title {
					font-size: 26rpx;
					font-weight: bold;
					color: #1F2428;

					&__input {
						text-align: right;
					}
				}

				&.fl {
					.block-title {
						margin-bottom: 40rpx;
					}
				}

				:deep() {
					.textarea {
						font-size: 24rpx;
					}
				}
			}
		}

		.btn {
			justify-content: center;
			width: 670rpx;
			height: 80rpx;
			margin: auto;
			color: #fff;
			border-radius: 200rpx;
			background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
		}
	}
</style>