<template>
	<view class="container fl">
		<view class="user" v-if="prevTypeList.length < 1">
			<userInfoVue />
		</view>
		<view class="back-block" v-if="prevTypeList.length >= 1">
			<view class="back fr" @click="back">
				<up-icon name="arrow-left" color="#000" size="20rpx" />
				<text style="margin-left: 8rpx;">返回上一级</text>
			</view>
		</view>
		<permissionItemVue :data="dataList" @click="toQueryStaffRegion" />
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { ref, nextTick, unref, onMounted } from 'vue';
	import userInfoVue from './components/user-info/user-info.vue';
	import permissionItemVue from './components/permission-item/permission-item.vue';
	import { queryStaffRegion } from '@/packageB/api';

	type RegionItem = {
		orgName : string,
		orgId : string,
		level : string,
		code : string
	}

	const type = ref<string>() // 当前级别
	const prevTypeList = ref<string[]>([]) // 记录已经获取级别
	const originalData = ref<any>({})
	const dataList = ref<RegionItem[]>([])

	onMounted(() => {
		toQueryStaffRegion()
	})

	// 获取员工区域
	const toQueryStaffRegion = async (item ?: RegionItem) => {
		if (type.value === 'shop') return
		try {
			const { data } = await queryStaffRegion({
				orgId: item?.orgId,
				level: item?.level
			})
			if (data && data.length > 0) {
				if (item) {
					prevTypeList.value.push(item.code)
					originalData.value[item.code] = dataList.value || []
				}
				nextTick(() => {
					type.value = data[0].code
					dataList.value = data
				})
			}
		} catch (e) {
			console.error('获取员工区域 error', e)
		}
	}

	// 返回上一级
	const back = () => {
		nextTick(() => {
			const prevType : any = [prevTypeList.value[prevTypeList.value.length - 1]]
			type.value = prevType
			dataList.value = unref(originalData)[prevType]
			prevTypeList.value.pop()
			originalData.value[prevType] = []
		})
	}
</script>

<style scoped lang="scss">
	.user {
		margin-bottom: 40rpx;
	}

	.back-block {
		margin-left: 20rpx;
		padding-top: 40rpx;
		padding-bottom: 40rpx;
		background: #efefef;
		position: sticky;
		top: 0;

		.back {
			width: 158rpx;
			height: 38rpx;
			border-radius: 200rpx;
			border: 1rpx solid #ffffff;
			background: linear-gradient(90deg, #ffffff 0%, #ffffff4d 100%);
			justify-content: center;


			.img {
				width: 8rpx;
				height: 14.4rpx;
				flex-shrink: 0;
				transform: rotate(180deg);
				margin-right: 8rpx;
			}

			text {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
			}
		}
	}
</style>