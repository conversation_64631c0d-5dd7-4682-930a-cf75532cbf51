<template>
	<view class="container">
		<template v-if="data && data.length > 0" v-for="(item, index) in data" :key="index">
			<view class="fr item" @click="$emit('click',item)">
				<view class="fr">
					<image class="folder"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-10/1728555725832.png"
						mode=""></image>
					<text v-if="item.code !== 'shop'" class="shop-name">{{item.orgName}}</text>
					<text v-else class="shop-name">{{item.orgId}} | {{item.orgName}}</text>
				</view>
				<up-icon v-if="item.code !== 'shop'" name="arrow-right" color="#999999" size="22rpx" />
			</view>
		</template>
		<view class="empty" v-else>
			<empty hasBackground/>
		</view>
	</view>
</template>

<script setup lang="ts">
	import empty from '@/components/empty/empty.vue'
	type RegionItem = {
		orgName : string,
		orgId : string,
		level : string,
		code : string
	}
	withDefaults(
		defineProps<{
			data : RegionItem[],
		}>(),
		{
			data: () => []
		}
	)

	defineEmits(['click'])
</script>

<style scoped lang="scss">
	.container {
		padding: 0 20rpx 40rpx;
	}

	.item {
		justify-content: space-between;
		padding: 34rpx 40rpx;
		border-radius: 20rpx;
		border: 2rpx solid #ffffff;
		background: #ffffff;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.folder {
			width: 52rpx;
			height: 39.88rpx;
			flex-shrink: 0;
		}

		.shop-name {
			margin-left: 40rpx;
			color: #1f2428;
			font-size: 28rpx;
			line-height: 38rpx;
		}

		.img {
			width: 12rpx;
			height: 21.6rpx;
			flex-shrink: 0;
		}
	}
</style>