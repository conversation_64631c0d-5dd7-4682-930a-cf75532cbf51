<template>
	<view class="bg"></view>
	<view class="container fr">
		<view class="fr user">
			<image class="img" :src="globalData.user.avatar" mode=""></image>
			<view class="fl info">
				<view class="name">{{globalData.user.userName}}</view>
				<view class="id">{{globalData.user.userId}}</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance, ComponentInternalInstance } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	const { globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
</script>

<style lang="scss" scoped>
	.container {
		padding: 40rpx 20rpx 0;
		justify-content: space-between;
		align-items: flex-start;
	}

	.bg {
		width: 100%;
		height: 392rpx;
		position: absolute;
		background: linear-gradient(180deg, #ffffff 0%, #fafafa00 100%);
		z-index: -1;
	}

	.user {
		.img {
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			flex-shrink: 0;
		}

		.info {
			margin-left: 40rpx;

			.name {
				color: #1f2428;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 36rpx;
				margin-bottom: 28rpx;
			}

			.id {
				color: #1f2428;
				font-size: 24rpx;
				line-height: 24rpx;
			}
		}
	}
</style>