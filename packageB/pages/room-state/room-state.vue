<template>
	<view>
		<view class="fl main">
			<!-- 搜索框 -->
			<view class="search-wrap">
				<view class="outline">
					<view class="fr search">
						<input type="text" v-model="state.roomCode" placeholder="输入房间号搜索"
							placeholder-style="color:#979A9A;font-size: 24rpx;" />
						<text class="fr btn-search" @click="search">查询</text>
					</view>
				</view>

				<!-- 筛选类型 -->
				<view class="fr selector">
					<!-- 选择楼层 -->
					<view class="item">
						<picker class="label" :range="floors" @change="selectFloor" :value="state.floorindex">
							<view class="fr label-content">
								<text>{{ floors?.[state.floorindex] || '楼层'}}</text>
								<uni-icons type="arrowdown" size="12" color="#979A9A"></uni-icons>
							</view>
						</picker>
					</view>
					<!-- 选择房型 -->
					<view class="item">
						<picker class="label" :range="roomTypeList" @change="selectRoomType" :value="state.roomTypeindex">
							<view class="fr label-content">
								<text>{{ roomTypeList?.[state.roomTypeindex] || '房型'}}</text>
								<uni-icons type="arrowdown" size="12" color="#979A9A"></uni-icons>
							</view>
						</picker>
					</view>
					<!-- 选择房态 -->
					<view class="item">
						<picker class="label" :range="roomStatusList" @change="selectRoomState" :value="state.roomStateindex"
							range-key="name">
							<view class="fr label-content">
								<text>{{ roomStatusList?.[state.roomStateindex]?.name || '房态'}}</text>
								<uni-icons type="arrowdown" size="12" color="#979A9A"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>

			<!-- 房间列表 -->
			<view class="fl rooms" :class="{noMore: !roomList.length}">
				<!-- <text class="list-title" v-if="roomList.length">点击空房/脏房/维修房的房号可进行房态调整</text> -->
				<view class="list" v-if="roomList.length">
					<block v-for="(item,index) in roomList" :key="index">
						<!-- <view class="item" :style="{background: rgba(item.roomStatusColor, .2)}"
							@tap="lookInfo(item.isClick,item.roomNum,index)"> -->
						<view class="item" :style="{background: rgba(item.roomStatusColor, .2)}">
							<text class="fr room-status" :style="{background:item.roomStatusColor}">{{ item.roomStatus }}</text>
							<text class="room-num">{{item.roomNum}}</text>
						</view>
					</block>
				</view>
				<emptyVue v-else></emptyVue>
			</view>

			<!-- 底部筛选-->
			<view class="footer" :style="{'padding-bottom': bottom + 'px'}">
				<view class="li">
					<view class="l-item" @tap="selectRoomStatus('空闲',1)">
						<view class="icon" :style="{background: state.rest.pointColor}"></view>
						<view :class="state.roomIndex==1?'active':''">空闲({{state.rest.count ? state.rest.count : 0}})
						</view>
					</view>
					<view class="l-item" @tap="selectRoomStatus('在住',2)">
						<view class="icon" :style="{background:state.isIn.pointColor}"></view>
						<view :class="state.roomIndex==2?'active':''">在住({{state.isIn.count ? state.isIn.count : 0}})
						</view>
					</view>
					<view class="l-item" @tap="selectRoomStatus('钟点',3)">
						<view class="icon" :style="{background:state.clock.pointColor}"></view>
						<view :class="state.roomIndex==3?'active':''">钟点</view>
					</view>
				</view>
				<view class="li">
					<view class="l-item" @tap="selectRoomStatus('预订',4)">
						<view class="icon" :style="{background:state.book.pointColor}"></view>
						<view :class="state.roomIndex==4?'active':''">预订({{state.book.count ? state.book.count : 0}})
						</view>
					</view>
					<view class="l-item" @tap="selectRoomStatus('维修',5)">
						<view class="icon" :style="{background:state.repair.pointColor}"></view>
						<view :class="state.roomIndex==5?'active':''">
							维修({{state.repair.count ? state.repair.count : 0}})</view>
					</view>
					<view class="l-item" @tap="selectRoomStatus('预离',6)">
						<view class="icon" :style="{background:state.mayLeave.pointColor}"></view>
						<view :class="state.roomIndex==6?'active':''">预离</view>
					</view>
				</view>
				<view class="li">
					<view class="l-item" @tap="selectRoomStatus('脏房',7)">
						<view class="icon" :style="{background:state.dirty.pointColor}"></view>
						<view :class="state.roomIndex==7?'active':''">脏房({{state.dirty.count ? state.dirty.count : 0}})
						</view>
					</view>

					<view class="l-item" @tap="selectRoomStatus('清洁',8)">
						<view class="icon" :style="{background:state.clean.pointColor}"></view>
						<view :class="state.roomIndex==8?'active':''">清洁({{state.clean.count ? state.clean.count : 0}})
						</view>
					</view>
					<view class="l-item" @tap="selectRoomStatus('自用',9)">
						<view class="icon" :style="{background:state.self.pointColor}"></view>
						<view :class="state.roomIndex==9?'active':''">自用</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 入住信息 -->
		<view class="mask" v-if="state.islook" @tap="hideinfo">
			<view class="roomInfo" :style="{'padding-bottom': bottom + 'px'}">
				<view class="roomNum">
					房间号 {{state.roomNum}}
				</view>
				<view class="roomContent">
					<!-- 正常使用房 -->
					<view v-if="state.click==1">
						<view class="itemList"><span>宾客姓名：</span>
							<view class="item">{{roominfo.guestName}}</view>
						</view>
						<view class="itemList"><span>抵店时间：</span>
							<view class="item">{{roominfo.inDatetime}}</view>
						</view>
						<view class="itemList"><span>预离时间：</span>
							<view class="item">{{roominfo.leaveTime}}</view>
						</view>
						<view class="itemList"><span>客户来源：</span>
							<view class="item">{{roominfo.customeTypeName}}</view>
						</view>
						<view class="itemList"><span>房间价格：</span>
							<view class="item">{{roominfo.disPrc}}</view>
						</view>
						<view class="itemList"><span>已交押金：</span>
							<view class="item">{{roominfo.deposit}}</view>
						</view>
						<view class="itemList"><span>备注信息：</span>
							<view class="item" style="color:#234DEF">{{roominfo.memoInfo}}</view>
						</view>
						<view class="itemList"><span>联房信息：</span>
							<view class="item">
								<p style="color:#FE655A">{{roominfo.mainRName}}{{roominfo.mainRName?',':''}}</p>
								{{roominfo.rnames}}
							</view>
						</view>
					</view>
					<!-- 维修房 -->
					<view v-if="state.click==2">
						<view class="itemList"><span>维修时间：</span>
							<view class="item">{{roominfo.wxTime}}</view>
						</view>
						<view class="itemList"><span>操作人：</span>
							<view class="item">{{roominfo.wxOperator}}</view>
						</view>
						<view class="itemList"><span>备注信息：</span>
							<view class="item" style="color:#234DEF">{{roominfo.wxMemo}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--修改房态-->
		<u-popup :show="updateRoomStatus" mode="center" :safe-area-inset-bottom="false" round="12">
			<view class="fl slot-content">
				<view class="bg"></view>
				<text class="theme-primary">房态修改</text>
				<view class="content" :class="{'content-2': state.click == 2}">
					<view class="fr">
						<text class="label">房号：</text>
						<text class="room-color">{{ roominfo.roomNum }}</text>
					</view>
					<block v-if="state.click == 2">
						<view class="fr">
							<text class="label">维修时间：</text>
							<text>{{ roominfo.wxTime }}</text>
						</view>
						<view class="fr">
							<text class="label">操作人：</text>
							<text>{{ roominfo.wxOperator }}</text>
						</view>
						<view class="fr">
							<text class="label">备注信息：</text>
							<text>{{ roominfo.wxMemo }}</text>
						</view>
					</block>
					<view class="fr">
						<view class="fr left">
							<text class="label">状态：</text>
							<text class="room-color current-room__status">{{ roominfo.rstatus }}转</text>
						</view>
						<view class="fr label-content">
							<picker mode="selector" :range="canUpdateRoomList" :value="updateRoom.defaultIndex" range-key="label"
								style="flex: 1" @change="confirm">
								<view style="width:100%;display: flex;justify-content: space-between;">
									<text class="theme-primary">{{ updateRoom.name }}</text>
									<uni-icons type="arrowdown" color="#979A9A" size="12"></uni-icons>
								</view>
							</picker>
						</view>
					</view>
					<view class="fr" v-if="updateRoom.name == '维修'">
						<text class="left label">周期：</text>
						<view class="fr label-content">
							<input v-model="repairCycle" type="text" style="font-size: 24rpx;">天
						</view>
					</view>
					<view class="fr" v-if="updateRoom.name == '空闲' && cleanPeople.length">
						<text class="left label">打扫人:</text>
						<view class="fr label-content">
							<picker mode="selector" style="flex: 1" :range="cleanPeople" range-key="cleanStaffName"
								:value="cleanPeople.defaultIndex" @change="onSelectCleanPeople">
								<view style="width:100%;display: flex;justify-content: space-between;">
									<text class="theme-primary">{{ cleanPeopleInfo.name }}</text>
									<uni-icons type="arrowdown" color="#979A9A" size="12"></uni-icons>
								</view>
							</picker>
						</view>
					</view>
					<view class="fl">
						<text class="remark label">备注</text>
						<textarea class="textarea" v-model.trim="memo" @focus="onfocus" cursor-spacing="20"></textarea>
					</view>
				</view>
				<view class="fr btns">
					<text class="fr btn theme-primary btn-cancel" @tap="updateRoomStatus = false">取消</text>
					<text class="fr btn btn-confirm" @tap="roomStatusChange">确认</text>
				</view>
			</view>
		</u-popup>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		ref,
		reactive,
		unref,
		getCurrentInstance
	} from 'vue'
	import {
		rgba, showToast
	} from '@/utils/util'
	import {
		queryRoomStatus as queryRoomStatusApi,
		getCleanPeople as getCleanPeopleApi,
		roomStatusChange as roomStatusChangeApi
	} from '../../api'
	import {
		useCommon
	} from '@/hooks/useGlobalData';
	import { dataType } from '@/types/index.d';
	import emptyVue from '@/components/empty/empty.vue'

	const bottom = uni.getSystemInfoSync().safeAreaInsets.bottom || 34,
		{ globalData } = useCommon(getCurrentInstance()),
		roomStatusList = Object.freeze([{
			name: '全部',
			value: ''
		},
		{
			name: '在住',
			value: 'isIn'
		},
		{
			name: '清洁',
			value: 'clean'
		},
		{
			name: '空闲',
			value: 'rest'
		},
		{
			name: '维修',
			value: 'repair'
		},
		{
			name: '脏房',
			value: 'dirty'
		},
		{
			name: '自用',
			value: 'self'
		},
		{
			name: '钟点',
			value: 'clock'
		},
		{
			name: '预离',
			value: 'mayLeave'
		},
		{
			name: '预订',
			value: 'book'
		}
		]),

		state = reactive({
			floor: '',
			roomState: '',
			roomType: '',
			roomCode: '',
			list: '',
			rest: '', //空房
			isIn: '', //入住
			dirty: '', //脏房
			clean: '', //清洁
			book: '', //预订
			repair: '', //维修
			mayLeave: '', //预离
			self: '', //自用
			clock: '', //钟点
			islook: false,
			click: 1,
			roomIndex: null,
			floorindex: null,
			roomStateindex: null,
			roomTypeindex: null,
			roomNum: ''
		}),
		floors = ref([]),
		roomTypeList = ref([]),
		list = ref([]),
		roomList = ref([]),
		roominfo = ref<dataType>({}),
		updateRoomStatus = ref(false),
		canUpdateRoomList = ref([]),
		updateRoom = reactive({
			defaultIndex: 0,
			name: ''
		}),
		cleanPeople = ref<dataType[]>([]),
		cleanPeopleInfo = reactive({
			defaultIndex: -1,
			name: '请选择打扫人员'
		}),
		repairCycle = ref(''),
		memo = ref(''),
		shopId = ref('')



	const confirm = (e) => {
		updateRoom.defaultIndex = e.detail.value
		updateRoom.name = unref(canUpdateRoomList)[updateRoom.defaultIndex].label
		getCleanPeople()
	}
	const onSelectCleanPeople = (e) => {
		cleanPeopleInfo.defaultIndex = e.detail.value
		cleanPeopleInfo.name = unref(cleanPeople)[cleanPeopleInfo.defaultIndex].cleanStaffName
	}

	const queryRoomStatus = async () => {
		try {
			const res = await queryRoomStatusApi({
				shopId: shopId.value,
				floorNum: state.floor,
				roomStatus: '',
				roomTypeName: state.roomType
			})
			let tempList = [];

			if (state.roomCode) {
				tempList = res.data.roomStatusList.filter(item => item.roomNum.indexOf(state.roomCode) != -1)
			} else if (state.roomState) {
				tempList = res.data.roomStatusList.filter(item => {
					return (((state.roomState == '钟点' || state.roomState == '自用') && item
						.openRoomTypeName == state.roomState) || (state.roomState == '预离' && item
							.isYuLi == 1) || (state.roomState == '全部') || (item.roomStatus == state
								.roomState))
				})
			} else {
				tempList = res.data.roomStatusList
			}
			list.value = res.data.roomStatusList
			roomList.value = [...tempList]
			state.rest = res.data.rest
			state.isIn = res.data.isIn //入住
			state.dirty = res.data.dirty //脏房
			state.clean = res.data.clean //清洁
			state.book = res.data.book //预订
			state.repair = res.data.repair //维修
			state.mayLeave = res.data.mayLeave //预离
			state.self = res.data.self //自用
			state.clock = res.data.clock //钟点
			if (!unref(roomTypeList).length && !unref(floors).length) {
				let floorsList = [],
					roomTypes = [];

				tempList.forEach(item => {
					if (roomTypes.indexOf(item.roomTypeName) < 0) {
						roomTypes.push(item.roomTypeName)
					}
					if (floorsList.indexOf(item.floorNum) < 0) {
						floorsList.push(item.floorNum)
					}
				});
				if (floorsList.length > 0) {
					floorsList.unshift('全部')
				}
				if (roomTypes.length > 0) {
					roomTypes.unshift('全部')
				}
				floors.value = floorsList
				roomTypeList.value = roomTypes
				console.log(roomTypeList)
			}
		} catch (e) {
			console.log('获取房态列表接口报错', e)
		}
	}

	const selectFloor = (e) => {
		state.floorindex = e.detail.value
		state.floor = unref(floors)[e.detail.value]
		queryRoomStatus()
	}
	const selectRoomType = (e) => {
		state.roomType = unref(roomTypeList)[e.detail.value]
		state.roomTypeindex = e.detail.value
		queryRoomStatus();
	}
	const selectRoomState = (e) => {
		state.roomState = unref(roomStatusList)[e.detail.value].name
		state.roomStateindex = e.detail.value
		queryRoomStatus();
	}
	const selectRoomStatus = (status, e) => {
		state.roomStateindex = ''
		//点击第一次筛选 第二次清空状态显示全部
		if (state.roomIndex && state.roomIndex == e) {
			state.roomIndex = ''
			state.roomState = ''
		} else {
			state.roomState = status,
				state.roomIndex = e
		}
		queryRoomStatus();
	}
	const search = () => {
		roomList.value = unref(list).filter(item => item.roomNum.indexOf(state.roomCode) >= 0);
	}

	const clear = () => {
		state.roomCode = ''
		roomList.value = [...list.value]
	}
	const getCleanPeople = async () => {
		try {
			const res = await getCleanPeopleApi({
				nameType: "保洁",
			})
			console.log("获取保洁人员列表", res.data)
			cleanPeople.value = res.data || []
		} catch (e) {
			console.log('获取保洁人员接口错误', e)
		}
	}
	const lookInfo = (click, roomNum, index) => {
		state.click = click;
		roominfo.value = unref(roomList)[index]
		console.log('当前的房间信息', roominfo.value)
		state.roomNum = roomNum

		switch (roominfo.value.rstatus) {
			case '脏房':
				canUpdateRoomList.value = [{
					label: "空闲",
					id: "rest",
				},
				{
					label: "维修",
					id: "repair",
				}
				]
				updateRoom.name = '空闲'
				updateRoom.defaultIndex = 0
				getCleanPeople()
				break;
			case '空闲':
				canUpdateRoomList.value = [{
					label: "脏房",
					id: "dirty",
				},
				{
					label: "维修",
					id: "repair",
				}
				]
				updateRoom.name = '脏房'
				updateRoom.defaultIndex = 0
				break;
			case '维修':
				canUpdateRoomList.value = [{
					label: "空闲",
					id: "rest",
				},
				{
					label: "脏房",
					id: "dirty",
				}
				]
				updateRoom.name = '空闲'
				updateRoom.defaultIndex = 0
				getCleanPeople()
				break;
		}

		if (click == 1) {
			state.islook = true
			return
		}
		if (click > 0 || ['空闲', '脏房', '维修'].includes(roominfo.value.rstatus)) {
			memo.value = ''
			repairCycle.value = ''
			updateRoom.defaultIndex = 0
			cleanPeopleInfo.name = '请选择打扫人员'
			cleanPeopleInfo.defaultIndex = -1
			updateRoomStatus.value = true
		}
	}
	const roomStatusChange = async () => {
		if (updateRoom.name == '维修') {
			if (repairCycle.value == '') {
				showToast('请输入维修周期')
				return
			}
			if (repairCycle.value == 0) {
				showToast('维修周期不能为0')
				return
			}
		}
		if (!memo.value) {
			showToast('请输入备注')
			return
		}
		if (updateRoom.name == '空闲') {
			if (cleanPeople.length && cleanPeopleInfo.defaultIndex < 0) {
				showToast('请选择打扫人员')
				return
			}
		}
		try {
			const res = await roomStatusChangeApi({
				roomId: unref(roominfo).id,
				oldStatus: unref(roominfo).rstatus,
				newStatus: updateRoom.name,
				cleaner: unref(cleanPeople)?.[cleanPeopleInfo.defaultIndex]?.cleanStaffId,
				repairCycle: unref(repairCycle),
				memo: unref(memo)
			})
			showToast({
				title: res.message,
				success() {
					updateRoomStatus.value = false
					let timer = setTimeout(() => {
						queryRoomStatus()
					}, 300)
				}
			})
		} catch (e) {
			console.log('修改房态接口报错', e)
		}

	}
	const hideinfo = () => {
		state.islook = false
	}
	const onfocus = (e) => {

	}
	onLoad((opts) => {
		console.log(opts,'opts');
		shopId.value = opts.shopId
		if (opts.label) {
			const obj = {
				'rest': 1,
				'isIn': 2,
				'book': 4,
				'mayLeave': 6
			}
			selectRoomState({
				detail: {
					value: roomStatusList.findIndex(item => item.value == opts.label)
				}
			})
			state.roomIndex = obj[opts.label]
		} else {
			queryRoomStatus()
		}
	})
</script>

<style lang="scss" scoped>
	.main {
		height: 100vh;

		.search-wrap {
			flex-shrink: 0;
			width: 100%;

			.outline {
				padding: 20rpx 0;
				margin-bottom: 40rpx;
				background-color: #fff;
			}

			.search {
				height: 64rpx;
				margin: 0 20rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 200rpx;
			}

			input {
				flex: 1;
				height: 64rpx;
				padding-left: 40rpx;
				font-size: 26rpx;
				font-weight: 300;
			}

			.btn-search {
				justify-content: center;
				width: 104rpx;
				height: 48rpx;
				font-size: 24rpx;
				color: #fff;
				background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				border-radius: 150rpx;
			}
		}

		.selector {
			justify-content: space-between;
			margin: 0 20rpx 40rpx;

			.item {
				width: 218rpx;
				height: 64rpx;
				background: #fff;
				border-radius: 200rpx;

				.label {
					height: 64rpx;
					padding: 0 24rpx;
					font-size: 26rpx;
					font-weight: bold;
				}

				.label-content {
					justify-content: space-between;
					width: 170rpx;
					height: 64rpx;
				}
			}
		}

		.rooms {
			flex: 1;
			overflow-y: auto;
			margin: 0 24rpx 24rpx;
			padding: 32rpx 25rpx 0 24rpx;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 16rpx;

			&.noMore {
				justify-content: center;
			}

			.list {
				display: flex;
				flex-wrap: wrap;
			}

			.list-title {
				display: block;
				margin-bottom: 32rpx;
				font-size: 26rpx;
				font-weight: bold;
				color: #222;
			}

			.item {
				position: relative;
				flex-shrink: 0;
				font-size: 0;
				width: 145rpx;
				height: 120rpx;
				margin-right: calc((100% - 580rpx) / 3);
				margin-bottom: 32rpx;
				padding-top: 60rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: bold;
				color: #222;
				border-radius: 16rpx;
			}

			.room-status {
				position: absolute;
				top: 0;
				left: 0;
				justify-content: center;
				height: 40rpx;
				min-width: 72rpx;
				font-size: 24rpx;
				line-height: 40rpx;
				color: #fff;
				font-weight: normal;
				border-radius: 16rpx 0 16rpx 0;
			}

			.room-num {
				display: inline-block;
				max-width: 145rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.item:nth-of-type(4n) {
				margin-right: 0;
			}
		}

		.footer {
			flex-shrink: 0;
			width: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			background: #fff;

			.li {
				width: 100%;
				padding: 0 30rpx;
				display: flex;
				box-sizing: border-box;

				.l-item {
					display: flex;
					flex: 1;
					align-items: center;
					margin-top: 30rpx;
					font-size: 28rpx;
					box-sizing: border-box;

					.icon {
						width: 16rpx;
						height: 16rpx;
						border-radius: 50%;
						margin-right: 16rpx;
					}

					.active {
						color: #48B7FF
					}
				}

				.l-item:nth-of-type(2) .icon {
					margin-left: 46rpx;
				}

				.l-item:last-child .icon {
					margin-left: 73rpx;
				}
			}
		}

	}

	.mask {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		z-index: 999
	}

	.roomInfo {
		width: 750rpx;
		/* height:548rpx; */
		background: rgba(255, 255, 255, 1);
		border: 1px solid rgba(255, 255, 255, 1);
		box-shadow: 0px 3rpx 10rpx rgba(116, 175, 232, 0.4);
		opacity: 1;
		border-radius: 50rpx 50rpx 0px 0px;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 2;
		padding-bottom: 20rpx;

		.roomNum {
			position: relative;
			top: -14rpx;
			width: 360rpx;
			height: 56rpx;
			margin: auto;
			text-align: center;
			line-height: 56rpx;
			color: #fff;
			font-size: 26rpx;
			background-image: url('https://sunmei-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-08-01/pic_t@2x (1).png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
		}

		.roomContent {
			box-sizing: border-box;
			overflow-y: auto;
			width: 678rpx;
			max-height: 700rpx;
			margin: 0 auto;
			padding: 32rpx;
			font-size: 28rpx;
			background-color: #1e61fc0f;
			border-radius: 16rpx;
			color: #222;

			.itemList {
				display: flex;
				margin-bottom: 24rpx;

				span {
					flex-shrink: 0;
					display: block;
					width: 150rpx;
					text-align: right;
				}

				&:last-child {
					margin-bottom: 0;
				}
			}

			.item {
				flex: 1;
				text-align: right;
				word-wrap: break-word;
				word-break: break-all;
			}
		}
	}

	.slot-content {
		width: 652rpx;
		border-radius: 16rpx;

		.bg {
			width: 100%;
			height: 234rpx;
			background: url("https://sunmei-smart-marketing.oss-cn-shanghai.aliyuncs.com/smartMarketing/2024-08-26/<EMAIL>") no-repeat;
			background-size: 100% 100%;
		}

		&>.theme-primary {
			margin: 46rpx 0 32rpx;
			text-align: center;
			font-size: 34rpx;
			font-weight: bold;
		}

		.content {
			margin: 0 24rpx 37rpx;
			padding: 24rpx;
			font-size: 24rpx;
			background-color: #1e61fc0f;
			backdrop-filter: blur(20rpx);
			border-radius: 16rpx;

			&>.fr {
				margin: 24rpx 0;
			}

			.label {
				width: 100rpx;
			}

			.left {
				flex-shrink: 0;
			}

			.textarea {
				width: 100%;
				height: 130rpx;
				padding: 20rpx;
				font-size: 24rpx;
				background-color: rgba(255, 255, 255, 0.96);
				border-radius: 4rpx 16rpx 16rpx 16rpx;
				box-sizing: border-box;
			}

			.room-color {
				font-weight: bold;
			}

			.current-room__status {
				margin-right: 16rpx;
			}

			.label-content {
				justify-content: space-between;
				flex: 1;
				height: 38rpx;
				padding: 0 24rpx;
				background-color: rgba(255, 255, 255, .96);
				border-radius: 8rpx;
			}

			.remark {
				margin-bottom: 16rpx;
			}

			&-2 {
				.label {
					width: 140rpx;
				}
			}
		}


		.btns {
			margin: 0 32rpx 51rpx;

			.btn {
				justify-content: center;
				flex: 1;
				height: 72rpx;
				font-size: 32rpx;
				font-weight: bold;
				border-radius: 40rpx;

				&-cancel {
					margin-right: 48rpx;
					border: 1rpx solid #1e61fc;
				}

				&-confirm {
					color: #fff;
					background-color: #1e61fc;
				}
			}
		}
	}
</style>