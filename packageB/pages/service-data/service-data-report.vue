<script setup lang="ts">
	import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import emptyVue from '@/components/empty/empty.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { shopDataReportDetail } from '../../api';
	import dayjs from 'dayjs';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const bgColor = ref('transparent')
	const titleColor = ref('#fff')
	// 门店信息
	const shop = ref<{ shopName : string, shopId : string, [key : string] : any }>({
		shopId: '',
		shopName: ''
	})
	const date = reactive<{ beginDate : string, endDate : string }>({
		beginDate: '',
		endDate: ''
	})
	const reportData = ref()

	onLoad((options) => {
		date.beginDate = JSON.parse(options.report).beginDate
		date.endDate = JSON.parse(options.report).endDate
		shop.value.shopId = JSON.parse(options.report).shopId
		toShopDataReportDetail()
	})

	onShow(() => {
		const pages = getCurrentPages()
		const currentPage : any = pages[pages.length - 1]
		if (currentPage.shop) {
			shop.value.shopId = currentPage.shop.shopId
			shop.value.shopName = currentPage.shop.shopName
			currentPage.shop = null
			toShopDataReportDetail()
		}
	})

	// 门店服务数据报告详情
	const toShopDataReportDetail = async () => {
		try {
			const { data } = await shopDataReportDetail({
				shopId: shop.value.shopId,
				...date
			})
			if (data && Object.keys(data).length > 0) {
				shop.value.shopId = data.shopId
				shop.value.shopName = data.shopName
				reportData.value = data
			} else {
				reportData.value = {}
			}
		} catch (e) {
			console.error('门店服务数据报告详情 error', e)
		}
	}

	const navigatorBack = () => {
		uni.navigateBack()
	}

	onPageScroll((e) => {
		if (e.scrollTop < 10) {
			bgColor.value = 'transparent'
			titleColor.value = '#fff'
		}
		if (e.scrollTop > 50) {
			bgColor.value = 'rgba(255,255,255,.4)'
			titleColor.value = 'rgba(#1F2428, .4)'
		}
		if (e.scrollTop > 80) {
			bgColor.value = 'rgba(255,255,255,.8)'
			titleColor.value = 'rgba(#1F2428, .8)'
		}
		if (e.scrollTop > 100) {
			bgColor.value = '#fff'
			titleColor.value = 'rgba(#1F2428, 1)'
		}
	})

	const btn_safe_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 40 + 'rpx'
	const padding_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 180 + 'rpx'
</script>

<template>
	<view>
		<commonBgVue height="588" />
		<u-navbar placeholder title="服务小结" :bgColor="bgColor" leftIconColor="#FFF" :titleStyle="{color: titleColor}"
			autoBack>
		</u-navbar>
		<view class="title">一周小结</view>
		<view class="subtitle">管辖门店服务数据</view>
		<view class="report-date">
			{{ dayjs(reportData?.beginDate ?? date?.beginDate).format('YYYY/MM/DD')}}-{{dayjs(reportData?.endDate ?? date?.endDate).format('YYYY/MM/DD')}}
		</view>
		<view class="shop fr" @click="route_to_view('/packageB/pages/select-store/select-store?from=seivice-data-report')">
			<view class="shop-name">{{shop?.shopName ? shop?.shopName  +'（当前门店）' : '请选择门店'}}</view>
			<up-icon name="arrow-down" color="#999999" size="22rpx" />
		</view>
		<view class="content" v-if="reportData && Object.keys(reportData).length>0">
			<view class="section">
				<view class="title-text">评价分</view>
				<view class="commentRateList">
					<template v-for="(item,index) in reportData?.commentRateList" :key="index">
						<view class="fr tr">
							<view class="left td">{{ item.channelId }}</view>
							<view class="td score">{{ item.hotelAvgScore }}</view>
							<view class="right td fr"
								:class="[ item.changeScore =='0.00' ? '': item.label == 1 ? 'up-text' : 'down-text']">
								<image class="img" v-if="item.label != 0"
									:src="item.changeScore =='0.00' ? '': item.label == 1 ? 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644716250.png' : 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644734157.png' " />
								{{ item.changeScore }}
							</view>
						</view>
					</template>
				</view>
			</view>
			<view class="section">
				<view class="">
					<view class="">
						<view class="title-text">差评</view>
					</view>
					<view class="commentRateList">
						<template>
							<view class="fr tr">
								<view class="left td">数量</view>
								<view class="td" style="font-weight: normal;">{{ reportData?.badPoint.badPointCount }}</view>
								<view class="right td"
									:class="[reportData?.badPoint.label == 1 ? 'up-text' : reportData?.badPoint.label == -1 ? 'down-text' : '' ]">
									{{ reportData?.badPoint.badPointRate }}
									<image class="img" v-if="reportData?.badPoint.label != 0"
										:src="reportData?.badPoint.label == 1 ? 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644716250.png' : 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644734157.png'" />
								</view>
							</view>
							<view class="fr tr">
								<view class="left td">差评率</view>
								<view class="td" style="font-weight: normal;">{{ reportData?.badPoint.badRate }}</view>
								<view class="td"></view>
							</view>
							<view class="fr tr">
								<view class="left td">回复率</view>
								<view class="td" style="font-weight: normal;">{{ reportData?.badPoint.badResponseRate }}</view>
								<view class="td"></view>
							</view>
						</template>
					</view>
				</view>
				<view style="margin-top: 80rpx;" v-if="reportData?.negLabelTop5?.length > 0">
					<view class="fr">
						<view class="title-text">差评高频词</view>
						<view class="top5">(TOP5)</view>
					</view>
					<view class="negative-comment-keyword fr">
						<view class="keyword" v-for="(item,index) in reportData?.negLabelTop5" :key="index">{{item}}</view>
					</view>
				</view>
			</view>
			<view class="section">
				<view class="">
					<view class="title-text">集团综合评价</view>
				</view>
				<view class="comprehensive-evaluation">
					<view class="fr row">
						<view class="point"></view>
						<view class="comprehensive-evaluation-text">{{ reportData?.evaluation?.hotelAvgScoreTxt }}</view>
					</view>
					<view class="fr row">
						<view class="point"></view>
						<view class="comprehensive-evaluation-text">{{ reportData?.evaluation?.commentCountTxt }}</view>
					</view>
					<view class="fr row">
						<view class="point"></view>
						<view class="comprehensive-evaluation-text">{{ reportData?.evaluation?.badPointTxt }}</view>
					</view>
				</view>
			</view>
		</view>
		<view style="padding: 0 20rpx;" v-else>
			<emptyVue hasBackground />
		</view>
	</view>
	<view class="btn-block">
		<view class="btn-text" @click="navigatorBack">
			继续查看其他时间周期服务数据报告
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>


<style scoped lang="scss">
	.title {
		color: #ffffff;
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		line-height: 36rpx;
		position: relative;
		margin-bottom: 40rpx;
		margin-top: 50rpx;
		text-align: center;

		&::before,
		&::after {
			content: "";
			position: absolute;
			top: 50%;
			width: 200rpx;
			height: 4rpx;
			background: linear-gradient(to right, rgba(247, 94, 59, 0), rgba(255, 178, 161, 0.5));
		}

		&::before {
			margin-left: -20rpx;
			transform: translateX(-100%);
		}

		&::after {
			background: linear-gradient(to left, rgba(247, 94, 59, 0), rgba(255, 178, 161, 0.5));
			margin-left: 20rpx;
		}
	}

	.subtitle {
		color: #ffffff;
		font-size: 64rpx;
		text-align: center;
		font-weight: bold;
		line-height: 64rpx;
	}

	.report-date {
		color: #ffffff;
		font-size: 22rpx;
		line-height: 22rpx;
		text-align: center;
		margin-top: 28rpx;
	}

	.shop {
		border-radius: 200rpx;
		background: #ffffffe6;
		justify-content: space-between;
		padding: 20rpx 40rpx;
		margin: 40rpx 20rpx 20rpx;

		&-name {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 24rpx;
		}
	}

	.content {
		padding: 0 20rpx;
		flex: 1;
		overflow-y: auto;
		padding-bottom: v-bind(padding_bottom);
	}

	.section {
		padding: 40rpx;
		border-radius: 20rpx;
		background: #ffffff;
		margin-bottom: 20rpx;

		.commentRateList {
			margin-top: 40rpx;
			border-radius: 20rpx;
			overflow: hidden;

			.score {
				font-weight: 400 !important;
			}

			.right {
				font-size: 24rpx;
				font-weight: 700 !important;
				font-family: "Din Bold";
				line-height: 24rpx;
				justify-content: center;

				.img {
					width: 16rpx;
					height: 20rpx;
					margin-right: 8rpx;
				}
			}

			.tr {
				height: 84rpx;

				&:nth-child(even) {
					background: #f75e3b0a;
				}

				&:nth-child(odd) {
					background: #f75e3b14;
				}

				.td {
					color: #1f2428;
					font-size: 22rpx;
					font-weight: bold;
					text-align: center;
					line-height: 22rpx;
					flex: 1;
					text-align: center;
				}

				.up-text {
					color: #FF4D4D;
				}

				.down-text {
					color: #56cc93;
				}
			}
		}

		&:last-child {
			margin-bottom: 0;
		}

		.title-text {
			color: #1f2428;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 32rpx;
		}

		.top5 {
			color: $uni-text-color;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 32rpx;
			margin-left: 16rpx;
		}

		.negative-comment-keyword {
			padding: 40rpx;
			padding-bottom: 20rpx;
			border-radius: 20rpx;
			background: #f75e3b14;
			margin-top: 40rpx;
			flex-wrap: wrap;
			justify-content: center;

			.keyword {
				padding: 16rpx 30rpx;
				background: #ffb2a166;
				border-radius: 200rpx;
				color: #1f2428;
				font-size: 22rpx;
				font-weight: bold;
				text-align: center;
				line-height: 22rpx;
				margin: 0 10rpx 20rpx;
			}
		}

		.comprehensive-evaluation {
			padding: 40rpx;
			border-radius: 20rpx;
			background: #f75e3b14;
			margin-top: 40rpx;

			.row {
				align-items: baseline;
				margin-bottom: 12rpx;

				&:last-child {
					margin-bottom: 0;
				}
			}

			.point {
				width: 16rpx;
				height: 16rpx;
				background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
				margin-right: 16rpx;
				border-radius: 50%;
				flex-shrink: 0;
			}

			&-text {
				color: #1f2428;
				font-size: 26rpx;
				line-height: 42rpx;
				word-break: break-all;
			}
		}
	}

	.btn-block {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		padding: 40rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding-bottom: v-bind(btn_safe_bottom);
		z-index: 2000000;

		.btn-text {
			color: #ffffff;
			font-size: 28rpx;
			font-weight: bold;
			text-align: center;
			line-height: 28rpx;
			border-radius: 200rpx;
			padding: 26rpx 0;
			background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
		}
	}
</style>