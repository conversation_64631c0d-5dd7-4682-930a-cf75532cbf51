<template>
	<view class="container fl">
		<view class="calendar">
			<cCalendarVue v-model="calendarValue" @confirm="confirm" />
		</view>
		<view class="content">
			<view class="list">
				<view class="fr item" v-if="reportList.length > 0" v-for="(item,index) in reportList" :key="index"
					@click="route_to_view(`./service-data-report?report=${JSON.stringify(item)}`)">
					<view class="fr">
						<view class="img">
							<image
								src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-01/1730450574513.png"
								mode=""></image>
							<view class="dot" v-if="item.redDot == 0"></view>
						</view>
						<view class="info">
							<view class="title">
								管辖范围门店服务数据报告已生成
							</view>
							<view class="date">
								{{dayjs(item.beginDate).format('YYYY/MM/DD')}} - {{dayjs(item.endDate).format('YYYY/MM/DD')}}
							</view>
						</view>
					</view>
					<up-icon name="arrow-right" color="#999999" size="22rpx" />
				</view>
				<template v-else>
					<emptyVue hasBackground />
				</template>
			</view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue';
	import emptyVue from '@/components/empty/empty.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import dayjs from 'dayjs';
	import { shopDataReport } from '../../api';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const calendarValue = ref<string[]>([
		dayjs().startOf('month').format('YYYY-MM'),
		dayjs().subtract(1, 'day').format('YYYY-MM')
	]),
		reportList = ref([])


	const confirm = () => {
		calendarValue.value[0] = dayjs(calendarValue.value[0]).format('YYYY-MM')
		calendarValue.value[1] = dayjs(calendarValue.value[1]).format('YYYY-MM')
		toShopDataReport()
	}

	onShow(() => {
		toShopDataReport()
	})

	// 门店服务数据报告
	const toShopDataReport = async () => {
		try {
			const { data } = await shopDataReport({
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && data.length > 0) {
				reportList.value = data
			} else {
				reportList.value = []
			}
		} catch (e) {
			console.error('门店服务数据报告 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.container {
		width: 100%;
		height: 100%;
	}

	.calendar {
		background: #fff;
		padding: 20rpx;
		margin-bottom: 40rpx;
	}

	.content {
		flex: 1;
		overflow-y: auto;
	}

	.list {
		padding: 0 20rpx 40rpx;

		.item {
			padding: 36rpx 40rpx 34rpx;
			border-radius: 20rpx;
			background: #ffffff;
			justify-content: space-between;
			margin-bottom: 20rpx;
		}

		.img {
			position: relative;
			width: 90rpx;
			height: 90rpx;
			flex-shrink: 0;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.dot {
			position: absolute;
			top: 4rpx;
			right: 4rpx;
			width: 16rpx;
			height: 16rpx;
			border-radius: 50%;
			opacity: 1;
			border: 2rpx solid #ffffff;
			background: #ff4d4d;
		}

		.info {
			margin-left: 40rpx;

			.title {
				color: #1f2428;
				font-size: 28rpx;
				line-height: 28rpx;
				margin-bottom: 28rpx;
			}

			.date {
				color: #1f2428b3;
				font-size: 24rpx;
				line-height: 24rpx;
			}
		}
	}
</style>