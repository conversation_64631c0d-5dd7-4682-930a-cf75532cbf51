<template>
	<view class="container fl">
		<commonBgVue height="374" />
		<u-navbar placeholder :title="title" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="picker-section" v-if="!hidePicker">
			<view class="picker fr"
				@click="route_to_view(`/packageB/pages/select-store/select-store?from=store-data-report`)">
				<view class="">{{shop?.shopName ?? '请选择门店'}}</view>
				<up-icon name="arrow-down" color="#999999" size="22rpx" />
			</view>
		</view>
		<view class="content">
			<view class="section">
				<view class="title fr">
					<text class="title-text">经营数据</text>
				</view>
				<view class="target-table">
					<cTable :header="operationsData.column" :list="operationsData.list">
						<template #default="scope">
							<view v-if="['tb', 'hb'].includes(scope.prop)" class="rate-item">
								<image v-if="scope.data[scope.prop] && scope.data[scope.prop] !== '-'" class="item-icon"
									:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop].indexOf('-') ==-1 ? '1729644716250' : '1729644734157'}.png`"
									mode="aspectFill"></image>
								<text class="item-text"
									:class="[(scope.data[scope.prop] === '-' || scope.data[scope.prop] == 0) ?  '' : scope.data[scope.prop]?.indexOf('-') == -1 ? 'up-text' : 'down-text']">{{ scope.data[scope.prop] }}</text>
							</view>
						</template>
					</cTable>
				</view>
			</view>


			<view class="section">
				<view class="title fr">
					<text class="title-text">渠道订单分析</text>
				</view>
				<view class="target-table">
					<cTable :header="channelOrder.column" :list="channelOrder.list">
						<template #default="scope">
							<view v-if="['tb', 'hb'].includes(scope.prop)" class="rate-item">
								<image v-if="scope.data[scope.prop] && scope.data[scope.prop] !== '-'" class="item-icon"
									:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop].indexOf('-') ==-1 ? '1729644716250' : '1729644734157'}.png`"
									mode="aspectFill"></image>
								<text class="item-text"
									:class="[(scope.data[scope.prop] === '-' || scope.data[scope.prop] == 0) ?  '' : scope.data[scope.prop].indexOf('-') == -1 ? 'up-text' : 'down-text']">{{ scope.data[scope.prop] }}</text>
							</view>
						</template>
					</cTable>
				</view>
			</view>

			<view class="section">
				<view class="calendar">
					<cCalendarVue v-model="calendarValue" @confirm="confirm" />
				</view>
				<view class="title fr">
					<text class="title-text">客源结构分析</text>
				</view>
				<view class="customer-source-structure-chars" style="margin-bottom: 60rpx;">
					<ringChartVue :data="customerStructureChartsData" type='c' />
				</view>
				<view class="c-subsection-vue">
					<c-subsection-vue v-model="currentIndex" :list="list" @change="change" />
				</view>
				<view class="customer-source-structure-chars">
					<ringChartVue :data="distributionDirectSalesChartsData" type='c' />
				</view>
			</view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { ref, getCurrentInstance, ComponentInternalInstance, onMounted } from "vue";
	import commonBgVue from "../../components/common-bg/common-bg"
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue';
	import cTable from '@/components/c-table/c-table.vue'
	import cSubsectionVue from "@/components/c-subsection/c-subsection.vue";
	import ringChartVue from '@/components/ring-chart/ring-chart.vue'
	import { useCommon } from '@/hooks/useGlobalData';
	import { channel_order_column, operate_column } from "./config";
	import { customerStructure, managerData } from "../../api";
	import dayjs from 'dayjs';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance();

	const list = ref([{ name: '分销占比' }, { name: '直销占比' }])
	const currentIndex = ref(0)
	const shop = ref<{ shopName : string, shopId : string | number, [key : string] : any }>() // 门店信息
	const tableWidth = ref<string>(), // 表格宽度
		title = ref<string>('单店数据运营报表')


	const operationsData = ref({
		column: operate_column,
		list: []
	});

	const channelOrder = ref({
		column: channel_order_column,
		list: []
	});
	const customerStructureData = ref(), //客源结构数据
		customerStructureChartsData = ref(), // 客源结构圆环图数据
		distributionDirectSalesChartsData = ref(), // 客源结构分销直销圆环图数据
		calendarValue = ref<string[]>([
			dayjs().startOf('month').format('YYYY-MM-DD'),
			dayjs().subtract(1, 'day').format('YYYY-MM-DD')
		])

	const hidePicker = ref<boolean>(false)
	onLoad((options) => {
		if (options.id) { // 巡店计划跳转
			hidePicker.value = true
			title.value = '门店数据'
			shop.value = { shopId: options.id, shopName: null }
			toManagerData()
			toCustomerStructure()
		}
	})

	onShow(() => {
		const pages = getCurrentPages()
		const currentPage : any = pages[pages.length - 1]
		if (currentPage.shop) {
			shop.value = currentPage.shop
			toManagerData()
			toCustomerStructure()
		}
	})

	onMounted(() => {
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.target-table`).boundingClientRect((res : any) => {
			tableWidth.value = res.width + 'px !important'
		}).exec();
	})

	// 门店经营数据
	const toManagerData = async () => {
		try {
			const { data } = await managerData({ shopId: shop.value?.shopId })
			if (data && Object.keys(data).length > 0) {
				operationsData.value.list = data.businessData
				channelOrder.value.list = data.channelData
			}
		} catch (e) {
			console.error('门店经营数据', e)
		}
	}

	const formatChartsData = (v : any) => {
		return v.map(({ dataName, count }) => {
			return {
				name: dataName,
				value: Number(count) ?? 0,
				labelShow: false
			}
		})
	}

	// 客源结构分析
	const toCustomerStructure = async () => {
		try {
			const { data } = await customerStructure({
				shopId: shop.value?.shopId,
				startDate: calendarValue.value[0] ?? '',
				endDate: calendarValue.value[1] ?? '',
			})
			if (data && Object.keys(data).length > 0) {
				customerStructureData.value = data
				customerStructureChartsData.value = { originData: data.dataList, data: formatChartsData(data.dataList) }
				if (currentIndex.value === 0) {
					distributionDirectSalesChartsData.value = { originData: data.fxList, data: formatChartsData(data.fxList) }
				} else {
					distributionDirectSalesChartsData.value = { originData: data.zxList, data: formatChartsData(data.zxList) }
				}
			} else {
				customerStructureData.value = {}
				customerStructureChartsData.value = {}
				distributionDirectSalesChartsData.value = {}
			}
		} catch (e) {
			console.error('客源结构分析', e)
		}
	}

	// 直销分销切换
	const change = () => {
		if (currentIndex.value === 0) {
			customerStructureData.value?.fxList ? distributionDirectSalesChartsData.value = { originData: customerStructureData.value?.fxList, data: formatChartsData(customerStructureData.value?.fxList) } : distributionDirectSalesChartsData.value = {}
		} else {
			customerStructureData.value?.zxList ? distributionDirectSalesChartsData.value = { originData: customerStructureData.value?.zxList, data: formatChartsData(customerStructureData.value?.zxList) } : distributionDirectSalesChartsData.value = {}
		}
	}

	const confirm = () => {
		toCustomerStructure()
	}

	const padding_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 20 + 'rpx'
</script>

<style scoped lang="scss">
	:deep() {
		.empty-img .empty {
			width: v-bind(tableWidth);
		}
	}

	.container {
		width: 100%;
		height: 100%;
	}


	.content {
		padding: 0 20rpx;
		flex: 1;
		overflow-y: auto;
		padding-bottom: v-bind(padding_bottom);
	}

	.picker-section {
		margin: 0 20rpx;
		margin-top: 20rpx;
		border-radius: 200rpx;
		background: #ffffffe6;
		margin-bottom: 40rpx;

		.picker {
			justify-content: space-between;
			padding: 16rpx 40rpx;
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 24rpx;
		}
	}

	.section {
		padding: 32rpx 40rpx 40rpx;
		background: #fff;
		border-radius: 23.07rpx;
		margin-bottom: 20rpx;

		.title {
			justify-content: space-between;
			margin-bottom: 40rpx;
			align-items: flex-start;

			&--tab {
				margin-bottom: 9rpx;
			}

			.title-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.title-tip {
				color: #999999;
				font-size: 24rpx;
				line-height: 24rpx;
				margin-left: 20rpx;
			}

			.title-btn {
				border-radius: 200rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				padding: 8rpx 16rpx;

				.btn-text {
					color: #1f2428;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 8rpx;
				}
			}
		}

		.target-table {
			border-radius: 20rpx;
			overflow: hidden;

			.target-item {
				display: flex;
				align-items: center;
				justify-content: center;

				.item-info {
					margin-left: 8rpx;
					width: 22rpx;
					height: 22rpx;
				}
			}

			.rate-item {
				display: flex;
				align-items: center;
				justify-content: center;

				.item-icon {
					width: 16rpx;
					height: 20rpx;
				}

				.item-text {
					padding-left: 8rpx;
					font-size: 24rpx;
					line-height: 24rpx;
					font-weight: 700;
					font-family: "Din Bold";
					color: #1F2428;
				}

				.up-text {
					color: #FF4D4D;
				}

				.down-text {
					color: #56CC93
				}
			}
		}

		.calendar {
			margin-bottom: 40rpx;

			:deep() {
				.calendar-picker {
					background: #fff;
				}
			}
		}

		.customer-source-structure-chars {
			height: 344rpx;
		}

		.c-subsection-vue {
			margin-bottom: 40rpx;
		}
	}
</style>