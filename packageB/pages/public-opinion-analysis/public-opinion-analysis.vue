<template>
	<view class="container">
		<view class="section" style="padding-bottom: 20rpx;">
			<view class="title fr">
				<text class="title-text">舆情指标</text>
				<view class="title-btn fr"
					@click="route_to_view(`/packageB/pages/driver-analysis/driver-analysis?shopId=${orgId}`,()=>{trackEvent('011')})">
					<text class="btn-text">动因分析</text>
					<up-icon name="arrow-right" color="#1F2428" size="20rpx" />
				</view>
			</view>
      <view class="description">
        <view class="fr">
          <image class="img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-21/1745200634744.png"/>
          <text class="text">说明</text>
        </view>
        <view class="details">
          2025-04-17之后的新增网评分不包含携程/美团等分销渠道点评分。按渠道查看点评分时，各分销渠道点评分不支持查看，直销App/小程序点评分可正常查看。
        </view>
      </view>
			<view class="date">
				<view style="margin-bottom: 40rpx;">
					<c-subsection-vue v-model="subsectionValue" :list="subsectionList" @change="updateData" />
				</view>
				<view class="calendar">
					<cCalendarVue v-model="calendarValue" :startDate="dayjs().subtract(1, 'year').format('YYYY-MM-DD')"
						@confirm="confirm" :showIcon="false" />
				</view>
			</view>
			<view class="fr score-section">
				<view class="fl score-block" v-if="shopOpinionAnalysisData.length > 0"
					v-for="(item,index) in shopOpinionAnalysisData" :key="index"
					:style="{backgroundImage:constant[index].background}">
					<view class="fr text" :style="{backgroundImage:constant[index].titleBackground}"
						@click="showModal(item.dataName, item.describe, item)">
						<view class="name">{{item.dataName}}</view>
						<image class="tip"
							src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729678941228.png"
							mode=""></image>
					</view>
					<view class="score">
						<text class="num">{{item.data}}</text>{{item.data !== '-' ? constant[index].unit : ''}}
					</view>
					<view class="fr month-on-month">
						<view class="month-on-month-text">环比</view>
						<image v-if="item.hb && item.hb != '-'" class="img"
							:src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${item.hb.indexOf('-') ==-1 ? '1729644716250' : '1729644734157'}.png`">
						</image>
						<view class="month-on-month-value"
							:class="[item.hb == '-' ?  '' : item.hb.indexOf('-') == -1 ? 'up-text' : 'down-text']">
							{{item.hb !== '-' ? item.hb.replace(/-/g, '') : item.hb}}
						</view>
					</view>
				</view>
				<view style="width: 100%;" v-else>
					<emptyVue />
				</view>
			</view>
		</view>
		<view class="section">
			<view class="title fr">
				<text class="title-text">按渠道分析</text>
			</view>
			<view class="tabs">
				<cTabs :list="tabs" v-model="dataTypeIndex" :isBig="false"
					itemStyle="height: 60rpx;align-items:flex-start;padding:0 20rpx" @change="handleDataType" />
			</view>
			<view class="channel-analysis">
				<mixChartVue :data="shopChannelAnalysisData" :pageScrollTop="pageScroll" />
			</view>
		</view>
		<view class="section">
			<view class="title fr">
				<text class="title-text">评分占比</text>
			</view>
			<view class="tabs">
				<cTabs :list="channelList" v-model="channelIndex" :isBig="false"
					itemStyle="height: 60rpx;align-items:flex-start;padding:0 20rpx" @change="handleChannel" />
			</view>
			<view class="proportion-ratings">
				<ringChartVue :data="shopScoreRateData" type="b" />
				<!-- <view class="proportion-ratings-chars"></view>
				<view class="float-layer">
					<view class="block fl" v-for="(item,index) in 5" :key="index">
						<view class="top fr row">
							<view class="spot l" v-if="(index+1)%2!==0"></view>
							<view class="name">4~4.5</view>
							<view class="spot r" v-if="(index+1)%2===0"></view>
						</view>
						<view class="proportion row" :class="[(index+1)%2===0 ? 'r':'l']">5（15%）</view>
					</view>
				</view> -->
			</view>
      <view class="btn-block fr"
        @click="route_to_view('/public/pages/webview/webview?url=' + webviewURL + '/pages/comment/comment' + '&shopId=' + orgId,()=>{trackEvent('012')})">
				<view class="btn fr">
					<view class="btn-text">查看实时评分</view>
					<up-icon name="arrow-right" color="#C35943" size="20rpx" />
				</view>
			</view>
		</view>
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onPageScroll } from '@dcloudio/uni-app'
	import { onMounted, ref, getCurrentInstance, ComponentInternalInstance } from 'vue';
	import cTabs from '@/components/c-tabs/c-tabs.vue'
	import mixChartVue from '@/components/mix-chart/mix-chart.vue';
	import ringChartVue from '@/components/ring-chart/ring-chart.vue';
	import cSubsectionVue from "@/components/c-subsection/c-subsection.vue";
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue';
	import cModal from '@/components/c-modal/index';
	import emptyVue from '@/components/empty/empty.vue';
	// import { doughnutChart, mixedLineAndBar } from './char';
	import { useCommon } from '@/hooks/useGlobalData';
	import dayjs from 'dayjs';
	import { shopChannelAnalysis, shopOpinionAnalysis, shopScoreRate } from '../../api';
	import { CHANNELLIST } from '@/packageB/constant';
	const { route_to_view, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)
  const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL

	const tabs = [
		{ name: '新增网评分', value: 1 },
		{ name: '差评率', value: 2 },
		{ name: '点评量', value: 3 },
		{ name: '卫生分', value: 4 },
		{ name: '位置分', value: 5 },
		{ name: '服务分', value: 6 },
		{ name: '设施分', value: 7 },
	]

	const newChannelList = CHANNELLIST.slice(1);
	const channelList = newChannelList

	const subsectionList : { name : string, value : string }[] = [
		{ name: '昨日', value: 'yesterday' },
		{ name: '近一周', value: 'week' },
		{ name: '本月', value: 'month' },
	]

	const constant = [
		{
			titleBackground: 'linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 178, 161, 0.05) 0%, rgba(247, 94, 59, 0.05) 100%)',
			unit: '分'
		},
		{
			titleBackground: 'linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%)',
			background: 'linear-gradient(203.5deg, rgba(188, 227, 208, 0.05) 0%, rgba(67, 191, 131, 0.05) 100%)',
			unit: ''
		},
		{
			titleBackground: 'linear-gradient(25.9deg, #ff8c1a 0%, #ffcf9e 100%)',
			background: 'linear-gradient(203.5deg, rgba(255, 140, 26, 0.05) 0%, rgba(255, 140, 26, 0.05) 100%)',
			unit: ''
		},
		{
			titleBackground: 'linear-gradient(17.3deg, #6046f2 0%, #c7a2fc 100%)',
			background: 'linear-gradient(203.5deg, rgba(96, 70, 242, 0.05) 0%, rgba(199, 162, 252, 0.05) 100%)',
			unit: ''
		},
		{
			titleBackground: 'linear-gradient(197.7deg, #91baff 0%, #1474fb 100%)',
			background: 'linear-gradient(203.5deg, rgba(145, 186, 255, 0.05) 0%, rgba(20, 116, 251, 0.05) 100%)',
			unit: ''
		}
	]

	const dataTypeIndex = ref<number>(0), // 类型index
		channelIndex = ref<number>(0), // 渠道index
		orgId = ref<string>(''),
		subsectionValue = ref<number>(1), // 默认近一周
		calendarValue = ref([]),
		shopOpinionAnalysisData = ref([]),
		shopChannelAnalysisData = ref({}),
		shopScoreRateData = ref(),
		pageScroll = ref<number>() // 页面滚动高度

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')
	const showModal = (title : string, content : string, data : any) => {
		modalShow.value = true
		modalTitle.value = title
		modalContent.value = content
	}

	onLoad((options) => {
		console.log(options.shopId);
		orgId.value = options.shopId
		handleDateFormat()
	})

	onMounted(() => {
		getData()
	})

	const getData = () => {
		toShopOpinionAnalysis()
		toShopChannelAnalysis()
		toShopScoreRate()
	}

	// 门店舆情分析指标
	const toShopOpinionAnalysis = async () => {
		try {
			const params = {
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1],
				orgId: orgId.value
			}
			const { data } = await shopOpinionAnalysis(params)
			if (data && data.length > 0) {
				shopOpinionAnalysisData.value = data
			} else {
				shopOpinionAnalysisData.value = []
			}
		} catch (e) {
			console.error('门店舆情分析指标 error', e)
		}
	}

	// 门店渠道分析
	const toShopChannelAnalysis = async () => {
		try {
			const params = {
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1],
				orgId: orgId.value,
				dataType: tabs[dataTypeIndex.value].value
			}
			const { data } = await shopChannelAnalysis(params)
			if (data) {
				const res = {
					categories: data.map(({ dataName }) => dataName),
					values: [
						{
							name: tabs[dataTypeIndex.value].name,
							type: 'column',
							index: 0,
							data: data.map(({ data }) => data == '-' ? 0 : data),
						},
						{
							name: '环比',
							type: 'line',
							index: 0,
							textColor: '#FF8C1A',
							linearColor: [[0, '#FF8C1A'], [1, '#FFCF9E']],
							data: data.map(({ hb }) => hb == '-' ? 0 : hb)
						},
						{
							name: '同比',
							type: 'line',
							index: 1,
							textColor: '#43BF83',
							linearColor: [[0, '#43BF83'], [1, '#BCE3D0']],
							data: data.map(({ tb }) => tb == '-' ? 0 : tb)
						}
					]
				}
				console.log(res);
				shopChannelAnalysisData.value = res
			} else {
				shopChannelAnalysisData.value = {}
			}
		} catch (e) {
			console.error('门店渠道分析 error', e)
		}
	}

	const formatChartsData = (v : any) => {
		return v.map(({ dataName, num }) => {
			return {
				name: dataName,
				value: Number(num),
				labelShow: false
			}
		})
	}

	// 门店评分占比
	const toShopScoreRate = async () => {
		try {
			const params = {
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1],
				orgId: orgId.value,
				channelType: channelList[channelIndex.value].value
			}
			const { data } = await shopScoreRate(params)
			if (data && data.length > 0) {
        const notZeroData = data.filter(item => item.num != '0')
        if (notZeroData.length > 0) {
          shopScoreRateData.value = { originData: notZeroData, data: formatChartsData(notZeroData) }
        } else {
          shopScoreRateData.value = []
        }
			} else {
				shopScoreRateData.value = []
			}
		} catch (e) {
			console.error('门店评分占比 error', e)
		}
	}

	// 日期参数格式化
	const handleDateFormat = () => {
		const value = subsectionList[subsectionValue.value].value
		let startDate = null, endDate = null
		if (value === 'yesterday') {
			const date = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
			startDate = date
			endDate = date
		} else if (value === 'week') {
			startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
			endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		} else if (value === 'month') {
			startDate = dayjs().startOf('month').format('YYYY-MM-DD');
			endDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
		}
		calendarValue.value = [startDate, endDate]
	}

	// 时间选择
	const updateData = () => {
		handleDateFormat()
		dataTypeIndex.value = 0
		channelIndex.value = 0
		getData()
	}

	// 自定义时间
	const confirm = () => {
		subsectionValue.value = null
		getData()
	}

	const handleDataType = () => {
		toShopChannelAnalysis()
	}

	const handleChannel = () => {
		toShopScoreRate()
	}

	onPageScroll((e) => {
		pageScroll.value = e.scrollTop
	})
</script>

<style scoped lang="scss">
	:deep(.c-modal) {
		width: auto !important;
		height: auto !important;
	}

	.container {
		padding: 20rpx 20rpx 40rpx;
	}

	.section {
		padding: 40rpx;
		background: #fff;
		border-radius: 26.46rpx;
		margin-bottom: 20rpx;

		.title {
			justify-content: space-between;
			margin-bottom: 40rpx;
			align-items: flex-start;

			&--tab {
				margin-bottom: 9rpx;
			}

			.title-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.title-tip {
				color: #999999;
				font-size: 24rpx;
				line-height: 24rpx;
				margin-left: 20rpx;
			}

			.title-btn {
				border-radius: 200rpx;
				border: 1rpx solid #ffffff;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				padding: 8rpx 16rpx;

				.btn-text {
					color: #1f2428;
					font-size: 22rpx;
					line-height: 22rpx;

					&-primary {
						color: #c35943;
					}
				}
			}
		}

    .description {
      border-radius: 20rpx;
      background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
      padding: 16rpx 20rpx;
      margin-bottom: 20rpx;

      .img {
        width: 22rpx;
        height: 22rpx;
        flex-shrink: 0;
      }

      .text {
        color: $uni-text-color;
        font-size: 26rpx;
        font-weight: bold;
        line-height: 26rpx;
        margin-left: 8rpx;
      }

      .details {
        margin-top: 8rpx;
        color: #1f2428b3;
        font-size: 22rpx;
        line-height: 38rpx;
        padding-left: 30rpx;
      }
    }

		.calendar {
			margin-bottom: 40rpx;
		}

		.score-section {
			flex-wrap: wrap;

			.score-block {
				min-width: 196rpx;
				border-radius: 20rpx;
				align-items: center;
				padding-bottom: 28rpx;
				margin-right: 20rpx;
				margin-bottom: 20rpx;

				&:nth-child(odd):not(:first-child) {
					margin-right: 0;
				}

				.text {
					text-align: left;
					justify-content: center;
					border-radius: 100rpx 100rpx 100rpx 0;
					padding: 12rpx 0;
					margin-bottom: 18rpx;
					width: 100%;

					.name {
						color: #ffffff;
						font-size: 24rpx;
						font-weight: bold;
						line-height: 24rpx;
					}

					.tip {
						width: 22rpx;
						height: 22rpx;
						flex-shrink: 0;
						margin-left: 8rpx;
					}
				}

				.score {
					color: #1f2428;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-bottom: 18rpx;

					.num {
						color: #1f2428;
						font-size: 42rpx;
						font-weight: 700;
						font-family: "DIN Bold";
						line-height: 42rpx;
						margin-right: 12rpx;
					}
				}

				.month-on-month {
					&-text {
						color: rgba(31, 36, 40, 0.7);
						font-size: 22rpx;
						line-height: 22rpx;
					}

					.img {
						width: 16rpx;
						height: 20rpx;
						flex-shrink: 0;
						margin-left: 8rpx;
					}

					&-value {
						font-size: 24rpx;
						font-weight: 700;
						font-family: "DIN Bold";
						line-height: 24rpx;
						margin-left: 8rpx;
					}
				}

				.up-text {
					color: #FF4D4D;
				}

				.down-text {
					color: #56CC93
				}
			}
		}

		.tabs {
			margin-bottom: 8rpx;
		}

		.channel-analysis {
			height: max-content;
			margin-bottom: 40rpx;
		}

		.proportion-ratings {
			height: 406rpx;
			position: relative;
			z-index: 2;
			margin-bottom: 60rpx;

			.proportion-ratings-chars {
				width: 100%;
				height: 100%;
			}

			.float-layer {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				align-items: center;
				z-index: -1;

				.block {
					width: 218rpx;
					// height: 98rpx;
					border-radius: 20rpx;
					background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f500 100%);
					padding: 20rpx;
					justify-content: space-between;

					.row {
						margin-bottom: 12rpx;

						&:last-child {
							margin-bottom: 0;
						}
					}

					&:nth-child(even) {
						background: linear-gradient(90deg, #f5f5f500 0%, #f5f5f5 100%);
						align-items: flex-end;
					}

					.top {
						justify-content: flex-start;

						.spot {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background: linear-gradient(225deg, #ffeca6 0%, #fcd80d 100%);
							margin-right: 12rpx;
						}

						.name {
							color: #1f2428;
							font-size: 22rpx;
							font-weight: bold;
							line-height: 22rpx;
						}


						.spot {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background: linear-gradient(225deg, #ffeca6 0%, #fcd80d 100%);
							margin-right: 12rpx;
						}

						.spot.l {
							margin-right: 12rpx;
						}

						.spot.r {
							margin-left: 12rpx;
						}

						.name {
							color: #1f2428;
							font-size: 22rpx;
							font-weight: bold;
							line-height: 22rpx;
						}

					}

					.proportion {
						color: #1f2428b3;
						font-size: 24rpx;
						font-weight: bold;
						line-height: 24rpx;
					}

					.proportion.l {
						margin-left: 28rpx;
					}

					.proportion.r {
						margin-right: 28rpx;
					}
				}
			}
		}

		.btn-block {
			justify-content: center;

			.btn {
				border-radius: 200rpx;
				border: 2rpx solid #F75E3B;
				padding: 14rpx 28rpx;

				&-text {
					color: #c35943;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 16rpx;
				}
			}
		}
	}

	:deep(.calendar-picker) {
		background: #fff;
		height: 54rpx;
	}
</style>