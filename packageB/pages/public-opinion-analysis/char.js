import * as echarts from 'echarts';

export function mixedLineAndBar(res) {
	return {
		grid: {
			top: "5%"
		},
		legend: {
			data: ['新增网评分', '同比', '环比'],
			icon: "circle",
			bottom: 0,
			itemWidth: 8,
			itemHeight: 8,
			itemGap: 20,
			textStyle: {
				color: "#1F2428",
				fontSize: 12
			}
		},
		xAxis: [{
			type: 'category',
			data: ['飞猪', '携程', '艺龙', '美团', '去哪', 'App'],
			axisPointer: {
				type: 'shadow'
			},
			axisLine: {
				lineStyle: {
					color: "#F2F2F2"
				}
			},
			axisTick: {
				show: false
			},
			axisLabel: {
				color: "rgba(31, 36, 40, 0.7)"
			}
		}],
		yAxis: [{
				type: 'value',
				min: 0,
				max: 100,
				interval: 20,
				axisLine: {
					show: true,
					lineStyle: {
						color: "#F2F2F2"
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: "dashed"
					}
				},
				axisLabel: {
					color: "rgba(31, 36, 40, 0.7)"
				}
			},
			{
				type: 'value',
				show: false,
				axisLabel: {
					show: false
				}
			},
			{
				type: 'value',
				show: false,
				axisLabel: {
					show: false
				}
			}
		],
		series: [{
				name: '新增网评分',
				type: 'bar',
				data: [23.0, 4.9, 7.0, 23.2, 25.6, 76.7],
				barWidth: 10,
				itemStyle: {
					borderRadius: [50, 50, 0, 0], //（顺时针左上，右上，右下，左下）
					color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
							offset: 0,
							color: 'rgba(247, 94, 59, 1)'
						},
						{
							offset: 1,
							color: 'rgba(255, 178, 161, 1)'
						}
					]),
				}
			},
			{
				name: '同比',
				type: 'line',
				label: {
					show: true,
					distance: 8,
					color: "#FF8C1A"
				},
				yAxisIndex: 1,
				data: [12.0, 32.2, 23.3, 14.5, 36.3, 10.2],
				symbol: "circle",
				symbolSize: 0,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
							offset: 0,
							color: '#FF8C1A'
						},
						{
							offset: 1,
							color: '#FFCF9E'
						}
					]),
				},
				lineStyle: {
					width: 3
				}
			},
			{
				name: '环比',
				type: 'line',
				label: {
					show: true,
					distance: 8,
					color: "#43BF83"
				},
				yAxisIndex: 2,
				data: [63.0, 32.1, 53.3, 62.5, 73.3, 12.2],
				symbol: "circle",
				symbolSize: 0,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
							offset: 0,
							color: '#43BF83'
						},
						{
							offset: 1,
							color: '#BCE3D0'
						}
					]),
				},
				lineStyle: {
					width: 3
				}
			}
		]
	};
}


export function doughnutChart(res) {
	return {
		color: ['#1474FB', '#6046F2', '#43BF83', '#F75E3B', '#FCD80D'],
		series: [{
			name: 'Access From',
			type: 'pie',
			radius: ['40%', '70%'],
			label: {
				show: false,
				position: 'center'
			},
			labelLine: {
				show: false
			},
			data: [{
					value: 1048,
					name: 'Search Engine'
				},
				{
					value: 735,
					name: 'Direct'
				},
				{
					value: 580,
					name: 'Email'
				},
				{
					value: 484,
					name: 'Union Ads'
				},
				{
					value: 300,
					name: 'Video Ads'
				}
			]
		}]
	};
}