<template>
	<view class="container">
		<view class="calendar">
			<cCalendarVue v-model="calendarValue" @confirm="confirm" />
		</view>
		<view class="section">
			<view class="tabs">
				<cTabsVue :list="tabs" v-model="currentIndex" :isBig="false" @change="setColumn"
					itemStyle="width:108rpx;height: 60rpx;align-items:flex-start;" />
			</view>
			<view class="table-block">
				<cTable :header="shopData.column" :list="shopData.list" :height="tbodyHeight" @updateList="sortList"
					@scrolltolower="scrolltolower">
					<template #default="scope">
						<view v-if="['rate'].includes(scope.prop)" class="rate-item"
							@click="route_to_view(`/packageB/pages/public-opinion-analysis/public-opinion-analysis?shopId=${scope.data.shopId}`)">
							<text class="text">查看</text>
						</view>
					</template>
				</cTable>
			</view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, getCurrentInstance, nextTick, onMounted, ref } from 'vue';
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import cTable from '@/components/c-table/c-table.vue'
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue';
	import { shopRank } from '../../api';
	import { OrgLevel, PaginationParams } from '@/types/dataCockpit';
	import { useCommon } from '@/hooks/useGlobalData';

	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance();
	const tabs : { name : string, value : 1 | 2 | 3 | 4 | 5 }[] = [
		{ name: '卫生分', value: 1 },
		{ name: '位置分', value: 2 },
		{ name: '服务分', value: 3 },
		{ name: '设施分', value: 4 },
		{ name: '差评率', value: 5 },
	]
	const currentIndex = ref(0),
		areaData = ref<OrgLevel>(),
		dateType = ref<'day' | 'month'>(),
		calendarValue = ref([]),
		tbodyHeight = ref<string>(),
		loadMore = ref<Boolean>(true)

	const shopData = ref({
		defaultColumn: [
			{
				name: '门店',
				prop: 'shopName',
				width: 156
			},
			{
				name: '门店ID',
				prop: 'shopId',
			},
			{
				name: '卫生分',
				prop: 'score',
			},
			{
				name: '操作',
				prop: 'rate',
				slot: 'rate',
			},
		],
		column: [],
		list: [],
	});

	const pagination = ref<PaginationParams>({
		pageNum: 1,
		pageSize: 20
	})

	onLoad((options) => {
		areaData.value = JSON.parse(options.areaData) ?? null
		dateType.value = options.dateType ?? null
	})

	onMounted(() => {
		setColumn()
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.table-block`).boundingClientRect((res : any) => {
			tbodyHeight.value = info.windowHeight - res.top - 42 - 20 - 20 + 'px'
		}).exec();
	})

	const setColumn = () => {
		shopData.value.column = JSON.parse(JSON.stringify(shopData.value.defaultColumn))
		shopData.value.column.splice(2, 1,
			{
				name: tabs[currentIndex.value].name,
				prop: 'score',
				sort: true,
				sortType: 'desc',
				orderByColumn: tabs[currentIndex.value].value
			}
		);
		reset()
		toShopRank()
	}

	// 门店排名
	const toShopRank = async (orderBy ?: number, sortBy ?: string) => {
		try {
			const params = {
				...areaData.value,
				dateType: dateType.value,
				dataType: tabs[currentIndex.value].value,
				orderBy,
				sortBy,
				startDate: calendarValue.value[0] ?? '',
				endDate: calendarValue.value[1] ?? '',
				...pagination.value
			}
			const { data } = await shopRank(params)
			if (data) {
				calendarValue.value = [data.startDate, data.endDate]
				if (data.shopList.length < pagination.value.pageSize) loadMore.value = false
				nextTick(() => {
					if (shopData.value.list && shopData.value.list.length > 0)
						shopData.value.list.push(...data.shopList)
					else
						shopData.value.list = data.shopList
				})
			}
		} catch (e) {
			console.error('门店排名 error', e)
		}
	}

	// 表格排序
	const sortList = (item : any) => {
		reset()
		toShopRank(item.orderByColumn, item.sortType)
	}

	const confirm = (e) => {
		setColumn()
	}

	const scrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		toShopRank()
	}


	const reset = () => {
		loadMore.value = true
		shopData.value.list = []
		pagination.value = {
			pageNum: 1,
			pageSize: 20
		}
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 20rpx;
	}

	.calendar {
		margin-bottom: 20rpx;
	}

	:deep(.calendar-picker) {
		background: #fff;
		border: none;
	}

	.section {
		padding: 40rpx;
		border-radius: 20rpx;
		background: #fff;

		.tabs {
			margin-bottom: 20rpx;
		}

		.table-block {
			.rate-item {
				.text {
					color: $uni-text-color;
				}
			}
		}
	}
</style>