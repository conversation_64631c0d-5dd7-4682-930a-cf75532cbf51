<!--
 * @Description: 经营诊断报告
 * @Author: gu_shiyuan"
 * @Date: 2024-11-18 09:46:01
 * @LastEditors: gu_shiyuan"
 * @LastEditTime: 2024-11-18 14:50:13
 * @FilePath: /smart-operation/packageB/pages/business-diagnosis/business-diagnosis.vue
-->

<template>
	<view class="fl container">
		<up-navbar title="经营诊断" :autoBack="true" fixed :bgColor="bgColor" :titleStyle="{color: titleColor}"
			:leftIconColor="titleColor">
		</up-navbar>
		<commonBgVue :height="bgHeight" />
		<view class="fl header">
			<text class="fr shop-name">尚客优龙华店经营健康分</text>
			<view class="fr shop-score">
				<text class="score DIN-Bold">{{ data.totalPoint }}</text>
				<text class="unit">分</text>
				<text class="rules"
					@click="route_to_view(`/public/pages/webview/webview?url=${webviewURL}/pages/rules/rules`)">计算规则</text>
			</view>
			<view class="fr report">
				<view class="fr report-date">
					<text class="report-date__text">报告时间范围：{{ date.startDate }}～{{ date.endDate }}</text>
				</view>
				<view class="fr" v-if="isShow">
					<picker mode="selector" :value="pickerValue" :range="dateList" @change="onChange">
						<view class="fr history">
							<text class="history-text">历史报告</text>
							<uni-icons type="arrowright" color="#C35943" size="10"></uni-icons>
						</view>

					</picker>
				</view>
			</view>
		</view>
		<view class="content">
			<!--历史报告-->
			<radarVue :data="data" :shopName="globalData?.shop?.shopName" @showModal="showPopup = true" />
			<!--改版后报告-->
			<!-- <radar2Vue :data="data" :shopName="globalData?.shop?.shopName" @showModal="showPopup = true" /> -->
			<rankingVue :data="data" />
			<intelligentDiagnosisVue :data="data" />
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import {
		onLoad,
		onReady,
		onPageScroll
	} from '@dcloudio/uni-app'
	import rankingVue from './components/ranking.vue';
	import intelligentDiagnosisVue from './components/intelligent-diagnosis.vue';
	import radarVue from './components/radar.vue';
	import commonBgVue from '../../components/common-bg/common-bg.vue';
	import radar2Vue from './components/radar2.vue';
	import {
		queryShopHealthIndex
	} from '../../api'
	import {
		useCommon
	} from '@/hooks/useGlobalData';
	import {
		getCurrentInstance,
		ref,
		unref,
	} from 'vue';
	import {
		formatDate, throttle
	} from '@/utils/util';
	const {
		globalData, route_to_view
	} = useCommon(getCurrentInstance())
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL

	const props = defineProps({
		shopId: {
			type: String,
			default: ''
		}
	})

	const navHeight = uni.getSystemInfoSync().safeAreaInsets.top + 44 + 'px'
	const bgHeight = ref<number | string>(374)
	const bgColor = ref('transparent')
	const titleColor = ref('#fff')
	const shopId = ref()
	/**
		* @description: 计算默认报告时间范围
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const getDate = () : any => {
		const date = new Date()
		const h = date.getHours()
		const week = date.getDay()
		const day = date.getDate()
		let sv = 0,
			ev = 0,
			diff = week == 0 ? 7 : week
		if (week == 1) {
			if (h >= 9) {
				ev = day - 2
				sv = day - 8
			} else {
				ev = day - 9
				sv = day - 15
			}
		} else {
			sv = day - diff - 7
			ev = day - diff - 1
		}
		const startDate = formatDate(new Date(new Date().setDate(sv)))
		const endDate = formatDate(new Date(new Date().setDate(ev)))
		return {
			startDate,
			endDate
		}
	}

	const data = ref({}),
		date = ref(getDate()),
		dateDiffInDays = (date1 : number, date2 : number) => {
			const diffTime = Math.abs(date2 - date1);
			const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
			return diffDays;
		},
		dateList = ref([]),
		pickerValue = ref(0),
		isShow = ref(true),
		showPopup = ref(false)


	/**
		* @description: 计算历史报告时间范围
		* @param {*} data
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const handleDateList = (data : any) : string[] => {
		const {
			firstStartTime,
			firstEndTime
		} = data
		const {
			startDate,
			endDate
		} = getDate()
		if (!firstStartTime) return []
		let arr = [`${firstStartTime}~${firstEndTime}`]
		if (startDate == firstStartTime && endDate == firstEndTime) {
			return arr
		}
		const days = dateDiffInDays(new Date(startDate), new Date(firstStartTime))
		for (let i = 0; i < days / 7; i++) {
			const start = new Date(firstStartTime)
			const end = new Date(firstEndTime)
			const start_date = formatDate(new Date(start.setDate(start.getDate() + (i + 1) * 7)))
			const end_date = formatDate(new Date(end.setDate(end.getDate() + (i + 1) * 7)))
			arr.push(`${start_date}~${end_date}`)
		}
		return arr
	}

	/**
		* @description: 获取健康报告信息
		* @param {*} Promise
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const toQueryShopHealthIndex = async () : Promise<void> => {
		try {
			const res = await queryShopHealthIndex({
				shopId: props.shopId,
				queryType: 2,
				...unref(date)
			})
			if (res.data) {
				if (res.data.data) {
					data.value = res.data.data
				} else {
					data.value = {}
				}
				dateList.value = handleDateList(res.data)
				pickerValue.value = dateList.value.indexOf(`${unref(date).startDate}~${unref(date).endDate}`)
			}
		} catch (e) {
			console.log('获取健康报告报错', e)
		}
	}

	/**
		* @description: 选择历史报告
		* @param {*} e
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const onChange = (e : any) : void => {
		pickerValue.value = e.detail.value
		const [startDate, endDate] = unref(dateList)[pickerValue.value].split('~')
		date.value = {
			startDate,
			endDate
		}
		toQueryShopHealthIndex()
	}

	/**
		* @description: 计算头部的高度设置顶部背景色高度
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const calcuteHeight = () : void => {
		uni.createSelectorQuery().select('.header').boundingClientRect((res : UniApp.NodeInfo) => {
			bgHeight.value = res.height + uni.upx2px(164) + 'px'
		}).exec()
	}

	onLoad((opts) => {
		if (opts.startDate && opts.endDate) {
			isShow.value = false
			date.value = {
				startDate: opts.startDate,
				endDate: opts.endDate
			}
		}
		shopId.value = opts.shopId
	})


	onReady(() => {
		toQueryShopHealthIndex()
		calcuteHeight()
	})

	/**
		* @description: 监听页面滚动设置头部背景色
		* @return {*}
		* @author: gu_shiyuan"
		*/
	onPageScroll((e) => {
		if (e.scrollTop < 10) {
			bgColor.value = 'transparent'
			titleColor.value = '#fff'
		}
		if (e.scrollTop > 50) {
			bgColor.value = 'rgba(255,255,255,.4)'
			titleColor.value = 'rgba(#1F2428, .4)'
		}
		if (e.scrollTop > 80) {
			bgColor.value = 'rgba(255,255,255,.8)'
			titleColor.value = 'rgba(#1F2428, .8)'
		}
		if (e.scrollTop > 100) {
			bgColor.value = '#fff'
			titleColor.value = 'rgba(#1F2428, 1)'
		}
	})
</script>

<style lang="scss" scoped>
	.container {

		.header {
			justify-content: space-between;
			align-items: stretch;
			padding-top: v-bind(navHeight);
			padding-bottom: 20rpx;
			font-size: 24rpx;

			.shop-name,
			.score,
			.unit,
			.rules {
				color: #fff;
			}

			.shop-name {
				justify-content: center;
				margin-bottom: 20rpx;
				font-size: 36rpx;
				margin-top: 20rpx;
			}

			.shop-score {
				justify-content: center;
				margin-bottom: 40rpx;
			}

			.score {
				font-size: 64rpx;
				line-height: 64rpx;
				margin-right: 9rpx;
			}

			.unit {
				margin-right: 20rpx;
			}

			.rules {
				font-size: 22rpx;
				text-decoration: underline;
			}

			.report {
				justify-content: space-between;
				margin: 0 40rpx;

				.report-date {
					flex-shrink: 0;
					justify-content: space-between;
					font-size: 22rpx;

					&__text {
						color: #fff;
					}
				}

				.history {
					justify-content: center;
					width: 136rpx;
					height: 38rpx;
					background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f5cc 100%);
					border-radius: 200rpx;

					&-text {
						color: #C35943;
						font-size: 22rpx;
						font-weight: bold;
					}
				}
			}

		}

		.content {
			margin: 0 20rpx 0;
		}
	}
</style>