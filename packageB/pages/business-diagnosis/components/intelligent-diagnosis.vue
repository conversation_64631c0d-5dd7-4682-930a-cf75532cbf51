<template>
	<view class="fl block">
		<view class="fr header">
			<image src="/static/tabbar/ai.png" mode="aspectFill" lazy-load class="logo"></image>
			<text class="title">智能诊断</text>
		</view>
		<view class="fl content">
			<text class="tips">以下为“综合健康指数”分数不佳的关键因素，可点击推荐提示方法进行提升</text>
			<scroll-view scroll-x="true" class="table">
				<view class="tr thead">
					<text class="td" v-for="(item, index) in thead" :key="index">{{ item }}</text>
				</view>
				<view class="tr" v-for="(item, index) in source" :key="index">
					<view class="td">
						<text>{{ item.name }}</text>
					</view>
					<view class="td" @tap="onBack(item.way)">
						<text class="theme-primary" :class="{'theme-primary__underline': item.way != '-'}">{{ item.way }}</text>
						<uni-icons v-if="item.way != '-'" type="arrowright" size="10" color="#1e61fc" class="icon"></uni-icons>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
	import {
		computed
	} from 'vue'
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		}
	})
	const zn = {
		adrPoint: {
			way: 'ADR提升攻略',
			name: 'ADR'
		},
		cardSoldPoint: {
			way: '售卡提升攻略',
			name: '售卡数'
		},
		directCrsPoint: {
			way: 'CRS直销提升攻略',
			name: 'CRS直销'
		},
		distributeCrsPoint: {
			way: 'CRS分销提升攻略',
			name: 'CRS分销'
		},
		occPoint: {
			way: 'OCC提升攻略',
			name: 'OCC'
		}
	}
	const source = computed(() => {
		const temp = ['adrPoint', 'cardSoldPoint', 'directCrsPoint', 'distributeCrsPoint', 'occPoint']
		const isAll = temp.map(key => props.data[key]).every(item => item == '-')
		if (isAll) {
			return [{
				name: '-',
				value: '-',
				way: '-'
			}, {
				name: '-',
				value: '-',
				way: '-'
			}]
		}
		const arr = temp.map(key => {
			return {
				name: zn[key].name,
				way: zn[key].way,
				value: props.data[key] == '-' ? 0 : Number(props.data[key])
			}
		})
		const minValue = arr.reduce((min, current) => {
			return min === undefined || current.value < min ? current.value : min;
		}, undefined);
		const secondMinValue = arr.reduce((min, current) => {
			if (current.value !== minValue) {
				return min === undefined || current.value < min ? current.value : min;
			}
			return min;
		}, undefined);
		const result = arr.filter(item => item.value === minValue || item.value === secondMinValue);
		// 根据需要对结果进行排序
		result.sort((a, b) => a.value - b.value);
		return result.slice(0, 2)
	})
	const thead = Object.freeze(['关键因素', '推荐提升方法'])
	const onBack = (str) => {
		if (str == '-') return
		uni.navigateTo({
			url: "/ai/pages/index/index?query=" + encodeURIComponent(str),
		})
	}
</script>

<style lang="scss" scoped>
	.block {
		padding: 40rpx;
		background-color: #fff;
		border-radius: 20rpx;

		.logo {
			flex-shrink: 0;
			width: 60rpx;
			height: 60rpx;
			margin-right: 16rpx;
		}

		.header {
			width: 212rpx;
			height: 52rpx;
			margin-bottom: 20rpx;
			background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.1) 100%);
			border-radius: 200rpx;

			.title {
				color: #1F2428;
				font-weight: bold;
			}
		}

		.content {
			margin: 0 20rpx 42rpx;

			.tips {
				padding: 29rpx 20rpx;
				margin-bottom: 20rpx;
				font-size: 20rpx;
				font-weight: bold;
				color: #1F2428;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 20rpx;
			}

			.table {
				overflow: hidden;
				box-sizing: border-box;
				width: 100%;
				font-size: 24rpx;
				border-radius: 16rpx;
				white-space: nowrap;
			}

			.td {
				width: 50%;
				text-align: center;
				display: inline-block;
				padding: 28rpx 0;
				word-wrap: break-word;
			}

			.thead {

				.td {
					font-size: 22rpx;
					font-weight: 700;
					background-color: #FFE0D9;
				}
			}

			.tr:not(.thead) {
				.td {
					line-height: 32rpx;
					color: #1F2428;

					.theme-primary {
						&__underline {
							text-decoration: underline;
						}
					}
				}

				&:nth-child(even) {
					background-color: #FFF9F7;
				}

				&:nth-child(odd) {
					background-color: #FFF3F0;
				}
			}
		}
	}
</style>