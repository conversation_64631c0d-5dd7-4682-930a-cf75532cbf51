<template>
	<view class="fl block">
		<view class="fl content">
			<view class="chart-wrap">
				<view class="fl">
					<text class="type">ADR</text>
					<text class="fr score">{{ data.adrPoint }}分</text>
				</view>
				<view class="fl">
					<text class="type">CRS直销</text>
					<text class="fr score">{{ data.directCrsPoint }}分</text>
				</view>
				<view class="fl">
					<text class="type">OCC</text>
					<text class="fr score">{{ data.occPoint }}分</text>
				</view>
				<view class="fl">
					<text class="type">CRS分销</text>
					<text class="fr score">{{ data.distributeCrsPoint }}分</text>
				</view>
				<view class="fl">
					<text class="type">售卡数</text>
					<text class="fr score">{{ data.cardSoldPoint }}分</text>
				</view>
				<view class="chart">
					<qiun-data-charts :loadingType="0"  type="radar" :canvas2d="true" canvasId="JvlNlKhzClXbpuzblPLTqIZBWmjAVJuD111"
						:opts="opts" :chart-data="chartData"></qiun-data-charts>
				</view>
			</view>
			<view class="fr legend">
				<text class="fr store">我的门店</text>
				<text class="fr store current-period">本期头部门店</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		nextTick,
		onMounted,
		watch,
		ref,
		unref,
		getCurrentInstance
	} from 'vue';
	
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		shopName: {
			type: String
		}
	})
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL
	const route_to_view = () => {
			uni.navigateTo({
				url: '/public/pages/webview/webview?url=' + webviewURL + '/pages/rules/rules'
			})
		},
		opts = ref({}),
		chartData = ref({})

	const handleChart = () => {
		const {
			firstAdrPoint,
			firstDirectCrsPoint,
			firstOccPoint,
			firstDistributeCrsPoint,
			firstCardSoldPoint,
			adrPointGap,
			directCrsPointGap,
			occPointGap,
			distributeCrsPointGap,
			cardSoldPointGap,
			adrPoint,
			occPoint,
			cardSoldPoint,
			distributeCrsPoint,
			directCrsPoint
		} = unref(props.data) || {}
		const val = uni.upx2px(5)
		opts.value = {
			fontSize: 12,
			padding: [val, val, val, val],
			dataLabel: false,
			enableScroll: false,
			legend: {
				show: false,
			},
			extra: {
				radar: {
					gridType: "circle",
					gridColor: '#F0F1F5',
					labelPointShow: true,
					labelPointRadius: 1,
					labelPointColor: '#fff',
					opacity: 0.04,
					max: Math.max.apply(null, [adrPointGap, occPointGap, cardSoldPointGap, distributeCrsPointGap,
						directCrsPointGap, firstAdrPoint, firstOccPoint, firstCardSoldPoint,
						firstDistributeCrsPoint, firstDirectCrsPoint
					].map(Number).filter(value => !isNaN(value))),
					radius: uni.upx2px(400),
					borderWidth: 0,
					labelShow: false,
				}
			}
		}
		chartData.value = {
			categories: ["ADR", "OCC", "售卡数", "CRS分销", "CRS直销"],
			series: [{
					name: '本期头部门店',
					data: [firstAdrPoint, firstOccPoint, firstCardSoldPoint, firstDistributeCrsPoint,
						firstDirectCrsPoint
					],
					color: '#FF9A36',
				},
				{
					name: '我的门店',
					data: [adrPoint == '-' ? 0 : adrPoint, occPoint == '-' ? 0 : occPoint, cardSoldPoint ==
						'-' ? 0 : cardSoldPoint, distributeCrsPoint == '-' ? 0 : distributeCrsPoint,
						directCrsPoint == '-' ? 0 : directCrsPoint
					],
					color: '#1E61FC'
				}
			],
		}
	}

	watch(() => props.data, (v) => {
		console.log("v", v, chartData.value)
		nextTick(() => {
			handleChart()
		})
	}, {
		deep: true
	})
</script>

<style lang="scss" scoped>
	.block {
		padding: 184rpx 40rpx 62rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		.header {
			padding: 40rpx 28rpx 28rpx;
			text-align: center;

			.title {
				font-size: 32rpx;
				font-weight: bold;
				color: #000;
				text-align: center;
			}

			.theme-primary {
				margin-left: 20rpx;
				font-size: 24rpx;
				text-decoration: underline;
			}
		}

		.content {

			.total-score {
				align-items: baseline;
				justify-content: center;
				margin-bottom: 104rpx;
				color: #000;
				font-size: 24rpx;

				.theme-primary {
					font-weight: bold;
					font-size: 40rpx;
				}
			}

			.chart-wrap {
				position: relative;
				width: 292rpx;
				height: 292rpx;
				margin: 0 auto 94rpx;

				.chart {
					width: 292rpx;
					height: 292rpx;
				}

				.chart4 {
					width: 100%;
					height: 100%;
				}

				.fl {
					position: absolute;
					align-items: center;
					font-size: 24rpx;

					.type {
						margin-bottom: 4rpx;
						line-height: 24rpx;
						font-weight: bold;
						color: #000;
					}

					.score {
						justify-content: center;
						color: #FF4D4D;
						font-size: 24rpx;
						font-weight: bold;
						font-family: "DIN Bold";
					}

					&:nth-child(1) {
						top: -10rpx;
						left: 50%;
						transform: translate(-50%, -100%);
					}

					&:nth-child(2) {
						top: 50%;
						left: -10rpx;
						transform: translate(-100%, -100%);
					}

					&:nth-child(3) {
						top: 50%;
						right: -10rpx;
						transform: translate(100%, -100%);
					}

					&:nth-child(4) {
						top: 90%;
						left: 0;
						transform: translate(-50%, 0);
					}

					&:nth-child(5) {
						top: 90%;
						right: 0;
						transform: translate(50%, 0);
					}
				}

			}

			.legend {
				justify-content: center;
				margin-bottom: 48rpx;

				.store {

					font-size: 26rpx;
					color: #777;

					&::before {
						content: "";
						width: 12rpx;
						height: 12rpx;
						margin-right: 12rpx;
						border-radius: 100%;
						background-color: #FF4D4D;
					}

					&.current-period {
						margin-left: 60rpx;

						&::before {
							background: linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%);
						}
					}
				}
			}
		}
	}
</style>