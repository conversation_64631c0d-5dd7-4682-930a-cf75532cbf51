<template>
	<view class="fl block">
		<!-- <cTabsVue v-model="tabValue" :list="tabList" :isBig="false" /> -->
		<view class="header">
			<text class="title">您的门店，全国排名为1，各分数与第一名的差距如下：</text>
		</view>
		<view class="content">
			<scroll-view scroll-x="true" class="table">
				<view style="width:max-content;">
					<view class="tr thead">
						<text class="td" v-for="(item, index) in thead" :key="index">{{ item }}</text>
					</view>
					<view class="tr tbody" v-if="data && JSON.stringify(data) != '{}'" v-for="(item, index) in list" :key="index">
						<text class="td">{{ item.name }}</text>
						<text class="td">{{ item.occ }}</text>
						<text class="td">{{ item.adr }}</text>
						<text class="td">{{ item.crs }}</text>
						<text class="td">{{ item.crs_1 }}</text>
						<text class="td">{{ item.card }}</text>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		computed,
		ref
	} from 'vue';
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	const props = defineProps({
		data: {
			type: Object,
			default: () => { }
		}
	})
	const tabList = [
		{ name: '全国排名', value: 0 },
		{ name: '事业部内排名', value: 1 },
		{ name: '大区内排名', value: 2 },
		{ name: '城区内排名', value: 3 },
	]
	const tabValue = ref(tabList[0].value)
	const thead = Object.freeze(['门店', 'OCC', 'ADR', 'CRS直销', 'CRS分销', '售卡数'])
	const list = computed(() => {
		const {
			firstAdrPoint,
			firstDirectCrsPoint,
			firstOccPoint,
			firstDistributeCrsPoint,
			firstCardSoldPoint,
			adrPointGap,
			directCrsPointGap,
			occPointGap,
			distributeCrsPointGap,
			cardSoldPointGap,
			adrPoint,
			occPoint, cardSoldPoint, distributeCrsPoint, directCrsPoint
		} = props.data
		return [{
			name: '第一门店',
			occ: firstOccPoint,
			adr: firstAdrPoint,
			crs: firstDirectCrsPoint,
			crs_1: firstDistributeCrsPoint,
			card: firstCardSoldPoint
		},
		{
			name: '我店',
			occ: occPoint,
			adr: adrPoint,
			crs: directCrsPoint,
			crs_1: distributeCrsPoint,
			card: cardSoldPoint
		},
		{
			name: '差距',
			occ: occPointGap,
			adr: adrPointGap,
			crs: directCrsPointGap,
			crs_1: distributeCrsPointGap,
			card: cardSoldPointGap
		}
		]
	})
</script>

<style lang="scss" scoped>
	.block {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;

		.header {
			padding: 28rpx 20rpx;
			margin-bottom: 20rpx;
			background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			border-radius: 16rpx;

			.title {
				font-size: 24rpx;
				color: #000;
				font-weight: bold;
			}
		}

		.content {
			.table {
				overflow: hidden;
				width: 100%;
				background-color: #fff;
				border-radius: 16rpx;
				white-space: nowrap;
			}

			.td {
				width: 150rpx;
				text-align: center;
				display: inline-block;
				padding: 28rpx 0;
				word-wrap: break-word;
				font-size: 22rpx;
			}

			.thead {


				.td {
					font-size: 22rpx;
					font-weight: 700;
					color: #1F2428;
					background-color: #FFE0D9;
				}
			}

			.tr:not(.thead) {

				.td {
					line-height: 32rpx;
					color: #1F2428;
				}
			}

			.tbody:nth-child(even) {
				background-color: #FFF9F7;
			}

			.tbody:nth-child(odd) {
				background-color: #FFF3F0;
			}
		}
	}
</style>