import { readonly } from "vue";
import { setDay } from "@/utils/util";

export const modalObj = readonly({
	'房费收入': '房费总收入',
	'平均房价': '平均房价=客房当日费用/实际售出客房数量',
	'出租率': '出租率=实际售出客房数量/可售房数量',
	'RevPar': '每间可售房收入=OCC*ADR',
	'CRS占比': 'CRS（直销+分销）间夜/当日总出租间夜',
	'直销占比': '小程序、App等直销间夜/当日总出租间夜',
	'会员占比': '当日个人会员间夜（包括直销和线下会员）/当日总出租间夜',
	'总间夜数': '所选时段该门店的累计间夜数',
	'扫码住': '所选时段该门店的累计扫码住间夜数',
	'扫码住占比': '所选时段扫码住间夜数/门店总间夜数',
	'售卡金额': '所选时段该门店实际完成的线上分销售卡金额',
	'售卡数': '所选时段该门店实际完成的线上分销售卡数量',
	'百房售卡数': '（心客源售卡张数+口袋宝售卡张数+搭售张数）/出租间数*100',
	'小美金卡': '即等级为V3的卡',
	'小美银卡': '即等级为V2的卡',
	'小美钻卡': '即等级为V4的卡'
})


export const marktingLineTabs = [
	{
		name: '房费收入',
		value: 'roomFeeList'
	},
	{
		name: '平均房价',
		value: 'adrList'
	},
	{
		name: '出租率',
		value: 'occRateList'
	},
	{
		name: 'RevPar',
		value: 'revParList'
	},
	{
		name: 'CRS占比',
		value: 'crsRateList'
	},
	{
		name: '直销占比',
		value: 'directSaleRateList'
	},
	{
		name: '会员占比',
		value: 'memberRateList'
	}
]

export const marktingRingTabs = [
	{
		name: '客源',
		value: 'customerSourceList'
	},
	{
		name: '直销',
		value: 'directSaleList'
	},
	{
		name: '分销',
		value: 'distributionList'
	},
	{
		name: '房型',
		value: 'roomTypeList'
	},
	{
		name: '促销',
		value: 'promotionList'
	}
]


export const sellingCardsLineTabs = [
	{
		name: '小美银卡金额',
		value: 'silverCardList'
	},
	{
		name: '小美金卡金额',
		value: 'goldenCardList'
	},
	{
		name: '小美钻卡金额',
		value: 'diamondCardList'
	}
]

export const sellingCardsRingTabs = [
	{
		name: '线上售卡占比',
		value: 'proportionOnlineCardSalesRate'
	},
	{
		name: '员工排名',
		value: 'employeeRanking'
	}
]

export const topTabs = [
	{
		name: '营销',
		value: 0
	},
	{
		name: '售卡',
		value: 1
	}
]

export const subsectionList = [
	{
		name: '昨日',
		value: 1,
		formt: (() => {
			return [
				setDay(1, '-'),
				setDay(1, '-')
			]
		})(),
	},
	{
		name: '近3日',
		value: 2,
		formt: (() => {
			return [
				setDay(3, '-'),
				setDay(1, '-'),
			]
		})(),
	},
	{
		name: '近一周',
		value: 3,
		formt: (() => {
			return [
				setDay(7, '-'),
				setDay(1, '-'),
			]
		})(),
	},
	{
		name: '本月',
		value: 4,
		formt: (() => {
			const date = new Date()
			const y = date.getFullYear()
			const m = (date.getMonth() + 1).toString().padStart(2, '0')
			return [
				y + '-' + m + '-01',
				setDay(1, '-'),
			]
		})(),
	}
]

export const header = [
	{ name: '排名', prop: 'ranking', slot: true },
	{ name: '姓名', prop: 'userName', slot: true },
	{ name: '售卡金额', prop: 'amount', slot: true },
	{ name: '售卡量', prop: 'count' }
]