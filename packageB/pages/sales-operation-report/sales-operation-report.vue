<template>
	<view class="container">
		<view class="section">
			<view class="section-header">
				<text class="section-header__title">销售经营报表</text>
				<cTabsVue :isBig="false" :list="topTabs" v-model="tabValue"
					itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;" />
			</view>
			<view class="subsection">
				<c-subsection-vue v-model="subsectionValue" :list="subsectionList" @change="updateData" />
			</view>
			<view class="calendar">
				<cCalendarVue v-model="calendarValue" :startDate="dayjs().subtract(1, 'year').format('YYYY-MM-DD')"
          :endDate="dayjs().subtract(1, 'day').format('YYYY-MM-DD')" " @confirm="confirm" :showIcon="false" />
			</view>
			<marktingDataVue v-if="tabValue == 0" :dataSource="saleComparisonData" />
			<sellingCardsDataVue v-if="tabValue == 1" :dataSource="salesCardBusinessReport" />
		</view>
		<marktingLineVue v-if="tabValue == 0" :dataSource="saleComparisonDataChart" :pageScroll="pageScroll" />
		<marktingRingVue v-if="tabValue == 0" :dataSource="saleOrderRoomNightData" />
		<sellingCardsLineVue v-if="tabValue == 1" :dataSource="salesCardBarChart" :pageScroll="pageScroll" />
		<sellingCardsRingVue v-if="tabValue == 1" :dataSource="salesCardPieChart" @scrolltolower="scrolltolower" />
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad, onPageScroll } from '@dcloudio/uni-app'
	import { nextTick, onMounted, ref, watch } from 'vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import cSubsectionVue from '@/components/c-subsection/c-subsection.vue';
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue'
	import marktingDataVue from './components/markting/markting-data.vue'
	import marktingLineVue from './components/markting/markting-line.vue';
	import marktingRingVue from './components/markting/markting-ring.vue';
	import sellingCardsDataVue from './components/selling-cards/selling-cards-data.vue'
	import sellingCardsLineVue from './components/selling-cards/selling-cards-line.vue'
	import sellingCardsRingVue from './components/selling-cards/selling-cards-ring.vue'
	import { topTabs, subsectionList } from './enums'
	import { getSaleComparisonData, getSaleComparisonDataChart, getSaleOrderRoomNightData, getSalesCardBarChart, getSalesCardBusinessReport, getSalesCardPieChart } from '../../api';
	import { PaginationParams, dataType } from '../../../types';
  import dayjs from "dayjs";
	const tabValue = ref(topTabs[0].value),
		shopId = ref<string>(''),
		subsectionValue = ref<number>(0), // 默认近一周
		calendarValue = ref<string[]>(subsectionList[subsectionValue.value].formt),
		saleComparisonData = ref<dataType>({}), // 营销数据
		saleComparisonDataChart = ref<dataType>({}), // 营销柱状折线图表数据
		saleOrderRoomNightData = ref<dataType>({}), // 营销饼图数据
		pageScroll = ref<number>(),
		salesCardBusinessReport = ref<dataType>({}), // 售卡数据
		salesCardBarChart = ref<dataType>({}), // 售卡柱状折线图表数据
		salesCardPieChart = ref<dataType>({}) // 售卡饼图数据

	const pagination = ref<PaginationParams>({
		pageNum: 1,
		pageSize: 20
	}), loadMore = ref<Boolean>(true)

	watch(
		() => tabValue.value,
		() => {
			reset()
			getData()
		}
	)

	onLoad((options) => {
		shopId.value = options.shopId
		tabValue.value = Number(options.tabValue) || topTabs[0].value
		getData()
	})

	const reset = () => {
		pagination.value.pageNum = 1
		pagination.value.pageSize = 20
		loadMore.value = true
		salesCardPieChart.value = {}
	}

	const getData = () => {
		if (tabValue.value === 0) {
			toGetSaleComparisonData()
			toGetSaleComparisonDataChart()
			toGetSaleOrderRoomNightData()
		} else {
			toGetSalesCardBusinessReport()
			toGetSalesCardBarChart()
			toGetSalesCardPieChart()
		}
	}

	// 时间选择
	const updateData = (item : any) => {
		calendarValue.value = item.formt
		reset()
		getData()
	}

	const confirm = () => {
		subsectionValue.value = null
		reset()
		getData()
	}

	// 销售比较数据
	const toGetSaleComparisonData = async () => {
		try {
			const { data } = await getSaleComparisonData({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && Object.keys(data).length > 0) {
				saleComparisonData.value = data
			}
		} catch (e) {
			console.error('销售比较数据 error', e)
		}
	}

	// 销售比较数据图表
	const toGetSaleComparisonDataChart = async () => {
		try {
			const { data } = await getSaleComparisonDataChart({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && Object.keys(data).length > 0) {
				saleComparisonDataChart.value = data
			}
		} catch (e) {
			console.error('销售比较数据图表 error', e)
		}
	}

	// 销售订单间夜数据
	const toGetSaleOrderRoomNightData = async () => {
		try {
			const { data } = await getSaleOrderRoomNightData({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && Object.keys(data).length > 0) {
				saleOrderRoomNightData.value = data
			} else {
				saleOrderRoomNightData.value = {}
			}
		} catch (e) {
			console.error('销售订单间夜数据 error', e)
		}
	}

	// 销售售卡经营报表
	const toGetSalesCardBusinessReport = async () => {
		try {
			const { data } = await getSalesCardBusinessReport({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && Object.keys(data).length > 0) {
				salesCardBusinessReport.value = data
			}
		} catch (e) {
			console.error('销售售卡经营报表 error', e)
		}
	}

	// 销售售卡条形图
	const toGetSalesCardBarChart = async () => {
		try {
			const { data } = await getSalesCardBarChart({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1]
			})
			if (data && Object.keys(data).length > 0) {
				salesCardBarChart.value = data
			}
		} catch (e) {
			console.error('销售售卡条形图 error', e)
		}
	}

	// 销售比较数据
	const toGetSalesCardPieChart = async () => {
		try {
			const { pageNum, pageSize } = pagination.value
			const { data } = await getSalesCardPieChart({
				shopId: shopId.value,
				startDate: calendarValue.value[0],
				endDate: calendarValue.value[1],
				...pagination.value
			})
			if (data && Object.keys(data).length > 0) {
				data.employeeRanking = data.employeeRanking || []
				if (data.employeeRanking.length < pageSize) {
					loadMore.value = false
				} else {
					loadMore.value = true
				}
				if (pageNum == 1) {
					nextTick(() => {
						salesCardPieChart.value = data
					})
				} else {
					nextTick(() => {
						salesCardPieChart.value.employeeRanking = [...(salesCardPieChart.value.employeeRanking || []), ...(data
							.employeeRanking || [])]
					})
				}
			}
		} catch (e) {
			console.error('销售比较数据 error', e)
		}
	}

	// 售卡排名分页加载
	const scrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		toGetSalesCardPieChart()
	}

  onPageScroll((e) => {
    pageScroll.value = e.scrollTop
  })
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;

		.section {
			padding: 0 40rpx 20rpx;
			margin-bottom: 20rpx;
			background: #ffffff;
			border-radius: 20rpx;

			&-header {
				display: flex;
				justify-content: space-between;
				padding: 40rpx 0;

				&__title {
					font-weight: bold;
					font-size: 32rpx;
					color: #1F2428;
					line-height: 32rpx;
				}
			}
		}

		.subsection,
		.calendar {
			margin-bottom: 40rpx;
		}
	}

	:deep(.calendar-picker) {
		background: #fff;
		height: 54rpx;
	}
</style>