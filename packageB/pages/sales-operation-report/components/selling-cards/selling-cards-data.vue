<template>
	<view class="fr">
		<view class="progress">
			<view class="fr tag" @tap="onShowModal('售卡金额')">
				<image
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-10/1741571889635.png"
					mode="aspectFill" lazy-load class="tag-icon"></image>
				<text class="tag-title">售卡金额</text>
				<image
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729678941228.png"
					lazy-load mode="aspectFill" class="icon-tips"></image>
			</view>
			<view class="fr arcbar">
				<arcbarVue :value="dataSource.salesCardAmount?.completionRate" />
			</view>
			<view class="fl income">
				<view class="fr actual">
					<view class="label">实际：</view>
					<view class="value fr">
						<view class="DIN-Bold">{{ dataSource.salesCardAmount?.actual || '-'}}</view>
            <text class="unit">元</text>
					</view>
				</view>
				<view class="fr budget">
					<view class="label">预算：</view>
					<view class="value fr">
						<view class="DIN-Bold">{{ dataSource.salesCardAmount?.budget || '-'}}</view>
            <text class="unit">元</text>
					</view>
				</view>
			</view>
		</view>
		<view class="progress">
			<view class="fr tag" @tap="onShowModal('售卡数')">
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-10/1741571956249.png"
					mode="aspectFill" lazy-load class="tag-icon"></image>
				<text class="tag-title">售卡数</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729678941228.png"
					lazy-load mode="aspectFill" class="icon-tips"></image>
			</view>
			<view class="fr arcbar">
				<arcbarVue start-color="#BCE3D0" end-color="#43BF83" background-color="rgba(67, 191, 131, 0.08)"
					font-color="#43BF83" :value="dataSource.salesCardCount?.completionRate" />
			</view>
			<view class="fl income">
				<view class="fr actual">
					<view class="label">实际：</view>
					<view class="value fr">
						<view class="DIN-Bold">{{ dataSource.salesCardCount?.actual || '-' }}</view>
            <text class="unit">张</text>
					</view>
				</view>
				<view class="fr budget">
					<view class="label">预算：</view>
					<view class="value fr">
						<view class="DIN-Bold">{{ dataSource.salesCardCount?.budget || '-'}}</view>
            <text class="unit">张</text>
          </view>
				</view>
			</view>
		</view>
	</view>

	<view class="card">
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('小美银卡')">
				<text class="card-item__title-text">小美银卡</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.silverCard?.cardAmount || '-'}}元/{{ dataSource.silverCard?.cardCount || '-'}}张</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.silverCard?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.silverCard?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.silverCard?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.silverCard?.ringRatio) }}%</text>
			</view>
		</view>
    <view class="card-item">
      <view class="fr card-item__title" @tap="onShowModal('小美金卡')">
        <text class="card-item__title-text">小美金卡</text>
        <image
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
          mode="aspectFill" lazy-load class="icon-tips"></image>
      </view>
      <view class="font-size">
        <text>{{ dataSource.goldenCard?.cardAmount || '-'}}元/{{ dataSource.goldenCard?.cardCount || '-'}}张</text>
      </view>
      <view class="compare-ratio font-size fr">
        <text class="text">环比</text>
        <image v-if="dataSource.goldenCard?.ringRatio > 0"
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
          mode="aspectFill" lazy-load class="icon"></image>
        <image v-if="dataSource.goldenCard?.ringRatio < 0"
          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
          mode="aspectFill" lazy-load class="icon"></image>
        <text class="DIN-Bold"
          :class="[dataSource.goldenCard?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.goldenCard?.ringRatio) }}%</text>
      </view>
    </view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('小美钻卡')">
				<text class="card-item__title-text">小美钻卡</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.diamondCard?.cardAmount || '-'}}元/{{ dataSource.diamondCard?.cardCount || '-'}}张</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.diamondCard?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.diamondCard?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.diamondCard?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.diamondCard?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('百房售卡数')">
				<text class="card-item__title-text">百房售卡数</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.hundredHouseSalesCard?.cardCount || '-'}}</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.hundredHouseSalesCard?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.hundredHouseSalesCard?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.hundredHouseSalesCard?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.hundredHouseSalesCard?.ringRatio) }}%</text>
			</view>
		</view>
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import arcbarVue from '../../../../components/arcbar/arcbar.vue';
	import cModal from '@/components/c-modal/index'
	import { modalObj } from '../../enums';
  import { stringRemoveSymbol } from "@/utils/util";

	defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		}
	})

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')

	const onShowModal = (type : string) : void => {
		modalShow.value = true
		modalTitle.value = type
		modalContent.value = modalObj[type]
	}
</script>

<style scoped lang="scss">
	$linear-gradient: linear-gradient(90deg, #f75e3b1a 0%, #f75e3b0d 100%), linear-gradient(90deg, #56cc931a 0%, #56cc930d 100%);
	$linear-gradient2: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%), linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%);
	;

	.progress {
		flex: 1;
		position: relative;
		justify-content: space-between;
		padding: 72rpx 20rpx 36rpx;
		margin-bottom: 40rpx;
		border-radius: 20rpx;

		.tag {
			position: absolute;
			top: 0;
			left: 0;
			padding: 10rpx 24rpx 10rpx 22rpx;
			font-weight: bold;
			line-height: 28rpx;
			border-radius: 20rpx 0 20rpx 0;

			&-title {
				margin: 0 8rpx;
				color: #fff;
        font-size: 28rpx;
			}

			&-icon {
				width: 30rpx;
        height: 26rpx;
			}
		}

		.income {
			align-self: flex-start;
			color: #1F2428;

			.actual {
				margin-bottom: 16rpx;
			}

			.label {
				font-size: 24rpx;
				line-height: 24rpx;
				color: #1F2428;
			}

			.value {
				font-size: 28rpx;
				height: 28rpx;
				color: #1F2428;

				.DIN-Bold {
					font-size: 40rpx;
					font-weight: 700;
					line-height: 40rpx;
				}

				&::before {
					content: '￥';
					font-family: "Din Bold";
					font-size: 32rpx;
				}
			}

      .unit {
        color: #1f2428;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 28rpx;
        margin-left: 8rpx;
      }
		}

		.arcbar {
			justify-content: center;
			margin-bottom: 20rpx;
		}


		&:first-child {
			margin-right: 22rpx;
		}

		@for $var from 1 through 2 {
			&:nth-child(#{$var}) {
				background: nth($linear-gradient, $var);

				.tag {
					background: nth($linear-gradient2, $var);
				}
			}
		}
	}



	.card {
		display: flex;
		flex-wrap: wrap;
		align-items: stretch;

		&-item {
			width: calc((100% - 40rpx) / 3);
			margin-bottom: 20rpx;
			margin-right: 20rpx;
			padding: 28rpx 0 12rpx 16rpx;
			border: 2rpx solid #f2f2f2;
			background: #ffffff;
			border-radius: 20rpx;

			&:nth-child(3n+3) {
				margin-right: 0;
			}

			&__title {
				margin-bottom: 28rpx;
				font-weight: bold;
				font-size: 24rpx;
				line-height: 24rpx;
				color: #1F2428;

				&-text {
					margin-right: 8rpx;
				}
			}

			.font-size {
				margin-bottom: 16rpx;
				font-size: 22rpx;
				line-height: 22rpx;
				color: #1F2428;
				word-break: break-all;

				.primary {
					font-size: 24rpx;
					color: #FF4D4D;
				}

				text {
					color: #1F2428
				}

				.text {
					color: #1f2428b3;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 8rpx;
				}
			}

			.icon {
				width: 16rpx;
				height: 20rpx;
				margin-right: 8rpx;
			}

			.up-text {
				color: #FF4D4D !important;
				font-size: 24rpx;
				line-height: 24rpx;
			}

			.down-text {
				color: #56CC93 !important;
				font-size: 24rpx;
				line-height: 24rpx;
			}
		}
	}

	.icon-tips {
		width: 22rpx;
		height: 22rpx;
	}
</style>