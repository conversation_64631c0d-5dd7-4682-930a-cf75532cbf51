<template>
	<view class="section padding40">
		<cTabsVue v-model="tabValue" :list="sellingCardsRingTabs" :isBig="false" @change="handleTabChange"
			itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;"></cTabsVue>
		<view class="chart">
			<ringChartVue v-if="tabValue == 0" :data="chartData" type="c"
				:c-keys="{labelKey:'cardName',valueKey:'cardRate'}" />
			<cTableVue v-show="tabValue == 1" :header="header" :list="list" @scrolltolower="$emit('scrolltolower')"
				height="514rpx">
				<template #default="scope">
					<view v-if="scope.prop === 'ranking'" class="rank">
						<view v-if="scope.index <= 2">
							<image class="img"
								:src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-02/${scope.index == 0 ? '1733120145481' : scope.index == 1 ? '1733120195739' : '1733120209949'}.png`"
								mode="aspectFill"></image>
						</view>
						<view v-else>
							{{scope.index+1}}
						</view>
					</view>
					<view v-if="scope.prop === 'userName'" class="fl">
						<view style="text-align: left;margin-bottom: 12rpx;">
							{{scope.data.userName}}
						</view>
						<view class="progress">
							<view class="progress-bar" :style="{width: scope.data.progress}"></view>
						</view>
					</view>
					<view v-if="scope.prop === 'amount'">￥{{scope.data.amount}}</view>
				</template>
			</cTableVue>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed, nextTick, onMounted, ref, watch } from 'vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import ringChartVue from '@/components/ring-chart/ring-chart.vue';
	import cTableVue from '@/components/c-table/c-table.vue';
	import { sellingCardsRingTabs, header } from '../../enums'
	const tabValue = ref(0)
	const chartData = ref({}), list = ref([])
	const props = defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		}
	})

	defineEmits(["scrolltolower"])

	watch(
		() => props.dataSource,
		() => {
			handleTabChange()
		}
	)

	const handleTabChange = () => {
		if (tabValue.value === 0) {
			setChartData()
		} else {
			setList()
		}
	}

	const setChartData = () => {
		const data = props.dataSource[sellingCardsRingTabs[tabValue.value].value]
		if (!data || data?.length <= 0) return chartData.value = {}
		data?.sort((a, b) => b.roomNights - a.roomNights)
		nextTick(() => {
			chartData.value = {
				originData: data.map((item) => ({ ...item, cardRate: item.cardRate + '%' })),
				data: data?.filter(item => item.roomNights != 0)?.map((item, index) => {
					return {
						name: item.cardName + ' ' + item.cardRate + '%',
						value: item.cardRate,
						labelShow: false
					}
				}) || []
			}
		})
	}

	const setList = () => {
		const data = props.dataSource[sellingCardsRingTabs[tabValue.value].value]
		if (!data) return list.value = []
		const firstData = data[0]?.amount
		const ratio = firstData / 100
		nextTick(() => {
			list.value = data.map(item => {
				return {
					...item,
					progress: (item.amount / ratio).toFixed(2) + '%'
				}
			})
		})
	}
</script>

<style scoped lang="scss">
	.section {
		padding: 40rpx 0;
		background: #ffffff;
		border-radius: 20rpx;

		.chart {
			padding: 0 40rpx;

			.rank {
				display: flex;
				align-items: center;

				.img {
					width: 40rpx;
					height: 50rpx;
					flex-shrink: 0;
				}
			}

			.progress {
				width: 164rpx;
				height: 10rpx;
				background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.2) 0%, rgba(247, 94, 59, 0.2) 100%);
				border-radius: 94rpx;

				&-bar {
					width: 0;
					height: 10rpx;
					background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
					border-radius: 94rpx;
				}
			}
		}
	}
</style>