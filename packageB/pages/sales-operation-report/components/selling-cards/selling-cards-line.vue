<template>
	<view class="section padding40">
		<cTabsVue v-model="tabValue" :list="sellingCardsLineTabs" :isBig="false"
			itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;"></cTabsVue>
		<view class="chart">
			<mixChartVue :data="data" :pageScrollTop="pageScroll" type="b" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed, ref } from 'vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import mixChartVue from '@/components/mix-chart/mix-chart.vue';	
	import { sellingCardsLineTabs } from '../../enums'
	const tabValue = ref(0)

	const props = defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		},
		pageScroll: {
			type: Number,
			default: 0
		}
	})

	const data = computed(() => {
		const data = props.dataSource[sellingCardsLineTabs[tabValue.value].value]
		if (!data) return {}
		return {
			categories: data?.map((item : any) => {
				let date = item.date
				if (date.includes('年')) {
					date = date.replace('年', '.')
				}
				if (date.includes('日')) {
					date = date.replace('月', '.').replace('日', '')
				} else {
					date = date.replace('月', '')
				}
				return date
			}),
			values: [
				{
					name: '上月同期',
					type: 'column',
					textColor: '#56CC93',
					data: data?.map((item : any) => item.lastAmount) || []
				},
				{
					name: sellingCardsLineTabs.find(item => item.value == sellingCardsLineTabs[tabValue.value].value)?.name,
					type: 'column',
					textColor: '#FF8C1A',
					data: data?.map((item : any) => item.amount) || []
				}
			]
		}
	})
</script>

<style scoped lang="scss">
	.section {
		padding: 40rpx 0;
		margin-bottom: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;

		.chart {
			padding: 0 40rpx;
		}
	}
</style>