<template>
	<view class="fr progress">
		<view class="fr tag" @tap="onShowModal('房费收入')">
			<image
				src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731659328466.png"
				mode="aspectFill" lazy-load class="tag-icon"></image>
			<text class="tag-title">房费收入</text>
			<image
				src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729678941228.png"
				lazy-load mode="aspectFill" class="icon-tips"></image>
		</view>
		<view class="fr income">
			<view class="fl actual">
				<text class="label">实际：</text>
				<text class="DIN-Bold value">{{ actual || '-' }}</text>
			</view>
			<view class="fl budget">
				<text class="label">预算：</text>
				<text class="DIN-Bold value">{{ budget  || '-'}}</text>
			</view>
		</view>
		<view class="arcbar">
			<arcbarVue :value="dataSource.roomFeeIncome?.completionRate" />
		</view>
	</view>
	<view class="card">
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('平均房价')">
				<text class="card-item__title-text">平均房价</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.adrDate?.actual || '-'}}元</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.adrDate?.budget || '-' }}元</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.adrDate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.adrDate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.adrDate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.adrDate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.adrDate?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('出租率')">
				<text class="card-item__title-text">出租率</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.occDate?.actual || '-'}}%</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.occDate?.budget || '-'}}%</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.occDate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.occDate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.occDate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.occDate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.occDate?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('RevPar')">
				<text class="card-item__title-text">RevPar</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.reParRate?.actual || '-'}}元</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.reParRate?.budget || '-'}}元</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.reParRate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.reParRate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.reParRate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.reParRate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.reParRate?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('CRS占比')">
				<text class="card-item__title-text">CRS占比</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.crsRate?.actual || '-'}}%</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.crsRate?.budget || '-'}}%</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.crsRate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.crsRate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.crsRate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.crsRate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.crsRate?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('直销占比')">
				<text class="card-item__title-text">直销占比</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.directSalesRate?.actual || '-'}}%</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.directSalesRate?.budget || '-'}}%</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.directSalesRate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.directSalesRate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.directSalesRate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.directSalesRate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.directSalesRate?.ringRatio) }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('会员占比')">
				<text class="card-item__title-text">会员占比</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="actual font-size">
				<text>实际：</text>
				<text>{{ dataSource.memberRate?.actual || '-'}}%</text>
			</view>
			<view class="budget font-size">
				<text>预算：</text>
				<text>{{ dataSource.memberRate?.budget || '-'}}%</text>
			</view>
			<view class="complete-ratio font-size">
				<text>完成率：</text>
				<text class="DIN-Bold primary">{{ dataSource.memberRate?.completionRate || '-'}}%</text>
			</view>
			<view class="compare-ratio font-size fr">
				<text class="text">环比</text>
				<image v-if="dataSource.memberRate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.memberRate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.memberRate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ stringRemoveSymbol(dataSource.memberRate?.ringRatio) }}%</text>
			</view>
		</view>

		<!--<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('总间夜数')">
				<text class="card-item__title-text">总间夜数</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.totalRoomNight?.roomNightNum }}</text>
			</view>
			<view class="compare-ratio font-size">
				<text class="text">环比</text>
				<image v-if="dataSource.totalRoomNight?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.totalRoomNight?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.totalRoomNight?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ dataSource.totalRoomNight?.ringRatio }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('扫码住')">
				<text class="card-item__title-text">扫码住</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.scanCodeStay?.roomNightNum }}</text>
			</view>
			<view class="compare-ratio font-size">
				<text class="text">环比</text>
				<image v-if="dataSource.scanCodeStay?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.scanCodeStay?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.scanCodeStay?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ dataSource.scanCodeStay?.ringRatio }}%</text>
			</view>
		</view>
		<view class="card-item">
			<view class="fr card-item__title" @tap="onShowModal('扫码住占比')">
				<text class="card-item__title-text">扫码住占比</text>
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png"
					mode="aspectFill" lazy-load class="icon-tips"></image>
			</view>
			<view class="font-size">
				<text>{{ dataSource.scanCodeStayRate?.roomNightNum }}</text>
			</view>
			<view class="compare-ratio font-size">
				<text class="text">环比</text>
				<image v-if="dataSource.scanCodeStayRate?.ringRatio > 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657769150.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<image v-if="dataSource.scanCodeStayRate?.ringRatio < 0"
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-15/1731657845365.png"
					mode="aspectFill" lazy-load class="icon"></image>
				<text class="DIN-Bold"
					:class="[dataSource.scanCodeStayRate?.ringRatio > 0 ? 'up-text' : 'down-text']">{{ dataSource.scanCodeStayRate?.ringRatio }}%</text>
			</view>
		</view>-->
	</view>
	<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts">
	import { computed, ref } from 'vue';
	import arcbarVue from '../../../../components/arcbar/arcbar.vue';
	import cModal from '@/components/c-modal/index'
	import { modalObj } from '../../enums';
  import { stringRemoveSymbol } from '@/utils/util'

	const props = defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		}
	})

	const modalShow = ref<boolean>(false)
	const modalTitle = ref<string>('')
	const modalContent = ref<string>('')

	const onShowModal = (type : string) : void => {
		modalShow.value = true
		modalTitle.value = type
		modalContent.value = modalObj[type]
	}

	const actual = computed(() => {
		const actualOrigin = props.dataSource.roomFeeIncome?.actual
		return actualOrigin > 100000 ? (actualOrigin / 10000).toFixed(2) + '万' : actualOrigin
	})

	const budget = computed(() => {
		const budgetOrigin = props.dataSource.roomFeeIncome?.budget
		return budgetOrigin > 100000 ? (budgetOrigin / 10000).toFixed(2) + '万' : budgetOrigin
	})
</script>

<style scoped lang="scss">
	.progress {
		position: relative;
		justify-content: space-between;
		padding: 16rpx 28rpx 14rpx;
		margin-bottom: 40rpx;
		background: linear-gradient(90deg, #f75e3b1a 0%, #f75e3b0d 100%);
		border-radius: 20rpx;

		.tag {
			position: absolute;
			top: 0;
			left: 0;
			padding: 4rpx 24rpx 4rpx 22rpx;
			font-weight: bold;
			line-height: 28rpx;
			background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
			border-radius: 20rpx 0 20rpx 0;

			&-title {
				margin: 0 8rpx;
				color: #fff;
			}

			&-icon {
				width: 30rpx;
				height: 40rpx;
			}
		}

		.income {
			margin-top: 68rpx;
			flex: 1;
			align-self: flex-start;
			color: #1F2428;

			.fl {
				flex: 1;
			}

			.label {
				margin-bottom: 16rpx;
				font-size: 24rpx;
				line-height: 24rpx;
			}

			.value {
				font-size: 40rpx;
				font-weight: 700;
				line-height: 40rpx;

				&::before {
					content: '￥';
					font-size: 32rpx;
				}
			}
		}

		.arcbar {
			flex-shrink: 0;
		}
	}

	.card {
		display: flex;
		flex-wrap: wrap;
		align-items: stretch;

		&-item {
			width: calc((100% - 40rpx) / 3);
			margin-bottom: 20rpx;
			margin-right: 20rpx;
			padding: 28rpx 0 12rpx 16rpx;
			border: 2rpx solid #f2f2f2;
			background: #ffffff;
			border-radius: 20rpx;

			&:nth-child(3n+3) {
				margin-right: 0;
			}

			&__title {
				margin-bottom: 28rpx;
				font-weight: bold;
				font-size: 24rpx;
				line-height: 24rpx;
				color: #1F2428;

				&-text {
					margin-right: 8rpx;
				}
			}

			.font-size {
				margin-bottom: 16rpx;
				font-size: 22rpx;
				line-height: 22rpx;
				color: #1F2428;
				word-break: break-all;

				.primary {
					font-size: 24rpx;
					color: #FF4D4D;
				}

				.text {
					color: #1f2428b3;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 8rpx;
				}
			}

			.icon {
				width: 16rpx;
				height: 20rpx;
				margin-right: 8rpx;
			}

			.up-text {
				color: #FF4D4D;
			}

			.down-text {
				color: #56CC93;
			}
		}
	}

	.icon-tips {
		width: 22rpx;
		height: 22rpx;
	}
</style>