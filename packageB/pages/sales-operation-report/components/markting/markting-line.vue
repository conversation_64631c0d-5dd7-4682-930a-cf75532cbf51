<template>
	<view class="section">
		<cTabsVue v-model="tabValue" :list="marktingLineTabs" :isBig="false"
			itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;"></cTabsVue>
		<view class="chart">
			<mixChartVue :data="data" :pageScrollTop="pageScroll" type="b" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed, ref } from 'vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import mixChartVue from '@/components/mix-chart/mix-chart.vue';
	import { marktingLineTabs } from '../../enums'
	const tabValue = ref(0)

	const props = defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		},
		pageScroll: {
			type: Number,
			default: 0
		}
	})

	const data = computed(() => {
		const data = props.dataSource[marktingLineTabs[tabValue.value].value]
		if (!data) return {}
		return {
			categories: data?.map((item : any) => {
				let date = item.date
				if (date.includes('年')) {
					date = date.replace('年', '.')
				}
				if (date.includes('日')) {
					date = date.replace('月', '.').replace('日', '')
				} else {
					date = date.replace('月', '')
				}
				return date
			}),
			values: [
				{
					name: '去年同期',
					type: 'column',
					textColor: '#56CC93',
					data: data?.map((item : any) => item.lastActual) || [],
          index: 0
				},
				{
					name: marktingLineTabs.find(item => item.value == marktingLineTabs[tabValue.value].value)?.name,
					type: 'column',
					textColor: '#FF8C1A',
					data: data?.map((item : any) => item.actual) || [],
          index: 0
				},
				{
					name: '完成率',
					type: 'line',
					textColor: '#F75E3B',
					linearColor: [[0, '#F75E3B'], [1, '#FFB2A1']],
					data: data?.map((item : any) => item.completionRate) || [],
          index: 1,
				}
			]
		}
	})
</script>

<style scoped lang="scss">
	.section {
		padding: 40rpx;
		margin-bottom: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;

		// .chart {
		// 	padding: 0 40rpx;
		// }
	}
</style>