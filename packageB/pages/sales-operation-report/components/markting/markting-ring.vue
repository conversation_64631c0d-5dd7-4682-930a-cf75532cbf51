<template>
	<view class="section">
		<cTabsVue v-model="tabValue" :list="marktingRingTabs" :isBig="false"
			itemStyle="height: 60rpx;padding: 0 20rpx;align-items:flex-start;"></cTabsVue>
		<view class="chart">
			<ringChartVue :data="data" type="b" :b-keys="{labelKey:'labelName',valueKey:'roomNightsRate'}" />
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed, ref } from 'vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import ringChartVue from '@/components/ring-chart/ring-chart.vue';
	import { marktingRingTabs } from '../../enums'
	const tabValue = ref<number>(0)

	const props = defineProps({
		dataSource: {
			type: Object,
			default: () => { }
		}
	})

	const data = computed(() => {
		const data = props.dataSource[marktingRingTabs[tabValue.value].value]
		if (!data || data?.length <= 0) return {}
		data?.sort((a, b) => b.roomNights - a.roomNights)
		return {
			originData: data.map((item) => ({ ...item, roomNightsRate: item.roomNightsRate + '%' })),
			data: data?.filter(item => item.roomNights != 0)?.map((item, index) => {
				return {
					name: item.labelName + ' ' + item.roomNightsRate + '%',
					value: item.roomNights,
					labelShow: false
				}
			}) || []
		}
	})
</script>

<style scoped lang="scss">
	.section {
		padding: 40rpx;
		background: #ffffff;
		border-radius: 20rpx;

		// .chart {
		// 	padding: 0 40rpx;
		// }
	}
</style>