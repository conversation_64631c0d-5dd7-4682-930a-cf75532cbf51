<template>
	<view class="w-container">
		<commonBgVue height="304" />
		<u-navbar placeholder title="智慧运营" bg-color="transparent" leftIconColor="#FFF"
			titleStyle="font-size: 36rpx;font-weight:700;color:#FFF;" autoBack>
		</u-navbar>
		<view class="content">
			<view class="fr tip">
				<view class="u-icon">
					<up-icon name="info-circle"></up-icon>
				</view>
				<text v-if="pageOptions.status == 0">您已完成巡店，报告将以电子版的形式发给签署人进行签章，请务必填写正确。</text>
				<text v-if="pageOptions.status == 1">如需重新发送电子报告，系统会将您退至“签章人信息未发送”节点, 您重新填写正确的签章人信息，重新进行发送即可。</text>
			</view>
			<view class="fr block">
				<text class="block-title">签署人：</text>
				<input v-model="form.signMan" type="text" placeholder="请填写签署人名称" class="block-title__input"
					placeholder-style="font-size: 26rpx;color:#999;" />
			</view>
			<view class="fr block">
				<text class="block-title">手机号：</text>
				<input v-model="form.phone" type="tel" placeholder="请填写签署人手机号" class="block-title__input"
					placeholder-style="font-size: 26rpx;color:#999;" maxlength="11" />
			</view>
			<view class="fr block">
				<text class="block-title">是否加盖公章：</text>
				<view class="radio-group">
					<up-radio-group v-model="form.commonSeal" shape="circle" usedAlone :size="size" activeColor="#C35943"
						inactiveColor="#C35943" labelColor="#1F2428">
						<up-radio label="是" :name="1" :labelSize="size" />
						<up-radio label="否" :name="0" :labelSize="size" />
					</up-radio-group>
				</view>
			</view>
			<view class="fr block" v-if="form.commonSeal == 1">
				<text class="block-title">公司企业名称：</text>
				<input v-model="form.company" type="text" placeholder="请填写公司企业名称" class="block-title__input"
					placeholder-style="font-size: 26rpx;color:#999;" />
			</view>
			<view class="fl block" v-if="pageOptions.status == 1">
				<cTextareaVue v-model="form.backReason" count maxlength="100" placehodler="请填写重新发电子报告理由"
					background="linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);" />
			</view>
		</view>
		<fixedBtnLayoutVue ref="fixedBtnLayoutRef">
			<view class="fr w-container-footer">
				<text class="fr btn btn-primary" @click="cancel">取消</text>
				<text class="fr btn btn-success" @tap="submit">{{ pageOptions.status == 0 ? '': '重新'}}发送</text>
			</view>
		</fixedBtnLayoutVue>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { getCurrentInstance, ref } from 'vue'
	import commonBgVue from '../../components/common-bg/common-bg.vue'
	import cTextareaVue from '../../components/c-textarea/c-textarea.vue';
	import fixedBtnLayoutVue from '../../components/fixed-btn-layout/fixed-btn-layout.vue';
	import { useContentLayoutHeight } from '../../hooks';
	import { isObject, showToast, telphoneReg } from '../../../utils/util';
	import { saveSign } from '../../api';
	import { useCommon } from '@/hooks/useGlobalData'
	const { fixedBtnLayoutRef, contentHeight, getFixedBtnLayoutHeight, getContentLayoutHeight } = useContentLayoutHeight('.content')
	const { setRouteParams } = useCommon(getCurrentInstance())
	const pageOptions = ref<Partial<{
		status : string | number;
		orderId : string | number;
		tripCode : string;
	}>>({})
	const form = ref<Partial<{
		commonSeal : number;
		signMan : string;
		phone : string;
		company ?: string;
		backReason ?: string;
	}>>({})
	const size = uni.upx2px(28)
	/**
	 * @description: 校验
	 */
	const validate = async () => {
		const validateRules = {
			signMan: { required: true, message: '请填写签署人名称' },
			phone: [
				{ required: true, message: '请填写签署人手机号' },
				{
					required: true, validator: (value : string) : any => {
						if (!telphoneReg.test(value)) {
							return false
						}
						return true
					}, message: '手机号格式错误~'
				}
			],
			commonSeal: {
				required: true, message: '请选择是否公章', validator: (value : string | number) => {
					if (value == undefined) {
						return false
					}
					return true
				}
			},
			company: { required: form.value.commonSeal == 1, message: '请填写公司企业名称' },
			backReason: { required: pageOptions.value.status == 1, message: '请填写重发理由' }
		}
		const validObj = (key : string, valueObj : any) => {
			if (valueObj.required) {
				if (Reflect.has(valueObj, 'validator')) {
					const res = valueObj.validator(form.value[key])
					if (!res) {
						showToast(valueObj.message)
						return false
					}
				} else {
					if (!form.value[key]?.trim()) {
						showToast(valueObj.message)
						return false
					}
				}
			}
			return true
		}
		for (const [key, value] of Object.entries(validateRules)) {
			if (isObject(value)) {
				if (!validObj(key, value)) {
					return false
				}
			} else {
				for (let i = 0, len = (value as []).length; i < len; i++) {
					if (!validObj(key, (value as [])[i])) {
						return false
					}
				}
			}
		}
		return true
	}
	/**
	 * @description : 发送报告
	 */
	const submit = async () : Promise<void> => {
		const res = await validate()
		if (res) {
			await saveSign({
				...form.value,
				...pageOptions.value
			})
			showToast({
				title: '发送成功',
				success: () => {
          setRouteParams('packageB/pages/shop-inspection-report/shop-inspection-report')
          setRouteParams('packageB/pages/shop-inspection/shop-inspection-trip')
					let timer = setTimeout(() => {
						clearTimeout(timer)
						timer = null
						uni.navigateBack({
              fail: (err: any) => {
                console.error(err)
              }
            })
					}, 1000)
				}
			})
		}
	}

	const cancel = () => {
		uni.navigateBack()
	}

	onLoad(async (opts) => {
		pageOptions.value = opts
		await getFixedBtnLayoutHeight()
		await getContentLayoutHeight()
	})
</script>

<style lang="scss" scoped>
	.w-container {
		.content {
			padding: 20rpx 20rpx 0;
			overflow-y: auto;
			height: v-bind(contentHeight);

			.u-icon {
				flex-shrink: 0;
				margin-right: 8rpx;
			}

			.tip {
				padding: 16rpx;
				margin-bottom: 20rpx;
				font-size: 22rpx;
				color: #1F2428;
				border-radius: 20rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
			}
		}

		&-footer {
			flex: 1;
			padding: 0 40rpx;
			justify-content: space-between;

			.btn {
				justify-content: center;
				width: 314rpx;
				height: 80rpx;
				border: 2rpx solid #c35943;
				font-weight: bold;
				color: #C35943;
				border-radius: 200rpx;

				&-primary {
					border: none;
					color: #fff;
					background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				}
			}
		}

		.block {
			padding: 40rpx;
			margin-bottom: 20rpx;
			background-color: #fff;
			border-radius: 20rpx;

			&.fr {
				justify-content: space-between;
			}

			&-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #1F2428;

				&__input {
					text-align: right;
				}
			}
		}
	}
</style>