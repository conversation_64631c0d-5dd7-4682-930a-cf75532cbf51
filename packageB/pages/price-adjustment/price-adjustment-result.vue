<template>
  <view class="container">
    <view class="section section-image fl">
      <image
        v-if="type==2" class="img"
        src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-05/1730789653760.png"
        mode=""></image>
      <image
        v-else class="img"
        src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-05/1730789683791.png"
        mode=""></image>
      <view class="title">
        {{ type == 2 ? '已通过调价申请' : '已驳回调价申请' }}
      </view>
    </view>
    <view class="section" v-if="pagination.total > 0">
      <view class="title fr">
        <view class="title-text fr">
          当前门店其他调价房型待审核：
          <view class="title-text-tip">
            {{ pagination.total }}
            <text>(单)</text>
          </view>
        </view>
      </view>
      <view class="table">
        <scroll-view class="list" scroll-y="ture" :style="{height:height}" @scrolltolower="scrolltolower">
          <view class="row" v-for="(item,index) in list" :key="index">
            <view class="td">{{ item.roomTypeName }}</view>
            <view class="td">￥{{ item.submitRackRate }}</view>
            <view class="td fr" style="justify-content: center;" @click="toDetail(item)">
              <text style="margin-right: 16rpx;">继续</text>
              <up-icon name="arrow-right" color="#999999" size="20rpx" />
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
  <view class="footer" @click="back">
    <view class="btn custom-btn-primary">返回列表</view>
  </view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
  import { onLoad } from '@dcloudio/uni-app'
  import { ComponentInternalInstance, getCurrentInstance, reactive, ref } from 'vue';
  import { useCommon } from '@/hooks/useGlobalData';
  import { applicationsList } from '../../api';

  const {route_to_view} = useCommon(getCurrentInstance() as ComponentInternalInstance)

  const type = ref(1)

  const list = ref([])
  const shopId = ref()

  const pagination = reactive({
    current: 1,
    maxResultCount: 20,
    total: 0,
    loading: true
  })

  onLoad((options) => {
    type.value = options.type
    if (options.type != 2) {
      uni.setNavigationBarTitle({
        title: '已驳回调价申请'
      })
    }
    shopId.value = options.shopId
    toApplications()
  })


  // 获取调价申请列表
  const toApplications = async () => {
    try {
      const {code, data} = await applicationsList({
        hotelMatching: shopId.value,
        auditStatusIndex: 1,
        current: pagination.current,
        maxResultCount: pagination.maxResultCount
      })
      if (code == 200) {
        pagination.total = data.total
        if (data.content?.length < pagination.maxResultCount) {
          pagination.loading = false
        }
        list.value = [...list.value, ...(data.content || [])]
      } else {
        list.value = []
      }
    } catch (e) {
      console.error('获取调价申请列表 error', e)
    }
  }

  const toDetail = (item) => {
    uni.redirectTo({
      url: `./price-adjustment-detail?roomPriceId=${item.roomPriceId}`
    })
  }

  const scrolltolower = () => {
    if (!pagination.loading) return
    pagination.current += 1
    toApplications()
  }

  const back = () => {
    uni.navigateBack()
  }
</script>

<style scoped lang="scss">
  .container {
    padding: 20rpx;
  }

  .section {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }


    .title {
      justify-content: space-between;

      &-text {
        color: #1f2428;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 32rpx;
      }

      &-text-tip {
        color: $uni-text-color;
        font-size: 32rpx;
        font-weight: bold;
        line-height: 32rpx;

        text {
          color: $uni-text-color;
          font-size: 24rpx;
          font-weight: bold;
          line-height: 24rpx;
        }
      }
    }

    &-image {
      padding: 60rpx 0;
      align-items: center;

      .img {
        flex-shrink: 0;
        width: 160rpx;
        height: 166rpx;
      }

      .title {
        color: #1f2428;
        font-size: 36rpx;
        font-weight: bold;
        line-height: 36rpx;
        margin-top: 28rpx;
      }
    }
  }

  .table {
    border-radius: 20rpx;
    margin-top: 40rpx;

    .row {
      display: flex;
      align-items: center;
      background: rgba(247, 94, 59, 0.08);

      &:first-child {
        border-radius: 20rpx 20rpx 0 0;
      }

      &:last-child {
        border-radius: 0 0 20rpx 20rpx;
      }

      &:nth-child(even) {
        background: rgba(247, 94, 59, 0.04)
      }

      .td {
        flex: 1;
        color: #1f2428;
        font-size: 22rpx;
        line-height: 22rpx;
        padding: 32rpx 0 30rpx;
        text-align: center;
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    background: #FFF;
    border-radius: 40rpx 40rpx 0 0;
    padding: 40rpx 40rpx 82rpx;

    .btn {
      color: #ffffff;
      font-size: 28rpx;
      font-weight: bold;
      text-align: center;
      line-height: 28rpx;
      padding: 26rpx 0;
      border-radius: 200rpx;
    }
  }
</style>
