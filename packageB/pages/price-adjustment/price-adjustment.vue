<template>
	<view>
		<view class="search fr">
			<cPickerVue :columns="columns" keyName="label" :value="auditStatusIndex" @change="bindPickerChange">
				<view class="picker">{{columns[auditStatusIndex].label}}</view>
			</cPickerVue>
			<up-input placeholder="请输入门店ID/门店名称" :placeholderStyle="placeholderStyle" shape="circle" border="none"
				v-model="hotelMatching" :customStyle="customStyle"></up-input>
		</view>
		<!--<view class="search-room_type">-->
		<!--	<up-search v-model="roomTypeMatching" :placeholderStyle="placeholderStyle" show-action placeholder="请输入房型名称"-->
		<!--		@custom="search" />-->
		<!--</view>-->
    <view class="search-section">
      <input class="uni-input" v-model="roomTypeMatching" placeholder="请输入房型名称" shape="circle" border="none"
        placeholder-class="placeholderClass" />
      <view class="button-group fr">
        <view class="custom-btn custom-btn-primary btn" @tap="search">查询</view>
      </view>
    </view>
		<view class="content">
			<view class="count">
				数量：<text class="num">{{pagination.total}}</text>
			</view>
			<scroll-view class="list" scroll-y="ture" :style="{height:height}" @scrolltolower="scrolltolower">
				<view class="block" v-if="list.length > 0" v-for="(item,index) in list" :key="index" @click="toDetail(item)">
					<view class="status fr">
						<view class="text" :class="`text-status-${item.auditStatusIndex}`">
							{{item.auditStatusIndex == 1 ?'待审核':item.auditStatusIndex == 2 ? '已审核' : '被驳回'}}
						</view>
					</view>
					<view class="info">
						<view class="row fr" v-for="(v,idx) in rowList" :key="idx">
							<view class="label">{{v.label}}：</view>
							<view class="value" v-if="idx!=4">{{item[v.prop]}}</view>
							<view class="num" v-else><text>￥</text>{{item[v.prop]}}</view>
						</view>
					</view>
				</view>
				<view v-else>
					<view class="">
						<emptyVue hasBackground text="未查询到审核信息" />
					</view>
				</view>
				<view class="fr empty" v-if="!pagination.loading && pagination.total">
					<text class="empty-text">到底了~</text>
				</view>
			</scroll-view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onShow } from '@dcloudio/uni-app'
  import { onMounted, ref, getCurrentInstance, reactive, ComponentInternalInstance, nextTick } from 'vue';
	import emptyVue from '@/components/empty/empty.vue';
	import { applicationsList } from '../../api';
	import cPickerVue from '../../components/c-picker/c-picker.vue';
	import { useCommon } from '@/hooks/useGlobalData';
	const instance = getCurrentInstance()
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const placeholderStyle = "color: #999999; font-size: 24rpx; font-weight: bold; line-height: 24rpx"
	const customStyle = { height: '64rpx', background: '#F5F5F5', padding: '20rpx 40rpx' }
	const rowList = [
		{ label: "提交时间", prop: 'submitTime' },
		{ label: "门店ID", prop: 'hotelCode' },
		{ label: "门店名称", prop: 'hotelName' },
		{ label: "房型名称", prop: 'roomTypeName' },
		{ label: "门市价", prop: 'submitRackRate' },
	],
		columns = [
			{ label: '待审核', value: 1 },
			{ label: '审核通过', value: 2 },
      { label: '审核驳回', value: 3 },
      { label: '全部', value: 0 }
		]

	const height = ref()
	const hotelMatching = ref<string>(), // 酒店匹配(酒店编码&酒店名称模糊匹配)
		roomTypeMatching = ref<string>(), // 房型匹配(房型名称模糊匹配)
		auditStatusIndex = ref<number>(0), //审核状态
		list = ref([])
	const pagination = reactive({
		current: 1,
		maxResultCount: 10,
		total: 0,
		loading: true
	})
	onShow(() => {
		reset()
		toApplications()
	})

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.list`).boundingClientRect((res : any) => {
			height.value = info.windowHeight - res.top - (info.safeAreaInsets.bottom || 20) + 'px'
		}).exec();
	})

	// 获取调价申请列表
	const toApplications = async () => {
		try {
			const { code, data } = await applicationsList({
				hotelMatching: hotelMatching.value,
				roomTypeMatching: roomTypeMatching.value,
				auditStatusIndex: columns[auditStatusIndex.value].value,
				current: pagination.current,
				maxResultCount: pagination.maxResultCount
			})
			if (code == 200) {
				pagination.total = data.total
				if (data.content?.length < pagination.maxResultCount) {
					pagination.loading = false
				}
				list.value = [...list.value, ...(data.content || [])]
			} else {
				list.value = []
			}
		} catch (e) {
			console.error('获取调价申请列表 error', e)
		}
	}

	const bindPickerChange = (e : any) => {
		auditStatusIndex.value = e.indexs[0]
		search()
	}

	const search = () => {
		reset()
    nextTick(()=>{
      toApplications()
    })
	}

	const scrolltolower = async () : Promise<void> => {
		if (!pagination.loading) return
		pagination.current += 1
		await toApplications()
	}

	const reset = () => {
    list.value = []
		pagination.loading = true
		pagination.current = 1
		pagination.maxResultCount = 10
	}

	const toDetail = (item : any) => {
		console.log(item);
		route_to_view(`./price-adjustment-detail?roomPriceId=${item.roomPriceId}`)
	}
</script>

<style scoped lang="scss">
	.search {
		padding: 20rpx;
		background: #fff;
		margin-bottom: 40rpx;

		:deep() {
			.c-picker {
				margin: 0 10rpx;
				background: #F5F5F5;
				width: 234rpx;
			}
		}
	}
  .search-section {
    margin: 0 20rpx 40rpx;
    background: #ffffffe6;
    padding: 8rpx 8rpx 8rpx 40rpx;
    border-radius: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    :deep() {
      .placeholderClass {
        color: #999999;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 24rpx;
      }

      .uni-input {
        width: 500rpx;
      }
    }

    .button-group {
      .btn {
        width: auto;
        padding: 12rpx 28rpx;
        margin: 0 8rpx 0 2rpx;
        font-size: 24rpx;
        font-weight: bold;
        line-height: 24rpx;
      }
    }
  }

	.search-room_type {
		margin-bottom: 40rpx;
		padding: 0 20rpx;

		:deep() {
			.u-search__content {
				width: 100%;
				padding-left: 40rpx;
				background: #fff !important;
				border: none;
			}

			.u-search__content__input {
				background-color: #fff !important;
				color: #303133 !important;
			}

			.u-search__content__input--placeholder {
				color: #999999 !important;
				font-size: 24rpx !important;
				font-weight: bold;
				line-height: 64rpx !important;
				background: #fff !important;
			}

			.u-search__content__icon {
				display: none;
			}

			.u-search__action {
				position: absolute;
				right: 28rpx;
				border-radius: 150rpx;
				opacity: 1;
				background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				color: #ffffff;
				font-size: 24rpx;
				font-weight: bold;
				line-height: 48rpx;
				width: 104rpx;
				height: 48rpx;
				z-index: 200;
			}
		}
	}

	.count {
		color: #1f2428;
		font-size: 32rpx;
		font-weight: bold;
		line-height: 32rpx;
		margin-bottom: 40rpx;

		.num {
			color: $uni-text-color;
			font-size: 32rpx;
			font-weight: bold;
			line-height: 32rpx;
		}
	}

	.list {

		.block {
			border-radius: 20rpx;
			background: #ffffff;
			padding: 20rpx 0 38rpx;
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.status {
			justify-content: flex-end;
			margin-bottom: 20rpx;


			.text {
				color: #43bf83;
				font-size: 22rpx;
				font-weight: bold;
				text-align: center;
				line-height: 22rpx;
				padding: 8rpx 28rpx;
				border-radius: 200rpx 0 0 200rpx;
				background: linear-gradient(203.5deg, rgba(188, 227, 208, 0.2) 0%, rgba(67, 191, 131, 0.2) 100%);

				&-status-1 {
					color: #FF8C1A;
					background: linear-gradient(119.6deg, rgb(255, 140, 26, 0.2) 0%, rgb(255, 207, 158, 0.2) 100%);
				}

				&-status-2 {
					color: #43bf83;
					background: linear-gradient(203.5deg, rgba(188, 227, 208, 0.2) 0%, rgba(67, 191, 131, 0.2) 100%);
				}

				&-status-3 {
					color: #F75E3B;
					background: linear-gradient(203.5deg, rgb(255, 178, 161, 0.2) 0%, rgb(247, 94, 59, 0.2) 100%);
				}
			}

		}

		.info {
			padding: 0 40rpx;

			.row {
				margin-bottom: 32rpx;
				justify-content: space-between;

				&:last-child {
					margin-bottom: 0;
				}

				.label {
					opacity: 0.7;
					color: #1f2428;
					font-size: 28rpx;
					line-height: 28rpx;
					flex-shrink: 0;
				}

				.value {
					color: #1f2428;
					font-size: 28rpx;
					font-weight: bold;
					word-break: break-all;
				}

				.num {
					color: #ff4d4d;
					font-size: 32rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					line-height: 32rpx;

					text {
						color: #ff4d4d;
						font-size: 24rpx;
						font-weight: 700;
						font-family: "DIN Bold";
						line-height: 24rpx;
					}
				}
			}
		}
	}

	.tip {
		display: flex;
		padding: 10rpx 16rpx;
		background: linear-gradient(90deg, #fef2ef 0%, #fcf6f5 100%);
		border-radius: 20rpx;
		margin-bottom: 20rpx;

		color: #1f2428;
		font-size: 22rpx;
		line-height: 32rpx;

		.t1 {
			flex-shrink: 0;
			width: 32rpx;
			height: 32rpx;
			border-radius: 50%;
			color: #f75e3b;
			font-size: 20rpx;
			line-height: 32rpx;
			font-weight: bold;
			text-align: center;
			background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.2) 0%, rgba(247, 94, 59, 0.2) 100%);
			margin-right: 8rpx;
		}
	}

	.content {
		padding: 0 20rpx;
	}


	.empty {
		justify-content: center;
		font-size: 22rpx;

		&-text {
			color: #999;
		}
	}
</style>