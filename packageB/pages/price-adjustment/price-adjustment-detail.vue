<template>
	<view class="container">
		<view class="section">
			<view class="row" v-for="(item,index) in section1" :key="index">
				<view class="label">{{ item.label }}：</view>
				<view class="value">{{applicationsInfoData[item.prop]}}</view>
			</view>
		</view>
		<view class="section">
			<view class="row" v-for="(item,index) in section2" :key="index">
				<view class="label">{{ item.label }}：</view>
				<view class="value">
					{{item.prop == 'weeks' ? applicationsInfoData[item.prop] : (applicationsInfoData?.beginDate?.replace('-', '.') || '' +'-'+ applicationsInfoData?.endDate?.replace('-', '.') || '') }}
				</view>
			</view>
		</view>
		<view class="section">
			<view class="title fr" v-if="applicationsInfoData?.auditStatusIndex == 1" @click="toDetail">
				<view class="title-text">
					调价涨幅明细
				</view>
				<view class="title-btn fr">
					<text class="text">查看</text>
					<up-icon name="arrow-right" color="#999999" size="22rpx" />
				</view>
			</view>
			<view class="row" v-for="(item,index) in applicationsInfoData?.changeRatePlanPriceInfoVOS" :key="index">
				<view class="label">{{ item.ratePlanName }}：</view>
				<view class="value num"><text class="unit">￥</text>{{item.price}}</view>
			</view>
		</view>
		<view class="section">
			<view class="row" v-for="(item,index) in section4" :key="index">
				<view class="label">{{ item.label }}：</view>
				<view class="value">{{applicationsInfoData[item.prop]}}</view>
			</view>
		</view>
		<view class="section">
			<view class="row" v-for="(item,index) in section5" :key="index">
				<view class="label">{{ item.label }}：</view>
				<view class="value">{{applicationsInfoData[item.prop]}}</view>
			</view>
		</view>
		<view class="section">
			<view class="status fr">
        <view class="label">审核状态：</view>
				<view class="text" :class="`text-status-${applicationsInfoData?.auditStatusIndex}`">
					{{applicationsInfoData?.auditStatusIndex == 1 ?'待审核':applicationsInfoData?.auditStatusIndex == 2 ? '已审核' : '被驳回'}}
				</view>
			</view>
			<view class="row" style="margin-top: 20rpx" v-for="(item,index) in section6" :key="index" v-if="applicationsInfoData?.auditStatusIndex !== 1">
				<view class="label">{{ item.label }}：</view>
				<view class="value">{{ applicationsInfoData[item.prop] }}</view>
			</view>
		</view>
		<view class="section">
			<view class="title fr" style="margin-bottom: 40rpx;">
				<view class="title-text fr">
					审核意见
					<text class="title-text-tip">
						（驳回必填）：
					</text>
				</view>
			</view>
      <cTextareaVue v-model="textareaValue"
        count
        :placeholderStyle="placeholderStyle"
        :disabled="applicationsInfoData?.auditStatusIndex !== 1"
        maxlength="100"
        background="#F5F5F5"
        :placeholder="applicationsInfoData?.auditStatusIndex !== 1 ? '无' : '请依据门店真实情况，填写真实的审核意见哦～~最少输入10个字'" />
		</view>
	</view>
	<view class="btn-block" v-if="applicationsInfoData?.auditStatusIndex === 1">
		<view class="btn-text-plain" @click="toResult(3)">
			驳回申请
		</view>
		<view class="btn-text" @click="toResult(2)">
			通过申请
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
  import cTextareaVue from '../../components/c-textarea/c-textarea.vue'
	import { onLoad } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, computed, getCurrentInstance, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { applicationAudit, applicationsInfo } from '../../api';
  import { showLoading, showToast } from '../../../utils/util';

	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const placeholderStyle = 'color: #999999; font-size: 26rpx;'
	const section1 = [
		{ label: '酒店ID', prop: 'hotelCode' },
		{ label: '门店名称', prop: 'hotelName' },
		{ label: '房型名称', prop: 'roomTypeName' },
	]
	const section2 = [
		{ label: '起始日期', prop: 'date' },
		{ label: '包含星期', prop: 'weeks' }
	]
	const section4 = [
		{ label: '价格附带', prop: 'incidentalServices' },
		{ label: '付款方式', prop: 'supportPaymentMethod' },
	]
	const section5 = [
		{ label: '提交人', prop: 'submitter' },
		{ label: '提交时间', prop: 'submitTime' },
	]
	const section6 = [
		{ label: '审核角色', prop: 'reviewer' },
	]
	const textareaValue = ref<string>(''),
		applicationsInfoData = ref<any>({}) // 调价详情信息
  const instance = getCurrentInstance() as ComponentInternalInstance

	onLoad((options) => {
		console.log(options.roomPriceId);
    if (options.roomPriceId) {
      showLoading('加载中')
      instance.appContext.app.config.globalProperties.$unLaunched.then((res : any) => {
        if (res) {
          toApplicationsInfo(options.roomPriceId)
        }
      })
    }
	})

	// 调价申请详情
	const toApplicationsInfo = async (roomPriceId : string) => {
		try {
			const { code, data } = await applicationsInfo({ roomPriceId })
			if (code === 200) {
        if (data) {
          textareaValue.value = data?.reviewOpinion || ''
          applicationsInfoData.value = data
        }
			}
		} catch (e) {
			console.error('调价申请详情 error', e)
		}
	}

	const toDetail = () => {
		const data = encodeURIComponent(JSON.stringify(applicationsInfoData.value))
		route_to_view(`./price-adjustment-date-detail?data=${data}`)
	}

	function validateInput(input) {
		// 校验规则：不可连续输入3位及以上的空格或字符
		if (/([\s\S])\1{2,}/.test(input)) {
			showToast('不可连续输入3位及以上的空格或字符')
			return false;
		}

		// 校验规则：可以输入汉字和数字和逗号
		if (!(/[\u4e00-\u9fa5\d(,|，)(.|。)]+$/.test(input))) {
			showToast('请输入汉字和数字')
			return false;
		}

		// 校验规则：不可输入纯数字
		if (/^\d+$/.test(input)) {
			showToast('不可输入纯数字')
			return false;
		}

		return true; // 输入内容符合校验规则
	}

	const toResult = async (type : number) => {
    if (!textareaValue.value) { showToast('请填写审核意见'); return }
    if (textareaValue.value.length < 10) { showToast('最少输入10个字'); return }
    if (!validateInput(textareaValue.value)) { return }
		try {
			await applicationAudit({
				roomPriceId: applicationsInfoData.value.roomPriceId,
				auditStatusIndex: type,
				reviewOpinion: textareaValue.value
			})
      textareaValue.value = ''
      await uni.redirectTo({
        url: `./price-adjustment-result?type=${type}&shopId=${applicationsInfoData.value.hotelCode}`
      })
		} catch (e) {
			console.error('调价申请审核 error', e)
		}
	}
	const btn_safe_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 40 + 'rpx'

	const padding_bottom = computed(() => {
		if (applicationsInfoData.value?.auditStatusIndex !== 1) {
			return uni.getSystemInfoSync().safeAreaInsets.bottom + 20 + 'rpx'
		} else {
			return uni.getSystemInfoSync().safeAreaInsets.bottom + 160 + 20 + 'rpx'
		}
	})
</script>

<style scoped lang="scss">
	.container {
		padding: 20rpx;
		padding-bottom: v-bind(padding_bottom);
	}

	.section {
		border-radius: 20rpx;
		background: #ffffff;
		padding: 30rpx 40rpx;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.title {
			justify-content: space-between;
			margin-bottom: 30rpx;

			&-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			&-text-tip {
				color: #1f2428b3;
				font-size: 26rpx;
				line-height: 26rpx;
			}

			&-btn {
				.text {
					color: #999999;
					font-size: 24rpx;
					line-height: 24rpx;
					margin-right: 16rpx;
				}
			}
		}

		.row {
			display: flex;
			justify-content: space-between;
      align-items: center;
			word-break: break-all;
			margin-bottom: 12rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				opacity: 0.7;
				color: #1f2428;
				font-size: 28rpx;
				line-height: 48rpx;
				min-width: 140rpx;
				flex-shrink: 0;
			}

			.value {
				color: #1f2428;
				font-size: 28rpx;
				font-weight: bold;
				line-height: 48rpx;
				text-align: right;
			}

			.unit {
				color: #ff4d4d;
				font-size: 24rpx;
				font-weight: 700;
				font-family: "DIN Bold";
			}

			.num {
				display: flex;
				align-items: flex-start;
				color: #ff4d4d;
				font-size: 32rpx;
				font-weight: 700;
				font-family: "DIN Bold";
				line-height: 32rpx;
			}
		}
	}

	.status {
		justify-content: space-between;

    .label {
      opacity: 0.7;
      color: #1f2428;
      font-size: 28rpx;
      line-height: 48rpx;
      min-width: 140rpx;
      flex-shrink: 0;
    }

		.text {
			color: #43bf83;
			font-size: 22rpx;
			font-weight: bold;
			text-align: center;
			line-height: 22rpx;
			padding: 8rpx 28rpx;
			border-radius: 200rpx 0 0 200rpx;
      transform: translateX(40rpx);
			background: linear-gradient(203.5deg, rgba(188, 227, 208, 0.2) 0%, rgba(67, 191, 131, 0.2) 100%);

			&-status-1 {
				color: #FF8C1A;
				background: linear-gradient(119.6deg, rgb(255, 140, 26, 0.2) 0%, rgb(255, 207, 158, 0.2) 100%);
			}

			&-status-2 {
				color: #43bf83;
				background: linear-gradient(203.5deg, rgba(188, 227, 208, 0.2) 0%, rgba(67, 191, 131, 0.2) 100%);
			}

			&-status-3 {
				color: #F75E3B;
				background: linear-gradient(203.5deg, rgb(255, 178, 161, 0.2) 0%, rgb(247, 94, 59, 0.2) 100%);
			}
		}
	}

	.btn-block {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		padding: 40rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding-bottom: v-bind(btn_safe_bottom);
		z-index: 2000000;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.btn-text-plain {
			width: 314rpx;
			border-radius: 200rpx;
			border: 2rpx solid $uni-text-color;
			background: #ffffff;
			color: $uni-text-color;
			padding: 26rpx 0;
			font-size: 28rpx;
			font-weight: bold;
			line-height: 28rpx;
			text-align: center;
		}

		.btn-text {
			width: 314rpx;
			color: #ffffff;
			font-size: 28rpx;
			font-weight: bold;
			text-align: center;
			line-height: 28rpx;
			border-radius: 200rpx;
			padding: 26rpx 0;
			background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
		}
	}

	:deep() {
		.u-textarea {
			border-radius: 20rpx !important;
			background-color: #F5F5F5 !important;
			border: none !important;
		}

		.u-textarea__count {
			background-color: #F5F5F5 !important;
		}

		.u-textarea__field {
			color: #1f2428 !important;
			font-size: 26rpx !important;
			line-height: 26rpx !important;
		}
	}
</style>