<template>
	<view>
		<view class="calendar">
			<cCalendarVue v-model="calendarValue" :startDate="minDate" :endDate="maxDate" @confirm="confirm" :showIcon="false" />
		</view>
		<view class="content">
			<view class="section">
				<view class="title fr">
					<view class="title-text-1">
						{{roomTypeName}}
					</view>
					<view class="title-text-2">
						调整后<text>{{expectedRackRate}}</text>
					</view>
				</view>
				<view class="table-block">
					<cTableVue cellHeight="auto" :header="header" :list="list" :isPaginate="false" :height="scrollHeight"
						@scrolltolower="scrolltolower">
						<template #default="scope">
							<view v-if="scope.prop === 'newRackRate'" class="rate-item">
								<image class="item-icon" v-if="scope.data.newRackRate - scope.data.oldRackRate != 0"
									:src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${(scope.data.newRackRate - scope.data.oldRackRate) > 0 ? '1729644716250' : '1729644734157'}.png`"
									mode="aspectFill"></image>
								<text class="item-text"
									:class="[(scope.data.newRackRate - scope.data.oldRackRate) > 0 ? 'up-text' : (scope.data.newRackRate - scope.data.oldRackRate) < 0 ?'down-text':'']">{{Math.abs(scope.data.newRackRate - scope.data.oldRackRate)}}</text>
							</view>
						</template>
					</cTableVue>
				</view>
			</view>
		</view>
	</view>
  <cWaterMark></cWaterMark>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
	import cCalendarVue from '@/components/c-calendar/c-calendar.vue';
	import cTableVue from '@/components/c-table/c-table.vue'
	import { applicationRelatedRecords } from '../../api';
	import dayjs from 'dayjs';
	const instance = getCurrentInstance();
	const header = [
		{ name: '日期', prop: 'rackRateDate' },
		{ name: '调整前', prop: 'oldRackRate' },
		{ name: '调整幅度', prop: 'newRackRate', slot: 'newRackRate' },
	]

	const list = ref([])
	const scrollHeight = ref()
	const pagination = reactive({
		current: 1,
		maxResultCount: 20,
		total: 0,
		loadMore: true
	})
	const calendarValue = ref<string[]>([])
	const roomPriceId = ref<string>()
	const roomTypeName = ref<string>()
  const expectedRackRate = ref<string>()
  const minDate = ref<string>()
  const maxDate = ref<string>()

	onLoad((options) => {
		const data = JSON.parse(decodeURIComponent(options.data))
		const { roomPriceId: id, beginDate, endDate, roomTypeName: roomName, expectedRackRate: price } = data
		calendarValue.value = [beginDate, endDate]
    minDate.value = beginDate
    maxDate.value = endDate
		roomPriceId.value = id
		roomTypeName.value = roomName
		expectedRackRate.value = price
		toApplicationRelatedRecords()
	})

	// 调价单关联房型调价记录
	const toApplicationRelatedRecords = async () => {
		try {
			const { current, maxResultCount } = pagination
			const { data } = await applicationRelatedRecords({
				current, maxResultCount,
				roomPriceId: roomPriceId.value,
				beginDate: calendarValue.value[0],
				endDate: calendarValue.value[1],
			})
			if (data.content) {
				if (data.content.length < pagination.current) pagination.loadMore = false
				nextTick(() => {
					if (list.value && list.value.length > 0)
						list.value.push(...data.content)
					else
						list.value = data.content
				})
			} else {
				list.value = []
			}
		} catch (e) {
			console.error('调价单关联房型调价记录 error', e)
		}
	}

	const scrolltolower = () => {
    if (!pagination.loadMore) return
    pagination.current += 1
    toApplicationRelatedRecords()
	}

	const confirm = () => {
		reset()
		toApplicationRelatedRecords()
	}

	const reset = () => {
		pagination.current = 1
		pagination.total = 0
		pagination.loadMore = true
		list.value = []
	}

	onMounted(() => {
		const info = uni.getSystemInfoSync()
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.table-block`).boundingClientRect((res : any) => {
			scrollHeight.value = info.windowHeight - res.top - 42 - 20 - 20 + 'px'
		}).exec();
	})
</script>

<style scoped lang="scss">
	.calendar {
		background: #fff;
		padding: 20rpx;
		margin-bottom: 40rpx;
	}

	.content {
		padding: 0 20rpx;
	}

	.section {
		padding: 40rpx;
		background: #fff;
		border-radius: 20rpx;

		.title {
			justify-content: space-between;
			margin-bottom: 40rpx;

			&-text-1 {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			&-text-2 {
				color: #1f2428;
				font-size: 22rpx;
				font-weight: bold;
				line-height: 22rpx;

				text {
					color: #ff4d4d;
					font-size: 28rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					text-align: center;
					line-height: 28rpx;
					margin-left: 8rpx;
				}
			}
		}
	}

	.rate-item {
		display: flex;
		align-items: center;
		justify-content: center;

		.item-icon {
			width: 16rpx;
			height: 20rpx;
		}

		.item-text {
			padding-left: 8rpx;
			font-size: 24rpx;
			line-height: 24rpx;
			font-weight: 700;
			font-family: "DIN Bold";
			color: #1F2428;
			white-space: nowrap;
		}

		.up-text {
			color: #FF4D4D;
		}

		.down-text {
			color: #56CC93;
		}
	}
</style>