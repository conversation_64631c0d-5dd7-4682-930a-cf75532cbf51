import { computed } from 'vue'
import { showLoading, showToast } from '@/utils/util'
import { ossUpload } from "../../ai/api/ai";
import { fileUpload } from "../api";
export const useUpload = () => {
	/**
		* @description: 压缩图片
		* @param {*} filePath
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const compressImage = (filePath : string) : Promise<string> => {
		return new Promise((resolve) => {
			uni.compressImage({
				src: filePath,
				quality: 30,
				success: (res : UniApp.CompressImageSuccessResult) => {
					resolve(res.tempFilePath)
				}
			})
		})
	}
	/**
		* @description: 上传接口
		* @param {*} filePath
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const upload = async (filePath : string) : Promise<string> => {
		try {
			const res = await fileUpload({
				filePath: filePath,
				header: {
					appId: 'smartOperating'
				},
				name: 'file',
			})
			const data = res.data
			return await data;
		} catch (e) {
			console.log("🚀 ~ upload ~ e:", e)
		}
	}

	/**
		* @description: 上传图片
		* @return {*}
		* @author: gu_shiyuan"
		*/
	const uploadImage = (count : number, sourceType : number = 1) : Promise<any> => {
		return new Promise((resolve) => {
			uni.chooseImage({
				count: count,
				sizeType: 'compressed',
				sourceType: sourceType ? ['album', 'camera'] : ['camera'],
				success: (res) => {
					resolve(res.tempFilePaths)
				}
			})
		})
	}
	/**
	 * @description: 批量上传
	 */
	const batchUpload = async (count : number, sourceType : number = 1) : Promise<any> => {
		const tempFilePaths = await uploadImage(count, sourceType)
		if (tempFilePaths) {
			showLoading('上传中')
			let successPaths = [], errCount = 0
			for (let i = 0, len = tempFilePaths.length; i < len; i++) {
				const compressFilePath = await compressImage(tempFilePaths[i])
				const filePath = await upload(compressFilePath)
				if (filePath) {
					successPaths.push(filePath)
				} else {
					errCount++
				}
			}
			if (count > 1) {
				showToast(`成功${successPaths.length},失败${errCount}`)
			}
			return successPaths
		}
	}
	/**
	 * @description: 预览图片
	 */
	const previewImage = (filePath : string[]) => {
		uni.previewImage({
			urls: filePath
		})
	}
	return {
		batchUpload,
		uploadImage,
		upload,
		compressImage,
		previewImage
	}
}

export const usePageParams = (props : Record<string, any>) => {
	const useParams = (0, computed)(() => {
		return (menuCode ?: string) => {
			const params : any = {
				orderId: props.options.orderId,
				// patrolMan: props.options.patrolMan,
				shopId: props.options.shopId
			}
			if (menuCode) {
				params.dcmCode = menuCode
			}
			return params
		}
	})
	return {
		useParams
	}
}