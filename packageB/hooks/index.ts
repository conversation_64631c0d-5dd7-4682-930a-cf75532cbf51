import { nextTick, ref, computed } from 'vue'
import fixedBtnLayoutVue from '../components/fixed-btn-layout/fixed-btn-layout.vue'

export const useContentLayoutHeight = (className ?: string) => {
	const fixedBtnLayoutHeight = ref(0)
	const fixedBtnLayoutRef = ref<InstanceType<typeof fixedBtnLayoutVue>>(null)
	const contentLayoutOffsetTop = ref(0)
	const getFixedBtnLayoutHeight = async () => {
		await nextTick()
		uni.createSelectorQuery().in(fixedBtnLayoutRef.value).select('.btn-block').boundingClientRect((res : UniApp.NodeInfo) => {			
			fixedBtnLayoutHeight.value = res.height
		}).exec()
	}
	const getContentLayoutHeight = async () => {
		await nextTick()
		uni.createSelectorQuery().select(className).boundingClientRect((res : UniApp.NodeInfo) => {
			contentLayoutOffsetTop.value = res.top
		}).exec()
	}
	const contentHeight = computed(() => {
		return `calc(100vh - ${fixedBtnLayoutHeight.value}px - ${contentLayoutOffsetTop.value}px)`
	})
	return {
		fixedBtnLayoutRef,
		contentHeight,
		fixedBtnLayoutHeight,
		getFixedBtnLayoutHeight,
		getContentLayoutHeight
	}
}