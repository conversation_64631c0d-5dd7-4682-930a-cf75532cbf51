import http, { httpUpload } from '@/utils/http'
import { DateParams, dataType, successCallbackResult } from '../../types'
import {
	ListShopParam,
	ShopChannelAnalysisParam,
	ShopMotivationAnalysisParam,
	ShopOpinionAnalysisParam,
	ShopScoreRateParam,
	shopRankParams,
} from '@/types/dataCockpit/index.ts'
import { CustomerStructureParams, GetSaleComparisonDataParams, GetSalesCardBarChartParams, GetSalesCardBusinessParams, ManagerDataParams, QueryCoreIndicatorsParams, QueryHomePageRoomStatusParams, QueryRoomStatusParams, QueryShopHealthIndexParams, ShopDataReportDetailParams, ShopLevelParams } from '../../types/work-bench'

// 获取消息列表
export const queryMessageList = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/queryMessageList',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 消息已读
export const readMessage = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/readMessage',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 消息详情
export const queryMessageDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/queryMessageDetail',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 运营资产概览
export const queryOperatingResourcesOverview = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/operatingResources/queryOperatingResourcesOverview',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店运营资产
export const queryShopOperatingResources = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/operatingResources/queryShopOperatingResources',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 店长运营资产
export const queryManagerOperatingResources = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/operatingResources/queryManagerOperatingResources',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 前台运营资产
export const queryReceptionOperatingResources = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/operatingResources/queryReceptionOperatingResources',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 获取员工区域
export const queryStaffRegion = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/regionalHierarchy/queryStaffRegion',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店排名
export const shopRank = (data : shopRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/shopRank',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店舆情分析指标
export const shopOpinionAnalysis = (data : ShopOpinionAnalysisParam) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/shopOpinionAnalysis',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店渠道分析
export const shopChannelAnalysis = (data : ShopChannelAnalysisParam) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/shopChannelAnalysis',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店评分占比
export const shopScoreRate = (data : ShopScoreRateParam) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/shopScoreRate',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 门店动因分析
export const shopMotivationAnalysis = (data : ShopMotivationAnalysisParam) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/shopMotivationAnalysis',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 获取门店列表
export const listShop = (data : ListShopParam) : Promise<successCallbackResult> => {
	return http({
		url: '/areaPermission/listShop',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 获取保洁人员列表
export const getCleanPeople = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: 'https://open-gateway-test.ethank.com.cn/smart/marketing/roomStatus/getCleanPeople',
		method: 'POST',
		data
	})
}

// 更新房态
export const roomStatusChange = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: 'https://open-gateway-test.ethank.com.cn/smart/marketing/roomStatus/roomStatusChange',
		method: 'POST',
		data
	})
}

// 门店经营数据
export const managerData = (data : ManagerDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopData/managerData',
		method: 'POST',
		data
	})
}

// 客源结构分析
export const customerStructure = (data : CustomerStructureParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopData/customerStructure',
		method: 'POST',
		data
	})
}

// 门店服务数据报告
export const shopDataReport = (data : DateParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopData/shopDataReport',
		method: 'POST',
		data
	})
}

// 门店服务数据报告详情
export const shopDataReportDetail = (data : ShopDataReportDetailParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopData/shopDataReportDetail',
		method: 'POST',
		data
	})
}

// 获取核心指标
export const queryCoreIndicators = (data : QueryCoreIndicatorsParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/queryCoreIndicators',
		method: 'POST',
		data
	})
}

// 获取门店健康指数
export const queryShopHealthIndex = (data : QueryShopHealthIndexParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/queryShopHealthIndex',
		method: 'POST',
		data
	})
}

// 获取首页房态
export const queryHomePageRoomStatus = (data : QueryHomePageRoomStatusParams) : Promise<successCallbackResult> => {
	return http({
		url: '/roomStatus/queryHomePageRoomStatus',
		method: 'POST',
		data
	})
}

// 销售比较数据
export const getSaleComparisonData = (data : GetSaleComparisonDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/getSaleComparisonData',
		method: 'POST',
		data
	})
}

// 销售比较数据图表
export const getSaleComparisonDataChart = (data : GetSaleComparisonDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/getSaleComparisonDataChart',
		method: 'POST',
		data
	})
}

// 销售订单间夜数据
export const getSaleOrderRoomNightData = (data : GetSaleComparisonDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/getSaleOrderRoomNightData',
		method: 'POST',
		data
	})
}

// 销售售卡经营报表
export const getSalesCardBusinessReport = (data : GetSalesCardBusinessParams) : Promise<successCallbackResult> => {
	return http({
		url: '/marketingData/getSalesCardBusinessReport',
		method: 'POST',
		data
	})
}

// 销售售卡条形图
export const getSalesCardBarChart = (data : GetSalesCardBusinessParams) : Promise<successCallbackResult> => {
	return http({
		url: '/marketingData/getSalesCardBarChart',
		method: 'POST',
		data
	})
}

// 销售售卡饼图
export const getSalesCardPieChart = (data : GetSalesCardBarChartParams) : Promise<successCallbackResult> => {
	return http({
		url: '/marketingData/getSalesCardPieChart',
		method: 'POST',
		data
	})
}

// 获取房态详情
export const queryRoomStatus = (data : QueryRoomStatusParams) : Promise<successCallbackResult> => {
	return http({
		url: '/roomStatus/queryRoomStatus',
		method: 'POST',
		data
	})
}

// 查询门店星级
export const shopLevel = (data : ShopLevelParams) : Promise<successCallbackResult> => {
	return http({
		url: '/shopMonitor/shopLevel',
		method: 'POST',
		data
	})
}

/**
 * @description: 获取pdf行程
 */
export const queryTripPdf = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/queryTripPdf',
		method: 'POST',
		data
	})
}
/**
 * @description: 保存线上签章,发送报告
 */
export const saveSign = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/saveSign',
		method: 'POST',
		data
	})
}

/**
 * @description: 写跟进
 */
export const recordFollow = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/recordFollow',
		method: 'POST',
		data
	})
}
/**
 * @description: 线上线下签章互转
 */
export const updateSign = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/updateSign',
		method: 'POST',
		data
	})
}

/**
 * @description: 线下签章已完成
 */
export const offlineSignDone = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/report/offlineSignDone',
		method: 'POST',
		data
	})
}

/**
 * @description: 查询巡店概览数据
 */
export const queryPatrolOverView = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/patrolData/queryPatrolOverView',
		method: 'POST',
		data
	})
}

/**
 * @description: 查询巡店分类数据
 */
export const queryPatrolClassification = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/patrolData/queryPatrolClassification',
		method: 'POST',
		data
	})
}

/**
 * @description: 巡店目的统计数据
 */
export const queryPatrolPurpose = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/patrolData/queryPatrolPurpose',
		method: 'POST',
		data
	})
}

/**
 * @description: 月度区域巡店数据
 */
export const queryMonthPatrolData = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/patrolData/queryMonthPatrolData',
		method: 'POST',
		data
	})
}

/**
 * @description: 调价申请列表
 */
export const applicationsList = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/application/applications',
		method: 'POST',
		data
	})
}

/**
 * @description: 调价申请详情
 */
export const applicationsInfo = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/application/info',
		method: 'POST',
		data
	})
}

/**
 * @description: 调价单关联房型调价记录
 */
export const applicationRelatedRecords = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/record/applicationRelatedRecords',
		method: 'POST',
		data
	})
}

/**
 * @description: 调价申请审核
 */
export const applicationAudit = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/application/audit',
		method: 'POST',
		data
	})
}

/**
 * @description: 文件上传
 */
export const fileUpload = (data : dataType = {}) : Promise<successCallbackResult> => {
	return httpUpload({
		url: '/file/fileUpload',
		method: 'POST',
		...data
	})
}

// 门店房型价量分析列表<用户调价依据>
export const calendarDayData = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '//changePrice/prepare/calendarDayData',
		method: 'POST',
		data
	})
}

/**
 * @description: 营销日历当日详情数据<调价前准备>
 */
export const calendarDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/prepare/calendarDetail',
		method: 'POST',
		data
	})
}

// 门店房型价量分析列表<用户调价依据>
export const valenceAnalysis = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/basis/valenceAnalysis',
		method: 'POST',
		data
	})
}

// 门店是否有未设置偏移值的价格码<调价前准备>
export const invalidOffsetRateCode = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/prepare/invalidOffsetRateCode',
		method: 'POST',
		data
	})
}

// 提交门店手动调价申请<调价申请>
export const manualSubmission = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/changePrice/application/manualSubmission',
		method: 'POST',
		data,
		need: true
	})
}

// 门店会员卡售卖进展
export const membershipCardSaleProgress = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/memberDevelop/membershipCardSaleProgress',
		method: 'POST',
		data,
		need: true
	})
}

// 员工售卖会员卡统计
export const staffSalesMembershipCardStatistics = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/memberDevelop/staffSalesMembershipCardStatistics',
		method: 'POST',
		data,
		need: true
	})
}

// 任务审核列表
export const taskReviewList = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/taskReview/list',
		method: 'POST',
		data
	})
}

// 任务审核详情
export const taskReviewDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/taskReview/detail',
		method: 'POST',
		data
	})
}

// 任务 - 审核通过
export const taskReviewPass = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/taskReview/pass',
		method: 'POST',
		data
	})
}

// 任务 - 审核驳回
export const taskReviewReject = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/taskReview/reject',
		method: 'POST',
		data
	})
}