<template>
	<view class="custom-textarea">
		<textarea :value="modelValue" :cursor="cursor" :cursor-spacing="20" :maxlength="maxlength"
			:placeholder="placeholder" :show-confirm-bar="false" :disabled="disabled" :placeholder-style="placeholderStyle" class="textarea"
			@input="handleInput" :key="new Date()" />
		<view class="count" v-if="count">
			{{num}}/{{maxlength}}
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed } from 'vue';
	const props = defineProps({
		maxlength: {
			type: [String, Number],
			default: 140
		},
		modelValue: {
			type: String,
			default: ''
		},
		placeholder: {
			type: String,
			default: '请输入说明内容...'
		},
		placeholderStyle: {
			type: String,
			default: 'font-size:24rpx;color:#999;'
		},
		background: {
			type: String,
			default: '#F5F5F5'
		},
		count: {
			type: <PERSON>olean,
			default: () => false
		},
    disabled: {
      type: Boolean,
      default: () => false
    }
	})

	const emit = defineEmits(['update:modelValue'])

	const num = computed(() => {
		return (props?.modelValue?.length > Number(props?.maxlength) ? props?.maxlength : props?.modelValue?.length) || 0
	})

	const cursor = computed(() => {
		return props?.modelValue?.length || -1
	})

	const handleInput = (e) => {
		let inputValue = e.target.value
		if (inputValue.length > props.maxlength) {
			inputValue = inputValue.slice(0, props.maxlength)
		}
		emit('update:modelValue', inputValue);
	}
</script>

<style scoped lang="scss">
	.custom-textarea {
		background: v-bind(background);
		border-radius: 20rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}

	.textarea {
		width: 100%;
	}

	.count {
		text-align: right;
		color: #999999;
		font-size: 24rpx;
		line-height: 24rpx;
	}
</style>