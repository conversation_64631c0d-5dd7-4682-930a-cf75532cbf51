<template>
	<up-modal :show="show" :title="title" :content="content">
		<template #default>
			<slot :show="show"></slot>
		</template>
		<template #confirmButton>
			<view class="fl">
				<text class="fr btn btn-primary" @tap="onCancel">{{cancelBtnText}}</text>
				<text class="fr btn" @tap="onConfirm">{{confirmBtnText}}</text>
			</view>
		</template>
	</up-modal>
</template>

<script setup lang="ts">
	import { ref } from 'vue'
	defineProps({
		title: {
			type: String,
			default: '温馨提示'
		},
		content: {
			type: String,
			default: ''
		},
		confirmBtnText: {
			type: String,
			default: '确认作废'
		},
		cancelBtnText: {
			type: String,
			default: '我再想想'
		}
	})
	const emits = defineEmits(['cancel', 'confirm'])
	const show = ref(false)
	const onCancel = () => {
		show.value = false
		emits('cancel')
	}
	const onConfirm = () => {
		emits('confirm')
	}
	const open = (status: boolean) => {
		show.value = status
	}
	defineExpose({
		open
	})
</script>

<style scoped lang="scss">
	
	.btn {
		justify-content: center;
		font-size: 30rpx;
		color: #999;
	}

	.btn-primary {
		height: 80rpx;
		margin-top: 60rpx;
		margin-bottom: 40rpx;
		color: #fff;
		font-size: 28rpx;
		background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
		border-radius: 200rpx;
	}
</style>