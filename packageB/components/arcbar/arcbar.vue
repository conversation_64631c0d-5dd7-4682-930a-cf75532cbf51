<template>
	<view style="display: flex;justify-content: center;">
		<view class="item-chart">
			<view class="chart-progress"
				:style="{ background: `conic-gradient(${startColor} 0, ${endColor} ${Math.min(100, value)}%, ${backgroundColor} ${Math.min(100, value)}%, ${backgroundColor})` }">
			</view>
			<view class="circle-start" />
			<view class="circle-end" :style="{transform: `rotate(${(360 * (Math.min(100, value)) / 100 - 180) - 8}deg)` }" />
			<view class="fl chart-info">
				<view class="fr">
					<text class="info-title">{{value}}</text> <text class="symbol">%</text>
				</view>
				<view class="fr info-num">
					已完成
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts" name="arcbar">
	import { computed } from 'vue'

	defineOptions({
		styleIsolation: 'shared'
	})

	const props = defineProps({
		value: {
			type: [String, Number],
			default: '0'
		},
		startColor: {
			type: String,
			default: '#FFB2A1'
		},
		endColor: {
			type: String,
			default: '#F75E3B'
		},
		fontColor: {
			type: String,
			default: '#FF4D4D'
		},
		backgroundColor: {
			type: String,
			default: '#FFF3F0'
		},
		width: {
			type: String,
			default: '160rpx'
		},
		height: {
			type: String,
			default: '160rpx'
		},
		centerWidth: {
			type: String,
			default: "64rpx"
		},
		fontSize: {
			type: String,
			default: '32rpx'
		},
		tagSize: {
			type: String,
			default: '22rpx'
		}
	})

	const dotWidth = computed(() => {
		return (parseFloat(props.width) - parseFloat(props.centerWidth) * 2) / 2 + 'rpx'
	})

	const radius = computed(() => {
		return parseFloat(props.width) / 2 + 'rpx'
	})
</script>

<style scoped lang="scss">
	.item-chart {
		position: relative;
		width: v-bind(width);
		height: v-bind(height);
		display: flex;
		justify-content: center;
		align-items: center;

		.chart-progress {
			position: absolute;
			margin: auto;
			width: v-bind(width);
			height: v-bind(height);
			border-radius: 50%;
			mask: radial-gradient(transparent, transparent v-bind(centerWidth), #000 v-bind(centerWidth), #000 0);
			transform: rotate(180deg);
		}

		.circle-start {
			position: absolute;
			bottom: 0;
			left: 50%;
			width: v-bind(dotWidth);
			height: v-bind(dotWidth);
			border-radius: 50%;
			background: v-bind(startColor);
			z-index: 1;
			transform: translate(-50%);
		}

		.circle-end {
			overflow: hidden;
			position: absolute;
			top: 0;
			left: 50%;
			z-index: 1;
			width: v-bind(dotWidth);
			height: v-bind(dotWidth);
			background: v-bind(endColor);
			transform-origin: 0 v-bind(radius);
			border-radius: 100%;
		}

		.chart-info {
			position: absolute;
			inset: 0;
			margin: auto;
			justify-content: center;

			&>.fr {
				justify-content: center;
			}

			.info-title {
				padding-bottom: 4rpx;
				font-size: v-bind(fontSize);
				line-height: v-bind(fontSize);
				text-align: center;
				color: v-bind(fontColor);
				font-weight: 700;
				font-family: 'DIN Bold';
			}

			.info-num {
				justify-content: center;
				font-size: v-bind(tagSize);
				line-height: v-bind(tagSize);
				color: #1F2428;
			}

			.symbol {
				margin-left: 4rpx;
				font-size: 20rpx;
				line-height: 20rpx;
				color: v-bind(fontColor);
			}
		}
	}
</style>