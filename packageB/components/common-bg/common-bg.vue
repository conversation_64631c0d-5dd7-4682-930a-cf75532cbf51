<template>
	<view class="bg"></view>
</template>

<script setup>
import { computed } from 'vue';
	const props = defineProps({
		height: {
			type: [String, Number],
			default: 374
		}
	})

	const height = computed(() => {
		if(typeof props.height == 'string'){
			if(props.height.includes('px')){
				return props.height
			}
		}
		return props.height + 'rpx'
	})
</script>

<style scoped lang="scss">
	.bg {
		position: absolute;
		width: 100%;
		height: v-bind(height);
		background: linear-gradient(52.4deg, #ff5733 0%, #c35943 31%, #c25742 79%, #f75735 100%);
		z-index: -100;

		&::after {
			content: '';
			position: absolute;
			right: 0;
			bottom: -100rpx;
			width: 100rpx;
			height: 100rpx;
			background-image: radial-gradient(circle at 0% 100%, transparent 100rpx, #C35943 0)
		}
	}
</style>