<template>
	<view class="c-picker" :class="className" @click="pickerShow = true">
		<view class="value">
			<slot></slot>
		</view>
		<up-icon name="arrow-down" color="#999999" size="22rpx" bold />
	</view>
	<up-picker :show="pickerShow" :columns="columns" :defaultIndex="[value]" confirmColor="#C35943"
		@confirm="handlePickerConfirm" v-bind="$attrs" @cancel="pickerShow = false" />
</template>

<script setup lang="ts">
	import { ref, watch } from 'vue';
	const props = defineProps({
		columns: {
			type: Array,
			default: () => []
		},
		value: {
			type: Number,
		},
		className: {
			type: String,
			default: ''
		}
	})

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})

	const columns = ref([])

	watch(
		() => props.columns,
		() => {
			columns.value = [props.columns]
		},
		{ immediate: true, deep: true }
	)

	const emit = defineEmits(['change'])

	const pickerShow = ref<Boolean>(false)
	const handlePickerConfirm = (value) => {
		emit('change', value)
		pickerShow.value = false
	}
</script>

<style lang="scss">
	.point-event {
		touch-action: none;
		pointer-events: none;
	}

	.c-picker {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 40rpx;
		background: #ffffffe6;
		border-radius: 200rpx;

		.value {
			color: #1f2428;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 24rpx;
		}
	}

	:deep() {
		.u-popup__content {
			.u-toolbar {
				.u-toolbar__wrapper__cancel {
					padding-right: 60rpx !important;
				}

				.u-toolbar__wrapper__confirm {
					padding-left: 60rpx !important;
				}

				.u-toolbar__cancel__wrapper,
				.u-toolbar__confirm__wrapper {
					height: 100% !important;

					text {
						line-height: 42px;
					}
				}
			}
		}
	}
</style>