<template>
	<view class="btn-block">
		<slot></slot>
	</view>
</template>

<script setup lang="ts">
	const btn_safe_bottom = uni.getSystemInfoSync().safeAreaInsets.bottom + 40 + 'rpx'
</script>

<style lang="scss" scoped>
	.btn-block {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		padding: 40rpx 10rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding-bottom: v-bind(btn_safe_bottom);
		z-index: 10000;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>