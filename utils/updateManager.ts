export const updateManager = () => {
	console.log('当前小程序版本', uni.getAccountInfoSync().miniProgram.version)
	if(uni.getAccountInfoSync().miniProgram.envVersion == 'release'){
		console.log = () => {}
	}
	if (uni.canIUse('getUpdateManager')) {
		let updateManager = uni.getUpdateManager(); //是否有新版本
		updateManager.onCheckForUpdate(function(res) { //无新版本
			console.log('onCheckForUpdate', res)
		});
			
		//新版本更新
		updateManager.onUpdateReady(function(res) {
			uni.showModal({
				title: '更新提示',
				content: '新版本已准备好，请重启应用',
				showCancel: false,
				success(res) {
					// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
					updateManager.applyUpdate();
				}
			});
		});
		
		updateManager.onUpdateFailed(function(res) { // 新的版本下载失败
			uni.showModal({
				title:"更新提示",
				content: '新版本下载失败，请手动删除小程序后重新进入',
				showCancel: false
			})
		});
	} else {
		uni.showModal({
			title: '更新提示',
			content:"当前微信版本较低，部分功能无法使用，请升级至最新版微信",
			showCancel: false,
			success() {
				// #ifdef MP-WEIXIN
				wx.exitMiniProgram()
				// #endif
			}
		})
	}
}