import { envMode } from './switchEnv'

export const localEnv = envMode
export const useLocalEnv = true  // 非HBuliderx调试

export const envObj: {
	[key: string]: any
} = {
	development: {
		baseURL_sh: "https://open-gateway-test.sunmeihotel.com/smart/operating/web",
		baseURL_qd: "https://athenatest.sunmeihotel.com/mini-app/mini",
		aiURL: "https://ai-server-test.sunmeihotel.com",
		webviewURL: "https://smart-operation-h5-test.sunmeihotel.com",
		baseURL_sh_ccs: "https://open-gateway-test.sunmeihotel.com/ccs/dataAnalysis/service"
	},
	pre: {
		baseURL_sh: "https://open-gateway-uat.ethank.com.cn/smart/operating/web",
		baseURL_qd: "https://athenapre.sunmeihotel.com/mini-app/mini",
		aiURL: "https://ai-server-uat.ethank.com.cn",
		webviewURL: "https://smart-operation-h5-uat.ethank.com.cn",
		baseURL_sh_ccs: "https://open-gateway-uat.ethank.com.cn/ccs/dataAnalysis/service"
	},
	production: {
		baseURL_sh: "https://open-gateway.ethank.com.cn/smart/operating/web",
		baseURL_qd: "https://athena.sunmeihotel.com/mini-app/mini",
		aiURL: "https://ai-server.ethank.com.cn",
		webviewURL: "https://smart-operation-h5.ethank.com.cn",
		baseURL_sh_ccs: "https://open-gateway.ethank.com.cn/ccs/dataAnalysis/service"
	}
}

export const protocl = envObj[localEnv]


