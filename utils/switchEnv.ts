import { showToast } from "./util"

export const env = uni.getAccountInfoSync().miniProgram.envVersion
console.log("🚀 ~ file: switchEnv.ts:4 ~ env:", env)

export const useSwitchEnv = true

export enum envMap {
	develop = 'development',
	trial = 'production',
	release = 'production'
}

export const switchEnv = (callback ?: Function) => {
	uni.showActionSheet({
		itemList: ['开发环境', '预发环境', '生产环境'],
		success: (res : UniApp.ShowActionSheetRes) => {
			const envArr = ['development', 'pre', 'production']
			uni.setStorageSync(env + '_currentEnv', envArr[res.tapIndex])
			callback?.()
			showToast('切换成功, 重新进入')
		}
	})
}

const getEnv = (useSwitchEnv: Boolean, env: string, envMap: any) => {
	// 先获取 envMap 中对应的值，避免重复获取
	const envMapValue = envMap[env];
	if (useSwitchEnv) {
		if (env === 'develop') {
			// 尝试从本地存储中获取当前环境，如果没有则使用 envMap 中的值
			const storedEnv = uni.getStorageSync(env + '_currentEnv');
			return storedEnv || envMapValue;
		}
		// 如果不是 develop 环境，返回 'production'
		return 'production';
	}
	// 如果不使用环境切换，直接返回 envMap 中的值
	return envMapValue;
}

export const envMode = getEnv(useSwitchEnv, env, envMap)