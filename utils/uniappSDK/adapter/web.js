// 读取小程序原版sdk
import QDTracker from '../packages/QDTracker-web';
const QDTWeb = {
    // 提供扩展性
    instance: QDTracker,
    // 平台
    uniPlatform: 'web',
    mpPltf: 'web',
    // 提供初始化和配置参数
    init(opt) {
        QDTWeb.adaptGlobalApi();
        // 预留空间uni参数支持预加工
        // 初始化
        return QDTracker.init(opt);
    },
    // 覆写小程序全局应用
    adaptGlobalApi(plugin) {
        // 确定全局变量
        return QDTracker.use(plugin);
    },
};


export default QDTWeb;
