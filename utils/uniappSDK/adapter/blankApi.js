const validMessage = '该平台缺少';
const QDTracker = {
    instance: null,
    // 通用方法
    init: () => {
        console.log(`QDT|uni|init ${validMessage} 初始化方法`);
    },
    setAccountInfo: () => {
        console.log(`QDT|uni|setAccountInfo ${validMessage} 设置自定义身份方法`);
    },
    setCommonData: () => {
        console.log(`QDT|uni|setCommonData ${validMessage} 设置自定义属性方法`);
    },
    track: () => {
        console.log(`QDT|uni|track ${validMessage} 自定义上报方法`);
    },
    setAes: () => {
        console.log(`QDT|uni|track ${validMessage} 自定义上报方法`);
    },

    // 小程序方法
    getIDs: () => {
        console.log(`QDT|uni|getIDs ${validMessage} 获取用户账号信息`);
    },
    addParamsToUrl: () => {
        console.log(`QDT|uni|addParamsToUrl ${validMessage} 给路径地址添加参数`);
    },
};

export default QDTracker;
