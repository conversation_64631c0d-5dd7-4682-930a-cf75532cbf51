// 读取小程序原版sdk
import QDTracker from '../packages/QDTracker-mp';
const QDTMini = {
    // 提供扩展性
    instance: QDTracker,
    // 平台
    uniPlatform: 'mp',
    // 小程序分类
    mpPltf: '',
    // 提供初始化和配置参数
    init(opt) {
        QDTMini.adaptGlobalApi();
        // 预留空间uni参数支持预加工
        // 初始化
        QDTracker.init({
            ...opt,
            mpPltf: QDTMini.mpPltf,
        });
    },
    // 覆写小程序全局应用
    adaptGlobalApi() {
        // 确定全局变量
        // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU
        // #ifdef MP-WEIXIN
        QDTMini.mpPltf = 'wx';
        // #endif

        // #ifdef MP-BAIDU
        QDTMini.mpPltf = 'smart';
        // #endif

        // #ifdef MP-ALIPAY
        QDTMini.mpPltf = 'alipay';
        // #endif

        // 通过额外绑定进行补充
        // console.log('onload');
        // #endif
    },
    // 覆写小程序页面应用
    adaptPageApi(pageOpt) {
        QDTracker.startPageMonitor(pageOpt, QDTracker);
    },
    // uni的页面page级别操作，专门用于页面级混入
    QDTMixin: {
        methods: {
            // 重写的方法，例如 onLoad
            customOnLoad(pageVm) {
                // 在这里添加您的自定义逻辑
                // 1. 页面包含自定义组件时，需要额外注册(自定义组件嵌套同理)
                const traverseChildren = children => {
                    (children || []).forEach(child => {
                        QDTMini.adaptPageApi(child.$mp.component || child.$scope);
                        if (child.$children && child.$children.length) {
                            traverseChildren(child.$children);
                        }
                    });
                };

                traverseChildren(pageVm.$children);

                // 2. 注册主页面
                if (!pageVm.$mp.page && !pageVm.$scope) return;
                QDTMini.adaptPageApi(pageVm.$mp.page || pageVm.$scope);
            },
        },
        // 使用 Vue 生命周期钩子来调用重写的方法
        onLoad() {
            // 此处同步需要更新页面生命周期的注册
            this.customOnLoad(this);
        },
    },
};

export default QDTMini;
