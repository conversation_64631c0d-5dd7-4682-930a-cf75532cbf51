// 读取unimpsdk，由于不同版本unimp对App&Page覆写场景不同。
// 此处页面级别监控暂不做统一管理
import QDTracker from '../packages/QDTracker-app';
const QDTMini = {
    // 提供扩展性
    instance: QDTracker,
    // 平台
    uniPlatform: 'mp',
    // 小程序分类
    mpPltf: '',
    // 提供初始化和配置参数
    init(opt) {
        QDTMini.adaptGlobalApi();
        // 预留空间uni参数支持预加工
        // 初始化
        QDTracker.init({
            ...opt,
            mpPltf: QDTMini.mpPltf,
        });
    },
    // 覆写小程序全局应用
    adaptGlobalApi() {
        // #ifdef APP
        QDTMini.mpPltf = 'uni';
        // #endif
    },
};

export default QDTMini;
