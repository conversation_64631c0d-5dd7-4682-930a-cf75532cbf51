function t(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,a)}return n}function e(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){o(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function n(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,e||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function o(t,e,a){return(e=n(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function r(t,e){if(null==t)return{};var n,a,o=function(t,e){if(null==t)return{};var n,a,o={},r=Object.keys(t);for(a=0;a<r.length;a++)n=r[a],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(a=0;a<r.length;a++)n=r[a],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var i={env:"uni",getCurrentPages:getCurrentPages,sdk_version:"1.2.0"};i.methods=uni,i.sdk_type="uniminiprogram",i.lifecycle={app:["onLaunch","onShow","onHide"],page:["onShow","onHide","onShareAppMessage"]};var s={config:{trackType:"track",path:{get:"/events/sdk/trace",post:"/events/sdk/trace"},apiMethod:"post",encryptMode:"default",apiHost:"https://v.qidian.qq.com",appkey:"",kfuin:"",appid:"",useId:!1,application:"",enable_compression:!1,track_interval:5e3,batch_max_time:8,url:"",session_interval:18e5,autoTrack:{appLaunch:!0,appShow:!0,appHide:!0,pageShow:!0,pageHide:!0}},accounts:{},idData:{},systemData:{},userDefineDataProperties:{},commonDataProperties:{},commonDataGlobel:{},pageData:{},setUserDefineData:function(t){this.userDefineDataProperties=e(e({},this.userDefineDataProperties),t)},setCommonData:function(t,n){this.commonDataProperties=e(e({},this.commonDataProperties),t),this.commonDataGlobel=e(e({},this.commonDataGlobel),n)},setQdtData:function(t,e){Object.keys(t||{}).forEach((function(n){"title"!==n&&t[n]&&(e[n]=t[n])}))},setPageData:function(t){this.pageData=t},setSystemData:function(t){this.systemData=e(e({},this.systemData),t)},getSystemData:function(t){return this.systemData[t]},setConfig:function(t){this.config=e(e({},this.config),t)},getConfig:function(t){return this.config[t]},setIdData:function(t){this.idData=e(e({},this.idData),t)},getIdData:function(t){return this.idData[t]},getIDs:function(){return this.idData},setAccountInfo:function(t){this.accounts=e(e({},this.accounts),t)},getParams:function(t){var n=this.getIdData("anonymous_id"),a=e({business_id:this.getIdData("bussid"),anonymous_id:n},this.accounts);return a.wx_applet_openid=this.getIdData("openid"),a.wx_unionid=this.getIdData("unionid"),e({type:this.getConfig("trackType"),time:Math.floor(+new Date),properties:e(e(e(e(e({application:this.getConfig("application"),appid:this.getConfig("appid")},this.systemData),this.pageData),this.commonDataProperties),this.userDefineDataProperties),t),account:a},this.commonDataGlobel)}},c=8;function u(t){return function(t){return function(t){for(var e="0123456789ABCDEF",n="",a=0;a<4*t.length;a++)n+=e.charAt(t[a>>2]>>a%4*8+4&15)+e.charAt(t[a>>2]>>a%4*8&15);return n}(function(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;for(var n=**********,a=-*********,o=-**********,r=*********,i=0;i<t.length;i+=16){var s=n,c=a,u=o,h=r;n=p(n,a,o,r,t[i+0],7,-*********),r=p(r,n,a,o,t[i+1],12,-*********),o=p(o,r,n,a,t[i+2],17,*********),a=p(a,o,r,n,t[i+3],22,-**********),n=p(n,a,o,r,t[i+4],7,-*********),r=p(r,n,a,o,t[i+5],12,**********),o=p(o,r,n,a,t[i+6],17,-**********),a=p(a,o,r,n,t[i+7],22,-********),n=p(n,a,o,r,t[i+8],7,**********),r=p(r,n,a,o,t[i+9],12,-**********),o=p(o,r,n,a,t[i+10],17,-42063),a=p(a,o,r,n,t[i+11],22,-**********),n=p(n,a,o,r,t[i+12],7,**********),r=p(r,n,a,o,t[i+13],12,-********),o=p(o,r,n,a,t[i+14],17,-**********),n=f(n,a=p(a,o,r,n,t[i+15],22,**********),o,r,t[i+1],5,-*********),r=f(r,n,a,o,t[i+6],9,-**********),o=f(o,r,n,a,t[i+11],14,643717713),a=f(a,o,r,n,t[i+0],20,-373897302),n=f(n,a,o,r,t[i+5],5,-701558691),r=f(r,n,a,o,t[i+10],9,38016083),o=f(o,r,n,a,t[i+15],14,-660478335),a=f(a,o,r,n,t[i+4],20,-405537848),n=f(n,a,o,r,t[i+9],5,568446438),r=f(r,n,a,o,t[i+14],9,-1019803690),o=f(o,r,n,a,t[i+3],14,-187363961),a=f(a,o,r,n,t[i+8],20,1163531501),n=f(n,a,o,r,t[i+13],5,-1444681467),r=f(r,n,a,o,t[i+2],9,-51403784),o=f(o,r,n,a,t[i+7],14,1735328473),n=d(n,a=f(a,o,r,n,t[i+12],20,-1926607734),o,r,t[i+5],4,-378558),r=d(r,n,a,o,t[i+8],11,-2022574463),o=d(o,r,n,a,t[i+11],16,1839030562),a=d(a,o,r,n,t[i+14],23,-35309556),n=d(n,a,o,r,t[i+1],4,-1530992060),r=d(r,n,a,o,t[i+4],11,1272893353),o=d(o,r,n,a,t[i+7],16,-155497632),a=d(a,o,r,n,t[i+10],23,-1094730640),n=d(n,a,o,r,t[i+13],4,681279174),r=d(r,n,a,o,t[i+0],11,-358537222),o=d(o,r,n,a,t[i+3],16,-722521979),a=d(a,o,r,n,t[i+6],23,76029189),n=d(n,a,o,r,t[i+9],4,-640364487),r=d(r,n,a,o,t[i+12],11,-421815835),o=d(o,r,n,a,t[i+15],16,530742520),n=g(n,a=d(a,o,r,n,t[i+2],23,-995338651),o,r,t[i+0],6,-198630844),r=g(r,n,a,o,t[i+7],10,1126891415),o=g(o,r,n,a,t[i+14],15,-1416354905),a=g(a,o,r,n,t[i+5],21,-57434055),n=g(n,a,o,r,t[i+12],6,1700485571),r=g(r,n,a,o,t[i+3],10,-1894986606),o=g(o,r,n,a,t[i+10],15,-1051523),a=g(a,o,r,n,t[i+1],21,-2054922799),n=g(n,a,o,r,t[i+8],6,1873313359),r=g(r,n,a,o,t[i+15],10,-30611744),o=g(o,r,n,a,t[i+6],15,-1560198380),a=g(a,o,r,n,t[i+13],21,1309151649),n=g(n,a,o,r,t[i+4],6,-145523070),r=g(r,n,a,o,t[i+11],10,-1120210379),o=g(o,r,n,a,t[i+2],15,718787259),a=g(a,o,r,n,t[i+9],21,-343485551),n=l(n,s),a=l(a,c),o=l(o,u),r=l(r,h)}return Array(n,a,o,r)}(function(t){for(var e=[],n=(1<<c)-1,a=0;a<t.length*c;a+=c)e[a>>5]|=(t.charCodeAt(a/c)&n)<<a%32;return e}(t),t.length*c))}(t)}function h(t,e,n,a,o,r){return l((i=l(l(e,t),l(a,r)))<<(s=o)|i>>>32-s,n);var i,s}function p(t,e,n,a,o,r,i){return h(e&n|~e&a,t,e,o,r,i)}function f(t,e,n,a,o,r,i){return h(e&a|n&~a,t,e,o,r,i)}function d(t,e,n,a,o,r,i){return h(e^n^a,t,e,o,r,i)}function g(t,e,n,a,o,r,i){return h(n^(e|~a),t,e,o,r,i)}function l(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}var m={track:function(t,n,a){var o=s.getParams(n);this._normalizeSendData(o),this._send(e({event:t},o))},_normalizeSendData:function(t){!function t(e){Object.keys(e).forEach((function(n){var o=e[n];"function"==typeof o&&(e[n]=e[n]()),o&&"object"===a(o)&&t(o)}))}(t||{})},_send:function(t,n){var a=s.getConfig("apiMethod"),o=s.getConfig("path")[a],r=s.getConfig("apiHost")+o;if(t&&t.event)if("get"===a)i.methods.request({url:r,method:"GET",headers:{"content-type":"application/x-www-form-urlencoded"},data:e({appkey:s.getConfig("appkey")},t),dataType:"base64",success:function(t){n&&n(t.header,t.data)},fail:function(t){}});else{var c=this.assamblePostParmas(t),h=c.data.length,p=c.ext,f=u("data=".concat(h,"&ext=").concat(p));h&&uni.request({url:r,method:"POST",data:e(e({appkey:s.getConfig("appkey")},c),{},{sign:f}),header:{"content-type":"application/json"},dataType:"base64",success:function(t){n&&n(t.header,t.data)},fail:function(t){}})}},assamblePostParmas:function(t){return{data:JSON.stringify([t]||{}),ext:3}}};var y={data:{},init:function(){try{var t;(t="alipay"===i.env?i.methods.getStorageSync({key:"__QDTracker__"}).data:i.methods.getStorageSync("__QDTracker__"))?function(t){try{JSON.parse(t)}catch(t){return!1}return!0}(t)&&(this.data=JSON.parse(t)):"alipay"===i.env?i.methods.setStorageSync({key:"__QDTracker__",data:JSON.stringify({})}):i.methods.setStorageSync("__QDTracker__",JSON.stringify({}))}catch(t){console.log("[QDTRACKER_ERROR]","init localStorage",t)}},setValue:function(t,e,n){this.data[t]=e,n&&this.updateLocalStorage()},getValue:function(t){return this.data[t]||""},getCache:function(){return this.data},updateLocalStorage:function(){try{return"alipay"===i.env?i.methods.setStorageSync({key:"__QDTracker__",data:JSON.stringify(this.data)}):i.methods.setStorageSync("__QDTracker__",JSON.stringify(this.data)),!0}catch(t){return console.log("[QDTRACKER_ERROR]","update localStorage",t),!1}}};function v(t){this.url=t,this.protocol="",this.host="",this.pathname="",this.search="",this.hash="",this.parseURL=function(){var t=this.url.match(/^(.*?):\/\/([^/?#]+)(\/[^?#]*)?(\?[^#]*)?(#.*)?$/);t&&(this.protocol=t[1],this.host=t[2],this.pathname=t[3]||"",this.search=t[4]||"",this.searchParams=new D(t[4]),this.hash=t[5]||"")},this.toString=function(){return this.search=this.searchParams.toString(),this.protocol+"://"+this.host+this.pathname+this.search+this.hash},this.parseURL()}function D(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.search=t,this.toString=function(){return this.search},this.append=function(t,e){var n="".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e||""));this.search.indexOf("?")>=0?this.search+="&".concat(n):this.search+="?".concat(n)}}function S(t){return String(t).replace(new RegExp("([.*+?^=!:${}()|[\\]/\\\\-])","g"),"\\$1")}var _={},b=0,P={onShowPage:function(t){b=+new Date,_={};var e=i.getCurrentPages();_.url=e&&e[e.length-1]&&e[e.length-1].route,_.title=this.data&&this.data.qdt&&this.data.qdt.title||"",s.setQdtData(this.data.qdt,_);var n,a,o,r,c="".concat(s.getSystemData("sw"),"*").concat(s.getSystemData("sh")).concat(s.getConfig("appid"));_.page_id=(n=c,r="".concat((a=function(t){return t.toString(36)})((o=function(){return Math.floor(2147483648*Math.random())})()^2147483647&function(t){var e=1;if(t){var n=0;e=0;for(var a=t.length-1;a>=0;a--)e=(e<<6&268435455)+(n=t.charCodeAt(a))+(n<<14),e=0!=(n=266338304&e)?e^n>>21:e}return e}(n)),"."),"".concat(a(+new Date),".").concat(r).concat(o().toString(36))),s.setPageData(_),s.getConfig("autoTrack").pageShow&&t.track("$MPPageShow",{});var u=e[e.length-1],h=u.options||u.querySet,p="";for(var f in h)p+=p?"&":"?",p+="".concat(f,"=").concat(h[f]);var d=h.channel;s.setCommonData({channel:d,path_parameter:p},{})},onHidePage:function(t){var e=+new Date-b;e>Math.pow(10,12)&&(e=0),s.getConfig("autoTrack").pageHide&&t.track("$MPPageHide",{dr:e})},onShareAppMessagePage:function(t,e){var n,a,o=i.getCurrentPages(),r=o[o.length-1].route,c=s.idData,u=c.anonymous_id,h=c.openid,p=parseInt(function(t,e){for(var n,a=new RegExp("(?:&|\\?)?".concat(S(encodeURIComponent(e)),"=([^&]*)(?:&|$)"),"g"),o="";n=a.exec(t);)if(n[1]){o=decodeURIComponent(n[1]);break}return o}(r,"qda_sharelevel")||1);console.log("[QDTRACKER] currentPagePath",r),s.getConfig("autoTrack").pageShare&&t.track("$MPShare",{share_oid:h,share_qid:u,share_url_path:r,share_level:p}),n=r,a=new RegExp("(&|\\?)?".concat(S(encodeURIComponent("qda_sharelevel")),"=([^&]*)(?:&|$)"),"g"),r=function(t,e,n){return t.indexOf("?")>-1?t+="&":t+="?",t+"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n.toString()))}(r=function(t){var e=t.charAt(t.length-1);return"&"!==e&&"?"!==e||(t=t.slice(0,-1)),t}(n.replace(a,"$1")),"qda_sharelevel",isNaN(p)?1:p+1),e.path=r}},k=["openid","aliUserId","unionid","bussid"];y.init();var O={initFinished:!1,idGeted:!1,methodsQueue:[],init:function(t){t.mpPltf&&O.setMpPltf(t.mpPltf),s.setConfig(t),t.useId||(this.idGeted=!0);var e=y.getValue("anonymous_id");e||(e=((Math.floor(10*Math.random())||1)+function(t,e){var n,a,o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(e=e||o.length,t)for(n=0;n<t;n++)r[n]=o[0|Math.random()*e];else for(r[8]=r[13]=r[18]=r[23]="-",r[14]="4",n=0;n<36;n++)r[n]||(a=0|16*Math.random(),r[n]=o[19==n?3&a|8:a]);return r.join("")}(3,10)+(new Date).getTime().toString().slice(2,13)).toString(),y.setValue("anonymous_id",e,!0)),s.setIdData({anonymous_id:e}),i.methods.getNetworkType({success:function(t){s.setSystemData({network_type:t.networkType,carrier:t.operatorName})},complete:this.getSystemInfo.bind(this)})},cacheMethods:function(t){return(!this.initFinished||!this.idGeted)&&(this.methodsQueue.push(t),!0)},getSystemInfo:function(){var t=this;i.methods.getSystemInfo({success:function(e){var n=e.system.indexOf(" ")>-1?e.system.split(" ")[1]:e.system,a=e.system.indexOf(" ")>-1?e.system.split(" ")[0]:e.system;console.log("[QDTracker LOG]: getSystemInfo",e),e.romName&&(a=e.romName),e.romVersion&&(n=e.romVersion);var o={sdk_type:i.sdk_type,sdk_version:i.sdk_version,sw:e.screenWidth,sh:e.screenHeight,os_version:n,manufacturer:e.brand,model:e.model,os:a,screen_resolution:"".concat(e.screenWidth,"_").concat(e.screenHeight),SDKVersion:e.SDKVersion,platform:e.platform,version:e.version,system:e.system,language:e.language,application_type:"uniApp",application:s.config.application,device_type:"移动端"};s.setSystemData(o),t.initFinished=!0,t.idGeted&&t.methodsQueue.length&&(t.methodsQueue.forEach((function(e){var n=e[0],a=e[1];t[n].apply(t,a)})),t.methodsQueue=[])}})},track:function(t,e,n){m.track(t,e,n)},setAccountInfo:function(t){try{var e=t||{},n=e.openid,a=e.aliUserId,o=e.unionid,r=e.bussid,i=e.wx_applet_openid,c=e.wx_unionid;s.setIdData({openid:i||n,aliUserId:a,unionid:c||o,bussid:r}),t&&s.setAccountInfo(t)}catch(t){console.log("[QDTRACKER_ERROR]","setAccountInfo",t)}},getIDs:function(){return s.getIDs()},setCommonData:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this,a=e.openid,o=e.aliUserId,c=e.unionid,u=e.bussid,h=r(e,k);a&&s.setIdData({openid:a}),o&&s.setIdData({aliUserId:o}),c&&s.setIdData({unionid:c}),u&&s.setIdData({bussid:u}),("alipay"!==i.env&&a||"alipay"===i.env&&o)&&(this.idGeted=!0,this.initFinished&&this.methodsQueue.length&&(this.methodsQueue.forEach((function(t){var e=t[0],a=t[1];n[e].apply(n,a)})),this.methodsQueue=[])),s.setCommonData(t,h),s.setUserDefineData(t)},use:function(t,e,n){if(!this.cacheMethods(["use",arguments])){var a=t.apply(this,e);n&&n(a)}},getCommonParams:function(){return[s.getParams()]},addParamsToUrl:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t;try{var a={};["anonymous_id"].forEach((function(t){a[t]=s.getConfig(t)||y.getValue(t)})),n=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=new v(t);if(Object.keys(e).forEach((function(t){n||a.searchParams.append(t,e[t])})),n){var o="";Object.keys(e).forEach((function(t){o+="&".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e[t]))}));var r=a.hash||"#";r.indexOf("?")>=0?a.hash=r+o:a.hash=r+"?".concat(o.slice(1))}return a.toString()}(t,{_qdasdk:JSON.stringify(a)},e)}catch(t){console.warn("addParamsToUrl error=",t)}return n},getConfig:function(t){return t?s.getConfig(t):s.config},startAppMonitor:function(){s.getConfig("autoTrack").appStart&&O.track("$AppStart",{})},endAppMonitor:function(){s.getConfig("autoTrack").appEnd&&O.track("$AppEnd",{})},startPageMonitor:function(t){i.lifecycle.page.forEach((function(e){!function(t,e,n){var a=e[n];e[n]=function(e){try{P["".concat(n,"Page")]&&P["".concat(n,"Page")].call(this,t,e)}catch(t){console.log("[QDTRACKER_ERROR]","pageProxy",t)}return a&&a.call(this,e)}}(O,t,e)}))},setMpPltf:function(t){console.log("[QDTRACKER_LOG]","setMpPltf",t),i.env=t,i.methods=uni,i.sdk_type="wxminiprogram",i.lifecycle={app:["onLaunch","onShow","onHide"],page:["onShow","onHide","onShareAppMessage"]}}};export{O as default};
