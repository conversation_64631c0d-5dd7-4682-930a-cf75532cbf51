var t=window;window.parent!==window&&window.inDapIF&&(t=window.parent);var e="",n="",r="",i="",a="",o="__qq_qidian_da_market",s=t[o]||"qidianDA";if(t[o]=s,s){var c=t[s]=t[s]||function(){};if(c[o]=c[o]||[],!c.loaded){c.loaded=!0,window.VERSION="1.0.0",window.PLATFORM="web";var d=t.location;e=d.protocol,n="https://tmac.qidian.qq.com",r="https://tmac.qidian.qq.com",i="//tmac.qidian.qq.com",a="//tmac.qidian.qq.com"}}var u="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach((function(e){w(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function f(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function _(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function v(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,f(r.key),r)}}function g(t,e,n){return e&&v(t.prototype,e),n&&v(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function w(t,e,n){return(e=f(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},m.apply(this,arguments)}function y(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},a=Object.keys(t);for(r=0;r<a.length;r++)n=a[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(r=0;r<a.length;r++)n=a[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function b(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))}function k(t){return new this((function(e,n){if(!t||void 0===t.length)return n(new TypeError(p(t)+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var i=r.length;function a(t,n){if(n&&("object"===p(n)||"function"==typeof n)){var o=n.then;if("function"==typeof o)return void o.call(n,(function(e){a(t,e)}),(function(n){r[t]={status:"rejected",reason:n},0==--i&&e(r)}))}r[t]={status:"fulfilled",value:n},0==--i&&e(r)}for(var o=0;o<r.length;o++)a(o,r[o])}))}function x(t,e){this.name="AggregateError",this.errors=t,this.message=e||""}function S(t){var e=this;return new e((function(n,r){if(!t||void 0===t.length)return r(new TypeError("Promise.any accepts an array"));var i=Array.prototype.slice.call(t);if(0===i.length)return r();for(var a=[],o=0;o<i.length;o++)try{e.resolve(i[o]).then(n).catch((function(t){a.push(t),a.length===i.length&&r(new x(a,"All promises were rejected"))}))}catch(t){r(t)}}))}x.prototype=Error.prototype;var E=setTimeout;function A(t){return Boolean(t&&void 0!==t.length)}function R(){}function I(t){if(!(this instanceof I))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],B(t,this)}function O(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,I._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(t){return void C(e.promise,t)}q(e.promise,r)}else(1===t._state?q:C)(e.promise,t._value)}))):t._deferreds.push(e)}function q(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"===p(e)||"function"==typeof e)){var n=e.then;if(e instanceof I)return t._state=3,t._value=e,void D(t);if("function"==typeof n)return void B((r=n,i=e,function(){r.apply(i,arguments)}),t)}t._state=1,t._value=e,D(t)}catch(e){C(t,e)}var r,i}function C(t,e){t._state=2,t._value=e,D(t)}function D(t){2===t._state&&0===t._deferreds.length&&I._immediateFn((function(){t._handled||I._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)O(t,t._deferreds[e]);t._deferreds=null}function T(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function B(t,e){var n=!1;try{t((function(t){n||(n=!0,q(e,t))}),(function(t){n||(n=!0,C(e,t))}))}catch(t){if(n)return;n=!0,C(e,t)}}I.prototype.catch=function(t){return this.then(null,t)},I.prototype.then=function(t,e){var n=new this.constructor(R);return O(this,new T(t,e,n)),n},I.prototype.finally=b,I.all=function(t){return new I((function(e,n){if(!A(t))return n(new TypeError("Promise.all accepts an array"));var r=Array.prototype.slice.call(t);if(0===r.length)return e([]);var i=r.length;function a(t,o){try{if(o&&("object"===p(o)||"function"==typeof o)){var s=o.then;if("function"==typeof s)return void s.call(o,(function(e){a(t,e)}),n)}r[t]=o,0==--i&&e(r)}catch(t){n(t)}}for(var o=0;o<r.length;o++)a(o,r[o])}))},I.any=S,I.allSettled=k,I.resolve=function(t){return t&&"object"===p(t)&&t.constructor===I?t:new I((function(e){e(t)}))},I.reject=function(t){return new I((function(e,n){n(t)}))},I.race=function(t){return new I((function(e,n){if(!A(t))return n(new TypeError("Promise.race accepts an array"));for(var r=0,i=t.length;r<i;r++)I.resolve(t[r]).then(e,n)}))},I._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){E(t,0)},I._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};var z=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==u)return u;throw new Error("unable to locate global object")}();"function"!=typeof z.Promise?z.Promise=I:(z.Promise.prototype.finally||(z.Promise.prototype.finally=b),z.Promise.allSettled||(z.Promise.allSettled=k),z.Promise.any||(z.Promise.any=S));var P=void 0,M=function(t){t.use=function(e,n,r){var i=e.apply(t,n);r&&r(i)};var e={};t.plugin=function(t,n){try{var r=P;if((e=e||{})[t])return;e[t]={opts:n,ran:[]},r._runPlugin(t)}catch(t){console.log("[QDTRACKER_ERROR]","plugin",t)}},t._runPlugin=function(t){var n=P,r=e[t];n.eachTracker((function(e){var i=n.getTracker(e);i&&!function(t,e){var n=parseInt(t.length,10)||0;if(0===n)return!1;for(var r=0;r<n;){if(e===t[r])return!0;r++}return!1}(r.ran,e)&&(i.task(t,r.opts),r.ran.push(e))}))},t._runPlugins=function(){if(e)for(var t in e)e.hasOwnProperty(t)&&P._runPlugin(t)},t._runInnerPlugins=function(){}},H=["bussid","qq","da_rpt_type"],j=["appkey","appid","openid","anonymous_id","visitorId","kfuin","preventAutoTrack"],N=function(t){t.setDomain=function(e){try{t.setParams("privateDomain",e),console.log("[QDTRACKER] setDomain",e),Object.keys(t.trackers).forEach((function(n){var r=t.trackers[n];console.log("[QDTRACKER] setDomain _tracker",n,e,r),r.set("ts",e)}))}catch(t){console.log("[QDTRACKER_ERROR]","setDomain",t)}},t.setAes=function(e){try{t.setParams("AES_FUNC",e)}catch(t){console.log("[QDTRACKER_ERROR]","setAes",t)}},t.setCommonData=function(e,n){try{e=e||{},t.setParams("commonData",l(l({},t.getParams("commonData")),e));var r=n||{},i=r.bussid,a=r.qq,o=r.da_rpt_type,s=y(r,H);t.setParams("outerData",l(l({},t.getParams("outerData")),s)),i&&t.setParams("bussid",i),a&&t.setParams("qq",a),o&&t.setParams("da_rpt_type",o)}catch(t){console.log("[QDTRACKER_ERROR]","setCommonData",t)}},t.setAccountInfo=function(e){try{e&&(t.qda_accountInfo=e,t.setParams("qda_accountInfo",e))}catch(t){console.log("[QDTRACKER_ERROR]","setAccountInfo",t)}},t.setPreFix=function(e){try{e&&(t._storage_prefix=String(e))}catch(t){console.log("[QDTRACKER_ERROR]","setPreFix",t)}},t.setReferrer=function(e){try{t.setParams("referrer",e)}catch(t){console.log("[QDTRACKER_ERROR]","setReferrer",t)}},t.init=function(e){try{console.log("[QDTRACKER] initParams",e);var n=e.tid,r=e.appkey,i=e.options;return t.debugMode=!!i.debug,M(t),t.tracker.create(n||"",r,i)}catch(t){console.log("[QDTRACKER_ERROR]","setDomain",t)}},t.reset=function(e){try{return t.tracker.reset(e)}catch(t){console.log("[QDTRACKER_ERROR]","setDomain",t)}}},U=function(t){t.getBasicInfo=function(e){try{var n=[];return Object.keys(t.trackers).forEach((function(e){var r=t.trackers[e].vals,i=r.appkey,a=r.kfuin,o=r.preventAutoTrack;n.push({appkey:i,kfuin:a,preventAutoTrack:o})})),e&&e(n),n}catch(t){console.log("[QDTRACKER_ERROR]","getBasicInfo",t)}},t.getCommonParams=function(){var e=[];try{Object.keys(t.trackers).forEach((function(n){var r=t.trackers[n].vals,i=r.appkey,a=r.appid,o=r.openid,s=r.anonymous_id,c=r.visitorId,d=r.kfuin,u=r.preventAutoTrack,h=y(r,j);e.push(l(l({kfuin:d,preventAutoTrack:u,visitorId:c},h),{},{account:{appkey:i,appid:a,wx_openid:o,anonymous_id:s},properties:l(l({},t.properties),t.properties.commonData)}))}))}catch(t){console.log("[QDTRACKER_ERROR]","getCommonParams",t)}return e}},F=function(t){this._data=t||[]};F.prototype.on=function(t){this._data[t]=!0},F.prototype.off=function(t){this._data[t]=!1},F.prototype.merge=function(t){for(var e=this._data.slice(),n=t._data,r=0;r<n.length;r++)e[r]=e[r]||n[r];return new F(e)},F.prototype.encode=function(){for(var t=[],e=0;e<this._data.length;e++)this._data[e]&&(t[Math.floor(e/6)]^=1<<e%6);for(e=0;e<t.length;e++)t[e]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(t[e]||0);return"".concat(t.join(""),"~")};var L=function(t){return function(){var e=window.console;void 0!==e&&"function"==typeof e[t]&&e[t].apply(e,arguments)}},Q=L("log");Q.group=L("group"),Q.groupEnd=L("groupEnd");var Z=function(){this._gFlags=new F,this._allFlags={}};Z.prototype.flag=function(t,e){var n;if(e){var r=this._allFlags;r[e]=r[e]||new F,n=r[e]}else n=this._gFlags;n.on(t)},Z.prototype.encode=function(t){var e=this._allFlags[t],n=this._gFlags;return(e?n.merge(e):n).encode()};var K=new Z,W=function(t){var e=t.platformInstance;e&&e.initTracker(t)},V=function(){this._s={},this._avg={},this._max={}};V.prototype.start=function(t,e){this._s[t]=e?+e:+new Date},V.prototype.end=function(t,e){var n=this._s[t];this._s[t]=null;var r=(e?+e:+new Date)-n;this._max[t]=Math.max(r,this._max[t]||0);var i=this._avg[t];i?(i.s=(i.s*i.n+r)/(i.n+1),++i.n):i=this._avg[t]={s:r,n:1}},V.prototype.encode=function(){var t,e,n="",r=this._avg;for(var i in r)r.hasOwnProperty(i)&&(t=Math.round(r[i].s||0),e=Math.round(this._max[i]||0),n+="".concat(i,"(").concat(t,"_").concat(e,")"));return n};var $=new V,X=new Date,G=function(){function t(e){_(this,t);var n=this.getTrackerTemplate(e);this.properties={},m(this,n)}return g(t,[{key:"getTrackerTemplate",value:function(e){return t[e?e.PLATFORM:"miniprogram"]}},{key:"setParams",value:function(t,e){t&&(this.properties[t]=e)}},{key:"getParams",value:function(t){if(t)return this.properties[t]}},{key:"delParams",value:function(t){t&&delete this.properties[t]}}],[{key:"getInstance",value:function(e){return this.instance||(this.instance=new t(e)),this.instance}}]),t}();w(G,"instance",null),w(G,"web",function(t,e){return{_storage_prefix:"",win:t,version:e,trackers:{},loadedTrackers:{},createdFunc:null,properties:{commonData:null,bussid:null,qda_accountInfo:null,qq:null,outerData:null,wechatInfo:{},referrer:"",privateDomain:"",AES_FUNC:null}}}());var J=G.getInstance(window);$.start("ready",X),t=window,function(t){t.setParams("commonData",{})}(J),function(t){t.provided=["setDomain","setAes","setCommonData","setAccountInfo","setPreFix","setReferrer","getCommonParams","getBasicInfo"],N(t),U(t)}(J),function(t){t.tracker={},t.trackers=[],t.tracker.ready=function(e){K.flag(5),e.call(t)},t.tracker.created=function(e){0===Object.keys(t.trackers).length?t.createdFunc=e:e.call(t)},t.tracker.track=function(e,n,r){t.tracker.eachTracker((function(i){var a=t.trackers[i]||{};a?a.track(e,n,(function(t){window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","Tracker track callback",t)}),r):console.log("[QDTRACKER_ERROR]","track","send msg error")}))},t.tracker.getTracker=function(e){return t.trackers[e]},t.tracker.eachTracker=function(e){for(var n in t.trackers)t.trackers.hasOwnProperty(n)&&e(n)},W(t)}(J);var Y=function(){var t;try{var e=new Uint32Array(1);window.crypto.getRandomValues(e),t=**********&e[0]}catch(e){t=Math.floor(***********Math.random())}return t},tt=function(){return Y().toString(36)};function et(){var t=navigator.userAgent.toLowerCase(),e={},n={IE:window.ActiveXObject||"ActiveXObject"in window,Chrome:t.indexOf("chrome")>-1&&t.indexOf("safari")>-1,Firefox:t.indexOf("firefox")>-1,Opera:t.indexOf("opera")>-1,Safari:t.indexOf("safari")>-1&&-1==t.indexOf("chrome"),Edge:t.indexOf("edge")>-1||t.indexOf("edg")>-1,QQBrowser:/qqbrowser/.test(t),WeixinBrowser:/MicroMessenger/i.test(t)};for(var r in n)if(n[r]){var i="";if("IE"==r)i=t.match(/(msie\s|trident.*rv:)([\w.]+)/)[2];else if("Chrome"==r){for(var a in navigator.mimeTypes)"application/360softmgrplugin"==navigator.mimeTypes[a].type&&(r="360");i=t.match(/chrome\/([\d.]+)/)[1]}else"Firefox"==r?i=t.match(/firefox\/([\d.]+)/)[1]:"Opera"==r?i=t.match(/opera\/([\d.]+)/)[1]:"Safari"==r?i=t.match(/version\/([\d.]+)/)[1]:"Edge"==r?i=t.match(/edg\/([\d.]+)/)[1]:"QQBrowser"==r&&(i=t.match(/qqbrowser\/([\d.]+)/)[1]);e.type=r,e.versions=parseInt(i)}return e}var nt,rt=function(t){return String(t).replace(new RegExp("([.*+?^=!:${}()|[\\]/\\\\-])","g"),"\\$1")},it=function(t){var e=(t||window).document.createElement("iframe");return e.src="javascript:false",e.title="",e.role="presentation",e.frameBorder="0",e.tabIndex="-1",(e.frameElement||e).style.cssText="position:absolute;width:0;height:0;border:0;",e},at="S3EVENT_LISTENERS".concat(tt()),ot=function(t,e,n){var r=function(e){n.call(t,e)},i=e=e.replace(/^on/i,"");e=(e||"").toLowerCase(),t.addEventListener?t.addEventListener(i,r,!1):t.attachEvent&&t.attachEvent("on".concat(i),r);var a=t[at]=t[at]||[];return a[a.length]=[e,n,r,i],t},st=function(t,e,n){var r=n;e=e.replace(/^on/i,"").toLowerCase();for(var i,a,o,s=t[at],c=!r,d=s.length;d--;)(i=s[d])[0]!==e||!c&&i[1]!==r||(a=i[3],o=i[2],t.removeEventListener?t.removeEventListener(a,o,!1):t.detachEvent&&t.detachEvent("on".concat(a),o),s.splice(d,1));return t},ct=function(t,e){var n,r,i=t.ownerDocument,a=!1;try{n=t.contentWindow,r=n.document}catch(o){a=!0,ot(t,"load",(function(){n=t.contentWindow,r=n.document,st(t,"load"),e(n,r,i.domain)})),t.src='javascript:void((function () {document.open("text/html", "replace");'+'document.domain = "'.concat(i.domain,'";')+"document.close();})())"}a||e(n,r,"")},dt='<!DOCTYPE html><html><head><meta charset="UTF-8"></head>',ut="</html>",ht=function(t,e){var n=(e||window).document,r=n.body||n.documentElement;r.insertBefore(t,r.firstChild)};function lt(t,e){if(e=e||window,nt||!t){var n=nt||e;return t&&t(n),n}!function(t,e){e=e||window;var n=it(e);ht(n,e),ct(n,(function(e,n,r){null==e.Array?(n.open("text/html","replace")._M_=function(){r&&(this.domain=r),e=this.defaultView||this.parentWindow,t(e)},n.write("".concat(dt,'<body onload="document._M_();"></body>').concat(ut)),n.close()):t(e)}))}((function(e){nt=e,t(e)}),e)}var ft=function(t){return t.match(/(.*?)(#.*)/)},pt=function(t){var e=lt();return t=t.replace(/\+/g," "),e.decodeURIComponent(t)},_t=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=ft(t);r&&(t=n?r[2].split("?")[1]||"":r[1]);if(!t)return"";var i=lt(),a=new i.RegExp("(?:&|\\?)?".concat(rt(i.encodeURIComponent(e)),"=([^&]*)(?:&|$)"),"");return(r=t.match(a))&&r[1]?pt(r[1]):""},vt=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],gt=null,wt=null,mt="_qd_latest",yt="_qd_fst";function bt(){if(gt)return gt;var t={};return vt.forEach((function(e){var n=function(){var t=window.location.href,e=t.indexOf("#"),n=t.indexOf("?");if(-1===e||-1===n)return!1;return n>e}(),r=_t(d.href,e,n);r.length&&(t["".concat(e)]=r)})),gt=t,t}function kt(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(wt&&!t)return wt;var e=bt(),n=function(t){try{var e=localStorage.getItem(mt);return e?(e=JSON.parse(e))[t]:void 0}catch(t){console.log("[QDTRACKER_ERROR]",t)}}("latest_utm")||{};return wt=l({},n),vt.forEach((function(t){var r="".concat(t);e[r]&&(n["latest_".concat(t)]=e[r])})),t?n:wt}function xt(){gt&&function(t,e){try{var n=localStorage.getItem(mt);(n=n?JSON.parse(n):{})[t]=e,localStorage.setItem(mt,JSON.stringify(n))}catch(t){console.log("[QDTRACKER_ERROR]",t)}}("latest_utm",kt(!0))}var St=["channel"];var Et=function(t){var e=(t=t||window).document;return("BackCompat"===e.compatMode?e.body:e.documentElement).clientWidth},At=function(t){var e=(t=t||window).document;return("BackCompat"===e.compatMode?e.body:e.documentElement).clientHeight},Rt=function(t){var e,n=function(){var t=navigator.userAgent,e="Unknown",n="Unknown",r="Unknown";e=t.match(/(ipad|playbook|silk)|(android(?!.*mobi))/i)||t.match(/Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|NetFront|Silk-Accelerated|(hpw|web)OS|Fennec|Minimo|Opera M(obi|ini)|Blazer|Dolfin|Dolphin|Skyfire|Zune/)?"移动端":"PC端";var i=/iPhone|iPod|iPad/;if(t.match(i))r="Apple",n=t.match(i)[0];else{var a=t.match(/Android .+?; (.+?) Build/);a&&a[1]?(r=a[1].split(" ")[0],n=a[1].split(" ")[1]):t.match(/Mac OS/)&&(r="Apple",n="macOS Device")}return{deviceType:e,deviceModel:n,deviceManufacturer:r}}(),r=n.deviceType,i=n.deviceModel,a=n.deviceManufacturer,o=function(){var t,e=navigator.userAgent,n=e.match(/NetType\/\w+/)?e.match(/NetType\/\w+/)[0]:"NetType/other";switch(n=n.toLowerCase().replace("nettype/","")){case"wifi":t="wifi";break;case"5g":t="5g";break;case"4g":t="4g";break;case"3g":case"3gnet":t="3g";break;case"2g":t="2g";break;default:t="未知"}return{networkType:t}}(),s=o.networkType;return t.application_type="Web",t.url_path=window.location.host,t.device_type=r,t.manufacturer=a,t.model=i,t.network_type=s,t.browser_language=navigator.language,t.os_version=function(){var t=navigator.userAgent,e="Unknown OS";if(-1!=t.indexOf("Win"))e="Windows",-1!=t.indexOf("Windows NT 10.0")?e+=" 10":-1!=t.indexOf("Windows NT 6.3")?e+=" 8.1":-1!=t.indexOf("Windows NT 6.2")?e+=" 8":-1!=t.indexOf("Windows NT 6.1")?e+=" 7":-1!=t.indexOf("Windows NT 6.0")?e+=" Vista":-1!=t.indexOf("Windows NT 5.1")?e+=" XP":-1!=t.indexOf("Windows NT 5.0")&&(e+=" 2000");else if(-1!=t.indexOf("Mac")){e="Mac OS";var n=t.match(/Mac OS X ([\d_]+)/);n&&n[1]&&(e+=" "+n[1].replace(/_/g,"."))}else-1!=t.indexOf("X11")?e="UNIX":-1!=t.indexOf("Linux")&&(e="Linux");return e}(),t.os=(e="Unknown",-1!=window.navigator.userAgent.indexOf("Windows NT 10.0")&&(e="Windows 10"),-1!=window.navigator.userAgent.indexOf("Windows NT 6.2")&&(e="Windows 8"),-1!=window.navigator.userAgent.indexOf("Windows NT 6.1")&&(e="Windows 7"),-1!=window.navigator.userAgent.indexOf("Windows NT 6.0")&&(e="Windows Vista"),-1!=window.navigator.userAgent.indexOf("Windows NT 5.1")&&(e="Windows XP"),-1!=window.navigator.userAgent.indexOf("Windows NT 5.0")&&(e="Windows 2000"),-1!=window.navigator.userAgent.indexOf("Mac")&&(e="MacOS"),-1!=window.navigator.userAgent.indexOf("X11")&&(e="UNIX"),-1!=window.navigator.userAgent.indexOf("Linux")&&(e="Linux"),e),t.browser=et()&&et().type,t.browser_version=et()&&et().versions,t.sw=window.screen.width,t.sh=window.screen.height,t.bw=Et(window),t.bh=At(window),t.screen_resolution="".concat(t.sw," x ").concat(t.sh),t},It=function(t){var e=bt(),n=kt(),r=function(){try{var t=localStorage.getItem(yt);return t?t==(new Date).setHours(0,0,0,0):(localStorage.setItem(yt,(new Date).setHours(0,0,0,0)),!0)}catch(t){console.log("[QDTRACKER_ERROR]",t)}}(),i=l(l(l({},e),n),r);return t=l(l({},t),i),xt(),t},Ot=function(t){var e,n=d.search,r=(e={},St.forEach((function(t){var n=_t(d.href,t);n.length&&(e["".concat(t)]=n)})),e);return(t=l(l({},t),r)).path_parameter=n,t},qt=function(t){var e=function(){var t={};try{var e=window.Cdp_App_H5_Bridge;e&&(t=JSON.parse(e.obtain_track_event_common_data()),window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","jsBridge.js nativeCommonData,",t,p(t)))}catch(t){console.warn("error",t)}return t}();return t=l(l({},t),e)},Ct=function(t,e){var n=ft(t);if(n&&(t=n[1]),!t)return"";for(var r=lt(),i=new r.RegExp("(?:&|\\?)?".concat(rt(r.encodeURIComponent(e)),"=([^&]*)(?:&|$)"),"g"),a=[];n=i.exec(t);)a.push(pt(n[1]));return a.length<=1?a[0]||"":a};function Dt(t,e){try{var n="__qdf".concat(e,"__"),r="qda_share".concat(e),i=Ct(t.location.href,r);return i?t.sessionStorage.setItem(n,i):i=t.sessionStorage.getItem(n),i}catch(t){console.log("[QDTRACKER_ERROR]",t)}}var Tt=function(t,e,n){var r=t.length;if(r>0){n=n||r,e=e||0;for(var i=new Array(n-e),a=0,o=e;o<n;o++,a++)i[a]=t[o];return i}return[]},Bt={Boolean:1,Number:1,String:1,Function:1,Array:1,Date:1,RegExp:1,Object:1,Error:1},zt=Object.prototype.toString,Pt=function(t){if(null==t)return String(t);var e=p(t),n="object";return e===n||"function"===e?(e=zt.call(t).slice(8,-1),Bt[e]?e.toLowerCase():n):p(t)},Mt={};Object.keys(Bt).forEach((function(t){Mt["is"+t]=function(e){return Pt(e)===t.toLowerCase()}}));var Ht=function(t){if(!t)return"";var e=[],n=/\[\]$/,r=lt().encodeURIComponent,i=function(t,n){n="function"==typeof n?n():null==n?"":n,e[e.length]="".concat(r(t),"=").concat(r(n))};return function t(r,a){var o,s;switch(Pt(a)){case"array":if(r)for(o=0,s=a.length;o<s;o++)if(n.test(r))i(r,a[o]);else{var c="object"===Pt(a[o])?o:"";t("".concat(r,"[").concat(c,"]"),a[o])}else for(o=0,s=a.length;o<s;o++)t(a[o].key,a[o].value);break;case"object":i(r,JSON.stringify(a));break;default:a="".concat(a),r?i(r,a):e[e.length]=a}return e}("",t).join("&").replace(/%20/g,"+")},jt=function(t,e,n){t=(n&&n._storage_prefix||"")+t;for(var r=[],i=(e=e||window).document.cookie.split(";"),a=new RegExp("^\\s*".concat(rt(t),"=\\s*(.*?)\\s*$")),o=0;o<i.length;o++){var s=i[o].match(a);s&&(r[r.length]=lt().decodeURIComponent(s[1]))}return r},Nt=function(t,e,n,r,i,a,o){n=n||window,e=lt().encodeURIComponent(e);var s="".concat((o&&o._storage_prefix||"")+t,"=").concat(e,"; ");if(null!=a&&(s+="path=".concat(a,"; ")),null!=r){var c=new Date;c.setTime(c.getTime()+r),s+="expires=".concat(c.toGMTString(),"; ")}null!=i&&(s+="domain=".concat(i,";")),s+="Secure=true;";var d=n.document,u=d.cookie;if(d.cookie=s,u===d.cookie){for(var h=jt(t,null,o),l=0;l<h.length;l++)if(e===h[l])return!0;return!1}return!0},Ut="S3COOKIENAME".concat(tt()),Ft=function(){var t="cookie",e=window.document;if(window.navigator.cookieEnabled)return!0;e[t]="".concat(Ut,"=1");var n=-1!==e[t].indexOf("".concat(Ut,"="));return e[t]="".concat(Ut,"=1; expires=Thu, 01-Jan-1970 00:00:01 GMT"),n},Lt=function(){var t="S3LOCALSTORAGE".concat(tt()),e="localStorage";try{return window[e].setItem(t,1),window[e].removeItem(t),!0}catch(t){return!1}},Qt=function(){this.tasks={}};Qt.prototype.set=function(t,e){this.tasks[t]=e},Qt.prototype.get=function(t){return this.tasks[t]};var Zt=new Qt,Kt=function(){this._s={},this._avg={},this._max={}};Kt.prototype.start=function(t,e){this._s[t]=e?+e:+new Date},Kt.prototype.end=function(t,e){var n=this._s[t];this._s[t]=null;var r=(e?+e:+new Date)-n;this._max[t]=Math.max(r,this._max[t]||0);var i=this._avg[t];i?(i.s=(i.s*i.n+r)/(i.n+1),++i.n):i=this._avg[t]={s:r,n:1}},Kt.prototype.encode=function(){var t,e,n="",r=this._avg;for(var i in r)r.hasOwnProperty(i)&&(t=Math.round(r[i].s||0),e=Math.round(this._max[i]||0),n+="".concat(i,"(").concat(t,"_").concat(e,")"));return n};var Wt=new Kt,Vt=function(){};Vt.prototype.run=function(t){var e=t.getPVParams();Wt.start("req-pv"),t.ping("$pageview",e,1)},Vt.prototype.remove=function(t){},Zt.set("pv",Vt);var $t=!1,Xt=window.navigator,Gt=Xt.userAgent,Jt=null!=window.chrome&&"Google Inc."===Xt.vendor&&/Chrome/.test(Gt)&&-1===Gt.indexOf("OPR")&&-1===Gt.indexOf("Edge"),Yt=window.navigator.userAgent,te=/iphone|ipod|android.*mobile|windows.*phone|blackberry.*mobile/i.test(Yt),ee=function(){this.removed=!1};ee.prototype.run=function(t){if(!Jt&&!te){var e=t.get("win");!function(t,e){var n=(e=e||window).document,r="DOMContentLoaded",i=r+42,a=n[at]=n[at]||[];if(a[a.length]=[i,t,t,r],!$t){$t=!0;var o=!1;if("complete"===n.readyState||"loading"!==n.readyState&&!n.documentElement.doScroll)lt().setTimeout(c);else if(n.addEventListener)n.addEventListener("DOMContentLoaded",u),e.addEventListener("load",u);else{n.attachEvent("onreadystatechange",u),e.attachEvent("onload",u);var s=!1;try{s=null==e.frameElement&&n.documentElement}catch(h){}if(s&&s.doScroll){function l(){if(!o){try{s.doScroll("left")}catch(t){return lt().setTimeout(l,50)}d(),c()}}l()}}}function c(){for(var t,r=n[at],a=0;t=r[a];)t[0]===i?((0,t[1])(e),r.splice(a,1)):a++;o=!0}function d(){n.addEventListener?(n.removeEventListener("DOMContentLoaded",u),e.removeEventListener("load",u)):(n.detachEvent("onreadystatechange",u),e.detachEvent("onload",u))}function u(){(n.addEventListener||"load"===e.event.type||"complete"===n.readyState)&&(d(),c())}}(t.wrap("id-cb",(function(){if(this.removed)t.flag(20);else{var n="".concat(t.get("ss"),"/da/id").concat("",".html"),r=Ht({q:t.get("qid"),p:t.get("pid"),t:t.get("kfuin"),a:t.get("aid"),c:t.get("cid"),s:t.getSid(),src:t.get("src"),pgv_pvi:t.get("pgv_pvi"),v:t.get("ver"),ts:t.getFullApi("id")});(function(t,e){var n=(e||window).document.createElement("iframe");n.src=t,n.title="",n.role="presentation",n.frameBorder="0",n.tabIndex="-1",(n.frameElement||n).style.cssText="position:absolute;width:0;height:0;border:0;",ht(n,e)})(n+="?".concat(r),e)}}),19),e)}},ee.prototype.remove=function(t){this.removed=!0},Zt.set("id",ee);var ne=function(t){return t.target||t.srcElement},re=function t(e){var n="",r=e.nodeType;if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=t(e)}else if(3===r||4===r)return e.nodeValue;return n},ie=function(){};ie.prototype.run=function(t){var e=t.get("win"),n=t.get("doc").documentElement;this.cb=t.wrap("clc-cb",(function(n){var r,i=ne(n),a=t.getCommonData(),o=(i.nodeName||"").toLowerCase();if(a.add("pw",function(t){var e=(t=t||window).document,n=e.body,r=e.documentElement,i="BackCompat"===e.compatMode?n:e.documentElement;return Math.max(r.scrollWidth,n.scrollWidth,i.clientWidth)}(e)),a.add("ph",function(t){var e=(t=t||window).document,n=e.body,r=e.documentElement,i="BackCompat"===e.compatMode?n:e.documentElement;return Math.max(r.scrollHeight,n.scrollHeight,i.clientHeight)}(e)),a.add("bw",Et(e)),a.add("bh",At(e)),a.add("bx",function(t){var e=(t=t||window).document;return t.pageXOffset||e.documentElement.scrollLeft||e.body.scrollLeft}(e)),a.add("by",function(t){var e=(t=t||window).document;return t.pageYOffset||e.documentElement.scrollTop||e.body.scrollTop}(e)),a.add("tag",o),i.href){var s=i.getAttribute("target");s&&a.add("target",s),a.add("href",i.href)}a.add("x",n.clientX||0),a.add("y",n.clientY||0);var c=0;"a"!==o&&"button"!==o||(r=re(i),c=1),"input"!==o||"button"!==i.type&&"submit"!==i.type||(r=i.value||"",c=1),r&&a.add("word",r.slice(0,20)),t.ping("click",a,c)}));var r=t.supportTouch;r&&t.flag(18),ot(n,r?"touchstart":"click",this.cb)},ie.prototype.remove=function(t){var e=t.get("doc").documentElement;st(e,"click",this.cb),t.supportTouch&&st(e,"touchstart",this.cb)},Zt.set("clc",ie);var ae=function(t,e){var n=window.history[t];window.history[t]=function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var a=n.apply(window.history,r);return e(),a}};function oe(t){var e,n;this.cb=t,void 0!==document.hidden?(e="hidden",n="visibilitychange"):void 0!==document.msHidden?(e="msHidden",n="msvisibilitychange"):void 0!==document.webkitHidden&&(e="webkitHidden",n="webkitvisibilitychange"),this.handleVisibilityChange=function(){t(document[e])},this.hidden=e,this.visibilityChange=n,void 0===document.addEventListener||void 0===document[e]?window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","This demo requires a browser, such as Google Chrome or Firefox, that supports the Page Visibility API."):document.addEventListener(n,this.handleVisibilityChange,!1)}oe.prototype.remove=function(){void 0===document.removeEventListener||void 0===document[this.hidden]?window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","This demo requires a browser, such as Google Chrome or Firefox, that supports the Page Visibility API."):document.removeEventListener(this.visibilityChange,this.handleVisibilityChange,!1)};var se="unload",ce="popstate",de="hashchange",ue=function(){};ue.prototype.run=function(t){var e=t.get("win"),n=0,r=+new Date,i=new oe((function(t){t?n+=+new Date-r:r=+new Date}));this.cb=function(){console.log("[QDTRACKER_LOG] unload event happen");try{i.remove(),n+=+new Date-r;var e=t.getCommonData();if(e.add("properties",{dr:n}),t.qdda&&(t.qdda.bussid||t.qdda.qq||t.qdda.qda_accountInfo)){var a={};t.qdda.bussid&&(a.bussid=t.qdda.bussid),t.qdda.qq&&(a.qq=t.qdda.qq),t.qdda.qda_accountInfo&&(a=l(l({},a),t.qdda.qda_accountInfo)),e.add("account",a)}t.ping("$pageclose",e,3)}catch(e){t.sendError("err","pc-cb",e.name,e.message,!0)}},ot(e,se,this.cb),t.get("singlePage")&&(t.vals.$$pageCloseRouterRewrite||(ae("pushState",this.cb),ae("replaceState",this.cb),t.vals.$$pageCloseRouterRewrite=!0),ot(e,ce,this.cb),ot(e,de,this.cb))},ue.prototype.remove=function(t){var e=t.get("win");st(e,se,this.cb),st(e,ce,this.cb),st(e,de,this.cb)},Zt.set("pc",ue);var he="hidden"in document?"hidden":"webkitHidden"in document?"webkitHidden":"mozHidden"in document?"mozHidden":"msHidden"in document?"msHidden":"",le=he.replace(/hidden/i,"visibilitychange"),fe="popstate",pe="hashchange",_e=function(){};_e.prototype.run=function(t){var e=this,n=t.get("win"),r=0,i=+new Date;(this.onVisibilityChange=function(){if(console.log("[QDTRACKER_LOG] onVisibilityChange",he,document[he]),document[he]){r=+new Date-i,isSinglePageRouterTrigger&&(i=+new Date);var e=t.getCommonData();if(e.add("properties",{dr:r}),t.qdda&&(t.qdda.bussid||t.qdda.qq||t.qdda.qda_accountInfo)){var n={};t.qdda.bussid&&(n.bussid=t.qdda.bussid),t.qdda.qq&&(n.qq=t.qdda.qq),t.qdda.qda_accountInfo&&(n=l(l({},n),t.qdda.qda_accountInfo)),e.add("account",n)}t.ping("$pagestay",e,3)}else i=+new Date},!t.vals.pageStayInited&&t.vals.pagestay&&le)&&(ot(n,le,this.onVisibilityChange),t.get("singlePage")&&(window.$$pageStayRouterRewrite||(ae("pushState",(function(){return e.onVisibilityChange(!0)})),ae("replaceState",(function(){return e.onVisibilityChange(!0)})),t.vals.$$pageStayRouterRewrite=!0),ot(n,fe,(function(){return e.onVisibilityChange(!0)})),ot(n,pe,(function(){return e.onVisibilityChange(!0)}))));t.vals.pagestay&&le&&(t.vals.pageStayInited=!0)},_e.prototype.remove=function(t){var e=this,n=t.get("win");st(n,fe,(function(){return e.onVisibilityChange(!0)})),st(n,pe,(function(){return e.onVisibilityChange(!0)})),st(n,le,this.onVisibilityChange)},Zt.set("ps",_e);var ve=function(){return"sendBeacon"in window.navigator},ge=function(){try{if("XMLHttpRequest"in window&&"withCredentials"in new window.XMLHttpRequest)return"xhr";if("XDomainRequest"in window)return"xdr"}catch(t){}return""},we=/msie (\d+\.\d+)/i.test(navigator.userAgent)?"".concat(document.documentMode)||RegExp.$1:"";function me(t){for(var e=t.length;--e>=0;)t[e]=0}var ye=256,be=286,ke=30,xe=15,Se=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),Ee=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),Ae=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),Re=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Ie=new Array(576);me(Ie);var Oe=new Array(60);me(Oe);var qe=new Array(512);me(qe);var Ce=new Array(256);me(Ce);var De=new Array(29);me(De);var Te,Be,ze,Pe=new Array(ke);function Me(t,e,n,r,i){this.static_tree=t,this.extra_bits=e,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=t&&t.length}function He(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}me(Pe);var je=function(t){return t<256?qe[t]:qe[256+(t>>>7)]},Ne=function(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255},Ue=function(t,e,n){t.bi_valid>16-n?(t.bi_buf|=e<<t.bi_valid&65535,Ne(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=n-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=n)},Fe=function(t,e,n){Ue(t,n[2*e],n[2*e+1])},Le=function(t,e){var n=0;do{n|=1&t,t>>>=1,n<<=1}while(--e>0);return n>>>1},Qe=function(t,e,n){var r,i,a=new Array(16),o=0;for(r=1;r<=xe;r++)o=o+n[r-1]<<1,a[r]=o;for(i=0;i<=e;i++){var s=t[2*i+1];0!==s&&(t[2*i]=Le(a[s]++,s))}},Ze=function(t){var e;for(e=0;e<be;e++)t.dyn_ltree[2*e]=0;for(e=0;e<ke;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.sym_next=t.matches=0},Ke=function(t){t.bi_valid>8?Ne(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0},We=function(t,e,n,r){var i=2*e,a=2*n;return t[i]<t[a]||t[i]===t[a]&&r[e]<=r[n]},Ve=function(t,e,n){for(var r=t.heap[n],i=n<<1;i<=t.heap_len&&(i<t.heap_len&&We(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!We(e,r,t.heap[i],t.depth));)t.heap[n]=t.heap[i],n=i,i<<=1;t.heap[n]=r},$e=function(t,e,n){var r,i,a,o,s=0;if(0!==t.sym_next)do{r=255&t.pending_buf[t.sym_buf+s++],r+=(255&t.pending_buf[t.sym_buf+s++])<<8,i=t.pending_buf[t.sym_buf+s++],0===r?Fe(t,i,e):(a=Ce[i],Fe(t,a+ye+1,e),0!==(o=Se[a])&&(i-=De[a],Ue(t,i,o)),r--,a=je(r),Fe(t,a,n),0!==(o=Ee[a])&&(r-=Pe[a],Ue(t,r,o)))}while(s<t.sym_next);Fe(t,256,e)},Xe=function(t,e){var n,r,i,a=e.dyn_tree,o=e.stat_desc.static_tree,s=e.stat_desc.has_stree,c=e.stat_desc.elems,d=-1;for(t.heap_len=0,t.heap_max=573,n=0;n<c;n++)0!==a[2*n]?(t.heap[++t.heap_len]=d=n,t.depth[n]=0):a[2*n+1]=0;for(;t.heap_len<2;)a[2*(i=t.heap[++t.heap_len]=d<2?++d:0)]=1,t.depth[i]=0,t.opt_len--,s&&(t.static_len-=o[2*i+1]);for(e.max_code=d,n=t.heap_len>>1;n>=1;n--)Ve(t,a,n);i=c;do{n=t.heap[1],t.heap[1]=t.heap[t.heap_len--],Ve(t,a,1),r=t.heap[1],t.heap[--t.heap_max]=n,t.heap[--t.heap_max]=r,a[2*i]=a[2*n]+a[2*r],t.depth[i]=(t.depth[n]>=t.depth[r]?t.depth[n]:t.depth[r])+1,a[2*n+1]=a[2*r+1]=i,t.heap[1]=i++,Ve(t,a,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var n,r,i,a,o,s,c=e.dyn_tree,d=e.max_code,u=e.stat_desc.static_tree,h=e.stat_desc.has_stree,l=e.stat_desc.extra_bits,f=e.stat_desc.extra_base,p=e.stat_desc.max_length,_=0;for(a=0;a<=xe;a++)t.bl_count[a]=0;for(c[2*t.heap[t.heap_max]+1]=0,n=t.heap_max+1;n<573;n++)(a=c[2*c[2*(r=t.heap[n])+1]+1]+1)>p&&(a=p,_++),c[2*r+1]=a,r>d||(t.bl_count[a]++,o=0,r>=f&&(o=l[r-f]),s=c[2*r],t.opt_len+=s*(a+o),h&&(t.static_len+=s*(u[2*r+1]+o)));if(0!==_){do{for(a=p-1;0===t.bl_count[a];)a--;t.bl_count[a]--,t.bl_count[a+1]+=2,t.bl_count[p]--,_-=2}while(_>0);for(a=p;0!==a;a--)for(r=t.bl_count[a];0!==r;)(i=t.heap[--n])>d||(c[2*i+1]!==a&&(t.opt_len+=(a-c[2*i+1])*c[2*i],c[2*i+1]=a),r--)}}(t,e),Qe(a,d,t.bl_count)},Ge=function(t,e,n){var r,i,a=-1,o=e[1],s=0,c=7,d=4;for(0===o&&(c=138,d=3),e[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=e[2*(r+1)+1],++s<c&&i===o||(s<d?t.bl_tree[2*i]+=s:0!==i?(i!==a&&t.bl_tree[2*i]++,t.bl_tree[32]++):s<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=0,a=i,0===o?(c=138,d=3):i===o?(c=6,d=3):(c=7,d=4))},Je=function(t,e,n){var r,i,a=-1,o=e[1],s=0,c=7,d=4;for(0===o&&(c=138,d=3),r=0;r<=n;r++)if(i=o,o=e[2*(r+1)+1],!(++s<c&&i===o)){if(s<d)do{Fe(t,i,t.bl_tree)}while(0!=--s);else 0!==i?(i!==a&&(Fe(t,i,t.bl_tree),s--),Fe(t,16,t.bl_tree),Ue(t,s-3,2)):s<=10?(Fe(t,17,t.bl_tree),Ue(t,s-3,3)):(Fe(t,18,t.bl_tree),Ue(t,s-11,7));s=0,a=i,0===o?(c=138,d=3):i===o?(c=6,d=3):(c=7,d=4)}},Ye=!1,tn=function(t,e,n,r){Ue(t,0+(r?1:0),3),Ke(t),Ne(t,n),Ne(t,~n),n&&t.pending_buf.set(t.window.subarray(e,e+n),t.pending),t.pending+=n},en=function(t,e,n,r){var i,a,o=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,n=4093624447;for(e=0;e<=31;e++,n>>>=1)if(1&n&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<ye;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),Xe(t,t.l_desc),Xe(t,t.d_desc),o=function(t){var e;for(Ge(t,t.dyn_ltree,t.l_desc.max_code),Ge(t,t.dyn_dtree,t.d_desc.max_code),Xe(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*Re[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(a=t.static_len+3+7>>>3)<=i&&(i=a)):i=a=n+5,n+4<=i&&-1!==e?tn(t,e,n,r):4===t.strategy||a===i?(Ue(t,2+(r?1:0),3),$e(t,Ie,Oe)):(Ue(t,4+(r?1:0),3),function(t,e,n,r){var i;for(Ue(t,e-257,5),Ue(t,n-1,5),Ue(t,r-4,4),i=0;i<r;i++)Ue(t,t.bl_tree[2*Re[i]+1],3);Je(t,t.dyn_ltree,e-1),Je(t,t.dyn_dtree,n-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,o+1),$e(t,t.dyn_ltree,t.dyn_dtree)),Ze(t),r&&Ke(t)},nn={_tr_init:function(t){Ye||(!function(){var t,e,n,r,i,a=new Array(16);for(n=0,r=0;r<28;r++)for(De[r]=n,t=0;t<1<<Se[r];t++)Ce[n++]=r;for(Ce[n-1]=r,i=0,r=0;r<16;r++)for(Pe[r]=i,t=0;t<1<<Ee[r];t++)qe[i++]=r;for(i>>=7;r<ke;r++)for(Pe[r]=i<<7,t=0;t<1<<Ee[r]-7;t++)qe[256+i++]=r;for(e=0;e<=xe;e++)a[e]=0;for(t=0;t<=143;)Ie[2*t+1]=8,t++,a[8]++;for(;t<=255;)Ie[2*t+1]=9,t++,a[9]++;for(;t<=279;)Ie[2*t+1]=7,t++,a[7]++;for(;t<=287;)Ie[2*t+1]=8,t++,a[8]++;for(Qe(Ie,287,a),t=0;t<ke;t++)Oe[2*t+1]=5,Oe[2*t]=Le(t,5);Te=new Me(Ie,Se,257,be,xe),Be=new Me(Oe,Ee,0,ke,xe),ze=new Me(new Array(0),Ae,0,19,7)}(),Ye=!0),t.l_desc=new He(t.dyn_ltree,Te),t.d_desc=new He(t.dyn_dtree,Be),t.bl_desc=new He(t.bl_tree,ze),t.bi_buf=0,t.bi_valid=0,Ze(t)},_tr_stored_block:tn,_tr_flush_block:en,_tr_tally:function(t,e,n){return t.pending_buf[t.sym_buf+t.sym_next++]=e,t.pending_buf[t.sym_buf+t.sym_next++]=e>>8,t.pending_buf[t.sym_buf+t.sym_next++]=n,0===e?t.dyn_ltree[2*n]++:(t.matches++,e--,t.dyn_ltree[2*(Ce[n]+ye+1)]++,t.dyn_dtree[2*je(e)]++),t.sym_next===t.sym_end},_tr_align:function(t){Ue(t,2,3),Fe(t,256,Ie),function(t){16===t.bi_valid?(Ne(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}},rn=function(t,e,n,r){for(var i=65535&t|0,a=t>>>16&65535|0,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{a=a+(i=i+e[r++]|0)|0}while(--o);i%=65521,a%=65521}return i|a<<16|0},an=new Uint32Array(function(){for(var t,e=[],n=0;n<256;n++){t=n;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[n]=t}return e}()),on=function(t,e,n,r){var i=an,a=r+n;t^=-1;for(var o=r;o<a;o++)t=t>>>8^i[255&(t^e[o])];return-1^t},sn={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},cn={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},dn=nn._tr_init,un=nn._tr_stored_block,hn=nn._tr_flush_block,ln=nn._tr_tally,fn=nn._tr_align,pn=cn.Z_NO_FLUSH,_n=cn.Z_PARTIAL_FLUSH,vn=cn.Z_FULL_FLUSH,gn=cn.Z_FINISH,wn=cn.Z_BLOCK,mn=cn.Z_OK,yn=cn.Z_STREAM_END,bn=cn.Z_STREAM_ERROR,kn=cn.Z_DATA_ERROR,xn=cn.Z_BUF_ERROR,Sn=cn.Z_DEFAULT_COMPRESSION,En=cn.Z_FILTERED,An=cn.Z_HUFFMAN_ONLY,Rn=cn.Z_RLE,In=cn.Z_FIXED,On=cn.Z_DEFAULT_STRATEGY,qn=cn.Z_UNKNOWN,Cn=cn.Z_DEFLATED,Dn=258,Tn=262,Bn=42,zn=113,Pn=666,Mn=function(t,e){return t.msg=sn[e],e},Hn=function(t){return 2*t-(t>4?9:0)},jn=function(t){for(var e=t.length;--e>=0;)t[e]=0},Nn=function(t){var e,n,r,i=t.w_size;r=e=t.hash_size;do{n=t.head[--r],t.head[r]=n>=i?n-i:0}while(--e);r=e=i;do{n=t.prev[--r],t.prev[r]=n>=i?n-i:0}while(--e)},Un=function(t,e,n){return(e<<t.hash_shift^n)&t.hash_mask},Fn=function(t){var e=t.state,n=e.pending;n>t.avail_out&&(n=t.avail_out),0!==n&&(t.output.set(e.pending_buf.subarray(e.pending_out,e.pending_out+n),t.next_out),t.next_out+=n,e.pending_out+=n,t.total_out+=n,t.avail_out-=n,e.pending-=n,0===e.pending&&(e.pending_out=0))},Ln=function(t,e){hn(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,Fn(t.strm)},Qn=function(t,e){t.pending_buf[t.pending++]=e},Zn=function(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e},Kn=function(t,e,n,r){var i=t.avail_in;return i>r&&(i=r),0===i?0:(t.avail_in-=i,e.set(t.input.subarray(t.next_in,t.next_in+i),n),1===t.state.wrap?t.adler=rn(t.adler,e,i,n):2===t.state.wrap&&(t.adler=on(t.adler,e,i,n)),t.next_in+=i,t.total_in+=i,i)},Wn=function(t,e){var n,r,i=t.max_chain_length,a=t.strstart,o=t.prev_length,s=t.nice_match,c=t.strstart>t.w_size-Tn?t.strstart-(t.w_size-Tn):0,d=t.window,u=t.w_mask,h=t.prev,l=t.strstart+Dn,f=d[a+o-1],p=d[a+o];t.prev_length>=t.good_match&&(i>>=2),s>t.lookahead&&(s=t.lookahead);do{if(d[(n=e)+o]===p&&d[n+o-1]===f&&d[n]===d[a]&&d[++n]===d[a+1]){a+=2,n++;do{}while(d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&d[++a]===d[++n]&&a<l);if(r=Dn-(l-a),a=l-Dn,r>o){if(t.match_start=e,o=r,r>=s)break;f=d[a+o-1],p=d[a+o]}}}while((e=h[e&u])>c&&0!=--i);return o<=t.lookahead?o:t.lookahead},Vn=function(t){var e,n,r,i=t.w_size;do{if(n=t.window_size-t.lookahead-t.strstart,t.strstart>=i+(i-Tn)&&(t.window.set(t.window.subarray(i,i+i-n),0),t.match_start-=i,t.strstart-=i,t.block_start-=i,t.insert>t.strstart&&(t.insert=t.strstart),Nn(t),n+=i),0===t.strm.avail_in)break;if(e=Kn(t.strm,t.window,t.strstart+t.lookahead,n),t.lookahead+=e,t.lookahead+t.insert>=3)for(r=t.strstart-t.insert,t.ins_h=t.window[r],t.ins_h=Un(t,t.ins_h,t.window[r+1]);t.insert&&(t.ins_h=Un(t,t.ins_h,t.window[r+3-1]),t.prev[r&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=r,r++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<Tn&&0!==t.strm.avail_in)},$n=function(t,e){var n,r,i,a=t.pending_buf_size-5>t.w_size?t.w_size:t.pending_buf_size-5,o=0,s=t.strm.avail_in;do{if(n=65535,i=t.bi_valid+42>>3,t.strm.avail_out<i)break;if(i=t.strm.avail_out-i,n>(r=t.strstart-t.block_start)+t.strm.avail_in&&(n=r+t.strm.avail_in),n>i&&(n=i),n<a&&(0===n&&e!==gn||e===pn||n!==r+t.strm.avail_in))break;o=e===gn&&n===r+t.strm.avail_in?1:0,un(t,0,0,o),t.pending_buf[t.pending-4]=n,t.pending_buf[t.pending-3]=n>>8,t.pending_buf[t.pending-2]=~n,t.pending_buf[t.pending-1]=~n>>8,Fn(t.strm),r&&(r>n&&(r=n),t.strm.output.set(t.window.subarray(t.block_start,t.block_start+r),t.strm.next_out),t.strm.next_out+=r,t.strm.avail_out-=r,t.strm.total_out+=r,t.block_start+=r,n-=r),n&&(Kn(t.strm,t.strm.output,t.strm.next_out,n),t.strm.next_out+=n,t.strm.avail_out-=n,t.strm.total_out+=n)}while(0===o);return(s-=t.strm.avail_in)&&(s>=t.w_size?(t.matches=2,t.window.set(t.strm.input.subarray(t.strm.next_in-t.w_size,t.strm.next_in),0),t.strstart=t.w_size,t.insert=t.strstart):(t.window_size-t.strstart<=s&&(t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,t.insert>t.strstart&&(t.insert=t.strstart)),t.window.set(t.strm.input.subarray(t.strm.next_in-s,t.strm.next_in),t.strstart),t.strstart+=s,t.insert+=s>t.w_size-t.insert?t.w_size-t.insert:s),t.block_start=t.strstart),t.high_water<t.strstart&&(t.high_water=t.strstart),o?4:e!==pn&&e!==gn&&0===t.strm.avail_in&&t.strstart===t.block_start?2:(i=t.window_size-t.strstart,t.strm.avail_in>i&&t.block_start>=t.w_size&&(t.block_start-=t.w_size,t.strstart-=t.w_size,t.window.set(t.window.subarray(t.w_size,t.w_size+t.strstart),0),t.matches<2&&t.matches++,i+=t.w_size,t.insert>t.strstart&&(t.insert=t.strstart)),i>t.strm.avail_in&&(i=t.strm.avail_in),i&&(Kn(t.strm,t.window,t.strstart,i),t.strstart+=i,t.insert+=i>t.w_size-t.insert?t.w_size-t.insert:i),t.high_water<t.strstart&&(t.high_water=t.strstart),i=t.bi_valid+42>>3,a=(i=t.pending_buf_size-i>65535?65535:t.pending_buf_size-i)>t.w_size?t.w_size:i,((r=t.strstart-t.block_start)>=a||(r||e===gn)&&e!==pn&&0===t.strm.avail_in&&r<=i)&&(n=r>i?i:r,o=e===gn&&0===t.strm.avail_in&&n===r?1:0,un(t,t.block_start,n,o),t.block_start+=n,Fn(t.strm)),o?3:1)},Xn=function(t,e){for(var n,r;;){if(t.lookahead<Tn){if(Vn(t),t.lookahead<Tn&&e===pn)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=3&&(t.ins_h=Un(t,t.ins_h,t.window[t.strstart+3-1]),n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==n&&t.strstart-n<=t.w_size-Tn&&(t.match_length=Wn(t,n)),t.match_length>=3)if(r=ln(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=Un(t,t.ins_h,t.window[t.strstart+3-1]),n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=Un(t,t.ins_h,t.window[t.strstart+1]);else r=ln(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(Ln(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,e===gn?(Ln(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(Ln(t,!1),0===t.strm.avail_out)?1:2},Gn=function(t,e){for(var n,r,i;;){if(t.lookahead<Tn){if(Vn(t),t.lookahead<Tn&&e===pn)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=3&&(t.ins_h=Un(t,t.ins_h,t.window[t.strstart+3-1]),n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==n&&t.prev_length<t.max_lazy_match&&t.strstart-n<=t.w_size-Tn&&(t.match_length=Wn(t,n),t.match_length<=5&&(t.strategy===En||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){i=t.strstart+t.lookahead-3,r=ln(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=i&&(t.ins_h=Un(t,t.ins_h,t.window[t.strstart+3-1]),n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,r&&(Ln(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((r=ln(t,0,t.window[t.strstart-1]))&&Ln(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=ln(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,e===gn?(Ln(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(Ln(t,!1),0===t.strm.avail_out)?1:2};function Jn(t,e,n,r,i){this.good_length=t,this.max_lazy=e,this.nice_length=n,this.max_chain=r,this.func=i}var Yn=[new Jn(0,0,0,0,$n),new Jn(4,4,8,4,Xn),new Jn(4,5,16,8,Xn),new Jn(4,6,32,32,Xn),new Jn(4,4,16,16,Gn),new Jn(8,16,32,32,Gn),new Jn(8,16,128,128,Gn),new Jn(8,32,128,256,Gn),new Jn(32,128,258,1024,Gn),new Jn(32,258,258,4096,Gn)];function tr(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Cn,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),jn(this.dyn_ltree),jn(this.dyn_dtree),jn(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),jn(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),jn(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var er=function(t){if(!t)return 1;var e=t.state;return!e||e.strm!==t||e.status!==Bn&&57!==e.status&&69!==e.status&&73!==e.status&&91!==e.status&&103!==e.status&&e.status!==zn&&e.status!==Pn?1:0},nr=function(t){if(er(t))return Mn(t,bn);t.total_in=t.total_out=0,t.data_type=qn;var e=t.state;return e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=2===e.wrap?57:e.wrap?Bn:zn,t.adler=2===e.wrap?0:1,e.last_flush=-2,dn(e),mn},rr=function(t){var e,n=nr(t);return n===mn&&((e=t.state).window_size=2*e.w_size,jn(e.head),e.max_lazy_match=Yn[e.level].max_lazy,e.good_match=Yn[e.level].good_length,e.nice_match=Yn[e.level].nice_length,e.max_chain_length=Yn[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),n},ir=function(t,e,n,r,i,a){if(!t)return bn;var o=1;if(e===Sn&&(e=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),i<1||i>9||n!==Cn||r<8||r>15||e<0||e>9||a<0||a>In||8===r&&1!==o)return Mn(t,bn);8===r&&(r=9);var s=new tr;return t.state=s,s.strm=t,s.status=Bn,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.sym_buf=s.lit_bufsize,s.sym_end=3*(s.lit_bufsize-1),s.level=e,s.strategy=a,s.method=n,rr(t)},ar={deflateInit:function(t,e){return ir(t,e,Cn,15,8,On)},deflateInit2:ir,deflateReset:rr,deflateResetKeep:nr,deflateSetHeader:function(t,e){return er(t)||2!==t.state.wrap?bn:(t.state.gzhead=e,mn)},deflate:function(t,e){if(er(t)||e>wn||e<0)return t?Mn(t,bn):bn;var n=t.state;if(!t.output||0!==t.avail_in&&!t.input||n.status===Pn&&e!==gn)return Mn(t,0===t.avail_out?xn:bn);var r=n.last_flush;if(n.last_flush=e,0!==n.pending){if(Fn(t),0===t.avail_out)return n.last_flush=-1,mn}else if(0===t.avail_in&&Hn(e)<=Hn(r)&&e!==gn)return Mn(t,xn);if(n.status===Pn&&0!==t.avail_in)return Mn(t,xn);if(n.status===Bn&&0===n.wrap&&(n.status=zn),n.status===Bn){var i=Cn+(n.w_bits-8<<4)<<8;if(i|=(n.strategy>=An||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(i|=32),Zn(n,i+=31-i%31),0!==n.strstart&&(Zn(n,t.adler>>>16),Zn(n,65535&t.adler)),t.adler=1,n.status=zn,Fn(t),0!==n.pending)return n.last_flush=-1,mn}if(57===n.status)if(t.adler=0,Qn(n,31),Qn(n,139),Qn(n,8),n.gzhead)Qn(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),Qn(n,255&n.gzhead.time),Qn(n,n.gzhead.time>>8&255),Qn(n,n.gzhead.time>>16&255),Qn(n,n.gzhead.time>>24&255),Qn(n,9===n.level?2:n.strategy>=An||n.level<2?4:0),Qn(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(Qn(n,255&n.gzhead.extra.length),Qn(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=on(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69;else if(Qn(n,0),Qn(n,0),Qn(n,0),Qn(n,0),Qn(n,0),Qn(n,9===n.level?2:n.strategy>=An||n.level<2?4:0),Qn(n,3),n.status=zn,Fn(t),0!==n.pending)return n.last_flush=-1,mn;if(69===n.status){if(n.gzhead.extra){for(var a=n.pending,o=(65535&n.gzhead.extra.length)-n.gzindex;n.pending+o>n.pending_buf_size;){var s=n.pending_buf_size-n.pending;if(n.pending_buf.set(n.gzhead.extra.subarray(n.gzindex,n.gzindex+s),n.pending),n.pending=n.pending_buf_size,n.gzhead.hcrc&&n.pending>a&&(t.adler=on(t.adler,n.pending_buf,n.pending-a,a)),n.gzindex+=s,Fn(t),0!==n.pending)return n.last_flush=-1,mn;a=0,o-=s}var c=new Uint8Array(n.gzhead.extra);n.pending_buf.set(c.subarray(n.gzindex,n.gzindex+o),n.pending),n.pending+=o,n.gzhead.hcrc&&n.pending>a&&(t.adler=on(t.adler,n.pending_buf,n.pending-a,a)),n.gzindex=0}n.status=73}if(73===n.status){if(n.gzhead.name){var d,u=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>u&&(t.adler=on(t.adler,n.pending_buf,n.pending-u,u)),Fn(t),0!==n.pending)return n.last_flush=-1,mn;u=0}d=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,Qn(n,d)}while(0!==d);n.gzhead.hcrc&&n.pending>u&&(t.adler=on(t.adler,n.pending_buf,n.pending-u,u)),n.gzindex=0}n.status=91}if(91===n.status){if(n.gzhead.comment){var h,l=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>l&&(t.adler=on(t.adler,n.pending_buf,n.pending-l,l)),Fn(t),0!==n.pending)return n.last_flush=-1,mn;l=0}h=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,Qn(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>l&&(t.adler=on(t.adler,n.pending_buf,n.pending-l,l))}n.status=103}if(103===n.status){if(n.gzhead.hcrc){if(n.pending+2>n.pending_buf_size&&(Fn(t),0!==n.pending))return n.last_flush=-1,mn;Qn(n,255&t.adler),Qn(n,t.adler>>8&255),t.adler=0}if(n.status=zn,Fn(t),0!==n.pending)return n.last_flush=-1,mn}if(0!==t.avail_in||0!==n.lookahead||e!==pn&&n.status!==Pn){var f=0===n.level?$n(n,e):n.strategy===An?function(t,e){for(var n;;){if(0===t.lookahead&&(Vn(t),0===t.lookahead)){if(e===pn)return 1;break}if(t.match_length=0,n=ln(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,n&&(Ln(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===gn?(Ln(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(Ln(t,!1),0===t.strm.avail_out)?1:2}(n,e):n.strategy===Rn?function(t,e){for(var n,r,i,a,o=t.window;;){if(t.lookahead<=Dn){if(Vn(t),t.lookahead<=Dn&&e===pn)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(r=o[i=t.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){a=t.strstart+Dn;do{}while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a);t.match_length=Dn-(a-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(n=ln(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(n=ln(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),n&&(Ln(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,e===gn?(Ln(t,!0),0===t.strm.avail_out?3:4):t.sym_next&&(Ln(t,!1),0===t.strm.avail_out)?1:2}(n,e):Yn[n.level].func(n,e);if(3!==f&&4!==f||(n.status=Pn),1===f||3===f)return 0===t.avail_out&&(n.last_flush=-1),mn;if(2===f&&(e===_n?fn(n):e!==wn&&(un(n,0,0,!1),e===vn&&(jn(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),Fn(t),0===t.avail_out))return n.last_flush=-1,mn}return e!==gn?mn:n.wrap<=0?yn:(2===n.wrap?(Qn(n,255&t.adler),Qn(n,t.adler>>8&255),Qn(n,t.adler>>16&255),Qn(n,t.adler>>24&255),Qn(n,255&t.total_in),Qn(n,t.total_in>>8&255),Qn(n,t.total_in>>16&255),Qn(n,t.total_in>>24&255)):(Zn(n,t.adler>>>16),Zn(n,65535&t.adler)),Fn(t),n.wrap>0&&(n.wrap=-n.wrap),0!==n.pending?mn:yn)},deflateEnd:function(t){if(er(t))return bn;var e=t.state.status;return t.state=null,e===zn?Mn(t,kn):mn},deflateSetDictionary:function(t,e){var n=e.length;if(er(t))return bn;var r=t.state,i=r.wrap;if(2===i||1===i&&r.status!==Bn||r.lookahead)return bn;if(1===i&&(t.adler=rn(t.adler,e,n,0)),r.wrap=0,n>=r.w_size){0===i&&(jn(r.head),r.strstart=0,r.block_start=0,r.insert=0);var a=new Uint8Array(r.w_size);a.set(e.subarray(n-r.w_size,n),0),e=a,n=r.w_size}var o=t.avail_in,s=t.next_in,c=t.input;for(t.avail_in=n,t.next_in=0,t.input=e,Vn(r);r.lookahead>=3;){var d=r.strstart,u=r.lookahead-2;do{r.ins_h=Un(r,r.ins_h,r.window[d+3-1]),r.prev[d&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=d,d++}while(--u);r.strstart=d,r.lookahead=2,Vn(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,t.next_in=s,t.input=c,t.avail_in=o,r.wrap=i,mn},deflateInfo:"pako deflate (from Nodeca project)"},or=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},sr={assign:function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var n=e.shift();if(n){if("object"!==p(n))throw new TypeError(n+"must be non-object");for(var r in n)or(n,r)&&(t[r]=n[r])}}return t},flattenChunks:function(t){for(var e=0,n=0,r=t.length;n<r;n++)e+=t[n].length;for(var i=new Uint8Array(e),a=0,o=0,s=t.length;a<s;a++){var c=t[a];i.set(c,o),o+=c.length}return i}},cr=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){cr=!1}for(var dr=new Uint8Array(256),ur=0;ur<256;ur++)dr[ur]=ur>=252?6:ur>=248?5:ur>=240?4:ur>=224?3:ur>=192?2:1;dr[254]=dr[254]=1;var hr={string2buf:function(t){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);var e,n,r,i,a,o=t.length,s=0;for(i=0;i<o;i++)55296==(64512&(n=t.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=t.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(e=new Uint8Array(s),a=0,i=0;a<s;i++)55296==(64512&(n=t.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=t.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?e[a++]=n:n<2048?(e[a++]=192|n>>>6,e[a++]=128|63&n):n<65536?(e[a++]=224|n>>>12,e[a++]=128|n>>>6&63,e[a++]=128|63&n):(e[a++]=240|n>>>18,e[a++]=128|n>>>12&63,e[a++]=128|n>>>6&63,e[a++]=128|63&n);return e},buf2string:function(t,e){var n,r,i=e||t.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(t.subarray(0,e));var a=new Array(2*i);for(r=0,n=0;n<i;){var o=t[n++];if(o<128)a[r++]=o;else{var s=dr[o];if(s>4)a[r++]=65533,n+=s-1;else{for(o&=2===s?31:3===s?15:7;s>1&&n<i;)o=o<<6|63&t[n++],s--;s>1?a[r++]=65533:o<65536?a[r++]=o:(o-=65536,a[r++]=55296|o>>10&1023,a[r++]=56320|1023&o)}}}return function(t,e){if(e<65534&&t.subarray&&cr)return String.fromCharCode.apply(null,t.length===e?t:t.subarray(0,e));for(var n="",r=0;r<e;r++)n+=String.fromCharCode(t[r]);return n}(a,r)},utf8border:function(t,e){(e=e||t.length)>t.length&&(e=t.length);for(var n=e-1;n>=0&&128==(192&t[n]);)n--;return n<0||0===n?e:n+dr[t[n]]>e?n:e}};var lr=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},fr=Object.prototype.toString,pr=cn.Z_NO_FLUSH,_r=cn.Z_SYNC_FLUSH,vr=cn.Z_FULL_FLUSH,gr=cn.Z_FINISH,wr=cn.Z_OK,mr=cn.Z_STREAM_END,yr=cn.Z_DEFAULT_COMPRESSION,br=cn.Z_DEFAULT_STRATEGY,kr=cn.Z_DEFLATED;function xr(t){this.options=sr.assign({level:yr,method:kr,chunkSize:16384,windowBits:15,memLevel:8,strategy:br},t||{});var e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new lr,this.strm.avail_out=0;var n=ar.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(n!==wr)throw new Error(sn[n]);if(e.header&&ar.deflateSetHeader(this.strm,e.header),e.dictionary){var r;if(r="string"==typeof e.dictionary?hr.string2buf(e.dictionary):"[object ArrayBuffer]"===fr.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,(n=ar.deflateSetDictionary(this.strm,r))!==wr)throw new Error(sn[n]);this._dict_set=!0}}function Sr(t,e){var n=new xr(e);if(n.push(t,!0),n.err)throw n.msg||sn[n.err];return n.result}xr.prototype.push=function(t,e){var n,r,i=this.strm,a=this.options.chunkSize;if(this.ended)return!1;for(r=e===~~e?e:!0===e?gr:pr,"string"==typeof t?i.input=hr.string2buf(t):"[object ArrayBuffer]"===fr.call(t)?i.input=new Uint8Array(t):i.input=t,i.next_in=0,i.avail_in=i.input.length;;)if(0===i.avail_out&&(i.output=new Uint8Array(a),i.next_out=0,i.avail_out=a),(r===_r||r===vr)&&i.avail_out<=6)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else{if((n=ar.deflate(i,r))===mr)return i.next_out>0&&this.onData(i.output.subarray(0,i.next_out)),n=ar.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===wr;if(0!==i.avail_out){if(r>0&&i.next_out>0)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else if(0===i.avail_in)break}else this.onData(i.output)}return!0},xr.prototype.onData=function(t){this.chunks.push(t)},xr.prototype.onEnd=function(t){t===wr&&(this.result=sr.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var Er={Deflate:xr,deflate:Sr,deflateRaw:function(t,e){return(e=e||{}).raw=!0,Sr(t,e)},gzip:function(t,e){return(e=e||{}).gzip=!0,Sr(t,e)},constants:cn},Ar=16209,Rr=function(t,e){var n,r,i,a,o,s,c,d,u,h,l,f,p,_,v,g,w,m,y,b,k,x,S,E,A=t.state;n=t.next_in,S=t.input,r=n+(t.avail_in-5),i=t.next_out,E=t.output,a=i-(e-t.avail_out),o=i+(t.avail_out-257),s=A.dmax,c=A.wsize,d=A.whave,u=A.wnext,h=A.window,l=A.hold,f=A.bits,p=A.lencode,_=A.distcode,v=(1<<A.lenbits)-1,g=(1<<A.distbits)-1;t:do{f<15&&(l+=S[n++]<<f,f+=8,l+=S[n++]<<f,f+=8),w=p[l&v];e:for(;;){if(l>>>=m=w>>>24,f-=m,0===(m=w>>>16&255))E[i++]=65535&w;else{if(!(16&m)){if(0==(64&m)){w=p[(65535&w)+(l&(1<<m)-1)];continue e}if(32&m){A.mode=16191;break t}t.msg="invalid literal/length code",A.mode=Ar;break t}y=65535&w,(m&=15)&&(f<m&&(l+=S[n++]<<f,f+=8),y+=l&(1<<m)-1,l>>>=m,f-=m),f<15&&(l+=S[n++]<<f,f+=8,l+=S[n++]<<f,f+=8),w=_[l&g];n:for(;;){if(l>>>=m=w>>>24,f-=m,!(16&(m=w>>>16&255))){if(0==(64&m)){w=_[(65535&w)+(l&(1<<m)-1)];continue n}t.msg="invalid distance code",A.mode=Ar;break t}if(b=65535&w,f<(m&=15)&&(l+=S[n++]<<f,(f+=8)<m&&(l+=S[n++]<<f,f+=8)),(b+=l&(1<<m)-1)>s){t.msg="invalid distance too far back",A.mode=Ar;break t}if(l>>>=m,f-=m,b>(m=i-a)){if((m=b-m)>d&&A.sane){t.msg="invalid distance too far back",A.mode=Ar;break t}if(k=0,x=h,0===u){if(k+=c-m,m<y){y-=m;do{E[i++]=h[k++]}while(--m);k=i-b,x=E}}else if(u<m){if(k+=c+u-m,(m-=u)<y){y-=m;do{E[i++]=h[k++]}while(--m);if(k=0,u<y){y-=m=u;do{E[i++]=h[k++]}while(--m);k=i-b,x=E}}}else if(k+=u-m,m<y){y-=m;do{E[i++]=h[k++]}while(--m);k=i-b,x=E}for(;y>2;)E[i++]=x[k++],E[i++]=x[k++],E[i++]=x[k++],y-=3;y&&(E[i++]=x[k++],y>1&&(E[i++]=x[k++]))}else{k=i-b;do{E[i++]=E[k++],E[i++]=E[k++],E[i++]=E[k++],y-=3}while(y>2);y&&(E[i++]=E[k++],y>1&&(E[i++]=E[k++]))}break}}break}}while(n<r&&i<o);n-=y=f>>3,l&=(1<<(f-=y<<3))-1,t.next_in=n,t.next_out=i,t.avail_in=n<r?r-n+5:5-(n-r),t.avail_out=i<o?o-i+257:257-(i-o),A.hold=l,A.bits=f},Ir=15,Or=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),qr=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Cr=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Dr=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),Tr=function(t,e,n,r,i,a,o,s){var c,d,u,h,l,f,p,_,v,g=s.bits,w=0,m=0,y=0,b=0,k=0,x=0,S=0,E=0,A=0,R=0,I=null,O=new Uint16Array(16),q=new Uint16Array(16),C=null;for(w=0;w<=Ir;w++)O[w]=0;for(m=0;m<r;m++)O[e[n+m]]++;for(k=g,b=Ir;b>=1&&0===O[b];b--);if(k>b&&(k=b),0===b)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(y=1;y<b&&0===O[y];y++);for(k<y&&(k=y),E=1,w=1;w<=Ir;w++)if(E<<=1,(E-=O[w])<0)return-1;if(E>0&&(0===t||1!==b))return-1;for(q[1]=0,w=1;w<Ir;w++)q[w+1]=q[w]+O[w];for(m=0;m<r;m++)0!==e[n+m]&&(o[q[e[n+m]]++]=m);if(0===t?(I=C=o,f=20):1===t?(I=Or,C=qr,f=257):(I=Cr,C=Dr,f=0),R=0,m=0,w=y,l=a,x=k,S=0,u=-1,h=(A=1<<k)-1,1===t&&A>852||2===t&&A>592)return 1;for(;;){p=w-S,o[m]+1<f?(_=0,v=o[m]):o[m]>=f?(_=C[o[m]-f],v=I[o[m]-f]):(_=96,v=0),c=1<<w-S,y=d=1<<x;do{i[l+(R>>S)+(d-=c)]=p<<24|_<<16|v|0}while(0!==d);for(c=1<<w-1;R&c;)c>>=1;if(0!==c?(R&=c-1,R+=c):R=0,m++,0==--O[w]){if(w===b)break;w=e[n+o[m]]}if(w>k&&(R&h)!==u){for(0===S&&(S=k),l+=y,E=1<<(x=w-S);x+S<b&&!((E-=O[x+S])<=0);)x++,E<<=1;if(A+=1<<x,1===t&&A>852||2===t&&A>592)return 1;i[u=R&h]=k<<24|x<<16|l-a|0}}return 0!==R&&(i[l+R]=w-S<<24|64<<16|0),s.bits=k,0},Br=cn.Z_FINISH,zr=cn.Z_BLOCK,Pr=cn.Z_TREES,Mr=cn.Z_OK,Hr=cn.Z_STREAM_END,jr=cn.Z_NEED_DICT,Nr=cn.Z_STREAM_ERROR,Ur=cn.Z_DATA_ERROR,Fr=cn.Z_MEM_ERROR,Lr=cn.Z_BUF_ERROR,Qr=cn.Z_DEFLATED,Zr=16180,Kr=16190,Wr=16191,Vr=16192,$r=16194,Xr=16199,Gr=16200,Jr=16206,Yr=16209,ti=function(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)};function ei(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}var ni,ri,ii=function(t){if(!t)return 1;var e=t.state;return!e||e.strm!==t||e.mode<Zr||e.mode>16211?1:0},ai=function(t){if(ii(t))return Nr;var e=t.state;return t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=Zr,e.last=0,e.havedict=0,e.flags=-1,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new Int32Array(852),e.distcode=e.distdyn=new Int32Array(592),e.sane=1,e.back=-1,Mr},oi=function(t){if(ii(t))return Nr;var e=t.state;return e.wsize=0,e.whave=0,e.wnext=0,ai(t)},si=function(t,e){var n;if(ii(t))return Nr;var r=t.state;return e<0?(n=0,e=-e):(n=5+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?Nr:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=n,r.wbits=e,oi(t))},ci=function(t,e){if(!t)return Nr;var n=new ei;t.state=n,n.strm=t,n.window=null,n.mode=Zr;var r=si(t,e);return r!==Mr&&(t.state=null),r},di=!0,ui=function(t){if(di){ni=new Int32Array(512),ri=new Int32Array(32);for(var e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(Tr(1,t.lens,0,288,ni,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;Tr(2,t.lens,0,32,ri,0,t.work,{bits:5}),di=!1}t.lencode=ni,t.lenbits=9,t.distcode=ri,t.distbits=5},hi=function(t,e,n,r){var i,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Uint8Array(a.wsize)),r>=a.wsize?(a.window.set(e.subarray(n-a.wsize,n),0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>r&&(i=r),a.window.set(e.subarray(n-r,n-r+i),a.wnext),(r-=i)?(a.window.set(e.subarray(n-r,n),0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0},li={inflateReset:oi,inflateReset2:si,inflateResetKeep:ai,inflateInit:function(t){return ci(t,15)},inflateInit2:ci,inflate:function(t,e){var n,r,i,a,o,s,c,d,u,h,l,f,p,_,v,g,w,m,y,b,k,x,S,E,A=0,R=new Uint8Array(4),I=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(ii(t)||!t.output||!t.input&&0!==t.avail_in)return Nr;(n=t.state).mode===Wr&&(n.mode=Vr),o=t.next_out,i=t.output,c=t.avail_out,a=t.next_in,r=t.input,s=t.avail_in,d=n.hold,u=n.bits,h=s,l=c,x=Mr;t:for(;;)switch(n.mode){case Zr:if(0===n.wrap){n.mode=Vr;break}for(;u<16;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(2&n.wrap&&35615===d){0===n.wbits&&(n.wbits=15),n.check=0,R[0]=255&d,R[1]=d>>>8&255,n.check=on(n.check,R,2,0),d=0,u=0,n.mode=16181;break}if(n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&d)<<8)+(d>>8))%31){t.msg="incorrect header check",n.mode=Yr;break}if((15&d)!==Qr){t.msg="unknown compression method",n.mode=Yr;break}if(u-=4,k=8+(15&(d>>>=4)),0===n.wbits&&(n.wbits=k),k>15||k>n.wbits){t.msg="invalid window size",n.mode=Yr;break}n.dmax=1<<n.wbits,n.flags=0,t.adler=n.check=1,n.mode=512&d?16189:Wr,d=0,u=0;break;case 16181:for(;u<16;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(n.flags=d,(255&n.flags)!==Qr){t.msg="unknown compression method",n.mode=Yr;break}if(57344&n.flags){t.msg="unknown header flags set",n.mode=Yr;break}n.head&&(n.head.text=d>>8&1),512&n.flags&&4&n.wrap&&(R[0]=255&d,R[1]=d>>>8&255,n.check=on(n.check,R,2,0)),d=0,u=0,n.mode=16182;case 16182:for(;u<32;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.head&&(n.head.time=d),512&n.flags&&4&n.wrap&&(R[0]=255&d,R[1]=d>>>8&255,R[2]=d>>>16&255,R[3]=d>>>24&255,n.check=on(n.check,R,4,0)),d=0,u=0,n.mode=16183;case 16183:for(;u<16;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.head&&(n.head.xflags=255&d,n.head.os=d>>8),512&n.flags&&4&n.wrap&&(R[0]=255&d,R[1]=d>>>8&255,n.check=on(n.check,R,2,0)),d=0,u=0,n.mode=16184;case 16184:if(1024&n.flags){for(;u<16;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.length=d,n.head&&(n.head.extra_len=d),512&n.flags&&4&n.wrap&&(R[0]=255&d,R[1]=d>>>8&255,n.check=on(n.check,R,2,0)),d=0,u=0}else n.head&&(n.head.extra=null);n.mode=16185;case 16185:if(1024&n.flags&&((f=n.length)>s&&(f=s),f&&(n.head&&(k=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(r.subarray(a,a+f),k)),512&n.flags&&4&n.wrap&&(n.check=on(n.check,r,f,a)),s-=f,a+=f,n.length-=f),n.length))break t;n.length=0,n.mode=16186;case 16186:if(2048&n.flags){if(0===s)break t;f=0;do{k=r[a+f++],n.head&&k&&n.length<65536&&(n.head.name+=String.fromCharCode(k))}while(k&&f<s);if(512&n.flags&&4&n.wrap&&(n.check=on(n.check,r,f,a)),s-=f,a+=f,k)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=16187;case 16187:if(4096&n.flags){if(0===s)break t;f=0;do{k=r[a+f++],n.head&&k&&n.length<65536&&(n.head.comment+=String.fromCharCode(k))}while(k&&f<s);if(512&n.flags&&4&n.wrap&&(n.check=on(n.check,r,f,a)),s-=f,a+=f,k)break t}else n.head&&(n.head.comment=null);n.mode=16188;case 16188:if(512&n.flags){for(;u<16;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(4&n.wrap&&d!==(65535&n.check)){t.msg="header crc mismatch",n.mode=Yr;break}d=0,u=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),t.adler=n.check=0,n.mode=Wr;break;case 16189:for(;u<32;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}t.adler=n.check=ti(d),d=0,u=0,n.mode=Kr;case Kr:if(0===n.havedict)return t.next_out=o,t.avail_out=c,t.next_in=a,t.avail_in=s,n.hold=d,n.bits=u,jr;t.adler=n.check=1,n.mode=Wr;case Wr:if(e===zr||e===Pr)break t;case Vr:if(n.last){d>>>=7&u,u-=7&u,n.mode=Jr;break}for(;u<3;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}switch(n.last=1&d,u-=1,3&(d>>>=1)){case 0:n.mode=16193;break;case 1:if(ui(n),n.mode=Xr,e===Pr){d>>>=2,u-=2;break t}break;case 2:n.mode=16196;break;case 3:t.msg="invalid block type",n.mode=Yr}d>>>=2,u-=2;break;case 16193:for(d>>>=7&u,u-=7&u;u<32;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if((65535&d)!=(d>>>16^65535)){t.msg="invalid stored block lengths",n.mode=Yr;break}if(n.length=65535&d,d=0,u=0,n.mode=$r,e===Pr)break t;case $r:n.mode=16195;case 16195:if(f=n.length){if(f>s&&(f=s),f>c&&(f=c),0===f)break t;i.set(r.subarray(a,a+f),o),s-=f,a+=f,c-=f,o+=f,n.length-=f;break}n.mode=Wr;break;case 16196:for(;u<14;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(n.nlen=257+(31&d),d>>>=5,u-=5,n.ndist=1+(31&d),d>>>=5,u-=5,n.ncode=4+(15&d),d>>>=4,u-=4,n.nlen>286||n.ndist>30){t.msg="too many length or distance symbols",n.mode=Yr;break}n.have=0,n.mode=16197;case 16197:for(;n.have<n.ncode;){for(;u<3;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.lens[I[n.have++]]=7&d,d>>>=3,u-=3}for(;n.have<19;)n.lens[I[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,S={bits:n.lenbits},x=Tr(0,n.lens,0,19,n.lencode,0,n.work,S),n.lenbits=S.bits,x){t.msg="invalid code lengths set",n.mode=Yr;break}n.have=0,n.mode=16198;case 16198:for(;n.have<n.nlen+n.ndist;){for(;g=(A=n.lencode[d&(1<<n.lenbits)-1])>>>16&255,w=65535&A,!((v=A>>>24)<=u);){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(w<16)d>>>=v,u-=v,n.lens[n.have++]=w;else{if(16===w){for(E=v+2;u<E;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(d>>>=v,u-=v,0===n.have){t.msg="invalid bit length repeat",n.mode=Yr;break}k=n.lens[n.have-1],f=3+(3&d),d>>>=2,u-=2}else if(17===w){for(E=v+3;u<E;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}u-=v,k=0,f=3+(7&(d>>>=v)),d>>>=3,u-=3}else{for(E=v+7;u<E;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}u-=v,k=0,f=11+(127&(d>>>=v)),d>>>=7,u-=7}if(n.have+f>n.nlen+n.ndist){t.msg="invalid bit length repeat",n.mode=Yr;break}for(;f--;)n.lens[n.have++]=k}}if(n.mode===Yr)break;if(0===n.lens[256]){t.msg="invalid code -- missing end-of-block",n.mode=Yr;break}if(n.lenbits=9,S={bits:n.lenbits},x=Tr(1,n.lens,0,n.nlen,n.lencode,0,n.work,S),n.lenbits=S.bits,x){t.msg="invalid literal/lengths set",n.mode=Yr;break}if(n.distbits=6,n.distcode=n.distdyn,S={bits:n.distbits},x=Tr(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,S),n.distbits=S.bits,x){t.msg="invalid distances set",n.mode=Yr;break}if(n.mode=Xr,e===Pr)break t;case Xr:n.mode=Gr;case Gr:if(s>=6&&c>=258){t.next_out=o,t.avail_out=c,t.next_in=a,t.avail_in=s,n.hold=d,n.bits=u,Rr(t,l),o=t.next_out,i=t.output,c=t.avail_out,a=t.next_in,r=t.input,s=t.avail_in,d=n.hold,u=n.bits,n.mode===Wr&&(n.back=-1);break}for(n.back=0;g=(A=n.lencode[d&(1<<n.lenbits)-1])>>>16&255,w=65535&A,!((v=A>>>24)<=u);){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(g&&0==(240&g)){for(m=v,y=g,b=w;g=(A=n.lencode[b+((d&(1<<m+y)-1)>>m)])>>>16&255,w=65535&A,!(m+(v=A>>>24)<=u);){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}d>>>=m,u-=m,n.back+=m}if(d>>>=v,u-=v,n.back+=v,n.length=w,0===g){n.mode=16205;break}if(32&g){n.back=-1,n.mode=Wr;break}if(64&g){t.msg="invalid literal/length code",n.mode=Yr;break}n.extra=15&g,n.mode=16201;case 16201:if(n.extra){for(E=n.extra;u<E;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.length+=d&(1<<n.extra)-1,d>>>=n.extra,u-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=16202;case 16202:for(;g=(A=n.distcode[d&(1<<n.distbits)-1])>>>16&255,w=65535&A,!((v=A>>>24)<=u);){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(0==(240&g)){for(m=v,y=g,b=w;g=(A=n.distcode[b+((d&(1<<m+y)-1)>>m)])>>>16&255,w=65535&A,!(m+(v=A>>>24)<=u);){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}d>>>=m,u-=m,n.back+=m}if(d>>>=v,u-=v,n.back+=v,64&g){t.msg="invalid distance code",n.mode=Yr;break}n.offset=w,n.extra=15&g,n.mode=16203;case 16203:if(n.extra){for(E=n.extra;u<E;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}n.offset+=d&(1<<n.extra)-1,d>>>=n.extra,u-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){t.msg="invalid distance too far back",n.mode=Yr;break}n.mode=16204;case 16204:if(0===c)break t;if(f=l-c,n.offset>f){if((f=n.offset-f)>n.whave&&n.sane){t.msg="invalid distance too far back",n.mode=Yr;break}f>n.wnext?(f-=n.wnext,p=n.wsize-f):p=n.wnext-f,f>n.length&&(f=n.length),_=n.window}else _=i,p=o-n.offset,f=n.length;f>c&&(f=c),c-=f,n.length-=f;do{i[o++]=_[p++]}while(--f);0===n.length&&(n.mode=Gr);break;case 16205:if(0===c)break t;i[o++]=n.length,c--,n.mode=Gr;break;case Jr:if(n.wrap){for(;u<32;){if(0===s)break t;s--,d|=r[a++]<<u,u+=8}if(l-=c,t.total_out+=l,n.total+=l,4&n.wrap&&l&&(t.adler=n.check=n.flags?on(n.check,i,l,o-l):rn(n.check,i,l,o-l)),l=c,4&n.wrap&&(n.flags?d:ti(d))!==n.check){t.msg="incorrect data check",n.mode=Yr;break}d=0,u=0}n.mode=16207;case 16207:if(n.wrap&&n.flags){for(;u<32;){if(0===s)break t;s--,d+=r[a++]<<u,u+=8}if(4&n.wrap&&d!==(4294967295&n.total)){t.msg="incorrect length check",n.mode=Yr;break}d=0,u=0}n.mode=16208;case 16208:x=Hr;break t;case Yr:x=Ur;break t;case 16210:return Fr;default:return Nr}return t.next_out=o,t.avail_out=c,t.next_in=a,t.avail_in=s,n.hold=d,n.bits=u,(n.wsize||l!==t.avail_out&&n.mode<Yr&&(n.mode<Jr||e!==Br))&&hi(t,t.output,t.next_out,l-t.avail_out),h-=t.avail_in,l-=t.avail_out,t.total_in+=h,t.total_out+=l,n.total+=l,4&n.wrap&&l&&(t.adler=n.check=n.flags?on(n.check,i,l,t.next_out-l):rn(n.check,i,l,t.next_out-l)),t.data_type=n.bits+(n.last?64:0)+(n.mode===Wr?128:0)+(n.mode===Xr||n.mode===$r?256:0),(0===h&&0===l||e===Br)&&x===Mr&&(x=Lr),x},inflateEnd:function(t){if(ii(t))return Nr;var e=t.state;return e.window&&(e.window=null),t.state=null,Mr},inflateGetHeader:function(t,e){if(ii(t))return Nr;var n=t.state;return 0==(2&n.wrap)?Nr:(n.head=e,e.done=!1,Mr)},inflateSetDictionary:function(t,e){var n,r=e.length;return ii(t)||0!==(n=t.state).wrap&&n.mode!==Kr?Nr:n.mode===Kr&&rn(1,e,r,0)!==n.check?Ur:hi(t,e,r,r)?(n.mode=16210,Fr):(n.havedict=1,Mr)},inflateInfo:"pako inflate (from Nodeca project)"};var fi=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},pi=Object.prototype.toString,_i=cn.Z_NO_FLUSH,vi=cn.Z_FINISH,gi=cn.Z_OK,wi=cn.Z_STREAM_END,mi=cn.Z_NEED_DICT,yi=cn.Z_STREAM_ERROR,bi=cn.Z_DATA_ERROR,ki=cn.Z_MEM_ERROR;function xi(t){this.options=sr.assign({chunkSize:65536,windowBits:15,to:""},t||{});var e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new lr,this.strm.avail_out=0;var n=li.inflateInit2(this.strm,e.windowBits);if(n!==gi)throw new Error(sn[n]);if(this.header=new fi,li.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=hr.string2buf(e.dictionary):"[object ArrayBuffer]"===pi.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(n=li.inflateSetDictionary(this.strm,e.dictionary))!==gi))throw new Error(sn[n])}function Si(t,e){var n=new xi(e);if(n.push(t),n.err)throw n.msg||sn[n.err];return n.result}xi.prototype.push=function(t,e){var n,r,i,a=this.strm,o=this.options.chunkSize,s=this.options.dictionary;if(this.ended)return!1;for(r=e===~~e?e:!0===e?vi:_i,"[object ArrayBuffer]"===pi.call(t)?a.input=new Uint8Array(t):a.input=t,a.next_in=0,a.avail_in=a.input.length;;){for(0===a.avail_out&&(a.output=new Uint8Array(o),a.next_out=0,a.avail_out=o),(n=li.inflate(a,r))===mi&&s&&((n=li.inflateSetDictionary(a,s))===gi?n=li.inflate(a,r):n===bi&&(n=mi));a.avail_in>0&&n===wi&&a.state.wrap>0&&0!==t[a.next_in];)li.inflateReset(a),n=li.inflate(a,r);switch(n){case yi:case bi:case mi:case ki:return this.onEnd(n),this.ended=!0,!1}if(i=a.avail_out,a.next_out&&(0===a.avail_out||n===wi))if("string"===this.options.to){var c=hr.utf8border(a.output,a.next_out),d=a.next_out-c,u=hr.buf2string(a.output,c);a.next_out=d,a.avail_out=o-d,d&&a.output.set(a.output.subarray(c,c+d),0),this.onData(u)}else this.onData(a.output.length===a.next_out?a.output:a.output.subarray(0,a.next_out));if(n!==gi||0!==i){if(n===wi)return n=li.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,!0;if(0===a.avail_in)break}}return!0},xi.prototype.onData=function(t){this.chunks.push(t)},xi.prototype.onEnd=function(t){t===gi&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=sr.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var Ei={Inflate:xi,inflate:Si,inflateRaw:function(t,e){return(e=e||{}).raw=!0,Si(t,e)},ungzip:Si,constants:cn},Ai={Deflate:Er.Deflate,deflate:Er.deflate,deflateRaw:Er.deflateRaw,gzip:Er.gzip,Inflate:Ei.Inflate,inflate:Ei.inflate,inflateRaw:Ei.inflateRaw,ungzip:Ei.ungzip,constants:cn},Ri=8;function Ii(t){return function(t){return function(t){for(var e="0123456789ABCDEF",n="",r=0;r<4*t.length;r++)n+=e.charAt(t[r>>2]>>r%4*8+4&15)+e.charAt(t[r>>2]>>r%4*8&15);return n}(function(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;for(var n=1732584193,r=-271733879,i=-1732584194,a=271733878,o=0;o<t.length;o+=16){var s=n,c=r,d=i,u=a;n=qi(n,r,i,a,t[o+0],7,-680876936),a=qi(a,n,r,i,t[o+1],12,-389564586),i=qi(i,a,n,r,t[o+2],17,606105819),r=qi(r,i,a,n,t[o+3],22,-1044525330),n=qi(n,r,i,a,t[o+4],7,-176418897),a=qi(a,n,r,i,t[o+5],12,1200080426),i=qi(i,a,n,r,t[o+6],17,-1473231341),r=qi(r,i,a,n,t[o+7],22,-45705983),n=qi(n,r,i,a,t[o+8],7,1770035416),a=qi(a,n,r,i,t[o+9],12,-1958414417),i=qi(i,a,n,r,t[o+10],17,-42063),r=qi(r,i,a,n,t[o+11],22,-1990404162),n=qi(n,r,i,a,t[o+12],7,1804603682),a=qi(a,n,r,i,t[o+13],12,-40341101),i=qi(i,a,n,r,t[o+14],17,-1502002290),n=Ci(n,r=qi(r,i,a,n,t[o+15],22,1236535329),i,a,t[o+1],5,-165796510),a=Ci(a,n,r,i,t[o+6],9,-1069501632),i=Ci(i,a,n,r,t[o+11],14,643717713),r=Ci(r,i,a,n,t[o+0],20,-373897302),n=Ci(n,r,i,a,t[o+5],5,-701558691),a=Ci(a,n,r,i,t[o+10],9,38016083),i=Ci(i,a,n,r,t[o+15],14,-660478335),r=Ci(r,i,a,n,t[o+4],20,-405537848),n=Ci(n,r,i,a,t[o+9],5,568446438),a=Ci(a,n,r,i,t[o+14],9,-1019803690),i=Ci(i,a,n,r,t[o+3],14,-187363961),r=Ci(r,i,a,n,t[o+8],20,1163531501),n=Ci(n,r,i,a,t[o+13],5,-1444681467),a=Ci(a,n,r,i,t[o+2],9,-51403784),i=Ci(i,a,n,r,t[o+7],14,1735328473),n=Di(n,r=Ci(r,i,a,n,t[o+12],20,-1926607734),i,a,t[o+5],4,-378558),a=Di(a,n,r,i,t[o+8],11,-2022574463),i=Di(i,a,n,r,t[o+11],16,1839030562),r=Di(r,i,a,n,t[o+14],23,-35309556),n=Di(n,r,i,a,t[o+1],4,-1530992060),a=Di(a,n,r,i,t[o+4],11,1272893353),i=Di(i,a,n,r,t[o+7],16,-155497632),r=Di(r,i,a,n,t[o+10],23,-1094730640),n=Di(n,r,i,a,t[o+13],4,681279174),a=Di(a,n,r,i,t[o+0],11,-358537222),i=Di(i,a,n,r,t[o+3],16,-722521979),r=Di(r,i,a,n,t[o+6],23,76029189),n=Di(n,r,i,a,t[o+9],4,-640364487),a=Di(a,n,r,i,t[o+12],11,-421815835),i=Di(i,a,n,r,t[o+15],16,530742520),n=Ti(n,r=Di(r,i,a,n,t[o+2],23,-995338651),i,a,t[o+0],6,-198630844),a=Ti(a,n,r,i,t[o+7],10,1126891415),i=Ti(i,a,n,r,t[o+14],15,-1416354905),r=Ti(r,i,a,n,t[o+5],21,-57434055),n=Ti(n,r,i,a,t[o+12],6,1700485571),a=Ti(a,n,r,i,t[o+3],10,-1894986606),i=Ti(i,a,n,r,t[o+10],15,-1051523),r=Ti(r,i,a,n,t[o+1],21,-2054922799),n=Ti(n,r,i,a,t[o+8],6,1873313359),a=Ti(a,n,r,i,t[o+15],10,-30611744),i=Ti(i,a,n,r,t[o+6],15,-1560198380),r=Ti(r,i,a,n,t[o+13],21,1309151649),n=Ti(n,r,i,a,t[o+4],6,-145523070),a=Ti(a,n,r,i,t[o+11],10,-1120210379),i=Ti(i,a,n,r,t[o+2],15,718787259),r=Ti(r,i,a,n,t[o+9],21,-343485551),n=Bi(n,s),r=Bi(r,c),i=Bi(i,d),a=Bi(a,u)}return Array(n,r,i,a)}(function(t){for(var e=[],n=(1<<Ri)-1,r=0;r<t.length*Ri;r+=Ri)e[r>>5]|=(t.charCodeAt(r/Ri)&n)<<r%32;return e}(t),t.length*Ri))}(t)}function Oi(t,e,n,r,i,a){return Bi((o=Bi(Bi(e,t),Bi(r,a)))<<(s=i)|o>>>32-s,n);var o,s}function qi(t,e,n,r,i,a,o){return Oi(e&n|~e&r,t,e,i,a,o)}function Ci(t,e,n,r,i,a,o){return Oi(e&r|n&~r,t,e,i,a,o)}function Di(t,e,n,r,i,a,o){return Oi(e^n^r,t,e,i,a,o)}function Ti(t,e,n,r,i,a,o){return Oi(n^(e|~r),t,e,i,a,o)}function Bi(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}var zi="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Pi(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Mi(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var n=function t(){if(this instanceof t){var n=[null];return n.push.apply(n,arguments),new(Function.bind.apply(e,n))}return e.apply(this,arguments)};n.prototype=e.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,r.get?r:{enumerable:!0,get:function(){return t[e]}})})),n}var Hi={exports:{}};var ji,Ni={exports:{}},Ui=Mi(Object.freeze({__proto__:null,default:{}}));function Fi(){return ji||(ji=1,t=Ni,Ni.exports,t.exports=function(){var t=t||function(t,e){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==zi&&zi.crypto&&(n=zi.crypto),!n)try{n=Ui}catch(t){}var r=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function t(){}return function(e){var n;return t.prototype=e,n=new t,t.prototype=null,n}}(),a={},o=a.lib={},s=o.Base={extend:function(t){var e=i(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=s.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:4*t.length},toString:function(t){return(t||u).stringify(this)},concat:function(t){var e=this.words,n=t.words,r=this.sigBytes,i=t.sigBytes;if(this.clamp(),r%4)for(var a=0;a<i;a++){var o=n[a>>>2]>>>24-a%4*8&255;e[r+a>>>2]|=o<<24-(r+a)%4*8}else for(var s=0;s<i;s+=4)e[r+s>>>2]=n[s>>>2];return this.sigBytes+=i,this},clamp:function(){var e=this.words,n=this.sigBytes;e[n>>>2]&=4294967295<<32-n%4*8,e.length=t.ceil(n/4)},clone:function(){var t=s.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],n=0;n<t;n+=4)e.push(r());return new c.init(e,t)}}),d=a.enc={},u=d.Hex={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],i=0;i<n;i++){var a=e[i>>>2]>>>24-i%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;r<e;r+=2)n[r>>>3]|=parseInt(t.substr(r,2),16)<<24-r%8*4;return new c.init(n,e/2)}},h=d.Latin1={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],i=0;i<n;i++){var a=e[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(a))}return r.join("")},parse:function(t){for(var e=t.length,n=[],r=0;r<e;r++)n[r>>>2]|=(255&t.charCodeAt(r))<<24-r%4*8;return new c.init(n,e)}},l=d.Utf8={stringify:function(t){try{return decodeURIComponent(escape(h.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return h.parse(unescape(encodeURIComponent(t)))}},f=o.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=l.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var n,r=this._data,i=r.words,a=r.sigBytes,o=this.blockSize,s=a/(4*o),d=(s=e?t.ceil(s):t.max((0|s)-this._minBufferSize,0))*o,u=t.min(4*d,a);if(d){for(var h=0;h<d;h+=o)this._doProcessBlock(i,h);n=i.splice(0,d),r.sigBytes-=u}return new c.init(n,u)},clone:function(){var t=s.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});o.Hasher=f.extend({cfg:s.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,n){return new t.init(n).finalize(e)}},_createHmacHelper:function(t){return function(e,n){return new p.HMAC.init(t,n).finalize(e)}}});var p=a.algo={};return a}(Math);return t}()),Ni.exports;var t}Ni.exports;var Li,Qi={exports:{}};function Zi(){return Li||(Li=1,t=Qi,Qi.exports,t.exports=function(t){return r=(n=t).lib,i=r.Base,a=r.WordArray,(o=n.x64={}).Word=i.extend({init:function(t,e){this.high=t,this.low=e}}),o.WordArray=i.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:8*t.length},toX32:function(){for(var t=this.words,e=t.length,n=[],r=0;r<e;r++){var i=t[r];n.push(i.high),n.push(i.low)}return a.create(n,this.sigBytes)},clone:function(){for(var t=i.clone.call(this),e=t.words=this.words.slice(0),n=e.length,r=0;r<n;r++)e[r]=e[r].clone();return t}}),t;var e,n,r,i,a,o}(Fi())),Qi.exports;var t}Qi.exports;var Ki,Wi={exports:{}};function Vi(){return Ki||(Ki=1,Wi.exports=function(t){return function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,n=e.init,r=e.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var e=t.byteLength,r=[],i=0;i<e;i++)r[i>>>2]|=t[i]<<24-i%4*8;n.call(this,r,e)}else n.apply(this,arguments)};r.prototype=e}}(),t.lib.WordArray}(Fi())),Wi.exports}var $i,Xi={exports:{}};function Gi(){return $i||($i=1,Xi.exports=function(t){return function(){var e=t,n=e.lib.WordArray,r=e.enc;function i(t){return t<<8&4278255360|t>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],i=0;i<n;i+=2){var a=e[i>>>2]>>>16-i%4*8&65535;r.push(String.fromCharCode(a))}return r.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>1]|=t.charCodeAt(i)<<16-i%2*16;return n.create(r,2*e)}},r.Utf16LE={stringify:function(t){for(var e=t.words,n=t.sigBytes,r=[],a=0;a<n;a+=2){var o=i(e[a>>>2]>>>16-a%4*8&65535);r.push(String.fromCharCode(o))}return r.join("")},parse:function(t){for(var e=t.length,r=[],a=0;a<e;a++)r[a>>>1]|=i(t.charCodeAt(a)<<16-a%2*16);return n.create(r,2*e)}}}(),t.enc.Utf16}(Fi())),Xi.exports}var Ji,Yi={exports:{}};function ta(){return Ji||(Ji=1,t=Yi,Yi.exports,t.exports=function(t){return function(){var e=t,n=e.lib.WordArray;function r(t,e,r){for(var i=[],a=0,o=0;o<e;o++)if(o%4){var s=r[t.charCodeAt(o-1)]<<o%4*2|r[t.charCodeAt(o)]>>>6-o%4*2;i[a>>>2]|=s<<24-a%4*8,a++}return n.create(i,a)}e.enc.Base64={stringify:function(t){var e=t.words,n=t.sigBytes,r=this._map;t.clamp();for(var i=[],a=0;a<n;a+=3)for(var o=(e[a>>>2]>>>24-a%4*8&255)<<16|(e[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|e[a+2>>>2]>>>24-(a+2)%4*8&255,s=0;s<4&&a+.75*s<n;s++)i.push(r.charAt(o>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(t){var e=t.length,n=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var a=0;a<n.length;a++)i[n.charCodeAt(a)]=a}var o=n.charAt(64);if(o){var s=t.indexOf(o);-1!==s&&(e=s)}return r(t,e,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),t.enc.Base64}(Fi())),Yi.exports;var t}Yi.exports;var ea,na={exports:{}};function ra(){return ea||(ea=1,na.exports=function(t){return function(){var e=t,n=e.lib.WordArray;function r(t,e,r){for(var i=[],a=0,o=0;o<e;o++)if(o%4){var s=r[t.charCodeAt(o-1)]<<o%4*2|r[t.charCodeAt(o)]>>>6-o%4*2;i[a>>>2]|=s<<24-a%4*8,a++}return n.create(i,a)}e.enc.Base64url={stringify:function(t,e){void 0===e&&(e=!0);var n=t.words,r=t.sigBytes,i=e?this._safe_map:this._map;t.clamp();for(var a=[],o=0;o<r;o+=3)for(var s=(n[o>>>2]>>>24-o%4*8&255)<<16|(n[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|n[o+2>>>2]>>>24-(o+2)%4*8&255,c=0;c<4&&o+.75*c<r;c++)a.push(i.charAt(s>>>6*(3-c)&63));var d=i.charAt(64);if(d)for(;a.length%4;)a.push(d);return a.join("")},parse:function(t,e){void 0===e&&(e=!0);var n=t.length,i=e?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var o=0;o<i.length;o++)a[i.charCodeAt(o)]=o}var s=i.charAt(64);if(s){var c=t.indexOf(s);-1!==c&&(n=c)}return r(t,n,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),t.enc.Base64url}(Fi())),na.exports}var ia,aa={exports:{}};function oa(){return ia||(ia=1,t=aa,aa.exports,t.exports=function(t){return function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,o=n.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=o.MD5=a.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var n=0;n<16;n++){var r=e+n,i=t[r];t[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var a=this._hash.words,o=t[e+0],c=t[e+1],f=t[e+2],p=t[e+3],_=t[e+4],v=t[e+5],g=t[e+6],w=t[e+7],m=t[e+8],y=t[e+9],b=t[e+10],k=t[e+11],x=t[e+12],S=t[e+13],E=t[e+14],A=t[e+15],R=a[0],I=a[1],O=a[2],q=a[3];R=d(R,I,O,q,o,7,s[0]),q=d(q,R,I,O,c,12,s[1]),O=d(O,q,R,I,f,17,s[2]),I=d(I,O,q,R,p,22,s[3]),R=d(R,I,O,q,_,7,s[4]),q=d(q,R,I,O,v,12,s[5]),O=d(O,q,R,I,g,17,s[6]),I=d(I,O,q,R,w,22,s[7]),R=d(R,I,O,q,m,7,s[8]),q=d(q,R,I,O,y,12,s[9]),O=d(O,q,R,I,b,17,s[10]),I=d(I,O,q,R,k,22,s[11]),R=d(R,I,O,q,x,7,s[12]),q=d(q,R,I,O,S,12,s[13]),O=d(O,q,R,I,E,17,s[14]),R=u(R,I=d(I,O,q,R,A,22,s[15]),O,q,c,5,s[16]),q=u(q,R,I,O,g,9,s[17]),O=u(O,q,R,I,k,14,s[18]),I=u(I,O,q,R,o,20,s[19]),R=u(R,I,O,q,v,5,s[20]),q=u(q,R,I,O,b,9,s[21]),O=u(O,q,R,I,A,14,s[22]),I=u(I,O,q,R,_,20,s[23]),R=u(R,I,O,q,y,5,s[24]),q=u(q,R,I,O,E,9,s[25]),O=u(O,q,R,I,p,14,s[26]),I=u(I,O,q,R,m,20,s[27]),R=u(R,I,O,q,S,5,s[28]),q=u(q,R,I,O,f,9,s[29]),O=u(O,q,R,I,w,14,s[30]),R=h(R,I=u(I,O,q,R,x,20,s[31]),O,q,v,4,s[32]),q=h(q,R,I,O,m,11,s[33]),O=h(O,q,R,I,k,16,s[34]),I=h(I,O,q,R,E,23,s[35]),R=h(R,I,O,q,c,4,s[36]),q=h(q,R,I,O,_,11,s[37]),O=h(O,q,R,I,w,16,s[38]),I=h(I,O,q,R,b,23,s[39]),R=h(R,I,O,q,S,4,s[40]),q=h(q,R,I,O,o,11,s[41]),O=h(O,q,R,I,p,16,s[42]),I=h(I,O,q,R,g,23,s[43]),R=h(R,I,O,q,y,4,s[44]),q=h(q,R,I,O,x,11,s[45]),O=h(O,q,R,I,A,16,s[46]),R=l(R,I=h(I,O,q,R,f,23,s[47]),O,q,o,6,s[48]),q=l(q,R,I,O,w,10,s[49]),O=l(O,q,R,I,E,15,s[50]),I=l(I,O,q,R,v,21,s[51]),R=l(R,I,O,q,x,6,s[52]),q=l(q,R,I,O,p,10,s[53]),O=l(O,q,R,I,b,15,s[54]),I=l(I,O,q,R,c,21,s[55]),R=l(R,I,O,q,m,6,s[56]),q=l(q,R,I,O,A,10,s[57]),O=l(O,q,R,I,g,15,s[58]),I=l(I,O,q,R,S,21,s[59]),R=l(R,I,O,q,_,6,s[60]),q=l(q,R,I,O,k,10,s[61]),O=l(O,q,R,I,f,15,s[62]),I=l(I,O,q,R,y,21,s[63]),a[0]=a[0]+R|0,a[1]=a[1]+I|0,a[2]=a[2]+O|0,a[3]=a[3]+q|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var a=e.floor(r/4294967296),o=r;n[15+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n[14+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,d=0;d<4;d++){var u=c[d];c[d]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}});function d(t,e,n,r,i,a,o){var s=t+(e&n|~e&r)+i+o;return(s<<a|s>>>32-a)+e}function u(t,e,n,r,i,a,o){var s=t+(e&r|n&~r)+i+o;return(s<<a|s>>>32-a)+e}function h(t,e,n,r,i,a,o){var s=t+(e^n^r)+i+o;return(s<<a|s>>>32-a)+e}function l(t,e,n,r,i,a,o){var s=t+(n^(e|~r))+i+o;return(s<<a|s>>>32-a)+e}n.MD5=a._createHelper(c),n.HmacMD5=a._createHmacHelper(c)}(Math),t.MD5}(Fi())),aa.exports;var t}aa.exports;var sa,ca={exports:{}};function da(){return sa||(sa=1,t=ca,ca.exports,t.exports=function(t){return n=(e=t).lib,r=n.WordArray,i=n.Hasher,a=e.algo,o=[],s=a.SHA1=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],s=n[3],c=n[4],d=0;d<80;d++){if(d<16)o[d]=0|t[e+d];else{var u=o[d-3]^o[d-8]^o[d-14]^o[d-16];o[d]=u<<1|u>>>31}var h=(r<<5|r>>>27)+c+o[d];h+=d<20?1518500249+(i&a|~i&s):d<40?1859775393+(i^a^s):d<60?(i&a|i&s|a&s)-1894007588:(i^a^s)-899497514,c=s,s=a,a=i<<30|i>>>2,i=r,r=h}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+a|0,n[3]=n[3]+s|0,n[4]=n[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;return e[r>>>5]|=128<<24-r%32,e[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),e[15+(r+64>>>9<<4)]=n,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}}),e.SHA1=i._createHelper(s),e.HmacSHA1=i._createHmacHelper(s),t.SHA1;var e,n,r,i,a,o,s}(Fi())),ca.exports;var t}ca.exports;var ua,ha={exports:{}};function la(){return ua||(ua=1,t=ha,ha.exports,t.exports=function(t){return function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,o=n.algo,s=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(t){return 4294967296*(t-(0|t))|0}for(var r=2,i=0;i<64;)t(r)&&(i<8&&(s[i]=n(e.pow(r,.5))),c[i]=n(e.pow(r,1/3)),i++),r++}();var d=[],u=o.SHA256=a.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(t,e){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],o=n[3],s=n[4],u=n[5],h=n[6],l=n[7],f=0;f<64;f++){if(f<16)d[f]=0|t[e+f];else{var p=d[f-15],_=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=d[f-2],g=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;d[f]=_+d[f-7]+g+d[f-16]}var w=r&i^r&a^i&a,m=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),y=l+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&u^~s&h)+c[f]+d[f];l=h,h=u,u=s,s=o+y|0,o=a,a=i,i=r,r=y+(m+w)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+a|0,n[3]=n[3]+o|0,n[4]=n[4]+s|0,n[5]=n[5]+u|0,n[6]=n[6]+h|0,n[7]=n[7]+l|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=e.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}});n.SHA256=a._createHelper(u),n.HmacSHA256=a._createHmacHelper(u)}(Math),t.SHA256}(Fi())),ha.exports;var t}ha.exports;var fa,pa={exports:{}};var _a,va={exports:{}};function ga(){return _a||(_a=1,t=va,va.exports,t.exports=function(t){return function(){var e=t,n=e.lib.Hasher,r=e.x64,i=r.Word,a=r.WordArray,o=e.algo;function s(){return i.create.apply(i,arguments)}var c=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],d=[];!function(){for(var t=0;t<80;t++)d[t]=s()}();var u=o.SHA512=n.extend({_doReset:function(){this._hash=new a.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],o=n[3],s=n[4],u=n[5],h=n[6],l=n[7],f=r.high,p=r.low,_=i.high,v=i.low,g=a.high,w=a.low,m=o.high,y=o.low,b=s.high,k=s.low,x=u.high,S=u.low,E=h.high,A=h.low,R=l.high,I=l.low,O=f,q=p,C=_,D=v,T=g,B=w,z=m,P=y,M=b,H=k,j=x,N=S,U=E,F=A,L=R,Q=I,Z=0;Z<80;Z++){var K,W,V=d[Z];if(Z<16)W=V.high=0|t[e+2*Z],K=V.low=0|t[e+2*Z+1];else{var $=d[Z-15],X=$.high,G=$.low,J=(X>>>1|G<<31)^(X>>>8|G<<24)^X>>>7,Y=(G>>>1|X<<31)^(G>>>8|X<<24)^(G>>>7|X<<25),tt=d[Z-2],et=tt.high,nt=tt.low,rt=(et>>>19|nt<<13)^(et<<3|nt>>>29)^et>>>6,it=(nt>>>19|et<<13)^(nt<<3|et>>>29)^(nt>>>6|et<<26),at=d[Z-7],ot=at.high,st=at.low,ct=d[Z-16],dt=ct.high,ut=ct.low;W=(W=(W=J+ot+((K=Y+st)>>>0<Y>>>0?1:0))+rt+((K+=it)>>>0<it>>>0?1:0))+dt+((K+=ut)>>>0<ut>>>0?1:0),V.high=W,V.low=K}var ht,lt=M&j^~M&U,ft=H&N^~H&F,pt=O&C^O&T^C&T,_t=q&D^q&B^D&B,vt=(O>>>28|q<<4)^(O<<30|q>>>2)^(O<<25|q>>>7),gt=(q>>>28|O<<4)^(q<<30|O>>>2)^(q<<25|O>>>7),wt=(M>>>14|H<<18)^(M>>>18|H<<14)^(M<<23|H>>>9),mt=(H>>>14|M<<18)^(H>>>18|M<<14)^(H<<23|M>>>9),yt=c[Z],bt=yt.high,kt=yt.low,xt=L+wt+((ht=Q+mt)>>>0<Q>>>0?1:0),St=gt+_t;L=U,Q=F,U=j,F=N,j=M,N=H,M=z+(xt=(xt=(xt=xt+lt+((ht+=ft)>>>0<ft>>>0?1:0))+bt+((ht+=kt)>>>0<kt>>>0?1:0))+W+((ht+=K)>>>0<K>>>0?1:0))+((H=P+ht|0)>>>0<P>>>0?1:0)|0,z=T,P=B,T=C,B=D,C=O,D=q,O=xt+(vt+pt+(St>>>0<gt>>>0?1:0))+((q=ht+St|0)>>>0<ht>>>0?1:0)|0}p=r.low=p+q,r.high=f+O+(p>>>0<q>>>0?1:0),v=i.low=v+D,i.high=_+C+(v>>>0<D>>>0?1:0),w=a.low=w+B,a.high=g+T+(w>>>0<B>>>0?1:0),y=o.low=y+P,o.high=m+z+(y>>>0<P>>>0?1:0),k=s.low=k+H,s.high=b+M+(k>>>0<H>>>0?1:0),S=u.low=S+N,u.high=x+j+(S>>>0<N>>>0?1:0),A=h.low=A+F,h.high=E+U+(A>>>0<F>>>0?1:0),I=l.low=I+Q,l.high=R+L+(I>>>0<Q>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;return e[r>>>5]|=128<<24-r%32,e[30+(r+128>>>10<<5)]=Math.floor(n/4294967296),e[31+(r+128>>>10<<5)]=n,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});e.SHA512=n._createHelper(u),e.HmacSHA512=n._createHmacHelper(u)}(),t.SHA512}(Fi(),Zi())),va.exports;var t}va.exports;var wa,ma={exports:{}};var ya,ba={exports:{}};function ka(){return ya||(ya=1,ba.exports=function(t){return function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,o=n.x64.Word,s=n.algo,c=[],d=[],u=[];!function(){for(var t=1,e=0,n=0;n<24;n++){c[t+5*e]=(n+1)*(n+2)/2%64;var r=(2*t+3*e)%5;t=e%5,e=r}for(t=0;t<5;t++)for(e=0;e<5;e++)d[t+5*e]=e+(2*t+3*e)%5*5;for(var i=1,a=0;a<24;a++){for(var s=0,h=0,l=0;l<7;l++){if(1&i){var f=(1<<l)-1;f<32?h^=1<<f:s^=1<<f-32}128&i?i=i<<1^113:i<<=1}u[a]=o.create(s,h)}}();var h=[];!function(){for(var t=0;t<25;t++)h[t]=o.create()}();var l=s.SHA3=a.extend({cfg:a.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new o.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var n=this._state,r=this.blockSize/2,i=0;i<r;i++){var a=t[e+2*i],o=t[e+2*i+1];a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),(I=n[i]).high^=o,I.low^=a}for(var s=0;s<24;s++){for(var l=0;l<5;l++){for(var f=0,p=0,_=0;_<5;_++)f^=(I=n[l+5*_]).high,p^=I.low;var v=h[l];v.high=f,v.low=p}for(l=0;l<5;l++){var g=h[(l+4)%5],w=h[(l+1)%5],m=w.high,y=w.low;for(f=g.high^(m<<1|y>>>31),p=g.low^(y<<1|m>>>31),_=0;_<5;_++)(I=n[l+5*_]).high^=f,I.low^=p}for(var b=1;b<25;b++){var k=(I=n[b]).high,x=I.low,S=c[b];S<32?(f=k<<S|x>>>32-S,p=x<<S|k>>>32-S):(f=x<<S-32|k>>>64-S,p=k<<S-32|x>>>64-S);var E=h[d[b]];E.high=f,E.low=p}var A=h[0],R=n[0];for(A.high=R.high,A.low=R.low,l=0;l<5;l++)for(_=0;_<5;_++){var I=n[b=l+5*_],O=h[b],q=h[(l+1)%5+5*_],C=h[(l+2)%5+5*_];I.high=O.high^~q.high&C.high,I.low=O.low^~q.low&C.low}I=n[0];var D=u[s];I.high^=D.high,I.low^=D.low}},_doFinalize:function(){var t=this._data,n=t.words;this._nDataBytes;var r=8*t.sigBytes,a=32*this.blockSize;n[r>>>5]|=1<<24-r%32,n[(e.ceil((r+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*n.length,this._process();for(var o=this._state,s=this.cfg.outputLength/8,c=s/8,d=[],u=0;u<c;u++){var h=o[u],l=h.high,f=h.low;l=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),d.push(f),d.push(l)}return new i.init(d,s)},clone:function(){for(var t=a.clone.call(this),e=t._state=this._state.slice(0),n=0;n<25;n++)e[n]=e[n].clone();return t}});n.SHA3=a._createHelper(l),n.HmacSHA3=a._createHmacHelper(l)}(Math),t.SHA3}(Fi(),Zi())),ba.exports}var xa,Sa={exports:{}};var Ea,Aa={exports:{}};function Ra(){return Ea||(Ea=1,t=Aa,Aa.exports,t.exports=function(t){var e,n,r;n=(e=t).lib.Base,r=e.enc.Utf8,e.algo.HMAC=n.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=r.parse(e));var n=t.blockSize,i=4*n;e.sigBytes>i&&(e=t.finalize(e)),e.clamp();for(var a=this._oKey=e.clone(),o=this._iKey=e.clone(),s=a.words,c=o.words,d=0;d<n;d++)s[d]^=1549556828,c[d]^=909522486;a.sigBytes=o.sigBytes=i,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,n=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(n))}})}(Fi())),Aa.exports;var t}Aa.exports;var Ia,Oa={exports:{}};var qa,Ca={exports:{}};function Da(){return qa||(qa=1,t=Ca,Ca.exports,t.exports=function(t){return n=(e=t).lib,r=n.Base,i=n.WordArray,a=e.algo,o=a.MD5,s=a.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:o,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var n,r=this.cfg,a=r.hasher.create(),o=i.create(),s=o.words,c=r.keySize,d=r.iterations;s.length<c;){n&&a.update(n),n=a.update(t).finalize(e),a.reset();for(var u=1;u<d;u++)n=a.finalize(n),a.reset();o.concat(n)}return o.sigBytes=4*c,o}}),e.EvpKDF=function(t,e,n){return s.create(n).compute(t,e)},t.EvpKDF;var e,n,r,i,a,o,s}(Fi(),da(),Ra())),Ca.exports;var t}Ca.exports;var Ta,Ba={exports:{}};function za(){return Ta||(Ta=1,t=Ba,Ba.exports,t.exports=function(t){t.lib.Cipher||function(e){var n=t,r=n.lib,i=r.Base,a=r.WordArray,o=r.BufferedBlockAlgorithm,s=n.enc;s.Utf8;var c=s.Base64,d=n.algo.EvpKDF,u=r.Cipher=o.extend({cfg:i.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,n){this.cfg=this.cfg.extend(n),this._xformMode=t,this._key=e,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?m:g}return function(e){return{encrypt:function(n,r,i){return t(r).encrypt(e,n,r,i)},decrypt:function(n,r,i){return t(r).decrypt(e,n,r,i)}}}}()});r.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var h=n.mode={},l=r.BlockCipherMode=i.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),f=h.CBC=function(){var t=l.extend();function n(t,n,r){var i,a=this._iv;a?(i=a,this._iv=e):i=this._prevBlock;for(var o=0;o<r;o++)t[n+o]^=i[o]}return t.Encryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;n.call(this,t,e,i),r.encryptBlock(t,e),this._prevBlock=t.slice(e,e+i)}}),t.Decryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,a=t.slice(e,e+i);r.decryptBlock(t,e),n.call(this,t,e,i),this._prevBlock=a}}),t}(),p=(n.pad={}).Pkcs7={pad:function(t,e){for(var n=4*e,r=n-t.sigBytes%n,i=r<<24|r<<16|r<<8|r,o=[],s=0;s<r;s+=4)o.push(i);var c=a.create(o,r);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}};r.BlockCipher=u.extend({cfg:u.cfg.extend({mode:f,padding:p}),reset:function(){var t;u.reset.call(this);var e=this.cfg,n=e.iv,r=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=r.createEncryptor:(t=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,n&&n.words):(this._mode=t.call(r,this,n&&n.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4});var _=r.CipherParams=i.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),v=(n.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,n=t.salt;return(n?a.create([1398893684,1701076831]).concat(n).concat(e):e).toString(c)},parse:function(t){var e,n=c.parse(t),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(e=a.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),_.create({ciphertext:n,salt:e})}},g=r.SerializableCipher=i.extend({cfg:i.extend({format:v}),encrypt:function(t,e,n,r){r=this.cfg.extend(r);var i=t.createEncryptor(n,r),a=i.finalize(e),o=i.cfg;return _.create({ciphertext:a,key:n,iv:o.iv,algorithm:t,mode:o.mode,padding:o.padding,blockSize:t.blockSize,formatter:r.format})},decrypt:function(t,e,n,r){return r=this.cfg.extend(r),e=this._parse(e,r.format),t.createDecryptor(n,r).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),w=(n.kdf={}).OpenSSL={execute:function(t,e,n,r,i){if(r||(r=a.random(8)),i)o=d.create({keySize:e+n,hasher:i}).compute(t,r);else var o=d.create({keySize:e+n}).compute(t,r);var s=a.create(o.words.slice(e),4*n);return o.sigBytes=4*e,_.create({key:o,iv:s,salt:r})}},m=r.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:w}),encrypt:function(t,e,n,r){var i=(r=this.cfg.extend(r)).kdf.execute(n,t.keySize,t.ivSize,r.salt,r.hasher);r.iv=i.iv;var a=g.encrypt.call(this,t,e,i.key,r);return a.mixIn(i),a},decrypt:function(t,e,n,r){r=this.cfg.extend(r),e=this._parse(e,r.format);var i=r.kdf.execute(n,t.keySize,t.ivSize,e.salt,r.hasher);return r.iv=i.iv,g.decrypt.call(this,t,e,i.key,r)}})}()}(Fi(),Da())),Ba.exports;var t}Ba.exports;var Pa,Ma={exports:{}};function Ha(){return Pa||(Pa=1,Ma.exports=function(t){return t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function n(t,e,n,r){var i,a=this._iv;a?(i=a.slice(0),this._iv=void 0):i=this._prevBlock,r.encryptBlock(i,0);for(var o=0;o<n;o++)t[e+o]^=i[o]}return e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize;n.call(this,t,e,i,r),this._prevBlock=t.slice(e,e+i)}}),e.Decryptor=e.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,a=t.slice(e,e+i);n.call(this,t,e,i,r),this._prevBlock=a}}),e}(),t.mode.CFB}(Fi(),za())),Ma.exports}var ja,Na={exports:{}};function Ua(){return ja||(ja=1,Na.exports=function(t){return t.mode.CTR=(e=t.lib.BlockCipherMode.extend(),n=e.Encryptor=e.extend({processBlock:function(t,e){var n=this._cipher,r=n.blockSize,i=this._iv,a=this._counter;i&&(a=this._counter=i.slice(0),this._iv=void 0);var o=a.slice(0);n.encryptBlock(o,0),a[r-1]=a[r-1]+1|0;for(var s=0;s<r;s++)t[e+s]^=o[s]}}),e.Decryptor=n,e),t.mode.CTR;var e,n}(Fi(),za())),Na.exports}var Fa,La={exports:{}};function Qa(){return Fa||(Fa=1,La.exports=function(t){
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
return t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function n(t){if(255==(t>>24&255)){var e=t>>16&255,n=t>>8&255,r=255&t;255===e?(e=0,255===n?(n=0,255===r?r=0:++r):++n):++e,t=0,t+=e<<16,t+=n<<8,t+=r}else t+=1<<24;return t}function r(t){return 0===(t[0]=n(t[0]))&&(t[1]=n(t[1])),t}var i=e.Encryptor=e.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize,a=this._iv,o=this._counter;a&&(o=this._counter=a.slice(0),this._iv=void 0),r(o);var s=o.slice(0);n.encryptBlock(s,0);for(var c=0;c<i;c++)t[e+c]^=s[c]}});return e.Decryptor=i,e}(),t.mode.CTRGladman}(Fi(),za())),La.exports}var Za,Ka={exports:{}};function Wa(){return Za||(Za=1,Ka.exports=function(t){return t.mode.OFB=(e=t.lib.BlockCipherMode.extend(),n=e.Encryptor=e.extend({processBlock:function(t,e){var n=this._cipher,r=n.blockSize,i=this._iv,a=this._keystream;i&&(a=this._keystream=i.slice(0),this._iv=void 0),n.encryptBlock(a,0);for(var o=0;o<r;o++)t[e+o]^=a[o]}}),e.Decryptor=n,e),t.mode.OFB;var e,n}(Fi(),za())),Ka.exports}var Va,$a={exports:{}};var Xa,Ga={exports:{}};var Ja,Ya={exports:{}};var to,eo={exports:{}};var no,ro={exports:{}};var io,ao={exports:{}};var oo,so={exports:{}};var co,uo={exports:{}};var ho,lo={exports:{}};function fo(){return ho||(ho=1,lo.exports=function(t){return function(){var e=t,n=e.lib,r=n.WordArray,i=n.BlockCipher,a=e.algo,o=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],d=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,**********:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:**********,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:**********,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:**********,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:**********,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,**********:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=a.DES=i.extend({_doReset:function(){for(var t=this._key.words,e=[],n=0;n<56;n++){var r=o[n]-1;e[n]=t[r>>>5]>>>31-r%32&1}for(var i=this._subKeys=[],a=0;a<16;a++){var d=i[a]=[],u=c[a];for(n=0;n<24;n++)d[n/6|0]|=e[(s[n]-1+u)%28]<<31-n%6,d[4+(n/6|0)]|=e[28+(s[n+24]-1+u)%28]<<31-n%6;for(d[0]=d[0]<<1|d[0]>>>31,n=1;n<7;n++)d[n]=d[n]>>>4*(n-1)+3;d[7]=d[7]<<5|d[7]>>>27}var h=this._invSubKeys=[];for(n=0;n<16;n++)h[n]=i[15-n]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,n){this._lBlock=t[e],this._rBlock=t[e+1],l.call(this,4,252645135),l.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),l.call(this,1,1431655765);for(var r=0;r<16;r++){for(var i=n[r],a=this._lBlock,o=this._rBlock,s=0,c=0;c<8;c++)s|=d[c][((o^i[c])&u[c])>>>0];this._lBlock=o,this._rBlock=a^s}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,l.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),l.call(this,16,65535),l.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function l(t,e){var n=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=n,this._lBlock^=n<<t}function f(t,e){var n=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=n,this._rBlock^=n<<t}e.DES=i._createHelper(h);var p=a.TripleDES=i.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),n=t.length<4?t.slice(0,2):t.slice(2,4),i=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=h.createEncryptor(r.create(e)),this._des2=h.createEncryptor(r.create(n)),this._des3=h.createEncryptor(r.create(i))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(p)}(),t.TripleDES}(Fi(),ta(),oa(),Da(),za())),lo.exports}var po,_o={exports:{}};var vo,go={exports:{}};var wo,mo={exports:{}};var yo,bo={exports:{}};function ko(){return yo||(yo=1,bo.exports=function(t){return function(){var e=t,n=e.lib.BlockCipher,r=e.algo,i=16,a=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],o=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]],s={pbox:[],sbox:[]};function c(t,e){var n=e>>24&255,r=e>>16&255,i=e>>8&255,a=255&e,o=t.sbox[0][n]+t.sbox[1][r];return o^=t.sbox[2][i],o+=t.sbox[3][a]}function d(t,e,n){for(var r,a=e,o=n,s=0;s<i;++s)r=a^=t.pbox[s],a=o=c(t,a)^o,o=r;return r=a,a=o,o=r,o^=t.pbox[i],{left:a^=t.pbox[i+1],right:o}}function u(t,e,n){for(var r,a=e,o=n,s=i+1;s>1;--s)r=a^=t.pbox[s],a=o=c(t,a)^o,o=r;return r=a,a=o,o=r,o^=t.pbox[1],{left:a^=t.pbox[0],right:o}}function h(t,e,n){for(var r=0;r<4;r++){t.sbox[r]=[];for(var s=0;s<256;s++)t.sbox[r][s]=o[r][s]}for(var c=0,u=0;u<i+2;u++)t.pbox[u]=a[u]^e[c],++c>=n&&(c=0);for(var h=0,l=0,f=0,p=0;p<i+2;p+=2)h=(f=d(t,h,l)).left,l=f.right,t.pbox[p]=h,t.pbox[p+1]=l;for(var _=0;_<4;_++)for(var v=0;v<256;v+=2)h=(f=d(t,h,l)).left,l=f.right,t.sbox[_][v]=h,t.sbox[_][v+1]=l;return!0}var l=r.Blowfish=n.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var t=this._keyPriorReset=this._key,e=t.words,n=t.sigBytes/4;h(s,e,n)}},encryptBlock:function(t,e){var n=d(s,t[e],t[e+1]);t[e]=n.left,t[e+1]=n.right},decryptBlock:function(t,e){var n=u(s,t[e],t[e+1]);t[e]=n.left,t[e+1]=n.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=n._createHelper(l)}(),t.Blowfish}(Fi(),ta(),oa(),Da(),za())),bo.exports}Hi.exports=function(t){return t}(Fi(),Zi(),Vi(),Gi(),ta(),ra(),oa(),da(),la(),fa||(fa=1,pa.exports=function(t){return n=(e=t).lib.WordArray,r=e.algo,i=r.SHA256,a=r.SHA224=i.extend({_doReset:function(){this._hash=new n.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=i._doFinalize.call(this);return t.sigBytes-=4,t}}),e.SHA224=i._createHelper(a),e.HmacSHA224=i._createHmacHelper(a),t.SHA224;var e,n,r,i,a}(Fi(),la())),ga(),wa||(wa=1,ma.exports=function(t){return n=(e=t).x64,r=n.Word,i=n.WordArray,a=e.algo,o=a.SHA512,s=a.SHA384=o.extend({_doReset:function(){this._hash=new i.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var t=o._doFinalize.call(this);return t.sigBytes-=16,t}}),e.SHA384=o._createHelper(s),e.HmacSHA384=o._createHmacHelper(s),t.SHA384;var e,n,r,i,a,o,s}(Fi(),Zi(),ga())),ka(),xa||(xa=1,Sa.exports=function(t){
/** @preserve
      (c) 2012 by Cédric Mesnil. All rights reserved.
      	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
          - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      */
return function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,o=n.algo,s=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),d=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),u=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),h=i.create([0,1518500249,1859775393,2400959708,2840853838]),l=i.create([1352829926,1548603684,1836072691,2053994217,0]),f=o.RIPEMD160=a.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var n=0;n<16;n++){var r=e+n,i=t[r];t[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var a,o,f,y,b,k,x,S,E,A,R,I=this._hash.words,O=h.words,q=l.words,C=s.words,D=c.words,T=d.words,B=u.words;for(k=a=I[0],x=o=I[1],S=f=I[2],E=y=I[3],A=b=I[4],n=0;n<80;n+=1)R=a+t[e+C[n]]|0,R+=n<16?p(o,f,y)+O[0]:n<32?_(o,f,y)+O[1]:n<48?v(o,f,y)+O[2]:n<64?g(o,f,y)+O[3]:w(o,f,y)+O[4],R=(R=m(R|=0,T[n]))+b|0,a=b,b=y,y=m(f,10),f=o,o=R,R=k+t[e+D[n]]|0,R+=n<16?w(x,S,E)+q[0]:n<32?g(x,S,E)+q[1]:n<48?v(x,S,E)+q[2]:n<64?_(x,S,E)+q[3]:p(x,S,E)+q[4],R=(R=m(R|=0,B[n]))+A|0,k=A,A=E,E=m(S,10),S=x,x=R;R=I[1]+f+E|0,I[1]=I[2]+y+A|0,I[2]=I[3]+b+k|0,I[3]=I[4]+a+x|0,I[4]=I[0]+o+S|0,I[0]=R},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;e[r>>>5]|=128<<24-r%32,e[14+(r+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),t.sigBytes=4*(e.length+1),this._process();for(var i=this._hash,a=i.words,o=0;o<5;o++){var s=a[o];a[o]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return i},clone:function(){var t=a.clone.call(this);return t._hash=this._hash.clone(),t}});function p(t,e,n){return t^e^n}function _(t,e,n){return t&e|~t&n}function v(t,e,n){return(t|~e)^n}function g(t,e,n){return t&n|e&~n}function w(t,e,n){return t^(e|~n)}function m(t,e){return t<<e|t>>>32-e}n.RIPEMD160=a._createHelper(f),n.HmacRIPEMD160=a._createHmacHelper(f)}(),t.RIPEMD160}(Fi())),Ra(),Ia||(Ia=1,Oa.exports=function(t){return r=(n=(e=t).lib).Base,i=n.WordArray,o=(a=e.algo).SHA256,s=a.HMAC,c=a.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:o,iterations:25e4}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var n=this.cfg,r=s.create(n.hasher,t),a=i.create(),o=i.create([1]),c=a.words,d=o.words,u=n.keySize,h=n.iterations;c.length<u;){var l=r.update(e).finalize(o);r.reset();for(var f=l.words,p=f.length,_=l,v=1;v<h;v++){_=r.finalize(_),r.reset();for(var g=_.words,w=0;w<p;w++)f[w]^=g[w]}a.concat(l),d[0]++}return a.sigBytes=4*u,a}}),e.PBKDF2=function(t,e,n){return c.create(n).compute(t,e)},t.PBKDF2;var e,n,r,i,a,o,s,c}(Fi(),la(),Ra())),Da(),za(),Ha(),Ua(),Qa(),Wa(),Va||(Va=1,$a.exports=function(t){return t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),e.Decryptor=e.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),e),t.mode.ECB;var e}(Fi(),za())),Xa||(Xa=1,Ga.exports=function(t){return t.pad.AnsiX923={pad:function(t,e){var n=t.sigBytes,r=4*e,i=r-n%r,a=n+i-1;t.clamp(),t.words[a>>>2]|=i<<24-a%4*8,t.sigBytes+=i},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Ansix923}(Fi(),za())),Ja||(Ja=1,Ya.exports=function(t){return t.pad.Iso10126={pad:function(e,n){var r=4*n,i=r-e.sigBytes%r;e.concat(t.lib.WordArray.random(i-1)).concat(t.lib.WordArray.create([i<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Iso10126}(Fi(),za())),to||(to=1,eo.exports=function(t){return t.pad.Iso97971={pad:function(e,n){e.concat(t.lib.WordArray.create([**********],1)),t.pad.ZeroPadding.pad(e,n)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971}(Fi(),za())),no||(no=1,ro.exports=function(t){return t.pad.ZeroPadding={pad:function(t,e){var n=4*e;t.clamp(),t.sigBytes+=n-(t.sigBytes%n||n)},unpad:function(t){var e=t.words,n=t.sigBytes-1;for(n=t.sigBytes-1;n>=0;n--)if(e[n>>>2]>>>24-n%4*8&255){t.sigBytes=n+1;break}}},t.pad.ZeroPadding}(Fi(),za())),io||(io=1,ao.exports=function(t){return t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding}(Fi(),za())),oo||(oo=1,so.exports=function(t){return n=(e=t).lib.CipherParams,r=e.enc.Hex,e.format.Hex={stringify:function(t){return t.ciphertext.toString(r)},parse:function(t){var e=r.parse(t);return n.create({ciphertext:e})}},t.format.Hex;var e,n,r}(Fi(),za())),co||(co=1,uo.exports=function(t){return function(){var e=t,n=e.lib.BlockCipher,r=e.algo,i=[],a=[],o=[],s=[],c=[],d=[],u=[],h=[],l=[],f=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var n=0,r=0;for(e=0;e<256;e++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,i[n]=p,a[p]=n;var _=t[n],v=t[_],g=t[v],w=257*t[p]^16843008*p;o[n]=w<<24|w>>>8,s[n]=w<<16|w>>>16,c[n]=w<<8|w>>>24,d[n]=w,w=16843009*g^65537*v^257*_^16843008*n,u[p]=w<<24|w>>>8,h[p]=w<<16|w>>>16,l[p]=w<<8|w>>>24,f[p]=w,n?(n=_^t[t[t[g^_]]],r^=t[t[r]]):n=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],_=r.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,n=t.sigBytes/4,r=4*((this._nRounds=n+6)+1),a=this._keySchedule=[],o=0;o<r;o++)o<n?a[o]=e[o]:(d=a[o-1],o%n?n>6&&o%n==4&&(d=i[d>>>24]<<24|i[d>>>16&255]<<16|i[d>>>8&255]<<8|i[255&d]):(d=i[(d=d<<8|d>>>24)>>>24]<<24|i[d>>>16&255]<<16|i[d>>>8&255]<<8|i[255&d],d^=p[o/n|0]<<24),a[o]=a[o-n]^d);for(var s=this._invKeySchedule=[],c=0;c<r;c++){if(o=r-c,c%4)var d=a[o];else d=a[o-4];s[c]=c<4||o<=4?d:u[i[d>>>24]]^h[i[d>>>16&255]]^l[i[d>>>8&255]]^f[i[255&d]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,o,s,c,d,i)},decryptBlock:function(t,e){var n=t[e+1];t[e+1]=t[e+3],t[e+3]=n,this._doCryptBlock(t,e,this._invKeySchedule,u,h,l,f,a),n=t[e+1],t[e+1]=t[e+3],t[e+3]=n},_doCryptBlock:function(t,e,n,r,i,a,o,s){for(var c=this._nRounds,d=t[e]^n[0],u=t[e+1]^n[1],h=t[e+2]^n[2],l=t[e+3]^n[3],f=4,p=1;p<c;p++){var _=r[d>>>24]^i[u>>>16&255]^a[h>>>8&255]^o[255&l]^n[f++],v=r[u>>>24]^i[h>>>16&255]^a[l>>>8&255]^o[255&d]^n[f++],g=r[h>>>24]^i[l>>>16&255]^a[d>>>8&255]^o[255&u]^n[f++],w=r[l>>>24]^i[d>>>16&255]^a[u>>>8&255]^o[255&h]^n[f++];d=_,u=v,h=g,l=w}_=(s[d>>>24]<<24|s[u>>>16&255]<<16|s[h>>>8&255]<<8|s[255&l])^n[f++],v=(s[u>>>24]<<24|s[h>>>16&255]<<16|s[l>>>8&255]<<8|s[255&d])^n[f++],g=(s[h>>>24]<<24|s[l>>>16&255]<<16|s[d>>>8&255]<<8|s[255&u])^n[f++],w=(s[l>>>24]<<24|s[d>>>16&255]<<16|s[u>>>8&255]<<8|s[255&h])^n[f++],t[e]=_,t[e+1]=v,t[e+2]=g,t[e+3]=w},keySize:8});e.AES=n._createHelper(_)}(),t.AES}(Fi(),ta(),oa(),Da(),za())),fo(),po||(po=1,_o.exports=function(t){return function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=r.RC4=n.extend({_doReset:function(){for(var t=this._key,e=t.words,n=t.sigBytes,r=this._S=[],i=0;i<256;i++)r[i]=i;i=0;for(var a=0;i<256;i++){var o=i%n,s=e[o>>>2]>>>24-o%4*8&255;a=(a+r[i]+s)%256;var c=r[i];r[i]=r[a],r[a]=c}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=a.call(this)},keySize:8,ivSize:0});function a(){for(var t=this._S,e=this._i,n=this._j,r=0,i=0;i<4;i++){n=(n+t[e=(e+1)%256])%256;var a=t[e];t[e]=t[n],t[n]=a,r|=t[(t[e]+t[n])%256]<<24-8*i}return this._i=e,this._j=n,r}e.RC4=n._createHelper(i);var o=r.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)a.call(this)}});e.RC4Drop=n._createHelper(o)}(),t.RC4}(Fi(),ta(),oa(),Da(),za())),vo||(vo=1,go.exports=function(t){return function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=[],a=[],o=[],s=r.Rabbit=n.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,n=0;n<4;n++)t[n]=16711935&(t[n]<<8|t[n]>>>24)|4278255360&(t[n]<<24|t[n]>>>8);var r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,n=0;n<4;n++)c.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(e){var a=e.words,o=a[0],s=a[1],d=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),h=d>>>16|4294901760&u,l=u<<16|65535&d;for(i[0]^=d,i[1]^=h,i[2]^=u,i[3]^=l,i[4]^=d,i[5]^=h,i[6]^=u,i[7]^=l,n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(t,e){var n=this._X;c.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),t[e+r]^=i[r]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,n=0;n<8;n++)a[n]=e[n];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<a[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<a[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<a[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<a[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<a[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<a[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<a[6]>>>0?1:0)|0,this._b=e[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=t[n]+e[n],i=65535&r,s=r>>>16,c=((i*i>>>17)+i*s>>>15)+s*s,d=((4294901760&r)*r|0)+((65535&r)*r|0);o[n]=c^d}t[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,t[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,t[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,t[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,t[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,t[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,t[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,t[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.Rabbit=n._createHelper(s)}(),t.Rabbit}(Fi(),ta(),oa(),Da(),za())),wo||(wo=1,mo.exports=function(t){return function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=[],a=[],o=[],s=r.RabbitLegacy=n.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],r=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var i=0;i<4;i++)c.call(this);for(i=0;i<8;i++)r[i]^=n[i+4&7];if(e){var a=e.words,o=a[0],s=a[1],d=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),h=d>>>16|4294901760&u,l=u<<16|65535&d;for(r[0]^=d,r[1]^=h,r[2]^=u,r[3]^=l,r[4]^=d,r[5]^=h,r[6]^=u,r[7]^=l,i=0;i<4;i++)c.call(this)}},_doProcessBlock:function(t,e){var n=this._X;c.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),t[e+r]^=i[r]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,n=0;n<8;n++)a[n]=e[n];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<a[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<a[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<a[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<a[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<a[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<a[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<a[6]>>>0?1:0)|0,this._b=e[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=t[n]+e[n],i=65535&r,s=r>>>16,c=((i*i>>>17)+i*s>>>15)+s*s,d=((4294901760&r)*r|0)+((65535&r)*r|0);o[n]=c^d}t[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,t[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,t[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,t[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,t[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,t[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,t[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,t[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.RabbitLegacy=n._createHelper(s)}(),t.RabbitLegacy}(Fi(),ta(),oa(),Da(),za())),ko());var xo=Pi(Hi.exports),So=function(t,e,n){var r=[t,e].join(t.indexOf("?")>=0?"&":"?"),i="S3PING_IMG".concat(tt()),a=new Image;window[i]=a;var o=function(t){if(a.onload=a.onerror=a.onabort=null,window[i]=null,a=null,n){var e=null;t&&((e=new Error).name="ImgPing".concat(t)),n(e)}};return a.onload=function(){o()},a.onerror=function(){o("Error")},a.onabort=function(){o("Abort")},a.src=r,!0},Eo=function(t,e,n,r){if("xhr"!==ge())return!1;var i,a,o=new window.XMLHttpRequest;return o.open("POST",t,!0),o.withCredentials=!1,r.enableCompression?(o.setRequestHeader("Content-Encoding","gzip"),o.setRequestHeader("Content-Type","application/json"),i=JSON.stringify(e),a=(new TextEncoder).encode(i),e=Ai.gzip(a,{level:9})):(o.setRequestHeader("Content-Type","application/json"),e=JSON.stringify(e)),n&&(o.onreadystatechange=function(){if(4===o.readyState){var t,e=o.status,r=null;if(e>=200&&e<400)t=JSON.parse(o.responseText)||{};else{var i="XhrPing".concat(e<500?"ClientError":"ServerError");(r=new Error("Error:".concat(i,", httpCode: ").concat(e))).name=i}n(r,{statusCode:e,data:t,headers:Io(o.getAllResponseHeaders())})}}),o.send(e),!0},Ao=function(t,e,n){var r=t.length+n.length+1;return we&&r>2083&&r-e.length>2048||r>5e3},Ro=function(t,e,n,r){var i=function(t,e){"string"!=typeof t&&(t=Ht(t));var n=e&&e.randomKey;return(n=null==n?"z":n)&&(t+=(t?"&":"")+"".concat(n,"=").concat(tt())),t}(e,n),a="";if(!e.forEach)return e="".concat(i,"&ext=3"),Ao(t,u,e)?(t=t.replace("/events/report","/events/sdk/trace"),Eo(t,e,h,r)):So(t,e,h);var o={account:{}},s=[],c=["appkey","appid","openid","kfuin","anonymous_id","visitorId"];e.forEach((function(t,e){t.key?"appkey"===t.key?a=t.value:"kfuin"===t.key?t.value:(t.key&&c.indexOf(t.key)>-1?o.account[t.key]=t.value:o[t.key]=t.value,s=[o]):(s[e]={account:{}},t&&t.forEach((function(t){a=t.appkey,t.kfuin,t.key&&c.indexOf(t.key)>-1?s[e].account[t.key]=t.value:s[e][t.key]=t.value})))}));var d=t.match(/(?:https?|ftp):\/\/[^\/]+/);if(!d)throw new Error('URL: "'.concat(t,'" not absolute url.'));var u=d[0];n&&n.transport;var h=n&&n.callback,l=r.pingMethods;"BEACON"!==l||ve()||(l=r.defaultMethods||"POST"),"$pageclose"!==r.event&&"$pagestay"!==r.event||!ve()||(l="BEACON");var f=r.encryptMode,p=r.AES_FUNC;window.btoa&&"GET"!==l||(f="close"),"aes"!==f||p||(f="close");var _=a||r.appkey||"";r.vals.qid;var v=_.substring(0,16);if(v.length<16)for(var g=16-v.length,w=0;w<g;w++)v+="0";var m,y,b=xo?xo.enc.Utf8.parse(v):"",k={iv:b,mode:xo?xo.mode.ECB:"",padding:xo?xo.pad.Pkcs7:""},x=JSON.stringify(s),S={},E="",A=0;switch(f){case"default":A=(E=window.btoa(window.encodeURIComponent(x))).length,e={appkey:_,data:E,ext:1,sign:Ii("data=".concat(A,"&ext=1"))};break;case"aes":m=b,y=x,A=(E=xo.AES.encrypt(y,m,k).ciphertext.toString(xo.enc.Base64)).length,e={appkey:_,data:E,ext:2,sign:Ii("data=".concat(A,"&ext=2"))};break;case"close":A=(E=x).length,"GET"!==l?e={appkey:_,data:E,ext:3,sign:Ii("data=".concat(A,"&ext=3"))}:(e="".concat(i,"&ext=3"),S={appkey:_,data:E,ext:3,sign:Ii("data=".concat(A,"&ext=3"))})}return"GET"===l?Ao(t,u,e)?(t=t.replace("/events/report","/events/sdk/trace"),Eo(t,S,h,r)):So(t,e,h):(t=t.replace("/events/report","/events/sdk/trace"),"BEACON"===l?function(t,e){var n=new Blob([JSON.stringify(e)],{type:"application/json"});return window.navigator.sendBeacon(t,n)}(t,e):Eo(t,e,h,r))},Io=function(t){var e=t.trim().split(/[\r\n]+/),n={};return e.forEach((function(t){var e=t.split(": "),r=e.shift(),i=e.join(": ");n[r]=i})),n},Oo=function(t,e,n,r){this.event=t,this.api=e,this.params=n,this.cb=r};Oo.prototype.getQueryData=function(){return this.params},Oo.prototype.size=function(){return this.encode({a:1}).length},Oo.prototype.ping=function(t,e,n){var r=this.params.filter({a:1});t?function(t,e,n,r){var i=ve();(n=n||{}).transport=i?"beacon":"img",Ro(t,e,n,r)}(this.api,r,{},n):Ro(this.api,r,{callback:e},n)},Oo.prototype.encode=function(t){if(!t&&null!=this._encodeValue&&!this._changed)return this._encodeValue;var e=this.params.encode(t);return this._changed=!1,t||(this._encodeValue=e),e};var qo=function(t){return t.toString(36)},Co="_EVENTS",Do=function(){};Do.prototype.on=function(t,e){var n=this[Co];n||(n=this[Co]={}),(n[t]=n[t]||[]).push(e)},Do.prototype.off=function(t,e){if(t){var n=this[Co];if(n&&n[t])if(e)for(var r=n[t],i=0;i<r.length;i++)r[i]===e&&(r[i]=null);else n[t].length=0}else this[Co]={}},Do.prototype.trigger=function(t){var e=Tt(arguments),n=this[Co];if(t=e.shift(),n&&n[t])for(var r=n[t],i=0;i<r.length;i++)r[i]&&r[i].apply(this,e)};var To=function(t,e){var n=t.prototype,r=function(){};r.prototype=e.prototype;var i=t.prototype=new r;for(var a in n)n.hasOwnProperty(a)&&(i[a]=n[a]);t.prototype.constructor=t,t.superClass=e.prototype};function Bo(t){return-1!==Object.prototype.toString.call(t).indexOf("Object")}var zo=function(t){var e=this;this.json=t&&t.params||[],this.suffixJSON=[],this._prefixLength=0,this.keyMap={},this.json.forEach((function(t,n){e.keyMap[t.key]=n})),this.account={}};To(zo,Do),zo.prototype.addAccoutInfo=function(t,e){2===arguments.length?this.account[t]=e:1===arguments.length&&(this.account=t)},zo.prototype.getAccoutInfo=function(t){return t?this.account[t]:this.account},zo.prototype.prefix=function(t,e){this._add(t,e)},zo.prototype.add=function(t,e){this._add(t,e)},zo.prototype.flat=function(){if(this.json&&this.json.length){var t=[];this.json.forEach((function(e){if(Bo(e.value))for(var n in e.value){var r=n.replace(/^\$/,"");t.push({key:r,value:e.value[n]})}else t.push(e)})),this.json=t,this.trigger("change")}},zo.prototype._add=function(t,e,n){if(this.keyMap[t]||0===this.keyMap[t]){n||(n=this.json.findIndex((function(e){return e.key===t})));var r=this.json[n].value;if(Bo(e))for(var i in e)r[i]=e[i];else this.json[n].value=e;this.trigger("change")}else{var a={key:t,value:e};null!=n?this.json.splice(n,0,a):(this.keyMap[t]=this.json.length,this.json.push(a)),this.trigger("change")}},zo.prototype.suffix=function(t,e){var n={key:t,value:e};this.suffixJSON.push(n),this.trigger("change")},zo.prototype.hasKey=function(t){for(var e=0,n=this.json.length;e<n;e++)if(this.json[e].key===t)return!0;return!1},zo.prototype.extend=function(t){for(var e in t)t.hasOwnProperty(e)&&null!=t[e]&&this.json.push({key:e,value:t[e]});this.trigger("change")},zo.prototype.toJSON=function(){var t=this.json.concat(this.suffixJSON);return Object.keys(this.account).length>0&&t.unshift({key:"account",value:this.account}),t},zo.prototype.filter=function(t){var e,n,r,i=this.json,a=[];for(Object.keys(this.account).length>0&&a.push({key:"account",value:this.account}),e=0,n=i.length;e<n;e++)r=i[e],t[r.key]||(Mt.isObject(r.value)&&(this._normalizeProperties(r.value),Object.keys(r.value||{}).forEach((function(e){t[e]&&delete r.value[e]}))),a.push(r));var o=this.suffixJSON;for(e=0,n=o.length;e<n;e++)r=o[e],t[r.key]||a.push(r);return a},zo.prototype._normalizeProperties=function(t){!function t(e){Object.keys(e).forEach((function(n){var r=e[n];Mt.isFunction(r)&&(e[n]=e[n]()),Mt.isObject(r)&&t(r)}))}(t||{})},zo.prototype.size=function(t){return this.encode(t).length},zo.prototype.encode=function(t){var e=this.toJSON();return zo.encode(e,t)},zo.encode=function(t,e){if(!t||!t.length)return"";var n={};return t.forEach((function(t){n[t.key]=t.value})),n},zo.encodeValue=function(t){return lt().encodeURIComponent(t).replace(/\(/g,"%28").replace(/\)/g,"%29")};var Po="QUEUESTORAGE",Mo=function(t){this.queue=t||[];var e=t[0];this.account=e.account};function Ho(){return window.__qda_workerTimer__}Mo.prototype.addAccoutInfo=function(t,e){2===arguments.length?this.account[t]=e:1===arguments.length&&(this.account=t),this.queue.forEach((function(n){n.addAccoutInfo(t,e)}))},Mo.prototype.getAccoutInfo=function(t){return t?this.account[t]:this.account},Mo.prototype.prefix=function(t,e){this.queue.forEach((function(n){n.prefix(t,e)}))},Mo.prototype.add=function(t,e){this.queue.forEach((function(n){n.add(t,e)}))},Mo.prototype.flat=function(){this.queue.forEach((function(t){t.flat()}))},Mo.prototype.suffix=function(t,e){this.queue.forEach((function(n){n.suffix(t,e)}))},Mo.prototype.hasKey=function(t){return!!this.queue.find((function(e){e.hasKey(t)}))},Mo.prototype.extend=function(t){this.queue.forEach((function(e){e.extend(t)}))},Mo.prototype.toJSON=function(){return this.queue.map((function(t){return t.toJSON()}))},Mo.prototype.filter=function(t){return this.queue.map((function(e){return e.filter(t)}))},Mo.prototype.size=function(t){return this.encode(t).length},Mo.prototype.encode=function(t){var e=this.toJSON()||[];return Mo.encode(e,t)},Mo.encode=function(t,e){return t.map((function(t){return zo.encode(t,e)}))},window.__qda_workerTimer__={setTimeout:window.setTimeout,clearTimeout:window.clearTimeout,setInterval:window.setInterval,clearInterval:window.clearInterval};var jo=Ho(),No=function(){var t=ve();this.max=ge()&&t?8192:2047,this.queue=[],this.batchQueue=[],this.timerId=0};No.prototype.run=function(t){this.stopping=!0,this.tracker=t,this.heartBeatInterval=t.get("hbt"),this.batchMaxTime=t.get("batch_max_time")||1,this.roundMaxTime=t.get("round_max_time")||1,this.batchApi=t.getFullApi("b"),this.eventApi=t.getFullApi("track"),2047===this.max&&this.tracker.flag(27)},No.prototype.start=function(){this.stopping=!1,this.batchSend()},No.prototype.stop=function(){this.stopping=!0},No.prototype.push=function(t,e,n,r,i){var a,o=this,s=this.batchQueue.length,c=this.batchQueue[0],d="batchSend",u=function(t){var e=t.map((function(t){return t.cb}));return function(t,n){e.forEach((function(e){e&&e(t,n)}))}};if(e.json.forEach((function(e){"event"===e.key&&(e.value=t)})),i&&"BEACON"===i)n=3,this.batchQueue.push({event:t,queryData:e,priority:n,cb:r,method:i}),a=this.createPing(d,this.batchQueue,1,u(this.batchQueue),i);else if(1===this.batchMaxTime)a=new Oo(t,this.eventApi,e,r);else{var h;if(c&&(h=c.method),h!==i&&(a=this.createPing(d,this.batchQueue,1,u(this.batchQueue),i),this.batchQueue=[],this.batchQueue.push({event:t,queryData:e,priority:n,cb:r,method:i})),s<this.batchMaxTime-1&&h===i)return this.tracker.flag(24),void this.batchQueue.push({event:t,queryData:e,priority:n,cb:r,method:i});s===this.batchMaxTime-1&&(this.batchQueue.push({event:t,queryData:e,priority:n,cb:r,method:i}),a=this.createPing(d,this.batchQueue,n,u(this.batchQueue),i),this.batchQueue=[])}this.timerId&&jo.clearTimeout(this.timerId),this.timerId=jo.setTimeout((function(){o.batchQueue.length>0?(a=o.createPing(d,o.batchQueue,n,u(o.batchQueue),i),l(a,n,i)):jo.clearTimeout(o.timerId),o.timerId=null}),this.heartBeatInterval);var l=function(t,e,n){if(o.stopping)return o.tracker.flag(24),void o._push(t,e);if(o._push(t,e),e&&e>1){var r=3===e;o.batchSend(r,n)}else{var i=1===e;o.waitForSend(i,n)}};l(a,n,i)},No.prototype._push=function(t,e){e?this.queue.unshift(t):this.queue.push(t)},No.prototype.waitForSend=function(t,e){var n=this;if(this.queue.length&&!n.heartBeat){var r=this.tracker.wrap("pq-timer",(function(){n.heartBeat=null,n.batchSend(void 0,e)})),i=t?200:n.heartBeatInterval;n.heartBeat=setTimeout(r,i)}},No.prototype._sendPing=function(t,e,n,r){if(this.tracker.canSend()||e){var i=this,a="req-".concat(t.params);Wt.start(a);var o=this.tracker.get("appkey"),s=this.tracker.get("kfuin"),c=this.tracker.get("anonymous_id"),d=t.event,u=r||this.tracker.vals.ping_method,h=this.tracker.vals.encrypt_mode||"default",l=!!this.tracker.vals.enable_compression,f=this.tracker.qdda&&this.tracker.qdda.AES_FUNC||window.__qq_qidian_da_market_AES_method;"batchSend"!==d&&(d=((t.params.json.filter((function(t){return"event"===t.key}))||[])[0]||{}).value||""),t.ping(e,this.tracker.wrap("pq-ipingcb",(function(e,r){n&&n(e,r),e&&i.tracker.sendError("req",t.pingName,e.name),Wt.end(a)})),{pingMethods:u,encryptMode:h,appkey:o,kfuin:s,event:d,anonymous_id:c,defaultMethods:this.tracker.vals.ping_method,vals:this.tracker.vals,AES_FUNC:f,enableCompression:l})}},No.prototype.batchSend=function(t,e){var n=this.queue[0];if(n&&(this.tracker.canSend()||t)){this.tracker.get("useOpenId")&&(n.params.getAccoutInfo("wechat")||(n.params.add("properties",{appid:this.tracker.get("appid")}),n.params.addAccoutInfo("wechat",{openid:this.tracker.get("openid"),appid:this.tracker.get("appid")})));var r=0,i=function(t){};for(t&&(!function(t,e){if(t&&"Array"==Object.prototype.toString.call(t).slice(8,-1)&&0!==t.length){var n=JSON.stringify(t);try{window.localStorage.setItem((e&&e._storage_prefix||"")+Po,n)}catch(t){return 1}}}(this.queue.slice(1),this.tracker&&this.tracker.qdda),this.roundMaxTime=1,i=function(t){});this.queue.length>0&&r++<this.roundMaxTime;){var a=this.queue.shift();if("pv"===a.pingName){this.tracker.trigger("pv-done");break}this._sendPing(a,t,o(a,r-1),e)}this.waitForSend(void 0,e)}function o(t,e){return function(n,r){t.cb&&t.cb(n,r),i(e)}}},No.prototype.createPing=function(t,e,n,r){var i=e.map((function(t){return t.queryData})),a=new Mo(i);return a.addAccoutInfo(""),new Oo(t,this.eventApi,a,r)},No.prototype.calQuery=function(t,e){null!=e&&(t=t.slice(0,e));var n=t[0];if(!n)return"";var r="&t=".concat(qo(+new Date));return 1===t.length?"l[]=".concat(n).concat(r):t},No.prototype.remove=function(t){this.heartBeat&&(clearTimeout(this.heartBeat),this.heartBeat=null)},Zt.set("pq",No);var Uo=function(t){var e=t.charAt(t.length-1);return"&"!==e&&"?"!==e||(t=t.slice(0,-1)),t},Fo=function(t,e,n){var r=t,i=ft(t),a="";if(i&&(t=i[1],a=i[2]),!t)return r;var o=lt();e=o.encodeURIComponent(e);var s="array"===Pt(n)?n:[n],c=s.length,d=new o.RegExp("(&|\\?)?(".concat(rt(e),"=)([^&]*)(&|$)"),"g"),u=0;for(t=t.replace(d,(function(t,e,n,r,i){if(e=e||"",u<c){var a=o.encodeURIComponent(s[u]);return u++,e+n+a+i}return e})),t=Uo(t),u<c&&(t+=t.indexOf("?")>=0?"&":"?");u<c;)t+="".concat(e,"=").concat(o.encodeURIComponent(s[u])),++u<c&&(t+="&");return t+a},Lo=function(t,e){var n=t,r=ft(t),i="";if(r&&(t=r[1],i=r[2]),!t)return n;var a=lt(),o=new a.RegExp("(&|\\?)?".concat(rt(a.encodeURIComponent(e)),"=([^&]*)(?:&|$)"),"g");return(t=Uo(t.replace(o,"$1")))+i},Qo={query:function(t,e){var n=t.get("win").location.href;return _t(n,e)},setup:function(t,e){!1!==t.get("useOpenId")&&this.setupShare(t,e)},wxConfig:function(t,e,n){var r=this,i=t.get("win").wx;this.wx=i,i&&(i.config({appId:e.wxAppid,timestamp:n.timestamp,nonceStr:n.nonceStr,signature:n.signature,jsApiList:["onMenuShareTimeline","onMenuShareAppMessage","onMenuShareQQ","onMenuShareWeibo","onMenuShareQZone"]}),i.ready((function(){r.setupShare(t,e)})))},setupShare:function(t,e){var n=this,r=t.genUUID(!0);wx.onMenuShareTimeline(n._genShareConfig(t,r,e,1)),wx.onMenuShareAppMessage(n._genShareConfig(t,r,e,2)),wx.onMenuShareQQ(n._genShareConfig(t,r,e,4)),wx.onMenuShareWeibo(n._genShareConfig(t,r,e,5)),wx.onMenuShareQZone(n._genShareConfig(t,r,e,6))},_genShareConfig:function(t,e,n,r){var i=t.get("win"),a=n.link||i.location.href;a=Lo(a,"qda_shareopenid"),a=Lo(a,"qda_shareid"),a=Lo(a,"qda_shareqid"),a=Lo(a,"qda_sharelevel");var o=a=Lo(a,"qda_sharelinkid");o=Fo(o,"qda_shareid",e),o=Fo(o,"qda_shareopenid",t.get("openid")),o=Fo(o,"qda_shareqid",t.get("qid")),o=Fo(o,"qda_sharelevel",n.shareLevel+1),o=Fo(o,"qda_sharelinkid",n.shareLinkid);var s=this,c={title:n.title||"",link:o,imgUrl:n.imgUrl||"",success:function(){s.shared(t,e,n,r,o),s.setupShare(t,n)}};switch(r){case 1:break;case 2:c.desc=n.desc||"",c.type=n.type||"",c.dataUrl=n.dataUrl||"";case 4:case 5:case 6:c.desc=n.desc||" "}return c},shared:function(t,e,n,r,i){var a="";switch(r){case 1:a="分享到微信朋友圈";break;case 2:a="分享给微信朋友";break;case 3:a="微信的其他渠道";break;case 4:a="分享到QQ";break;case 5:a="分享到QQ空间";break;case 6:a="分享到腾讯微博";break;default:a="未知"}var o=t.getCommonData();o.add("properties",{$level:t.get("level"),$share_to:a,$share_id:e,$share_level:n.shareLevel+1,$share_url_path:i}),t.ping("$share",o,1),n.success&&n.success({$share_to:a,$share_id:e,$share_level:n.shareLevel+1,$share_url_path:i})}},Zo=function(){};Zo.prototype.run=function(t,e){e.shareLevel=parseInt(Dt(t.get("win"),"level"),10)||0,e.shareLinkid=Dt(t.get("win"),"linkid")||t.genUUID(!0),Qo.setup(t,e)};var Ko="weixin".split(","),Wo={weixin:Zo},Vo=function(){this.runners={}};Vo.prototype.run=function(t,e){this.callRunner(t,"run",e)},Vo.prototype.remove=function(t){this.callRunner(t,"remove")},Vo.prototype.callRunner=function(t,e,n){for(var r=0;r<Ko.length;r++){var i=Ko[r],a=this.runners[i]=this.runners[i]||new Wo[i],o=a[e];o&&o.call(a,t,n)}},Zt.set("share",Vo);var $o=Ho(),Xo=function(){},Go="_pid";Xo.prototype.run=function(t){var e=t.get("doc").documentElement,n=this;this.addPid=t.wrap("clc-cb",(function(e){var r=function(t,e,n){"function"==typeof e&&(n=e,e=""),e=(e||"").toLowerCase();for(var r,i,a=t;a&&(r=a,i=void 0,(i=!e||(r&&r.nodeName||"").toLowerCase()===e)&&"function"==typeof n&&(i=n(r)),!i);)a=a.parentNode;return a}(ne(e),"a",(function(t){return t.href}));r&&(Ct(r.href,"wpa_type")||"wpa"===Ct(r.href,"_type"))&&(t.set("wpa_clc",!0),n.timeoutId=$o.setTimeout((function(){t.set("wpa_clc",!1)}),250),Ct(r.href,Go)||(r.href="".concat(r.href,"&").concat(Go,"=").concat(t.get("pid"))))}));var r=t.supportTouch;r&&t.flag(35),ot(e,r?"touchstart":"mousedown",this.addPid)},Xo.prototype.remove=function(t){var e=t.get("doc").documentElement;this.timeoutId&&$o.clearTimeout(this.timeoutId),st(e,t.supportTouch?"touchstart":"mousedown",this.addPid)},Zt.set("wpapid",Xo);var Jo=function(){};Jo.prototype.run=function(t){var e=t.get("win"),n=t.get("doc"),r="".concat(t.get("ats"),"/ar/ActCap/pvRpt");e.__QIDIAN=e.__QIDIAN||{};var i=t.getCommonData(!0);i.add("eptype",te?2:1),i.add("ua",e.navigator.userAgent),i.add("refurl",t.getReferrer()),i.add("title",n.title),i.add("qidianid",t.get("visitorId")),i.add("visitorid",t.get("visitorId")),i.extend(undefined);var a=e.location.hash;"#"===a.charAt(0)&&(a=a.slice(1)),a&&i.add("uh",a),Wt.start("req-track"),Ro(r,i.toJSON(),{transport:"img",callback:t.wrap("track-cb",(function(e){e||(Wt.end("req-track"),t.trigger("track-done"))}),27)})},Jo.prototype.remove=function(t){},Zt.set("track",Jo);var Yo=function(t){var e=1;if(t){var n=0;e=0;for(var r=t.length-1;r>=0;r--)e=(e<<6&268435455)+(n=t.charCodeAt(r))+(n<<14),e=0!=(n=266338304&e)?e^n>>21:e}return e},ts=function(){this.vals={},Do.apply(this,arguments)};To(ts,Do),ts.prototype.set=function(t,e){if("string"==typeof t){var n=this.vals[t];n!==e&&(this.vals[t]=e,this.trigger("change",t,e,n))}else{var r=t;for(var i in r)r.hasOwnProperty(i)&&this.set(i,r[i])}},ts.prototype.get=function(t){return this.vals[t]||""},ts.prototype.map=function(t){for(var e in this.vals)if(this.vals.hasOwnProperty(e)){var n=this.vals[e];n&&t(e,n)}};var es=new ts;function ns(t){return t&&"object"===Pt(t)&&!t.nodeType&&t!==t.window}var rs="__QIDIANDA_SENDED",is=function(t,e,n){this.module=t,this.version=e,this.trackingServer=n};is.prototype={module:"",version:"",trackingServer:"",wrap:function(t,e,n,r){var i=null==e||"function"===Pt(e),a=i?e:e[t],o=function(){var e=r?r.id:null;try{var i;return n&&K.flag(n,e),$.start(t),a&&(i=a.apply(this,arguments)),$.end(t),i}catch(t){t[rs]||(t[rs]=!0)}};return o.displayName=t,i||(e[t]=o),o},errorQueue:[],sending:!1,sendError:function(t,e,n,r,i,a,o){}};var as={};var os={wx:{pv:"/mini/pv",id:"/ping/id",b:"/mini/b","/r/o":"/r/o","/jsonp/wx":"/jsonp/wx",token:"/mini/token",share:"/mini/share",evt:"/mini/evt",page:"/mini/page",track:"/events/sdk/trace"},common:{pv:"/ping/pv",id:"/ping/id",b:"/ping/b","/r/o":"/r/o","/jsonp/wx":"/jsonp/wx",pc:"/ping/pc",clc:"/ping/clc",evt:"/ping/evt",page:"/mini/page",track:"/events/sdk/trace"}},ss=["www.baidu.com","www.sogou.com","www.so.com","www.google.com","www.bing.com","sg.search.yahoo.com","www.youdao.com"],cs={"www.baidu.com":"百度","www.sogou.com":"搜狗","www.so.com":"360","www.google.com":"谷歌","www.bing.com":"bing","sg.search.yahoo.com":"雅虎","www.youdao.com":"有道"};function ds(t){var e=jt("lastSE",null,t)[0],n=document.referrer.split("/")[2]||"";return ss.forEach((function(e){n.indexOf(e)>-1&&Nt("lastSE",e,window,31536e6,null,"/",t)})),{currentSE:cs[n]||"",lastSE:cs[e]||""}}var us=function(t,e,n){var r=(t=t||window).location,i=function(t){return"/"===(t=function(t){return t?(t.length>1&&t.lastIndexOf("/")===t.length-1&&(t=t.substr(0,t.length-1)),0!==t.indexOf("/")&&(t="/".concat(t)),t):"/"}(t))?1:t.split("/").length}(null!=n?n:r.pathname),a=function(t){return function(t){return 0===t.indexOf(".")?t.substr(1):t}(t).split(".").length}(null!=e?e:r.hostname);return"".concat(a+(i>1?"-".concat(i):""),"-")},hs=function(t,e,n,r,i){for(var a=jt(t,e,i),o=us(e,n,r),s=0;s<a.length;s++){var c=a[s];if(0===c.indexOf(o))return c.slice(o.length).replace(/%2d/g,"-")}return""},ls=function(t,e,n,r,i,a,o){var s=us(n,i,a);return e="".concat(e),Nt(t,s+e.replace(/\-/g,"%2d"),n,r,i,a,o)},fs=/[^:]+:\/\/([^:\/]+)/,ps=function(t){var e=t.match(fs);return e?e[1]:""},_s={createSid:function(){this._hasCookie=this.supportCookie,this._hasCookie||this.flag(9),this._sidExpiredTime=0;var t=es.get("sid");if(t)return this.flag(10),void this.set("sid",t);var e=this.get("win"),n=_t(e.location.search,"ADTAG"),r=qo(Yo(n)),i=this.get("doc").referrer,a=qo(Yo(i));if(this._hasCookie){var o,s,c,d=this.getCookieSid(),u=d[0],h=d[1];if(!u||!h)return this.flag(11),void this.recreateSid(r,a);if(u&&h){var l=u.split("."),f=l[0],p=l[1];o=n&&f!==r||i&&p!==a&&(s=i,c=e.location.href,!(ps(s)===ps(c)))}if(!o)return this.flag(12),void this._saveSid(u,h);var _=hs("_qddac",e,null,null,this.qdda).split(".");if(_[0]===r&&_[1]===a)return this.flag(13),void this._saveSid("".concat(_[0],".").concat(_[1]),"".concat(_[2],".").concat(_[3]));this.recreateSid(r,a)}else this.recreateSid(r,a)},recreateSid:function(t,e){t=t||qo(Yo(_t(this.get("win").location.search,"ADTAG"))),e=e||qo(Yo(this.get("doc").referrer));var n="".concat(t,".").concat(e),r=this.random();this._saveSid(n,r)},refreshCookie:function(){if(this._hasCookie){var t=this.getCookieSid(),e=t[0],n=t[1];if(e&&n){var r=this.get("sid");if(r[0]!==e||r[1]!==n)return void this.flag(15);this._setSidCookie()}else this.recreateSid()}else{+new Date>=this._sidExpiredTime&&(this.flag(14),this.recreateSid())}},_setSidCookie:function(){var t=18e5,e=+new Date,n=new Date;if(n.setHours(23),n.setMinutes(59),n.setSeconds(59),n.setMilliseconds(999),n=+n,this._hasCookie){var r=this.get("win"),i=this.get("sid"),a=ls("_qdda",i[0],r,Math.min(t,n-e),null,"/",this.qdda);a&=ls("_qddab",i[1],r,null,null,"/",this.qdda),this._hasCookie=a}this._hasCookie||(this._sidExpiredTime=Math.min(e+t,n))},_saveSid:function(t,e){this._setGlobal("sid",[t,e]),this._setSidCookie()},_getSid:function(){var t=this.get("sid");return t?t.join("."):""},getSid:function(t){return t||this.refreshCookie(),this._getSid()},getCookieSid:function(){var t=this.get("win");return[hs("_qdda",t,null,"/",this.qdda),hs("_qddab",t,null,"/",this.qdda)]},setClosedSid:function(){if(this._hasCookie){var t=this.getCookieSid(),e=t[0],n=t[1];if(e&&n){var r=this.get("win"),i=this._getSid();ls("_qddac",i,r,Math.max(this._getLoadedTime()+5e3,1e4),null,null,this.qdda)}}},_getLoadedTime:function(){var t=this.get("win"),e=t.performance||t.webkitPerformance,n=e&&e.timing;if(!n)return 0;var r=n.navigationStart;return 0===r?0:n.loadEventStart-r}},vs="anonymous_id",gs=[vs];var ws,ms=Ho(),ys=function(t,e,n){if(as[t])return as[t];var r=new is(t,e,n);return as[t]=r,r}("i"),bs="__qq_qidian_da_pid",ks=+new Date;Zt.set("pq",No),Zt.set("pv",Vt),Zt.set("ps",_e),Zt.set("id",ee),Zt.set("clc",ie),Zt.set("pc",ue),Zt.set("share",Vo),Zt.set("wpapid",Xo),Zt.set("track",Jo);var xs=function(t,e,n,r){var i,a=this;a.qdda=t,ts.apply(a,arguments),a.name=n,r&&(a.flag(6),a.set(r)),a.supportCookie=Ft(),a.supportTouch=(i="DocumentTouch",!!("ontouchstart"in window||window[i]&&document instanceof window[i])),a.id=e,a.timerId=void 0,a.createIds(),a.tasks={},a.task("pq"),a.tasks.pq.start();var o=function(t){try{var e=window.localStorage.getItem((t&&t._storage_prefix||"")+Po);return JSON.parse(e)}catch(t){console.log("[QDTRACKER_ERROR]",t)}}(a.qdda);o&&o.forEach((function(t){var e=new zo(t);e.hasKey("time_free")||e.add("time_free",!0),a.tasks.pq.push("",new zo(t))})),function(t){try{window.localStorage.removeItem((t&&t._storage_prefix||"")+Po)}catch(t){console.log("[QDTRACKER_ERROR]",t)}}(a.qdda),t.provided.forEach((function(t){a[t]=a.qdda[t]})),a.init()};xs.prototype={init:function(){var t=this;this.get("preventAutoTrack")||(this.get("singlePage")&&this.singlePageEvent(),t.task("pv"),t.task("pc"),t.on("pv-done",(function(){t.task("id")}))),this.get("pagestay")&&t.task("ps"),t.task("wpapid")},openPageStay:function(){var t=this;try{if(!this.vals.pagestay&&!this.vals.pageStayInited){var e="hidden"in document?"hidden":"webkitHidden"in document?"webkitHidden":"mozHidden"in document?"mozHidden":"msHidden"in document?"msHidden":"",n=e.replace(/hidden/i,"visibilitychange"),r=this;!this.vals.pageStayInited&&n&&ot(window,n,(function(){try{if(document[e]){timeSpanStay=+new Date-ks;var t=r.getCommonData();if(t.add("properties",{dr:timeSpanStay}),r.qdda&&(r.qdda.bussid||r.qdda.qq||r.qdda.qda_accountInfo)){var n={};r.qdda.bussid&&(n.bussid=r.qdda.bussid),r.qdda.qq&&(n.qq=r.qdda.qq),r.qdda.qda_accountInfo&&(n=l(l({},n),this.qdda.qda_accountInfo)),t.add("account",n)}r.ping("$pagestay",t,3)}else ks=+new Date}catch(t){console.log("[QDTRACKER_ERROR]","QidianDA_WEBSDK openPageStay Error")}})),n&&(this.vals.pageStayInited=!0);var i=window.location.href;ms.setInterval((function(){try{var e=window.location.href;if(e!==i){timeSpanStay=+new Date-ks;var n=r.getCommonData();if(n.add("properties",{dr:timeSpanStay,url:decodeURIComponent(i)}),r.qdda&&(r.qdda.bussid||r.qdda.qq||r.qdda.qda_accountInfo)){var a={};r.qdda.bussid&&(a.bussid=r.qdda.bussid),r.qdda.qq&&(a.qq=r.qdda.qq),r.qdda.qda_accountInfo&&(a=l(l({},a),t.qdda.qda_accountInfo)),n.add("account",a)}r.ping("$pagestay",n,3),ks=+new Date,i=e}}catch(t){console.log("[QDTRACKER_ERROR]","QidianDA_WEBSDK openPageStay Error")}}),5e3)}}catch(t){console.log("[QDTRACKER_ERROR]","QidianDA_WEBSDK openPageStay Error")}},singlePageEvent:function(){try{var t=this;!function(){d.href;var e=window.history.pushState,n=window.history.replaceState;e&&(window.history.pushState=function(){e.apply(window.history,arguments),t.timerId&&(ms.clearTimeout(t.timerId),t.timerId=void 0),t.timerId=ms.setTimeout((function(){t.reset()})),d.href}),n&&(window.history.replaceState=function(){n.apply(window.history,arguments),t.timerId&&(ms.clearTimeout(t.timerId),t.timerId=void 0),t.timerId=ms.setTimeout((function(){t.reset()})),d.href});var r=e?"popstate":"hashchange",i=function(){t.timerId=ms.setTimeout((function(){t.reset()})),d.href};document.removeEventListener(r,i),document.addEventListener(r,i)}()}catch(t){console.log("[QDTRACKER_ERROR]","QidianDA_WEBSDK SinglePageEvent Error")}},createIds:function(){this.createPid(),this.createQid(),this.createSid(),this.createVisitorId()},_setGlobal:function(t,e){this.set(t,e),es.set(t,e)},_genQDAValue:function(t){return"QD.".concat(t)},_calQDAValue:function(t){if(!t||"string"!=typeof t)return"";var e=t.match(/QD\.([\w\W]+)/);return e&&e[1]||""},genUUID:function(t){var e=this.get("win"),n=this.get("doc"),r=e.navigator.userAgent+(n.cookie?n.cookie:"")+this.getReferrer()+e.location.href;return r="".concat(qo(Y()^**********&Yo(r)),"."),t?"".concat(qo(+new Date),".").concat(r).concat(tt()):r+this.random()},createPid:function(){var t=this.get("win"),e="__qq_qidian_da_pid",n=t[e],r=this._calQDAValue(n);r||(r=this.genUUID(),t[e]=this._genQDAValue(r)),this.set("pid",r)},createVisitorId:function(){var t=this.get("win"),e="__QIDIAN",n=e;this.qdda&&this.qdda._storage_prefix&&(n=this.qdda._storage_prefix+e);var r=t[n]&&t[n].visitorId;if(!r){var i="tencentSig";if(Lt()?r=this.qdda&&this.qdda._storage_prefix?t.localStorage.getItem(this.qdda._storage_prefix+i):t.localStorage.getItem(i):Ft()&&(r=(r=jt(i,null,this.qdda))&&r[0]),!r){r=(Math.floor(10*Math.random())||1)+function(t,e){var n,r,i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),a=[];if(e=e||i.length,t)for(n=0;n<t;n++)a[n]=i[0|Math.random()*e];else for(a[8]=a[13]=a[18]=a[23]="-",a[14]="4",n=0;n<36;n++)a[n]||(r=0|16*Math.random(),a[n]=i[19==n?3&r|8:r]);return a.join("")}(3,10)+(new Date).getTime().toString().slice(2,13);var a=jt("_qddaz",null,this.qdda)[0];a=jt("_qddaz",null,this.qdda)[0];var o=this._calQDAValue(a);o&&(r=o),Lt()?this.qdda&&this.qdda._storage_prefix?t.localStorage.setItem(this.qdda._storage_prefix+i,r):t.localStorage.setItem(i,r):Ft()&&Nt(i,r,null,null,null,null,this.qdda)}}return r=String(r),this.set("visitorId",r),r},createQid:function(){var t,e,n=this.get("win");if(this.supportCookie){var r=this.get("qidkey")||"_qddaz",i=31536e6;t=jt(r,null,this.qdda)[0],(e=this._calQDAValue(t))&&!isNaN(Number(e))||(e=this.createVisitorId()),t=this._genQDAValue(e);var a,o=this.get("qiddomain");if(o)Nt(r,t,n,i,o,"/",this.qdda)&&(a=o);else a=function(t,e,n,r,i,a){var o,s={".com":1,".net":1,".org":1,".edu":1,".gov":1,".cn":1,".tw":1,".hk":1};if(i)for(o=0;o<i.length;o++)s[".".concat(i[o])]=1;var c=n.location.hostname.split("."),d="",u="";for(o=c.length-1;o>=0;o--)if(!s[u=".".concat(c[o]).concat(u)]&&Nt(t,e,n,r,u,"/",a)){d=u;break}return d}(r,t,n,i,["qq.com"],this.qdda);a||Nt(r,t,n,i,null,"/",this.qdda)}else t=n[bs],(e=this._calQDAValue(t))||(e=randomId()),n[bs]=this._genQDAValue(e);return this.set("qid",e),e},setValsByHand:function(t){try{t.appkey&&this.set("appkey",t.appkey),this.qdda&&(this.qdda.da_rpt_type=t.da_rpt_type)}catch(t){console.log("[QDTRACKER_ERROR]","setValsByHand",t)}},decodeUrl:function(t){return decodeURIComponent(t)},getSessionInterval:function(){var t=parseInt(window.localStorage.getItem("sdk_session_start")||(new Date).getTime(),10);return(new Date).getTime()-t},getCommonData:function(t){var e=this,n=ds(this.qdda),r=new zo,i={sdk_version:this.get("ver"),page_id:this.get("pigd"),sdk_type:"jssdk",title:this.get("doc").title,referrer:this.getReferrer(),session_interval:this.getSessionInterval()};n.currentSE&&(i.search_engine=n.currentSE),n.lastSE&&(i.latest_search_engine=n.lastSE),i.url=this.decodeUrl(this.get("doc").location.href),this.get("is_first_day")&&(i.is_first_day=this.get("is_first_day"));var a=this.get("kfuin");a&&r.add("kfuin",a),this.get("appkey")&&r.add("appkey",this.get("appkey")),r.add("type",this.qdda&&this.qdda.da_rpt_type||"track"),r.add("time",+new Date);var o=this.qdda.outerData;if(o)for(var s in o)r.add(s,o[s]);this.get("useOpenId")&&this.get("openid");var c=this.qdda.commonData;if(c)for(var u in c)i[u]=c[u];["utm","latestutm"].forEach((function(t){var n=e.get(t);if(n)for(var r in n)i[r]=n[r]}));var h=this.qdda.properties.commonData;if(h)for(var l in h)i[l]=h[l];r.add("properties",i),r.json&&r.json.forEach((function(t){"qid"===t.key&&(t.value=String(t.value))}));var f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={};try{var r=_t(d.href,"_qdasdk",e),i=JSON.parse(decodeURIComponent(r));Object.keys(i).forEach((function(t){gs.includes(t)&&(n[t]=i[t])}))}catch(t){}return n[t]||""}(vs,this.get("afterHash"));return r.addAccoutInfo("anonymous_id",f||this.get("qid")),r},getCommonQuery:function(t){return Ht(this.getCommonData(t).toJSON())},getFullApi:function(t){var e="common";return this.get("useOpenId")&&(e="wx"),this.get("ts")+os[e][t]},getReferrer:function(){return this.decodeUrl(this.qdda.referrer||this.get("doc").referrer)},ping:function(t,e,n,r,i){"$pageclose"===t&&this.setClosedSid(),e.add("event",t);var a=this.tasks.pq,o=window.localStorage.getItem("sdk_session"),s=this.get("session_interval")||18e5,c=function(){return ms.setTimeout((function(){e.json=e.filter({dr:1}),window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","$SessionEnd",e),window.localStorage.setItem("sdk_session",""),window.localStorage.setItem("sdk_session_start","")}),s)};if(Lt()){var d=window.localStorage.getItem("sdk_session_start");if(e.add("sessionId",o),o){if(d){var u=window.localStorage.getItem("sdk_session_time_id");ms.clearTimeout(Number(u)),window.localStorage.setItem("sdk_session_start",(new Date).getTime()),u=c(),window.localStorage.setItem("sdk_session_time_id",u)}}else{o=tt();var h=c();window.localStorage.setItem("sdk_session",o),window.localStorage.setItem("sdk_session_start",(new Date).getTime()),window.localStorage.setItem("sdk_session_time_id",h),e.json=e.filter({dr:1})}}window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","event",e),a.push(t,e,n||0,r,i)},jsonp:function(t,e,n){!function(t,e,n,r,i,a){var o=i?i.ownerDocument:document,s=o.defaultView||o.parentWindow,c=o.createElement("SCRIPT");a=a||"callback",n=n||1e4;var d,u,h=new RegExp("(?:\\?|&)".concat(a,"=([^&]*)")),l=function(t){return function(){try{if(t){var n=new Error;n.name="Timeout",e.call(s,n)}else{var r=Tt(arguments);r.unshift(null),e.apply(s,r),lt().clearTimeout(d)}s[u]=null,delete s[u]}catch(n){Q(n)}finally{c&&c.parentNode&&c.parentNode.removeChild(c)}}},f=t.match(h);u=f?f[1]:"S3JSONPPREFIX"+tt(),s[u]=l(!1),n&&(d=lt().setTimeout(l(!0),n)),f||(t+="".concat((t.indexOf("?")<0?"?":"&")+a,"=").concat(u)),function(t,e,n,r,i){if(t.setAttribute("type","text/javascript"),r&&t.setAttribute("charset",r),t.setAttribute("src",e),i)i.insertBefore(t,i.firstChild);else{var a=n.getElementsByTagName("script"),o=a[a.length-1];if(o)o.parentNode.insertBefore(t,o);else{var s=n.getElementsByTagName("head")[0];s.insertBefore(t,s.firstChild)}}}(c,t,o,r,i)}("".concat(t,"?").concat(Ht(e.toJSON())),n)},task:function(t){var e=this.tasks[t];if(!e){var n=Zt.get(t);if(!n)return void this.flag(8);e=this.tasks[t]=new n}var r=Tt(arguments,1);r.unshift(this),e.run.apply(e,r)},remove:function(){for(var t in this.tasks)"pq"!==t&&this.tasks.hasOwnProperty(t)&&this.tasks[t].remove(this);var e=this.tasks.pq;this.tasks={pq:e}},random:function(){return"".concat(tt(),".").concat(qo(+new Date))},flag:function(t){K.flag(t,this.id)},wrap:function(t,e,n){return ys.wrap(t,e,n,this)},sendError:function(t,e,n,r,i){},track:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;try{var i=this.getCommonData();if("$pageview"===t&&(i=this.getPVParams()),i.add("event",t),this.qdda.bussid||this.qdda.qq||this.qdda.qda_accountInfo){var a={};for(var o in this.qdda.bussid&&(a.bussid=this.qdda.bussid),this.qdda.qq&&(a.qq=this.qdda.qq),this.qdda.qda_accountInfo&&(a=l(l({},a),this.qdda.qda_accountInfo)),a)i.addAccoutInfo(o,a[o])}i.add("properties",e),this.ping(t,i,0,n,r)}catch(t){console.log("[QDTRACKER_ERROR]","track",t)}},privateTrack:function(t,e,n,r){try{var i=this.vals;e!==i.appkey&&e!==i.kfuin||this.track(t,n,r)}catch(t){console.log("[QDTRACKER_ERROR]","privateTrack",t)}},getWechatValue:function(t){return this.qdda.wechat&&this.qdda.wechat[t]||this.get(t)},getIDs:function(t){try{window.QDTracker.debugMode&&console.log("[QDTRACKER_LOG]","openid:".concat(this.get("openid"),"qid:").concat(this.get("qid")));var e={qid:this.get("qid")},n=this.getWechatValue("openid");n&&(e.openid=n);var r=this.getWechatValue("unionid");r&&(e.unionid=r),t&&t(e)}catch(t){console.log("[QDTRACKER_ERROR]","getIDs",t)}},send:function(t){var e,n="send".concat((e=t,(e=String(e)).charAt(0).toUpperCase()+e.slice(1).toLowerCase())),r=Array.prototype.slice.call(arguments,1);"function"==typeof this[n]&&this[n].apply(this,r)},reset:function(t){try{this.remove(),window[bs]=null,this.createIds(),this.init()}catch(t){console.log("[QDTRACKER_ERROR]","reset",t)}},canSend:function(){return!this.get("useOpenId")||this.get("openid")},getPVParams:function(){var t=this.get("win");this.get("doc"),this.getFullApi("pv");var e=t.screen,n=e.orientation||e.mozOrientation||e.msOrientation||"";n&&n.type&&(n=n.type),ws||(ws={tz:(new Date).getTimezoneOffset()/60});var r=this.getCommonData(!0),i=t.location.hash,a={};if(a={level:this.get("level"),sw:e.width,sh:e.height,bw:Et(t),bh:At(t)},this.get("fqid")&&(a.$share_qid=this.get("fqid")),this.get("foid")&&(a.$share_oid=this.get("foid")),"#"===i.charAt(0)&&(i=i.slice(1)),i&&(a.$uh=i),r.add("properties",ws),r.add("properties",a),this.qdda.bussid||this.qdda.qq||this.qdda.qda_accountInfo){var o={};this.qdda.bussid&&(o.bussid=this.qdda.bussid),this.qdda.qq&&(o.qq=this.qdda.qq),this.qdda.qda_accountInfo&&(o=l(l({},o),this.qdda.qda_accountInfo)),r.add("account",o)}return r}},function(t,e,n){var r;function i(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])}"boolean"!=typeof t?(n=e,e=t,r=i):r=t?function t(e,n){for(var r in n)n.hasOwnProperty(r)&&(ns(n[r])?(e[r]=e[r]||{},t(e[r],n[r])):e[r]=n[r])}:i,r(e,n)}(xs.prototype,_s),To(xs,ts);var Ss=function(){function o(){_(this,o),this.id=tt()}return g(o,[{key:"initAssembler",value:function(t){!function(t){var e=t.properties.commonData;e=It(e),e=Ot(e),e=Rt(e),e=qt(e),t.properties.commonData=e}(t)}},{key:"initCaptor",value:function(t){}},{key:"initMisc",value:function(t){}},{key:"initStorage",value:function(t){}},{key:"initTracker",value:function(o){!function(o){o.tracker.create=function(s,c,d,u){try{"string"!=typeof c&&(u=d,d=c,c=""),isNaN(Number(s))&&(c=s,s="");var h,l,f=s+(c?"|".concat(c):"");if(o.loadedTrackers[f])return;if(o.loadedTrackers[f]=!0,"string"==typeof d?(l=d,h=u||{}):(h=d||{},l=null==u?"_".concat(tt()):u),o.trackers[l])throw new Error('Tracker name: "'.concat(l,'" exist.'));t.navigator.userAgent.toLowerCase().indexOf("micromessenger")<0&&(h.useOpenId=!1);var p=Dt(t,"id");h.fid=p;var _=Dt(t,"openid");h.foid=_;var v=Dt(t,"qid");h.fqid=v;var g=Dt(t,"level");return h.level=g,c&&(h.appkey=c),s&&(h.kfuin=s),h.win=t,h.doc=t.document,h.ver=o.version,h.ptc=e,h.ts=h.url||o.getParams("privateDomain")||n,h.ats=i,h.authors=a,h.ss=r,h.hbt=h.track_interval||0,h.utm=o.getParams("utm"),h.latestutm=o.getParams("latestutm"),h.is_first_day=o.getParams("firstDay"),h.ping_method||(h.ping_method="POST"),h.encrypt_mode||(h.encrypt_mode="default"),h.enable_compression=!!h.enable_compression,o.trackers[l]=new xs(o,f,l,h),o._runPlugins(),o.createdFunc&&o.createdFunc.call(window),o.trackers[l]}catch(t){console.log("[QDTRACKER_ERROR]","create",t)}}}(o)}},{key:"setup",value:function(){var t="__qq_qidian_da_market_svr_domain";window[t]&&(n=window[t])}}],[{key:"run",value:function(t){var e=o.getInstance();return e.setup(t),e.initAssembler(t),e.initCaptor(t),e.initMisc(t),e.initStorage(t),e.initTracker(t),e}},{key:"getInstance",value:function(){return this.instance?this.instance:this.instance=new o}}]),o}(),Es={Boolean:1,Number:1,String:1,Function:1,Array:1,Date:1,RegExp:1,Object:1,Error:1},As=Object.prototype.toString;Object.keys(Es).forEach((function(t){}));var Rs="__QIDIANDA_SENDED",Is=function(t,e,n){this.module=t,this.version=e,this.trackingServer=n};Is.prototype={module:"",version:"",trackingServer:"",wrap:function(t,e,n,r){var i=null==e||"function"===function(t){if(null==t)return String(t);var e=p(t),n="object";return e===n||"function"===e?(e=As.call(t).slice(8,-1),Es[e]?e.toLowerCase():n):p(t)}(e),a=i?e:e[t],o=function(){var e=r?r.id:null;try{var i;return n&&K.flag(n,e),$.start(t),a&&(i=a.apply(this,arguments)),$.end(t),i}catch(t){t[Rs]||(t[Rs]=!0)}};return o.displayName=t,i||(e[t]=o),o},errorQueue:[],sending:!1,sendError:function(t,e,n,r,i,a,o){}};var Os={};var qs=function(t){var e=t||window,n="document";return e[n]&&(e=e[n]),"prerender"===e.visibilityState};!function(t){var e=window.__qda_workerTimer__;if(!/MSIE 10/i.test(navigator.userAgent))try{var n=new Blob(["\t\t\t\t\tvar fakeIdToId = {};\t\t\t\t\tonmessage = function (event) {\t\t\t\t\t\tvar data = event.data,\t\t\t\t\t\t\tname = data.name,\t\t\t\t\t\t\tfakeId = data.fakeId,\t\t\t\t\t\t\ttime;\t\t\t\t\t\tif(data.hasOwnProperty('time')) {\t\t\t\t\t\t\ttime = data.time;\t\t\t\t\t\t}\t\t\t\t\t\tswitch (name) {\t\t\t\t\t\t\tcase 'setInterval':\t\t\t\t\t\t\t\tfakeIdToId[fakeId] = setInterval(function () {\t\t\t\t\t\t\t\t\tpostMessage({fakeId: fakeId});\t\t\t\t\t\t\t\t}, time);\t\t\t\t\t\t\t\tbreak;\t\t\t\t\t\t\tcase 'clearInterval':\t\t\t\t\t\t\t\tif (fakeIdToId.hasOwnProperty (fakeId)) {\t\t\t\t\t\t\t\t\tclearInterval(fakeIdToId[fakeId]);\t\t\t\t\t\t\t\t\tdelete fakeIdToId[fakeId];\t\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\tbreak;\t\t\t\t\t\t\tcase 'setTimeout':\t\t\t\t\t\t\t\tfakeIdToId[fakeId] = setTimeout(function () {\t\t\t\t\t\t\t\t\tpostMessage({fakeId: fakeId});\t\t\t\t\t\t\t\t\tif (fakeIdToId.hasOwnProperty (fakeId)) {\t\t\t\t\t\t\t\t\t\tdelete fakeIdToId[fakeId];\t\t\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\t}, time);\t\t\t\t\t\t\t\tbreak;\t\t\t\t\t\t\tcase 'clearTimeout':\t\t\t\t\t\t\t\tif (fakeIdToId.hasOwnProperty (fakeId)) {\t\t\t\t\t\t\t\t\tclearTimeout(fakeIdToId[fakeId]);\t\t\t\t\t\t\t\t\tdelete fakeIdToId[fakeId];\t\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\tbreak;\t\t\t\t\t\t}\t\t\t\t\t}\t\t\t\t"]);t=window.URL.createObjectURL(n)}catch(s){}var r,i={},a=0,o="[workerTimer]-";if("undefined"!=typeof Worker){function c(){do{**********==a?a=0:a++}while(i.hasOwnProperty(a));return a}try{r=new Worker(t),e.setInterval=function(t,e){var n=c();return i[n]={callback:t,parameters:Array.prototype.slice.call(arguments,2)},r.postMessage({name:"setInterval",fakeId:n,time:e}),n},e.clearInterval=function(t){i.hasOwnProperty(t)&&(delete i[t],r.postMessage({name:"clearInterval",fakeId:t}))},e.setTimeout=function(t,e){var n=c();return i[n]={callback:t,parameters:Array.prototype.slice.call(arguments,2),isTimeout:!0},r.postMessage({name:"setTimeout",fakeId:n,time:e}),n},e.clearTimeout=function(t){i.hasOwnProperty(t)&&(delete i[t],r.postMessage({name:"clearTimeout",fakeId:t}))},r.onmessage=function(t){var e,n,r,a=t.data.fakeId;if(i.hasOwnProperty(a)&&(r=(e=i[a]).callback,n=e.parameters,e.hasOwnProperty("isTimeout")&&e.isTimeout&&delete i[a]),"string"==typeof r)try{r=new Function(r)}catch(t){console.log(o+"Error parsing callback code string: ",t)}"function"==typeof r&&r.apply(window,n)},r.onerror=function(t){console.log(t)}}catch(d){console.log(o+"workerTImer Initialisation failed",d)}}}("worker.js");J.version=window.VERSION,J.platform="web",window.QDTracker||(window.QDTracker=J),Ss.run(J);var Cs=function(t,e,n){if(Os[t])return Os[t];var r=new Is(t,e,n);return Os[t]=r,r}("i",VERSION,n);J.setParams("m",Cs);var Ds=Cs.wrap("run",(function(t,e){if(!function(t){var e="navigator";return(t=t||window)[e]&&"preview"===t[e].loadPurpose}(t=t||window)){var n=t.document,r=!1,i=function(){e(t)};qs(t)?ot(n,"visibilitychange",(function e(){r||qs(t)||(r=!0,i(),st(n,"visibilitychange",e))})):i()}}),1),Ts=(window.localStorage.setItem("sdk_session",""),window.localStorage.setItem("sdk_session_start",""),void window.localStorage.setItem("sdk_session_time_id",""));Ds(t,Cs.wrap("init",Ts,2));export{J as default};
