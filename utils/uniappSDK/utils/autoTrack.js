!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).autoTrack=t()}(this,(function(){"use strict";const e={A:!0,BUTTON:!0,INPUT:!0,TEXTAREA:!0};let t={};const n=["MARK","STRONG","B","EM","I","U","ABBR","INS","DEL","S","SUP"];let o=["data-qidian-click"],r=!0,c=!1;function i(n,o=1){const r=function(n){const o=n.tagName.toUpperCase();return Object.assign({},e,t)[o]}(n);if(!0===r&&function(e){return!e.children.length}(n))return!0;if("object"==typeof r){const e=r.max_level||1;return a(n)<=e}return!1}function a(e,t=1){if(!e.children||0===e.children.length)return t;let n=t;for(const o of e.children){const e=a(o,t+1);n=Math.max(n,e)}return n}return{apply:function(e,a){const s=e.getCommonParams()[0].heatmap||{},l="default"===s.clickmap;function u(t){if(!r)return;const{target:c}=t;let s=!1;n.includes(c.tagName.toUpperCase())&&function(e){if("DIV"!==e.tagName.toUpperCase())return!1;const{children:t}=e;if(!t.length)return!1;for(let e=0;e<t.length;e++)if(!n.includes(t[e].tagName.toUpperCase()))return!1;return!0}(c.parentElement)&&(console.log("[QDTRACKER] div只包含样式标签"),!s&&f(e,a,c),s=!0),function(e){for(let t=0;t<o.length;t++)if(e.hasAttribute(o[t]))return!0;return!1}(c)&&(console.log("[QDTRACKER] track_attr 中自定义属性"),!s&&f(e,a,c),s=!0),i(c)&&(console.log("[QDTRACKER] interceptTags 中配置标签"),!s&&f(e,a,c),s=!0)}t=s.collect_tags,s.track_attr&&(o=s.track_attr.concat(o)),c=s.iframeEnable;const f=function(e,t,n){const o=e.tracker.track,r=function(e){const t=e,n=t.id,o=t.innerText,r=t.getAttribute("name"),c=t.className,i=t.tagName.toLowerCase(),a=function(e){const t=[];let n=e;function o(e){let t=1;for(;e.previousSibling;)1===(e=e.previousSibling).nodeType&&(t+=1);return t}for(;n!==e.ownerDocument;){let e=n.tagName.toLowerCase();n.id?e+=`[@id='${n.id}']`:n.className?e+=`[contains(@class, '${n.className.replace(/\s+/g," ")}')]`:"html"!==e&&"body"!==e&&(e+=`[${o(n)}]`),t.unshift(e),n=n.parentNode}return`/${t.join("/")}`}(t),s=t.getBoundingClientRect().left+t.offsetWidth/2,l=t.getBoundingClientRect().top+t.offsetHeight/2;return{element_id:n,element_content:o,element_name:r,element_class_name:c,element_type:i,element_path:a,touch_x:parseInt(s,10),touch_y:parseInt(l,10)}}(n);o("$WebClick",r)};function d(e){try{e.contentWindow.document.removeEventListener("click",u,!0),e.contentWindow.document.addEventListener("click",u,!0)}catch(e){console.error("无法为 iframe 添加点击事件监听器:",e)}}if(l&&(document.removeEventListener("click",u,!0),document.addEventListener("click",u,!0),c)){const e=document.getElementsByTagName("iframe");for(const t of e)d(t);new MutationObserver((e=>{for(const t of e)for(const e of t.addedNodes)e.tagName&&"IFRAME"===e.tagName.toUpperCase()&&d(e)})).observe(document.body,{childList:!0,subtree:!0})}},startIntercepting:function(){r=!0},stopIntercepting:function(){r=!1},startInterceptingIframe:function(){c=!0},stopInterceptingIframe:function(){c=!1}}}));
