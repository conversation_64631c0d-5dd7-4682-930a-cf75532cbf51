/*
*  2023.12.06 uniApp 兼容版本主入口
* */
// 兜底通用版本所有接口
import blankAPI from './adapter/blankApi';

// 参照环境变量设置当前环境
// #ifdef H5
import adaptApi from './adapter/web'
// #endif

// #ifdef MP-WEIXIN || MP-BAIDU || MP-ALIPAY
import adaptApi from './adapter/mp';
// #endif

// #ifdef APP
import adaptApi from './adapter/app';
// #endif

let QDTracker = {};

const refactTrack = function (QDTracker, key) {
    if (key === 'track') {
        const oldTrack = QDTracker.track;
        QDTracker.track = function () {
            const arr = [].slice.call(arguments, 0);

            // 留有拓展uni后台感知逻辑
            return oldTrack.apply(QDTracker, arr);
        };
    }
};

// 平台检测，目前支持H5 & MP & App
if (typeof adaptApi === 'undefined') {
    console.error('该平台尚未兼容敬请期待');
    QDTracker = blankAPI;
} else {
    QDTracker = adaptApi;
    // 单独抽离适配器init
    const initFn = QDTracker.init
    /*
        由于小程默认跨三种小程序平台，先遍历所有uni版本中需要暴露的api
        1. 对应api中有适配版本，优先使用适配版本
        2. 没有适配版本，则默认兼容，从实例中直接获取默认接口
        3. web版本无需通过
    */
    // #ifdef H5 || APP
    QDTracker = {
        ...QDTracker,
        ...adaptApi.instance,
        init: initFn
    };
    // #endif
    // #ifdef MP-WEIXIN || MP-BAIDU || MP-ALIPAY
    Object.keys(blankAPI).forEach((key) => {
        if (!(key in adaptApi)) {
            if (typeof adaptApi.instance === 'object' && typeof adaptApi.instance[key] === 'function') {
                QDTracker[key] = adaptApi.instance[key].bind(adaptApi.instance);
            } else {
                QDTracker[key] = blankAPI[key].bind(blankAPI);
            }
        }
        // track更新逻辑
        refactTrack(QDTracker, key);
    });
    // #endif
}

const QDTMixin = adaptApi.QDTMixin;

export {
    QDTracker,
    QDTMixin,
}
