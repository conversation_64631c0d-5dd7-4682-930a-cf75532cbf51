import { showType, fn, dataType } from '@/types/index.d'

export const isObject = (data: any) => {
  return Object.prototype.toString.call(data) == '[object Object]'
}
export const isArray = (data: any) => {
  return Array.prototype.toString.call(data) == '[object Array]'
}

export const showToast = (opts: string | undefined | Partial<showType>) => {
  let showToastOptions: Partial<showType> = {
    title: '',
    icon: 'none',
    mask: true
  }
  if (typeof opts == 'string') {
    showToastOptions.title = opts
  }
  if (isObject(opts)) {
    showToastOptions = {...showToastOptions, ...(opts as Partial<showType>)}
  }
  uni.showToast(showToastOptions)
}

export const showLoading = (opts ?: string | Partial<showType>) => {
  let showLoadingOptions: Partial<showType> = {
    title: '',
    icon: 'none',
    mask: true
  }
  if (typeof opts == 'string') {
    showLoadingOptions.title = opts
  }
  if (isObject(opts)) {
    showLoadingOptions = {...showLoadingOptions, ...(opts as Partial<showType>)}
  }
  uni.showLoading(showLoadingOptions)
}

export const hideLoading = () => {
  wx.hideLoading({
    noConflict: true
  })
}

export const showModal = (opts: string | UniApp.ShowModalOptions, callback ?: (args: UniApp.ShowModalRes) => void) => {
  let showModalOptions: UniApp.ShowModalOptions = {
    content: '',
    confirmColor: '#C35943',
    cancelColor: '#777',
    success: (res: UniApp.ShowModalRes) => {
      callback?.(res)
    }
  }
  if (typeof opts == 'string') {
    showModalOptions.content = opts
  }
  if (isObject(opts)) {
    showModalOptions = {...showModalOptions, ...(opts as UniApp.ShowModalOptions)}
  }
  uni.showModal<UniApp.ShowModalOptions>(showModalOptions)
}

export const debounce = function (fn: fn, wait: number) {
  let timeout: any = null;
  return function (...args: any) {
    let context = this;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      fn.apply(context, args)
    }, wait);
  };
}

export const throttle = function (func: fn, delay: number) {
  let lastCall = 0; // 上一次调用的时间

  return function (...args: any) {
    const now = Date.now(); // 当前时间

    if (now - lastCall >= delay) {
      lastCall = now; // 更新上一次调用时间
      return func.apply(this, args); // 调用传入的函数
    }
  };
}

export const formatDate = (date: Date = new Date()) => {
  const newDate = new Date(date)
  const y = newDate.getFullYear()
  const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
  const d = newDate.getDate().toString().padStart(2, 0)
  return `${y}-${m}-${d}`
}

export const formatMonth = (date: Date = new Date()) => {
  const newDate = new Date(date)
  const y = newDate.getFullYear()
  const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
  return `${y}-${m}`
}

export const setMonth = (month: number = 1, date: Date = new Date()) => {
  const newDate = new Date(date)
  const currentDay = newDate.getDate()
  newDate.setMonth(newDate.getMonth() - month)
  if (newDate.getDate() < currentDay) {
    newDate.setDate(0)
  }
  return formatDate(newDate)
}

export const setTime = (date: Date = new Date()) => {
  const newDate = new Date(date)
  const h = newDate.getHours().toString().padStart(2, 0)
  const m = newDate.getMinutes().toString().padStart(2, 0)
  return `${h}:${m}`
}
export const setDay = (day: number = 0, type: string = '-', date: Date = new Date()) => {
  const newDate = new Date(date)
  if (type == '+') {
    return formatDate(new Date(newDate.setDate(newDate.getDate() + day)))
  } else {
    return formatDate(new Date(newDate.setDate(newDate.getDate() - day)))
  }
}
export const fullDate = (date?: Date = new Date()) => {
  const newDate = new Date(date)
  const y = newDate.getFullYear()
  const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
  const d = newDate.getDate().toString().padStart(2, 0)
  const h = newDate.getHours().toString().padStart(2, 0)
  const mm = newDate.getMinutes().toString().padStart(2, 0)
  const s = newDate.getSeconds().toString().padStart(2, 0)
  return `${y}-${m}-${d} ${h}:${mm}:${s}`
}


/**
 * 移除字符串的空格
 * @param {*} str 传字符串
 * @param {Number} type 移除的位置 1 前  2 后  3 前后  4全部
 */
export function removeSpaces(str: string, type: number = 4): string {
  if (type == 1) {
    return str.replace(/^\s*/, "")
  } else if (type == 2) {
    return str.replace(/(\s*$)/g, "")
  } else if (type == 3) {
    return str.replace(/^\s*|\s*$/g, "")
  } else {
    return str.replace(/\s*/g, "")
  }
}


export const cardIDReg = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/ // 身份证号, 支持1/2代(15位/18位数字)

export const regCard = /^[1-9]\d{9,29}$/

/**
 * 预览文件
 * @param {*} content 文件链接
 */
export const previewFile = (content: any) => {
  showLoading('文件下载中...')
  let ext = content.split('.').pop(); // 截取文件后缀
  const downloadTask = uni.downloadFile({
    url: encodeURI(content),
    // filePath: uni.env.USER_DATA_PATH + '/' + '文件' + '.' + ext, // 指定文件下载后存储的路径
    success: function (res) {
      let filePath = res.tempFilePath;
      uni.openDocument({
        filePath: filePath as string,
        fileType: ext,
        success: function (res) {
          console.log('打开文档成功')
          uni.setStorageSync('documentStatus', 'open')
          hideLoading()
        },
        fail: function (err) {
          console.log('文件打开失败', err)
          showToast('文件打开失败')
        }
      })
    },
    fail: function (err) {
      console.log('文件下载失败', err)
      showToast('文件下载失败')
    }
  })
  return downloadTask
}

export const getUuid = () => {
  return 'xxxxxxxxxxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}


export const groupDataByFiveMinutes = (data) => {
  const groupedData: dataType = {};
  const fiveMinutes = 5 * 60 * 1000; // 5分钟的毫秒数

  data.forEach((item: dataType) => {
    // 计算组的基准时间（当前时间戳向下取整到最近的5分钟）
    const baseTime = Math.floor(item.totalTime / fiveMinutes) * fiveMinutes;

    // 如果该组不存在，则初始化
    if (!groupedData[baseTime]) {
      groupedData[baseTime] = [];
    }

    // 将数据添加到对应组
    groupedData[baseTime].push(item);
  });
  // 转换对象为数组（可选）
  return Object.keys(groupedData).map(key => ({
    label: parseInt(key, 10),
    data: groupedData[key]
  }));
}


export const rgba = (hex: string, opacity: number) => {
  let red = parseInt(hex.substring(1, 3), 16)
  let green = parseInt(hex.substring(3, 5), 16)
  let blue = parseInt(hex.substring(5, 7), 16)
  return `rgba(${red}, ${green}, ${blue}, ${opacity})`
}

export const telphoneReg = new RegExp(/^1\d{10}$/)


/**
 * @description : 日期组件格式化
 */
export const formatter = (type: string, value: number | string) => {
  if (type === 'year') {
    return `${value}年`;
  }
  if (type === 'month') {
    return `${value}月`;
  }
  if (type === 'day') {
    return `${value}日`;
  }
  return value;
};

export const canvas2d = true

// 字符串移除特殊符号
export const stringRemoveSymbol = (str: string, char = "-") => {
  return str ? str.toString().replace(char, '') : '-'
}

// 导出一个正则表达式，用于匹配文件扩展名
export const fileExtensions = /\.(doc|xls|ppt|pdf|docx|xlsx|pptx)$/i;

interface TimeCheckResult {
  isWithinHours: boolean;
  remainingTime: string;
  diffInHours: number;
  isExpired: boolean;
}

/**
 * 检查目标时间距离当前时间是否在指定的小时范围内
 * @param targetTime 要检查的目标时间（Date 对象或可被 Date 解析的字符串）
 * @param hours 指定的小时数（正数表示未来范围，负数表示过去范围）
 * @returns 如果目标时间在当前时间的指定小时范围内，返回 true；否则返回 false
 */
export /**
 * 检查目标时间距离当前时间是否在指定的小时范围内
 * @param targetTime 要检查的目标时间（Date 对象或可被 Date 解析的字符串，如 "1998-02-16 22:30:31"）
 * @param hours 指定的小时数（正数表示未来范围，负数表示过去范围）
 * @returns 如果目标时间在当前时间的指定小时范围内，返回 true；否则返回 false
 */
function isTimeWithinHours(
  targetTime: Date | string,
  hours: number
): boolean {
  // 处理输入为目标时间字符串的情况
  let time: Date;

  if (typeof targetTime === 'string') {
    // 替换空格为 'T' 以便更好地兼容不同浏览器
    const isoString = targetTime.replace(' ', 'T');
    time = new Date(isoString);

    // 如果仍然无效，尝试其他格式（可选）
    if (isNaN(time.getTime())) {
      time = new Date(targetTime);
    }
  } else {
    time = targetTime;
  }

  // 验证时间对象是否有效
  if (isNaN(time.getTime())) {
    throw new Error(`Invalid target time: ${targetTime}`);
  }

  const now = new Date();
  const timeDiffInMs = time.getTime() - now.getTime();
  const timeDiffInHours = timeDiffInMs / (1000 * 60 * 60);

  if (hours >= 0) {
    // 检查未来时间范围 (0 <= 目标时间-现在 <= hours)
    return timeDiffInHours >= 0 && timeDiffInHours <= hours;
  } else {
    // 检查过去时间范围 (-hours >= 现在-目标时间 >= 0)
    return timeDiffInHours <= 0 && timeDiffInHours >= hours;
  }
}

/**
 * 获取两个时间之间的时间差（包含小时、分钟、秒和毫秒差）
 * @param startTime 开始时间（Date对象或可解析的字符串，如 "2023-05-20 12:00:00"）
 * @param endTime 结束时间（同startTime格式）
 * @param format 返回格式：'string' | 'object'（默认返回字符串）
 * @returns 返回对象包含时间差各部分和毫秒差 { timeParts: { hours, minutes, seconds }, milliseconds: number }
 */
export function getTimeRemaining(
  startTime: Date | string,
  endTime: Date | string,
  format: 'string' | 'object' = 'string'
): string | { timeParts: { hours: number; minutes: number; seconds: number }; milliseconds: number } {
  // 解析时间（兼容字符串格式）
  const parseTime = (time: Date | string): Date => {
    if (typeof time === 'string') {
      const isoString = time.includes('T') ? time : time.replace(' ', 'T');
      const date = new Date(isoString);
      if (isNaN(date.getTime())) throw new Error(`无效的时间格式: ${time}`);
      return date;
    }
    return time;
  };

  const start = parseTime(startTime);
  const end = parseTime(endTime);
  const diff = end.getTime() - start.getTime(); // 毫秒差

  // 计算绝对值（支持过去时间）
  const absoluteDiff = Math.abs(diff);
  const seconds = Math.floor((absoluteDiff / 1000) % 60);
  const minutes = Math.floor((absoluteDiff / (1000 * 60)) % 60);
  const hours = Math.floor(absoluteDiff / (1000 * 60 * 60)); // 不再限制24小时

  // 按需返回格式
  if (format === 'object') {
    return {
      timeParts: { hours, minutes, seconds },
      milliseconds: diff
    };
  }

  // 格式化字符串（智能省略为0的单位）
  const parts = [];
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`); // 至少显示秒

  return `${diff < 0 ? '已过去 ' : ''}${parts.join(' ')} (${diff}毫秒)`;
}

/**
 * 姓名脱敏方法
 * @param name 原始姓名
 * @param maskChar 脱敏字符，默认为*
 * @returns 脱敏后的姓名
 */
export function desensitizeName(name: string, maskChar: string = '*'): string {
  if (!name || name.length === 0) return name;

  const length = name.length;

  if (length === 1) {
    // 1个字直接脱敏
    return maskChar;
  } else if (length === 2) {
    // 两个字后面一个字脱敏
    return name[0] + maskChar;
  } else {
    // 大于3个字，除了收尾脱敏
    const firstChar = name[0];
    const lastChar = name[length - 1];
    const middle = maskChar.repeat(length - 2);
    return firstChar + middle + lastChar;
  }
}

// 特殊错误信息
export const errorInfo = [
	`MiniProgramError
null is not an object (evaluating 'r.width')`,
	`MiniProgramError
Cannot read property 'split' of undefined
TypeError: Cannot read property 'split' of undefined`,
	`MiniProgramError
Cannot read properties of null (reading 'width')`,
	`request:fail -109:net::ERR_ADDRESS_UNREACHABLE`,
	`request:fail 无法连接服务器`,
	`request:fail fail:time out`,
	`request:fail -7:net::ERR_TIMED_OUT`,
	`request:fail 网络连接已中断`,
	`request:fail fail:network interrupted error`,
	`request:fail interrupted`,
	`request:fail 请求超时`,
	`request:fail -103:net::ERR_CONNECTION_ABORTED`,
	`{"errno":5,"errMsg":"request:fail fail:time out"}`,
	`{"errno":600003,"errMsg":"request:fail fail:network interrupted error"}`,
	`{"errno":600001,"errMsg":"request:fail 请求超时。"}`,
	`hideLoading:fail:toast can't be found`,
	`navigateBack:fail cannot navigate back at first page`
]