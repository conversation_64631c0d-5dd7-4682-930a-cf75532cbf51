
import { showType, fn, dataType } from '@/types/index.d'
export const isObject = (data : any) => {
	return Object.prototype.toString.call(data) == '[object Object]'
}
export const isArray = (data: any) => {
	return Array.prototype.toString.call(data) == '[object Array]'
}

export const showToast = (opts : string | undefined | Partial<showType>) => {
	let showToastOptions : Partial<showType> = {
		title: '',
		icon: 'none',
		mask: true
	}
	if (typeof opts == 'string') {
		showToastOptions.title = opts
	}
	if (isObject(opts)) {
		showToastOptions = { ...showToastOptions, ...(opts as Partial<showType>) }
	}
	uni.showToast(showToastOptions)
}

export const showLoading = (opts ?: string | Partial<showType>) => {
	let showLoadingOptions : Partial<showType> = {
		title: '',
		icon: 'none',
		mask: true
	}
	if (typeof opts == 'string') {
		showLoadingOptions.title = opts
	}
	if (isObject(opts)) {
		showLoadingOptions = { ...showLoadingOptions, ...(opts as Partial<showType>) }
	}
	uni.showLoading(showLoadingOptions)
}

export const hideLoading = () => {
	wx.hideLoading({
		noConflict: true
	})
}

export const showModal = (opts : string | UniApp.ShowModalOptions, callback ?: (args : UniApp.ShowModalRes) => void) => {
	let showModalOptions : UniApp.ShowModalOptions = {
		content: '',
		confirmColor: '#C35943',
		cancelColor: '#777',
		success: (res : UniApp.ShowModalRes) => {
			callback?.(res)
		}
	}
	if (typeof opts == 'string') {
		showModalOptions.content = opts
	}
	if (isObject(opts)) {
		showModalOptions = { ...showModalOptions, ...(opts as UniApp.ShowModalOptions) }
	}
	uni.showModal<UniApp.ShowModalOptions>(showModalOptions)
}

export const debounce = function (fn : fn, wait : number) {
	let timeout : any = null;
	return function (...args : any) {
		let context = this;
		if (timeout) clearTimeout(timeout);
		timeout = setTimeout(() => {
			fn.apply(context, args)
		}, wait);
	};
}

export const throttle = function(func:fn, delay: number) {
  let lastCall = 0; // 上一次调用的时间

  return function(...args: any) {
    const now = Date.now(); // 当前时间

    if (now - lastCall >= delay) {
      lastCall = now; // 更新上一次调用时间
      return func.apply(this, args); // 调用传入的函数
    }
  };
}

export const formatDate = (date : Date = new Date()) => {
	const newDate = new Date(date)
	const y = newDate.getFullYear()
	const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
	const d = newDate.getDate().toString().padStart(2, 0)
	return `${y}-${m}-${d}`
}

export const formatMonth = (date : Date = new Date()) => {
	const newDate = new Date(date)
	const y = newDate.getFullYear()
	const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
	return `${y}-${m}`
}

export const setMonth = (month : number = 1, date : Date = new Date()) => {
	const newDate = new Date(date)
	const currentDay = newDate.getDate()
	newDate.setMonth(newDate.getMonth() - month)
	if (newDate.getDate() < currentDay) {
		newDate.setDate(0)
	}
	return formatDate(newDate)
}

export const setTime = (date : Date = new Date()) => {
	const newDate = new Date(date)
	const h = newDate.getHours().toString().padStart(2, 0)
	const m = newDate.getMinutes().toString().padStart(2, 0)
	return `${h}:${m}`
}
export const setDay = (day : number = 0, type : string = '-', date : Date = new Date()) => {
	const newDate = new Date(date)
	if (type == '+') {
		return formatDate(new Date(newDate.setDate(newDate.getDate() + day)))
	} else {
		return formatDate(new Date(newDate.setDate(newDate.getDate() - day)))
	}
}
export const fullDate = (date?: Date = new Date()) => {
	const newDate = new Date(date)
	const y = newDate.getFullYear()
	const m = (newDate.getMonth() + 1).toString().padStart(2, 0)
	const d = newDate.getDate().toString().padStart(2, 0)
	const h = newDate.getHours().toString().padStart(2, 0)
	const mm = newDate.getMinutes().toString().padStart(2, 0)
	const s = newDate.getSeconds().toString().padStart(2, 0)
	return  `${y}-${m}-${d} ${h}:${mm}:${s}`
}


/**
 * 移除字符串的空格
 * @param {*} str 传字符串
 * @param {Number} type 移除的位置 1 前  2 后  3 前后  4全部
 */
export function removeSpaces(str : string, type : number = 4) : string {
	if (type == 1) {
		return str.replace(/^\s*/, "")
	} else if (type == 2) {
		return str.replace(/(\s*$)/g, "")
	} else if (type == 3) {
		return str.replace(/^\s*|\s*$/g, "")
	} else {
		return str.replace(/\s*/g, "")
	}
}


export const cardIDReg = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/ // 身份证号, 支持1/2代(15位/18位数字)

export const regCard = /^[1-9]\d{9,29}$/

/**
 * 预览文件
 * @param {*} content 文件链接
 */
export const previewFile = (content : any) => {
	showLoading('文件下载中...')
	let ext = content.split('.').pop(); // 截取文件后缀
	const downloadTask = uni.downloadFile({
		url: encodeURI(content),
		// filePath: uni.env.USER_DATA_PATH + '/' + '文件' + '.' + ext, // 指定文件下载后存储的路径
		success: function (res) {
			let filePath = res.tempFilePath;
			uni.openDocument({
				filePath: filePath as string,
				fileType: ext,
				success: function (res) {
					console.log('打开文档成功')
					uni.setStorageSync('documentStatus', 'open')
					hideLoading()
				},
				fail: function (err) {
					console.log('文件打开失败', err)
					showToast('文件打开失败')
				}
			})
		},
		fail: function (err) {
			console.log('文件下载失败', err)
			showToast('文件下载失败')
		}
	})
	return downloadTask
}

export const getUuid = () => {
	return 'xxxxxxxxxxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = (Math.random() * 16) | 0,
			v = c === 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}


export const groupDataByFiveMinutes = (data) =>{
    const groupedData: dataType = {};
    const fiveMinutes = 5 * 60 * 1000; // 5分钟的毫秒数

    data.forEach((item: dataType) => {
        // 计算组的基准时间（当前时间戳向下取整到最近的5分钟）
        const baseTime = Math.floor(item.totalTime / fiveMinutes) * fiveMinutes;

        // 如果该组不存在，则初始化
        if (!groupedData[baseTime]) {
            groupedData[baseTime] = [];
        }

        // 将数据添加到对应组
        groupedData[baseTime].push(item);
    });
    // 转换对象为数组（可选）
    return Object.keys(groupedData).map(key => ({
        label: parseInt(key, 10),
        data: groupedData[key]
    }));
}


export const rgba = (hex: string, opacity: number) => {
	let red = parseInt(hex.substring(1, 3), 16)
	let green = parseInt(hex.substring(3, 5), 16)
	let blue = parseInt(hex.substring(5, 7), 16)
	return `rgba(${red}, ${green}, ${blue}, ${opacity})`
}

export const telphoneReg = new RegExp(/^1[3|4|5|7|8|9][0-9]\d{8}$/)


/**
 * @description : 日期组件格式化
 */
export const formatter = (type : string, value : number | string) => {
	if (type === 'year') {
		return `${value}年`;
	}
	if (type === 'month') {
		return `${value}月`;
	}
	if (type === 'day') {
		return `${value}日`;
	}
	return value;
};

export const canvas2d = true

// 字符串移除特殊符号
export const stringRemoveSymbol = (str: string, char="-") => {
	return str ? str.toString().replace(char, '') : '-'
}

// 导出一个正则表达式，用于匹配文件扩展名
export const fileExtensions = /\.(doc|xls|ppt|pdf|docx|xlsx|pptx)$/i;