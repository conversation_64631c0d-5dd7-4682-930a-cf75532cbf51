import { requestOptions } from '../types'
import { showLoading, hideLoading } from './util'
import { useLocalEnv, protocl } from './env'

let prefix = useLocalEnv ? protocl : {
	aiURL: process.env.aiURL,
	webviewURL: process.env.webviewURL,
	baseURL_sh: process.env.baseURL_sh,
	baseURL_qd: process.env.baseURL_qd,
	baseURL_sh_ccs: process.env.baseURL_sh_ccs,
}

const filterUrlNotLoading = ['/login', '/history/page', '/ai/speech/tencent/recognition/oss', '/fileInfo/page', '/shopInspection/form/orderRead', '/shopInspection/form/listTargetCode', '/miniUser/getUserPages']

uni.addInterceptor('request', {
	invoke(args : requestOptions) {
		if (args.url.includes('http') || args.url.includes('https')) {

		} else {
			if (!filterUrlNotLoading.includes(args.url)) {
				showLoading('加载中')
			}
			if (args.APP_BASE_URL) {
				args.url = prefix[args.APP_BASE_URL] + args.url
			} else {
				const baseURL = args.service == 'qd' ? prefix.baseURL_qd : prefix.baseURL_sh
				args.url = baseURL + args.url
			}
			args.header = args.header || {}
			args.header.appId = "test"
			if (args.url.includes('/refreshToken')) {
				Object.assign(args.header, {
					Authorization: uni.getStorageSync('refreshToken')
				})
			} else if (!args.url.includes('/login')) {
				Object.assign(args.header, {
					Authorization: uni.getStorageSync('token')
				})
			}
		}
		console.log('拦截器参数:', args)
	},
	success(args) {
		if (!filterUrlNotLoading.includes(args?.header?.path)) {
			hideLoading()
		}
	},
	fail() {
		hideLoading()
	},
	complete() {
	}
})

uni.addInterceptor('uploadFile', {
	invoke(args) {
		if (args.url.includes('http') || args.url.includes('https')) {

		} else {
			showLoading('加载中')
			if (args.APP_BASE_URL) {
				args.url = prefix[args.APP_BASE_URL] + args.url
			} else {
				const baseURL = args.service == 'qd' ? prefix.baseURL_qd : prefix.baseURL_sh
				args.url = baseURL + args.url
			}
			args.header = args.header || {}
			Object.assign(args.header, {
				// appId: 'test',
				// shopId: uni.getStorageSync('shopId'),
				Authorization: args.url == '/refreshToken' ? uni.getStorageSync('refreshToken') : uni.getStorageSync('token')
			})
		}
		console.log('拦截器参数:', args)
	},
	success() {
		hideLoading()
	},
	fail() {
		hideLoading()
	},
	complete() {

	}
})