import { successCallbackResult, requestOptionsType } from '../types'
import { showToast, isObject } from '@/utils/util.ts'
import { refreshToken } from '@/api'
const filterApis = ['/notice/errorPush']

const http = (opts : requestOptionsType) : Promise<successCallbackResult> => {
	const need = opts.need
	delete opts.need
	const params = Object.assign({
		method: 'GET'
	}, opts)
	console.log("params=========>", params.url, "入参-", params)
	return new Promise((resolve, reject) => {
		uni.request({
			...params,
			success: (res : UniApp.RequestSuccessCallbackResult) => {
				console.log("res=========", params.url, "返参-", res)
				const data = res.data as successCallbackResult
				if (res.statusCode == 200) {
					if (data.code == 200) {
						resolve(data)
					} else if (data.code == 202) {
						resolve(data)
					} else if (data.code == 3) {
						if (params.url.includes('/refreshToken')) { showToast(data.message); return }
						refreshToken({ refreshToken: uni.getStorageSync('refreshToken') }).then(response => {
							if (response.code == 200) {
								uni.setStorageSync('token', response.data.token)
								uni.setStorageSync('refreshToken', response.data.refreshToken)
								http(opts).then(r => {
									if (r.code == 200) {
										resolve(r)
									}
								})
							}
						})
					} else if (/^5\d{2}$/.test(data.code.toString())) {
						if (need) {
							resolve(data)
						} else {
							if (filterApis.includes(params.url)) return
							showToast("页面出错了,请稍后再试~")
							reject({ ...data, url: params.url })
						}
					} else if (data.code == 10033) {
						resolve(data)
					} else if (data.code == 10059) {
						// resolve(data)
					} else if (data.code == 10011) {
						resolve(data)
					} else {
						if (need) {
							resolve(data)
						} else {
							if (filterApis.includes(params.url)) return
							showToast(data.message)
						}
					}
				} else {
					if (filterApis.includes(params.url)) return
					showToast("页面出错了,请稍后再试~")
					reject({ ...data, url: params.url })
				}
			},
			fail: (err : UniApp.GeneralCallbackResult) => {
				reject(isObject(err) ? { ...err, url: params.url } : err)
			}
		})
	})
}

export const httpUpload = (opts : UniApp.RequestOptions) : Promise<successCallbackResult> => {
	const params = Object.assign({
		header: {
			'Content-Type': 'multipart/form-data'
		}
	}, opts)
	console.log("params", params)
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			...params,
			success: (res : UniApp.UploadFileSuccessCallbackResult) => {
				const data = JSON.parse(res.data)
				if (res.statusCode == 200) {
					if (data.code == 200) {
						resolve(data)
					} else if (data.code == 402) {
						refreshToken({}).then(resToken => {
							uni.setStorageSync('token', resToken.authToken)
							uni.setStorageSync('refreshToken', resToken.refreshToken)
							httpUpload(opts)
						})
					} else if (/^5\d{2}$/.test(data.code.toString())) {
						showToast("页面出错了,请稍后再试~")
					} else {
						showToast(data.message)
					}
				} else {
					showToast("页面出错了，请稍后再试～")
					reject({ ...data, url: params.url })
				}
			},
			fail: (err : UniApp.GeneralCallbackResult) => {
				reject(isObject(err) ? { ...err, url: params.url } : err)
			}
		})
	})
}

export default http