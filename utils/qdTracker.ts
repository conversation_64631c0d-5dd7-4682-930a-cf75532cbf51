import { QDTracker, QDTMixin, } from '../utils/uniappSDK/index';

// 用于统一qdt的变量
let qdTracker;

// #ifdef MP
QDTracker.init({
  debug: true, // 开启调试模式，默认关闭
  appid: 'wx4aa2f3322c37c875', //小程序appid
  appkey: '0MA006L0QA6R9I3K', // 上面第二步里新增的appKey
  application: '智慧运营专家', // 小程序名称
  apiHost: 'https://report1.tmac.qidian.qq.com',   // 接口请求域名，请联系项目交付同事提供上报域名。
  encryptMode: 'close', // 加密模式 close-关闭  default-base64 aes-aes加密
  // useId: false, // 设置为true后。微信，百度需上报openid，支付宝需上报aliUserId。已废弃，可忽略。
  track_interval: 0,          // 发送间隔时间(ms)
  batch_max_time: 10,          // 批量发送数量
  session_interval: 30 * 60 * 1000,   // session 间隔时间(ms)
  autoTrack: {                        // 自动上报开关
    appLaunch: true, //小程序启动事件是否自动上报
    appShow: true,  //小程序展示事件是否自动上报
    appHide: true,  //小程序隐藏事件是否自动上报
    pageShow: true, //小程序页面浏览是否自动上报
    pageHide: true, //小程序页面隐藏是否自动上报
    pageShare: true,  //小程序分享是否自动上报
    mpClick: true,  //小程序元素点击是否自动上报
    // mpClick为当Page中定义的事件处理函数被触发时采集,目前只支持tap/ longtap/longpress三类事件；
  },
});
// #endif

// 小程序与web统一变量
qdTracker = QDTracker;

qdTracker.install = (Vue) => {
  Vue.mixin(QDTMixin);
  Vue.config.globalProperties.$qdTracker = qdTracker;
}

export default qdTracker
