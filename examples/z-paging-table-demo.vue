<template>
  <view class="demo-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">Z-Paging 固定列表格</text>
      <text class="page-subtitle">支持下拉刷新、上拉加载、固定列、表头固定</text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索员工姓名或邮箱"
        v-model="searchKeyword"
        @confirm="handleSearch"
      />
      <button class="search-btn" @click="handleSearch">搜索</button>
    </view>

    <!-- 表格容器 -->
    <view class="table-wrapper">
      <z-paging-table
        ref="tableRef"
        :columns="tableColumns"
        :sticky-header="true"
        :refresher-enabled="true"
        :loading-more-enabled="true"
        row-key="id"
        empty-text="暂无员工数据"
        @query="handleQuery"
        @refresh="handleRefresh"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 顶部内容 -->
        <template #top>
          <view class="table-stats">
            <view class="stat-item">
              <text class="stat-number">{{ totalCount }}</text>
              <text class="stat-label">总员工</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ onlineCount }}</text>
              <text class="stat-label">在线</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ newCount }}</text>
              <text class="stat-label">本月新增</text>
            </view>
          </view>
        </template>

        <!-- 自定义姓名列 -->
        <template #name="{ row }">
          <view class="name-cell">
            <image class="user-avatar" :src="row.avatar" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-name">{{ row.name }}</text>
              <text class="user-id">工号: {{ row.employeeId }}</text>
            </view>
          </view>
        </template>

        <!-- 自定义部门列 -->
        <template #department="{ row }">
          <view class="department-cell">
            <text class="department-name">{{ row.department }}</text>
            <text class="department-code">{{ row.departmentCode }}</text>
          </view>
        </template>

        <!-- 自定义薪资列 -->
        <template #salary="{ row }">
          <view class="salary-cell" :class="getSalaryLevel(row.salary)">
            <text class="salary-amount">¥{{ row.salary.toLocaleString() }}</text>
            <text class="salary-level">{{ getSalaryLevelText(row.salary) }}</text>
          </view>
        </template>

        <!-- 自定义状态列 -->
        <template #status="{ row }">
          <view class="status-badge" :class="'status-' + row.status">
            <view class="status-dot"></view>
            <text>{{ getStatusText(row.status) }}</text>
          </view>
        </template>

        <!-- 自定义操作列 -->
        <template #actions="{ row, index }">
          <view class="action-buttons">
            <button class="btn btn-primary btn-sm" @click="handleView(row)">
              查看
            </button>
            <button class="btn btn-warning btn-sm" @click="handleEdit(row)">
              编辑
            </button>
            <button class="btn btn-danger btn-sm" @click="handleDelete(row, index)">
              删除
            </button>
          </view>
        </template>

        <!-- 底部内容 -->
        <template #bottom>
          <view class="table-footer">
            <text class="footer-text">数据更新时间: {{ lastUpdateTime }}</text>
          </view>
        </template>
      </z-paging-table>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import ZPagingTable from '@/components/z-paging-table/z-paging-table.vue'

// 响应式数据
const tableRef = ref()
const searchKeyword = ref('')
const totalCount = ref(0)
const onlineCount = ref(0)
const newCount = ref(0)
const lastUpdateTime = ref('')

// 表格列配置
const tableColumns = ref([
  // 固定列
  {
    key: 'name',
    title: '员工信息',
    width: 200,
    fixed: true,
    align: 'left'
  },
  {
    key: 'employeeId',
    title: '工号',
    width: 100,
    fixed: true,
    align: 'center'
  },
  
  // 可滚动列
  {
    key: 'department',
    title: '部门',
    width: 150,
    align: 'center'
  },
  {
    key: 'position',
    title: '职位',
    width: 140,
    align: 'center'
  },
  {
    key: 'email',
    title: '邮箱',
    width: 200,
    align: 'left'
  },
  {
    key: 'phone',
    title: '联系电话',
    width: 140,
    align: 'center'
  },
  {
    key: 'salary',
    title: '薪资',
    width: 120,
    align: 'right',
    sortable: true
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    align: 'center'
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    align: 'center'
  },
  {
    key: 'actions',
    title: '操作',
    width: 200,
    align: 'center'
  }
])

// 模拟数据生成
const generateMockData = (pageNo: number, pageSize: number) => {
  const departments = ['技术部', '产品部', '设计部', '运营部', '市场部', '人事部', '财务部']
  const positions = ['工程师', '经理', '专员', '主管', '总监', '助理', '实习生']
  const statuses = ['active', 'inactive', 'pending']
  
  const data = []
  const start = (pageNo - 1) * pageSize
  
  for (let i = 0; i < pageSize; i++) {
    const id = start + i + 1
    if (id > 200) break // 最多200条数据
    
    const department = departments[Math.floor(Math.random() * departments.length)]
    const position = positions[Math.floor(Math.random() * positions.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    
    data.push({
      id,
      name: `员工${id}`,
      employeeId: `EMP${String(id).padStart(4, '0')}`,
      department,
      departmentCode: department.substring(0, 2).toUpperCase(),
      position,
      email: `employee${id}@company.com`,
      phone: `138${String(id).padStart(8, '0')}`,
      salary: 8000 + Math.floor(Math.random() * 20000),
      joinDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      status,
      avatar: `https://via.placeholder.com/60x60?text=${id}`
    })
  }
  
  return data
}

// 查询数据
const handleQuery = async (pageNo: number, pageSize: number) => {
  console.log('查询数据:', pageNo, pageSize, searchKeyword.value)
  
  try {
    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const data = generateMockData(pageNo, pageSize)
    
    // 如果有搜索关键词，过滤数据
    let filteredData = data
    if (searchKeyword.value) {
      filteredData = data.filter(item => 
        item.name.includes(searchKeyword.value) || 
        item.email.includes(searchKeyword.value)
      )
    }
    
    // 更新统计数据
    if (pageNo === 1) {
      totalCount.value = 200
      onlineCount.value = Math.floor(Math.random() * 50) + 100
      newCount.value = Math.floor(Math.random() * 20) + 5
      lastUpdateTime.value = new Date().toLocaleString()
    }
    
    // 完成数据加载
    const noMore = pageNo * pageSize >= 200
    tableRef.value?.complete(filteredData, noMore)
    
  } catch (error) {
    console.error('查询失败:', error)
    tableRef.value?.complete([], true)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

// 刷新数据
const handleRefresh = () => {
  console.log('刷新数据')
  // z-paging 会自动调用 handleQuery
}

// 搜索
const handleSearch = () => {
  console.log('搜索:', searchKeyword.value)
  tableRef.value?.reset() // 重置分页并重新加载
}

// 选择变化
const handleSelectionChange = (e: any) => {
  console.log('选择变化:', e)
}

// 排序变化
const handleSortChange = (e: any) => {
  console.log('排序变化:', e)
  // 这里可以实现排序逻辑
  tableRef.value?.reset()
}

// 操作按钮事件
const handleView = (row: any) => {
  uni.showToast({
    title: `查看 ${row.name}`,
    icon: 'none'
  })
}

const handleEdit = (row: any) => {
  uni.showToast({
    title: `编辑 ${row.name}`,
    icon: 'none'
  })
}

const handleDelete = (row: any, index: number) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除员工 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        // 这里应该调用删除API，然后刷新数据
        tableRef.value?.refresh()
      }
    }
  })
}

// 辅助函数
const getStatusText = (status: string) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    pending: '待入职'
  }
  return statusMap[status] || '未知'
}

const getSalaryLevel = (salary: number) => {
  if (salary >= 20000) return 'high'
  if (salary >= 15000) return 'medium'
  return 'low'
}

const getSalaryLevelText = (salary: number) => {
  if (salary >= 20000) return '高薪'
  if (salary >= 15000) return '中等'
  return '基础'
}
</script>

<style lang="scss" scoped>
.demo-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.page-header {
  padding: 30rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .page-title {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .page-subtitle {
    display: block;
    font-size: 28rpx;
    opacity: 0.9;
  }
}

.search-bar {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  
  .search-input {
    flex: 1;
    padding: 20rpx;
    border: 1px solid #ddd;
    border-radius: 8rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
  }
  
  .search-btn {
    padding: 20rpx 40rpx;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.table-wrapper {
  flex: 1;
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.table-stats {
  display: flex;
  padding: 30rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  
  .stat-item {
    flex: 1;
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
    }
    
    .stat-label {
      display: block;
      font-size: 24rpx;
      opacity: 0.9;
    }
  }
}

.table-footer {
  padding: 20rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-top: 1px solid #eee;
  
  .footer-text {
    font-size: 24rpx;
    color: #666;
  }
}

/* 自定义单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .user-info {
    .user-name {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }
    
    .user-id {
      display: block;
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }
}

.department-cell {
  .department-name {
    display: block;
    font-size: 28rpx;
    color: #333;
  }
  
  .department-code {
    display: block;
    font-size: 24rpx;
    color: #666;
    margin-top: 4rpx;
  }
}

.salary-cell {
  .salary-amount {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
  }
  
  .salary-level {
    display: block;
    font-size: 20rpx;
    margin-top: 4rpx;
  }
  
  &.high {
    .salary-amount { color: #52c41a; }
    .salary-level { color: #52c41a; }
  }
  
  &.medium {
    .salary-amount { color: #1890ff; }
    .salary-level { color: #1890ff; }
  }
  
  &.low {
    .salary-amount { color: #fa8c16; }
    .salary-level { color: #fa8c16; }
  }
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  
  .status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
  }
  
  &.status-active {
    background-color: #f6ffed;
    color: #52c41a;
    .status-dot { background-color: #52c41a; }
  }
  
  &.status-inactive {
    background-color: #fff7e6;
    color: #fa8c16;
    .status-dot { background-color: #fa8c16; }
  }
  
  &.status-pending {
    background-color: #e6f7ff;
    color: #1890ff;
    .status-dot { background-color: #1890ff; }
  }
}

.action-buttons {
  display: flex;
  gap: 8rpx;
  justify-content: center;
}

.btn {
  padding: 8rpx 16rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  
  &.btn-sm {
    padding: 6rpx 12rpx;
    font-size: 22rpx;
  }
  
  &.btn-primary {
    background-color: #1890ff;
    color: white;
  }
  
  &.btn-warning {
    background-color: #fa8c16;
    color: white;
  }
  
  &.btn-danger {
    background-color: #ff4d4f;
    color: white;
  }
}
</style>
