<template>
  <view class="container">
    <view class="header">
      <text class="title">固定列表格示例</text>
      <text class="subtitle">左侧固定列，右侧可横向滚动</text>
    </view>

    <!-- 固定列表格 -->
    <view class="table-container">
      <uni-table-fixed
        :columns="tableColumns"
        :data="tableData"
        :border="true"
        :stripe="true"
        row-key="id"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @scroll="handleScroll"
      >
        <!-- 自定义姓名列 -->
        <template #name="{ row, column, index }">
          <view class="name-cell">
            <image class="avatar" :src="row.avatar" mode="aspectFill"></image>
            <text class="name-text">{{ row.name }}</text>
          </view>
        </template>

        <!-- 自定义状态列 -->
        <template #status="{ row }">
          <view class="status-cell" :class="'status-' + row.status">
            {{ getStatusText(row.status) }}
          </view>
        </template>

        <!-- 自定义操作列 -->
        <template #action="{ row, index }">
          <view class="action-cell">
            <button class="action-btn edit" @click="handleEdit(row, index)">编辑</button>
            <button class="action-btn delete" @click="handleDelete(row, index)">删除</button>
          </view>
        </template>
      </uni-table-fixed>
    </view>

    <!-- 滚动信息显示 -->
    <view class="scroll-info">
      <text>横向滚动距离: {{ scrollLeft }}px</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UniTableFixed from '@/components/uni-table-fixed/uni-table-fixed.vue'

// 滚动状态
const scrollLeft = ref(0)

// 表格列配置
const tableColumns = ref([
  // 固定列
  {
    key: 'name',
    title: '姓名',
    width: 150,
    fixed: 'left', // 左侧固定
    align: 'left'
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    fixed: 'left', // 左侧固定
    align: 'center',
    sortable: true
  },
  
  // 可滚动列
  {
    key: 'email',
    title: '邮箱',
    width: 200,
    align: 'left'
  },
  {
    key: 'phone',
    title: '电话',
    width: 150,
    align: 'center'
  },
  {
    key: 'department',
    title: '部门',
    width: 120,
    align: 'center'
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    align: 'center'
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    align: 'right',
    sortable: true
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    align: 'center'
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    align: 'center'
  },
  {
    key: 'action',
    title: '操作',
    width: 150,
    align: 'center'
  }
])

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    phone: '13800138000',
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    joinDate: '2023-01-15',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    position: '后端工程师',
    salary: 18000,
    joinDate: '2022-08-20',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    email: '<EMAIL>',
    phone: '13800138002',
    department: '产品部',
    position: '产品经理',
    salary: 16000,
    joinDate: '2023-03-10',
    status: 'inactive',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 4,
    name: '赵六',
    age: 30,
    email: '<EMAIL>',
    phone: '13800138003',
    department: '设计部',
    position: 'UI设计师',
    salary: 14000,
    joinDate: '2022-12-05',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 5,
    name: '钱七',
    age: 29,
    email: '<EMAIL>',
    phone: '13800138004',
    department: '运营部',
    position: '运营专员',
    salary: 12000,
    joinDate: '2023-05-18',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  }
])

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    pending: '待入职'
  }
  return statusMap[status] || '未知'
}

// 处理选择变化
const handleSelectionChange = (e: any) => {
  console.log('选择变化:', e)
}

// 处理排序变化
const handleSortChange = (e: any) => {
  console.log('排序变化:', e)
}

// 处理滚动
const handleScroll = (e: any) => {
  scrollLeft.value = e.scrollLeft
  console.log('表格滚动:', e)
}

// 处理编辑
const handleEdit = (row: any, index: number) => {
  console.log('编辑:', row, index)
  uni.showToast({
    title: `编辑 ${row.name}`,
    icon: 'none'
  })
}

// 处理删除
const handleDelete = (row: any, index: number) => {
  console.log('删除:', row, index)
  uni.showModal({
    title: '确认删除',
    content: `确定要删除 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        tableData.value.splice(index, 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.table-container {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  height: 600rpx; /* 固定高度以便测试滚动 */
}

.scroll-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  
  text {
    font-size: 28rpx;
    color: #333;
  }
}

/* 自定义单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  
  .avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .name-text {
    font-size: 28rpx;
    color: #333;
  }
}

.status-cell {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  
  &.status-active {
    background-color: #e7f5e7;
    color: #52c41a;
  }
  
  &.status-inactive {
    background-color: #fff2e8;
    color: #fa8c16;
  }
  
  &.status-pending {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.action-cell {
  display: flex;
  gap: 10rpx;
  
  .action-btn {
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
    border: none;
    
    &.edit {
      background-color: #1890ff;
      color: #fff;
    }
    
    &.delete {
      background-color: #ff4d4f;
      color: #fff;
    }
  }
}
</style>
