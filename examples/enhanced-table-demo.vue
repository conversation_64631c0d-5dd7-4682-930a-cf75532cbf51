<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">增强表格组件演示</text>
      <text class="demo-subtitle">支持固定列、表头固定、分页、排序等功能</text>
    </view>

    <!-- 增强表格 -->
    <enhanced-table
      :columns="tableColumns"
      :data="tableData"
      :loading="loading"
      :show-pagination="true"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      title="员工管理系统"
      table-height="600px"
      @refresh="handleRefresh"
      @page-change="handlePageChange"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 工具栏右侧自定义内容 -->
      <template #toolbar-right>
        <view class="toolbar-actions">
          <button class="action-btn primary" @click="handleAdd">
            <text class="icon">+</text>
            新增员工
          </button>
          <button class="action-btn" @click="handleExport">
            <text class="icon">↓</text>
            导出数据
          </button>
          <button class="action-btn" @click="handleRefresh">
            <text class="icon">↻</text>
            刷新
          </button>
        </view>
      </template>

      <!-- 自定义姓名列 -->
      <template #name="{ row, column, index }">
        <view class="name-cell">
          <image class="avatar" :src="row.avatar" mode="aspectFill"></image>
          <view class="name-info">
            <text class="name-text">{{ row.name }}</text>
            <text class="name-subtitle">ID: {{ row.id }}</text>
          </view>
        </view>
      </template>

      <!-- 自定义薪资列 -->
      <template #salary="{ row }">
        <view class="salary-cell" :class="getSalaryClass(row.salary)">
          ¥{{ row.salary.toLocaleString() }}
        </view>
      </template>

      <!-- 自定义状态列 -->
      <template #status="{ row }">
        <view class="status-cell" :class="'status-' + row.status">
          <view class="status-dot"></view>
          <text>{{ getStatusText(row.status) }}</text>
        </view>
      </template>

      <!-- 自定义操作列 -->
      <template #action="{ row, index }">
        <view class="action-cell">
          <button class="action-btn mini primary" @click="handleEdit(row, index)">
            编辑
          </button>
          <button class="action-btn mini danger" @click="handleDelete(row, index)">
            删除
          </button>
          <button class="action-btn mini" @click="handleView(row, index)">
            查看
          </button>
        </view>
      </template>
    </enhanced-table>

    <!-- 统计信息 -->
    <view class="stats-panel">
      <view class="stat-item">
        <text class="stat-label">总员工数</text>
        <text class="stat-value">{{ total }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">在职员工</text>
        <text class="stat-value">{{ activeCount }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">平均薪资</text>
        <text class="stat-value">¥{{ averageSalary.toLocaleString() }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import EnhancedTable from '@/components/enhanced-table/enhanced-table.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 表格列配置
const tableColumns = ref([
  // 固定列
  {
    key: 'name',
    title: '姓名',
    width: 180,
    fixed: true,
    align: 'left'
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    fixed: true,
    align: 'center',
    sortable: true
  },
  
  // 可滚动列
  {
    key: 'email',
    title: '邮箱',
    width: 220,
    align: 'left'
  },
  {
    key: 'phone',
    title: '电话',
    width: 140,
    align: 'center'
  },
  {
    key: 'department',
    title: '部门',
    width: 120,
    align: 'center'
  },
  {
    key: 'position',
    title: '职位',
    width: 140,
    align: 'center'
  },
  {
    key: 'salary',
    title: '薪资',
    width: 120,
    align: 'right',
    sortable: true
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    align: 'center'
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    align: 'center'
  },
  {
    key: 'action',
    title: '操作',
    width: 180,
    align: 'center'
  }
])

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    phone: '13800138000',
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    joinDate: '2023-01-15',
    status: 'active',
    avatar: 'https://via.placeholder.com/50x50'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    position: '后端工程师',
    salary: 18000,
    joinDate: '2022-08-20',
    status: 'active',
    avatar: 'https://via.placeholder.com/50x50'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    email: '<EMAIL>',
    phone: '13800138002',
    department: '产品部',
    position: '产品经理',
    salary: 16000,
    joinDate: '2023-03-10',
    status: 'inactive',
    avatar: 'https://via.placeholder.com/50x50'
  }
]

// 计算属性
const activeCount = computed(() => {
  return tableData.value.filter(item => item.status === 'active').length
})

const averageSalary = computed(() => {
  if (tableData.value.length === 0) return 0
  const totalSalary = tableData.value.reduce((sum, item) => sum + item.salary, 0)
  return Math.round(totalSalary / tableData.value.length)
})

// 生成模拟数据
const generateMockData = (page: number, size: number) => {
  const start = (page - 1) * size
  const data = []
  
  for (let i = 0; i < size; i++) {
    const index = start + i
    if (index >= 100) break // 最多100条数据
    
    const baseData = mockData[index % mockData.length]
    data.push({
      ...baseData,
      id: index + 1,
      name: `${baseData.name}_${index + 1}`,
      email: `user${index + 1}@company.com`,
      phone: `138${String(index + 1).padStart(8, '0')}`,
      salary: baseData.salary + Math.floor(Math.random() * 5000)
    })
  }
  
  return data
}

// 加载数据
const loadData = async (page = 1) => {
  loading.value = true
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const data = generateMockData(page, pageSize.value)
    tableData.value = data
    total.value = 100 // 模拟总数据量
    currentPage.value = page
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleRefresh = () => {
  loadData(currentPage.value)
}

const handlePageChange = (page: number) => {
  loadData(page)
}

const handleSortChange = (e: any) => {
  console.log('排序变化:', e)
  // 这里可以实现排序逻辑
}

const handleSelectionChange = (e: any) => {
  console.log('选择变化:', e)
}

const handleAdd = () => {
  uni.showToast({
    title: '新增员工',
    icon: 'none'
  })
}

const handleExport = () => {
  uni.showToast({
    title: '导出数据',
    icon: 'none'
  })
}

const handleEdit = (row: any, index: number) => {
  uni.showToast({
    title: `编辑 ${row.name}`,
    icon: 'none'
  })
}

const handleDelete = (row: any, index: number) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除员工 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        tableData.value.splice(index, 1)
        total.value--
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}

const handleView = (row: any, index: number) => {
  uni.showToast({
    title: `查看 ${row.name} 详情`,
    icon: 'none'
  })
}

// 辅助函数
const getStatusText = (status: string) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    pending: '待入职'
  }
  return statusMap[status] || '未知'
}

const getSalaryClass = (salary: number) => {
  if (salary >= 20000) return 'high'
  if (salary >= 15000) return 'medium'
  return 'low'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  margin-bottom: 30rpx;
  text-align: center;
  
  .demo-title {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .demo-subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.toolbar-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  color: #333;
  border-radius: 8rpx;
  font-size: 28rpx;
  
  &.primary {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
  }
  
  &.danger {
    background-color: #ff4d4f;
    color: #fff;
    border-color: #ff4d4f;
  }
  
  &.mini {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
  }
  
  .icon {
    font-size: 28rpx;
    font-weight: bold;
  }
}

/* 自定义单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 20rpx;
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .name-info {
    .name-text {
      display: block;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
    
    .name-subtitle {
      display: block;
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }
}

.salary-cell {
  font-weight: 600;
  
  &.high {
    color: #52c41a;
  }
  
  &.medium {
    color: #1890ff;
  }
  
  &.low {
    color: #fa8c16;
  }
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8rpx;
  
  .status-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
  }
  
  &.status-active .status-dot {
    background-color: #52c41a;
  }
  
  &.status-inactive .status-dot {
    background-color: #fa8c16;
  }
  
  &.status-pending .status-dot {
    background-color: #1890ff;
  }
}

.action-cell {
  display: flex;
  gap: 8rpx;
  justify-content: center;
}

.stats-panel {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  
  .stat-item {
    flex: 1;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 10rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    
    .stat-label {
      display: block;
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
    }
    
    .stat-value {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #1890ff;
    }
  }
}
</style>
