<template>
  <view class="container">
    <view class="header">
      <text class="title">CSS Sticky 固定列表格</text>
      <text class="subtitle">支持表头固定、下拉加载、横向滚动固定列</text>
    </view>

    <!-- 使用 z-paging 实现下拉加载 -->
    <z-paging 
      ref="paging" 
      v-model="dataList" 
      @query="queryList"
      :refresher-enabled="true"
      :loading-more-enabled="true"
    >
      <template #top>
        <view class="table-header">
          <text>员工信息表</text>
        </view>
      </template>

      <!-- 表格容器 -->
      <view class="table-container">
        <uni-table :data="dataList" :border="true" :stripe="true">
          <!-- 表头 -->
          <uni-thead>
            <uni-tr>
              <!-- 固定列 -->
              <uni-th 
                width="120" 
                align="left" 
                :fixed="true" 
                :fixed-left="0"
              >
                姓名
              </uni-th>
              <uni-th 
                width="80" 
                align="center" 
                :fixed="true" 
                :fixed-left="120"
                sortable
              >
                年龄
              </uni-th>
              
              <!-- 可滚动列 -->
              <uni-th width="200" align="left">邮箱</uni-th>
              <uni-th width="150" align="center">电话</uni-th>
              <uni-th width="120" align="center">部门</uni-th>
              <uni-th width="120" align="center">职位</uni-th>
              <uni-th width="100" align="right" sortable>薪资</uni-th>
              <uni-th width="120" align="center">入职日期</uni-th>
              <uni-th width="100" align="center">状态</uni-th>
              <uni-th width="150" align="center">操作</uni-th>
            </uni-tr>
          </uni-thead>
          
          <!-- 数据行 -->
          <uni-tr v-for="(item, index) in dataList" :key="item.id">
            <!-- 固定列 -->
            <uni-td 
              width="120" 
              align="left" 
              :fixed="true" 
              :fixed-left="0"
            >
              <view class="name-cell">
                <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
                <text class="name-text">{{ item.name }}</text>
              </view>
            </uni-td>
            <uni-td 
              width="80" 
              align="center" 
              :fixed="true" 
              :fixed-left="120"
            >
              {{ item.age }}
            </uni-td>
            
            <!-- 可滚动列 -->
            <uni-td width="200" align="left">{{ item.email }}</uni-td>
            <uni-td width="150" align="center">{{ item.phone }}</uni-td>
            <uni-td width="120" align="center">{{ item.department }}</uni-td>
            <uni-td width="120" align="center">{{ item.position }}</uni-td>
            <uni-td width="100" align="right">¥{{ item.salary.toLocaleString() }}</uni-td>
            <uni-td width="120" align="center">{{ item.joinDate }}</uni-td>
            <uni-td width="100" align="center">
              <view class="status-cell" :class="'status-' + item.status">
                {{ getStatusText(item.status) }}
              </view>
            </uni-td>
            <uni-td width="150" align="center">
              <view class="action-cell">
                <button class="action-btn edit" @click="handleEdit(item, index)">编辑</button>
                <button class="action-btn delete" @click="handleDelete(item, index)">删除</button>
              </view>
            </uni-td>
          </uni-tr>
        </uni-table>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 数据列表
const dataList = ref([])
const paging = ref()

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    phone: '13800138000',
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    joinDate: '2023-01-15',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    position: '后端工程师',
    salary: 18000,
    joinDate: '2022-08-20',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    email: '<EMAIL>',
    phone: '13800138002',
    department: '产品部',
    position: '产品经理',
    salary: 16000,
    joinDate: '2023-03-10',
    status: 'inactive',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 4,
    name: '赵六',
    age: 30,
    email: '<EMAIL>',
    phone: '13800138003',
    department: '设计部',
    position: 'UI设计师',
    salary: 14000,
    joinDate: '2022-12-05',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  },
  {
    id: 5,
    name: '钱七',
    age: 29,
    email: '<EMAIL>',
    phone: '13800138004',
    department: '运营部',
    position: '运营专员',
    salary: 12000,
    joinDate: '2023-05-18',
    status: 'active',
    avatar: 'https://via.placeholder.com/40x40'
  }
]

// 查询数据
const queryList = (pageNo: number, pageSize: number) => {
  // 模拟网络请求延迟
  setTimeout(() => {
    const start = (pageNo - 1) * pageSize
    const end = start + pageSize
    const newData = []
    
    // 生成模拟数据
    for (let i = start; i < end && i < 50; i++) {
      const baseData = mockData[i % mockData.length]
      newData.push({
        ...baseData,
        id: i + 1,
        name: `${baseData.name}_${i + 1}`,
        email: `user${i + 1}@example.com`,
        phone: `138${String(i + 1).padStart(8, '0')}`
      })
    }
    
    paging.value.complete(newData)
  }, 500)
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    pending: '待入职'
  }
  return statusMap[status] || '未知'
}

// 处理编辑
const handleEdit = (row: any, index: number) => {
  console.log('编辑:', row, index)
  uni.showToast({
    title: `编辑 ${row.name}`,
    icon: 'none'
  })
}

// 处理删除
const handleDelete = (row: any, index: number) => {
  console.log('删除:', row, index)
  uni.showModal({
    title: '确认删除',
    content: `确定要删除 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        dataList.value.splice(index, 1)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.table-header {
  padding: 20rpx;
  background-color: #fff;
  text-align: center;
  
  text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.table-container {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 自定义单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  
  .avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  
  .name-text {
    font-size: 28rpx;
    color: #333;
  }
}

.status-cell {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  
  &.status-active {
    background-color: #e7f5e7;
    color: #52c41a;
  }
  
  &.status-inactive {
    background-color: #fff2e8;
    color: #fa8c16;
  }
  
  &.status-pending {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.action-cell {
  display: flex;
  gap: 10rpx;
  justify-content: center;
  
  .action-btn {
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
    border: none;
    
    &.edit {
      background-color: #1890ff;
      color: #fff;
    }
    
    &.delete {
      background-color: #ff4d4f;
      color: #fff;
    }
  }
}
</style>
