<template>
  <view class="container">
    <view class="scroll-info">
      <text>横向滚动距离: {{ scrollLeft }}px</text>
      <text>纵向滚动距离: {{ scrollTop }}px</text>
    </view>
    
    <cTable 
      :header="tableHeader" 
      :list="tableData" 
      height="400"
      @scroll="handleTableScroll"
      @scrolltolower="loadMore"
    >
      <template #default="scope">
        <view v-if="scope.prop === 'action'">
          <button @click="handleAction(scope.data)">操作</button>
        </view>
      </template>
    </cTable>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import cTable from '@/components/c-table/c-table.vue'

// 滚动距离状态
const scrollLeft = ref(0)
const scrollTop = ref(0)

// 表格配置
const tableHeader = ref([
  { prop: 'name', label: '姓名', width: '200' },
  { prop: 'age', label: '年龄', width: '100' },
  { prop: 'city', label: '城市', width: '150' },
  { prop: 'email', label: '邮箱', width: '250' },
  { prop: 'phone', label: '电话', width: '180' },
  { prop: 'action', label: '操作', width: '120', slot: true }
])

const tableData = ref([
  { name: '张三', age: 25, city: '北京', email: '<EMAIL>', phone: '13800138000' },
  { name: '李四', age: 30, city: '上海', email: '<EMAIL>', phone: '13800138001' },
  { name: '王五', age: 28, city: '广州', email: '<EMAIL>', phone: '13800138002' },
  // ... 更多数据
])

/**
 * 处理表格滚动事件
 * @param scrollData 滚动数据，包含 scrollLeft 和 scrollTop
 */
const handleTableScroll = (scrollData: { scrollLeft: number, scrollTop: number, detail: any }) => {
  console.log('表格滚动事件:', scrollData)
  
  // 更新滚动距离
  scrollLeft.value = scrollData.scrollLeft
  scrollTop.value = scrollData.scrollTop
  
  // 可以根据滚动距离做一些业务逻辑
  if (scrollData.scrollLeft > 100) {
    console.log('横向滚动超过100px')
  }
  
  if (scrollData.scrollTop > 200) {
    console.log('纵向滚动超过200px')
  }
}

const loadMore = () => {
  console.log('加载更多数据')
  // 加载更多逻辑
}

const handleAction = (data: any) => {
  console.log('操作按钮点击:', data)
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}

.scroll-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  
  text {
    font-size: 28rpx;
    color: #333;
  }
}
</style>
