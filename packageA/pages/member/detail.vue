<template>
  <view class="member-detail">
    <view class="detail-area">
      <cAreaPicker @setArea="setArea" :defaultName="areaData.orgName" :showShop="false"></cAreaPicker>
    </view>

    <view class="detail-content">
      <view class="content-view">
        <!-- 关键指标 ---start -->
        <view class="content-title">
          <text class="title-text">关键指标</text>
          <view class="title-btn">
            <view class="btn-item" :class="{ active: dateType === 'day' }" @click="setDateType('day')">
              日
            </view>
            <view class="btn-item" :class="{ active: dateType === 'month' }" @click="setDateType('month')">
              月
            </view>
          </view>
        </view>

        <cTabs :list="targetList" v-model:modelValue="targetIndex" :isBig="false" :itemWidth="170" textColor="#1F2428" @change="changeTarget"></cTabs>

        <view class="chart-box">
          <barCharts v-if="keyDetail.data.length" :barNum="2" :lineNum="dateType === 'day' ? 0 : 1" :detail="keyDetail" :leftUnit="chartUnit" rightUnit="%"></barCharts>
        </view>
        <!-- 关键指标 ---end -->

        <!-- 关键驱动因素 ---start -->
        <view class="content-title">
          <text class="title-text">关键驱动因素</text>
        </view>
        
        <view class="content-chart">
          <view class="content-btn-left">
            <view v-if="targetIndex === 0" class="btn-item" :class="{ active: driveType === 1 }" @click="setDriveType(1)">
              售卡数
            </view>
            <view v-if="targetIndex === 0" class="btn-item" :class="{ active: driveType === 2 }" @click="setDriveType(2)">
              售卡类型
            </view>
            <view v-if="targetIndex === 1" class="btn-item" :class="{ active: driveType === 3 }" @click="setDriveType(3)">
              售卡门店
            </view>
            <view v-if="[2, 3].includes(targetIndex) && dateType === 'month'" class="btn-item" :class="{ active: driveType === 4 }" @click="setDriveType(4)">
              复购率
            </view>
            <view v-if="[2, 3].includes(targetIndex)" class="btn-item" :class="{ active: driveType === 5 }" @click="setDriveType(5)">
              会员占比
            </view>
          </view>
        </view>

        <template v-if="driveType === 2">
          <accumulationCharts :detail="channelDetail" :leftUnit="leftUnit"></accumulationCharts>
          <cDate :dateType="dateType" :minData="dateType === 'day' ? Date.parse(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`) : Date.parse(`${new Date().getFullYear()}/1/1`)" @setDate="setDate"></cDate>
          <ringCharts :detail="ringDetail" @updateTooltip="updateTooltip">
            <view class="tooltip-info">
              <view class="tooltip-title">
                <text class="text">{{ tooltipInfo?.name }}：</text>
                <text class="num">{{ tooltipInfo?.value }}%</text>
              </view>

              <view class="sub-title">
                <text class="text">数量：</text>
                <text class="num">{{ tooltipInfo?.num }}张</text>
              </view>
              <view class="sub-title">
                <text class="text">金额：</text>
                <text class="num">{{ tooltipInfo?.amount }}元</text>
              </view>
            </view>
          </ringCharts>
        </template>
        <view class="chart-box" v-else>
          <barCharts v-if="driveDetail.data.length && driveType !== 4" :barNum="[1, 5].includes(driveType) ? 2 : dateType === 'day' ? 1 : 2" :lineNum="([4, 5].includes(driveType) || (driveType === 3 && dateType === 'month')) ? 1 : 0" :yAxisNum="driveType === 1 ? 2 : 1" :detail="driveDetail" :leftUnit="leftUnit" :rightUnit="rightUnit"></barCharts>
          <areaCharts v-if="driveDetail.data.length && driveType === 4" :lineNum="1" :detail="driveDetail" :leftUnit="leftUnit"></areaCharts>
          <view class="empty-wraper" v-if="!driveDetail.data.length">
            <emptyVue />
          </view>
        </view>
        <!-- 关键驱动因素 ---end -->
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup name="memberDetail">
import { ref, reactive, watch, nextTick, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted, computed } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import cDate from '@/components/c-date/index.vue'
import barCharts from '@/components/c-chart/barCharts/index.vue'
import accumulationCharts from '@/components/c-chart/accumulationCharts/index.vue'
import ringCharts from '@/components/c-chart/ringCharts/index.vue'
import areaCharts from '@/components/c-chart/areaCharts/index.vue'
import { queryKeyIndex, queryKeyDrive } from '@/packageA/api/member'
import { successCallbackResult } from '@/types'
import type { QueryKeyIndexParams, QueryKeyIndexRes, QueryKeyDriveParams, QueryKeyDriveRes } from '@/packageA/api/member'
import { showToast, formatDate } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import emptyVue from '@/components/empty/empty.vue';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
})

const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: null,
  level: '',
  orgName: ''
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name

  changeTarget()
}

const targetList = reactive([  
  { name: '售卡金额' },  
  { name: '售卡门店' },
  { name: '直销占比' },
  { name: '会员占比' }
]);
const targetIndex = ref<number>(0)
const chartUnit = computed(() => {
  if ([1].includes(targetIndex.value)) return '门店数'
  if ([2, 3].includes(targetIndex.value)) return '%'
  if ([0].includes(targetIndex.value)) return '元'
  return ''
})
// 切换tab，也是初始化入口
const changeTarget = () => {
  if (targetIndex.value === 0) {
    driveType.value = 1
  } else if (targetIndex.value === 1) {
    driveType.value = 3
  } else if ([2, 3].includes(targetIndex.value) && dateType.value === 'day') {
    driveType.value = 5
  } else {
    driveType.value = 4
  }
  keyDetail.value = {
    categories: [],
    data: []
  }
  getKeyIndexRes().then((res) => {
    keyDetail.value = res
  })
  setDriveType()
}

const leftUnit = computed(() => {
  if ([1].includes(driveType.value)) return '张'
  if ([2, 4].includes(driveType.value)) return '%'
  if ([3].includes(driveType.value)) return '门店数'
  if ([5].includes(driveType.value)) return '会员数'
  return ''
})

const rightUnit = computed(() => {
  if ([1].includes(driveType.value)) return '元'
  if ([3].includes(driveType.value)) return '%'
  if ([5].includes(driveType.value)) return '活跃会员数'
  return ''
})
const dateType = ref<string>('day')
const setDateType = (type: string) => {
  if (type === dateType.value) return
  dateType.value = type
  if (type === 'day') {
    ringDate.value = formatDate(Date.now() - 24 * 60 * 60 * 1000)
  } else {
    ringDate.value = `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}-01`
  }
  keyDetail.value = {
    categories: [],
    data: []
  }
  getKeyIndexRes().then((res) => {
    keyDetail.value = res
  })
  setDriveType()
}

const keyDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 动因分析关键指标查询
const getKeyIndexRes = (dataType?: number) => {
  const params: QueryKeyIndexParams = {
    ...areaData.value,
    dataType: dataType || targetIndex.value + 1,
    dateType: dateType.value
  }

  return queryKeyIndex(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: dateType.value === 'day' ? '上周同期' : '去年同期',
      data: res.data.map((item: QueryKeyIndexRes) => item.termValue)
    })
    data.push({
      name: dateType.value === 'day' ? '当日' : '当年',
      data: res.data.map((item: QueryKeyIndexRes) => item.actualValue)
    })
    if (dateType.value === 'month') {
      data.push({
        name: '完成率',
        data: res.data.map((item: QueryKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    return {
      categories: res.data.map((item: QueryKeyIndexRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }

  })
}

// 关键因素
const driveType = ref<number>(1)
// 切换关键驱动因素tab，也是初始化入口
const setDriveType = (type?: number) => {
  if (type === driveType.value) return
  if (type) driveType.value = type

  getKeyDrive()
}

const ringDate = ref<string>(formatDate(Date.now() - 24 * 60 * 60 * 1000))
const setDate = (date: string) => {
  ringDate.value = dateType.value === 'day' ? date : date + '-01'
  const item: QueryKeyDriveRes = keyDriveRes.value?.filter((item: QueryKeyDriveRes) => item.slotDate === ringDate.value)?.[0]
  const ringList = [{ name: '小美金卡', 
                      value: item.xmGoldenCardRate,
                      num: item.xmGoldenCardCount,
                      amount: item.xmGoldenCardPrice,
                    }, 
                    { name: '小美银卡', 
                      value: item.xmSilverCardRate,
                      num: item.xmSilverCardCount,
                      amount: item.xmSilverCardPrice,
                    }, 
                    { name: '小美钻卡', 
                      value: item.xmDiamondCardRate, 
                      num: item.xmDiamondCardCount,
                      amount: item.xmDiamondCardPrice,
                    }, 
                    { name: '金卡会员', 
                      value: item.goldMemberRate, 
                      num: item.goldMemberCount,
                      amount: item.goldMemberPrice,
                    }, 
                    { name: '玫瑰金会员', 
                      value: item.roseGoldMemberRate, 
                      num: item.roseGoldMemberCount,
                      amount: item.roseGoldMemberPrice,
                    }, 
                    { name: '银卡会员', 
                      value: item.silverCardMemberRate, 
                      num: item.silverCardMemberCount,
                      amount: item.silverCardMemberPrice,
                    }, 
                    { name: '其他', 
                      value: item.otherCardMemberRate,
                      num: item.otherCardMemberCount,
                      amount: item.otherCardMemberPrice,
                    }]
  ringDetail.value = ringList
}

const keyDriveRes = ref<Array<QueryKeyDriveRes>>()
const driveDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
const ringDetail = ref<Array<{name: string, value: number | string}>>([])
const channelDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
const getKeyDrive = () => {
  const params: QueryKeyDriveParams = {
    ...areaData.value,
    dataType: driveType.value,
    dateType: dateType.value
  }

  driveDetail.value = {
    categories: [],
    data: []
  }

  queryKeyDrive(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    keyDriveRes.value = res.data
    if (driveType.value === 2) {
      channelDetail.value.data = []
      channelDetail.value.categories = res.data.map((item: QueryKeyDriveRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7))
      channelDetail.value.data.push({
        name: '小美金卡',
        data: res.data.map((item: QueryKeyDriveRes) => item.xmGoldenCardRate)
      })
      channelDetail.value.data.push({
        name: '小美银卡',
        data: res.data.map((item: QueryKeyDriveRes) => item.xmSilverCardRate)
      })
      channelDetail.value.data.push({
        name: '小美钻卡',
        data: res.data.map((item: QueryKeyDriveRes) => item.xmDiamondCardRate)
      })
      channelDetail.value.data.push({
        name: '金卡会员',
        data: res.data.map((item: QueryKeyDriveRes) => item.goldMemberRate)
      })
      channelDetail.value.data.push({
        name: '玫瑰金会员',
        data: res.data.map((item: QueryKeyDriveRes) => item.roseGoldMemberRate)
      })
      channelDetail.value.data.push({
        name: '银卡会员',
        data: res.data.map((item: QueryKeyDriveRes) => item.silverCardMemberRate)
      })
      channelDetail.value.data.push({
        name: '其他',
        data: res.data.map((item: QueryKeyDriveRes) => item.otherCardMemberRate)
      })
      const item: QueryKeyDriveRes = res.data.filter((item: QueryKeyDriveRes) => item.slotDate === ringDate.value)?.[0]
      if (!item) return
      const ringList = [{ name: '小美金卡', 
                          value: item.xmGoldenCardRate,
                          num: item.xmGoldenCardCount,
                          amount: item.xmGoldenCardPrice,
                        }, 
                        { name: '小美银卡', 
                          value: item.xmSilverCardRate,
                          num: item.xmSilverCardCount,
                          amount: item.xmSilverCardPrice,
                        }, 
                        { name: '小美钻卡', 
                          value: item.xmDiamondCardRate, 
                          num: item.xmDiamondCardCount,
                          amount: item.xmDiamondCardPrice,
                        }, 
                        { name: '金卡会员', 
                          value: item.goldMemberRate, 
                          num: item.goldMemberCount,
                          amount: item.goldMemberPrice,
                        }, 
                        { name: '玫瑰金会员', 
                          value: item.roseGoldMemberRate, 
                          num: item.roseGoldMemberCount,
                          amount: item.roseGoldMemberPrice,
                        }, 
                        { name: '银卡会员', 
                          value: item.silverCardMemberRate, 
                          num: item.silverCardMemberCount,
                          amount: item.silverCardMemberPrice,
                        }, 
                        { name: '其他', 
                          value: item.otherCardMemberRate,
                          num: item.otherCardMemberCount,
                          amount: item.otherCardMemberPrice,
                        }]
      ringDetail.value = ringList
    } else {
      driveDetail.value.categories = res.data.map((item: QueryKeyDriveRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7))

      if (driveType.value === 1) {
        driveDetail.value.data.push({
          name: '售卡数量',
          data: res.data.map((item: QueryKeyDriveRes) => item.salesCardsCount || 0)
        })
        driveDetail.value.data.push({
          name: '售卡金额',
          data: res.data.map((item: QueryKeyDriveRes) => item.salesCardsPrice || 0)
        })
      } else if (driveType.value === 3) {
        driveDetail.value.data.push({
          name: '售卡门店数',
          data: res.data.map((item: QueryKeyDriveRes) => (dateType.value === 'day' ? item.dailyCardStoreSales : item.numberCardSalesStores) || 0)
        })

        if (dateType.value === 'month') {
          driveDetail.value.data.push({
            name: '零售卡门店数',
            data: res.data.map((item: QueryKeyDriveRes) => item.retailCardShop || 0)
          })
          driveDetail.value.data.push({
            name: '动销率',
            data: res.data.map((item: QueryKeyDriveRes) => item.salesVelocity || 0)
          })
        }
      } else if (driveType.value === 4) {
        driveDetail.value.data.push({
          name: '复购率',
          data: res.data.map((item: QueryKeyDriveRes) => item.repurchaseRate || 0)
        })
      } else if (driveType.value === 5) {
        driveDetail.value.data.push({
          name: '新增付费会员数',
          data: res.data.map((item: QueryKeyDriveRes) => item.newlyaddedPaidMembers || 0)
        })
        driveDetail.value.data.push({
          name: '新增免费会员数',
          data: res.data.map((item: QueryKeyDriveRes) => item.addFreeMembers || 0)
        })
        driveDetail.value.data.push({
          name: '活跃会员数',
          data: res.data.map((item: QueryKeyDriveRes) => item.numberActiveMembers || 0)
        })
      }
    }
  })
}

// tooltip更新
const tooltipInfo = ref()
const updateTooltip = (info) => {
  tooltipInfo.value = info
}

watch(() => props.params, (val) => {
  nextTick(() => {
    if (val.id) setArea(props.params.id, props.params.level, props.params.orgName)
  })
}, {deep: true, immediate: true})

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('D029', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.member-detail {
  position: relative;
	width: 100%;
	height: 100%;
  display: flex;
  flex-direction: column;

  .detail-area {
    padding: 0 20rpx;
  }

  .detail-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx;
    margin-top: 20rpx;

    .content-view {
      padding: 0 40rpx 0 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 22rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 0;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .content-chart {
        position: relative;

        .content-btn-left {
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      .title-btn, .content-btn-left {
        display: flex;
        align-items: center;
        height: 48rpx;
        border: 2rpx solid #f2f2f2;
        border-radius: 24rpx;

        .btn-item {
          padding: 0 24rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }
    }
  }

  .chart-box {
    min-height: 680rpx;

    .empty-wraper {
      height: 680rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tooltip-info {
    .tooltip-title {
      display: flex;
      align-items: center;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: #1F2428;
        font-weight: bold;
      }

      .num {
        font-size: 24rpx;
        padding-left: 20rpx;
        font-weight: bold;
      }
    }

    .sub-title {
      display: flex;
      align-items: center;
      padding-top: 20rpx;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: #1F2428;
      }

      .num {
        font-size: 24rpx;
        font-weight: bold;
      }
    }
  }
}
</style>