<template>
  <cTabs :list="btnList" v-model:modelValue="tabIndex" textColor="#fff" bgUrl="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-13/1736760628218.png" @change="handleTabChange"></cTabs>
</template>

<script lang="ts" setup name="cBtnGroup">
import cTabs from '@/components/c-tabs2/index.vue'
import { computed, ref, watch } from 'vue';

const props = defineProps({
  level: {
    type: String,
    default: '0',
  },

  tabType: {
    type: String,
    default: '10',
  },

  showShop: { // 是否显示门店按钮
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:tabType', 'change'])

const btnList = computed(() => {
  const list = [
    {
      name: '地区总部',
      value: '10'
    },
    {
      name: '大区',
      value: '30'
    },
    {
      name: '城区',
      value: '40'
    },
    {
      name: '我的门店',
      value: '50'
    }
  ]

  if (Number(props.level) <= 10) { // 集团、地区总部
    emit('update:tabType', '10')
    return list.filter((item) => Number(item.value) <= (props.showShop ? 50 : 40))
  } else if (Number(props.level) === 30) { // 大区
    emit('update:tabType', '30')
    return list.filter((item) => Number(item.value) >= 30 && Number(item.value) <= (props.showShop ? 50 : 40))
  } else if (Number(props.level) === 40) { // 城区
    emit('update:tabType', '40')
    return list.filter((item) => Number(item.value) >= 40)
  }
})

const tabIndex = ref<number>(0)
watch(() => props.tabType, (val) => {
  tabIndex.value = btnList.value.findIndex((item) => item.value === val)
})
const handleTabChange = () => {
  emit('update:tabType', btnList.value[tabIndex.value].value)
  emit('change', btnList.value[tabIndex.value].value)
}
</script>
