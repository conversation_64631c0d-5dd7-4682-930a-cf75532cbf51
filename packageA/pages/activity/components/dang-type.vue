<template>
  <view class="dang-type">
    <view class="type-wraper" @click="handleOpen">
      <text class="type-text">{{ chooseItem?.value ? chooseItem.label : '请选择档位变化' }}</text>
      <up-icon v-if="!show" name="arrow-down" color="#C5C5C5" size="24rpx" />
      <up-icon v-else name="arrow-up" color="#C5C5C5" size="24rpx" />
    </view>

    <up-picker :show="show" :columns="columns" keyName="label" :itemHeight="50" @confirm.stop="handleConfirm" @cancel.stop="show = false" />
  </view>
</template>

<script lang="ts" setup name="dangType">
import { ref, reactive } from 'vue';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const props = defineProps({

});

const emit = defineEmits(['setType']);

const show = ref<boolean>(false);
const chooseItem = ref<{label: string; value: number}>();
const columns = reactive<Array<Array<{label: string; value: number}>>>([[
  {
    label: '提1档',
    value: 4
  },
  {
    label: '提2档',
    value: 5
  },
  {
    label: '提3档',
    value: 6
  },
  {
    label: '保档',
    value: 3
  },
  {
    label: '降1档',
    value: 0
  },
  {
    label: '降2档',
    value: 1
  },
  {
    label: '降3档',
    value: 2
  }
]]);

const handleConfirm = (e: any) => {
  chooseItem.value = e.value[0];
  show.value = false;
  emit('setType', chooseItem.value);
};

const handleOpen = () => {
  show.value = true
}
</script>

<style lang="scss">
.dang-type {
  padding-bottom: 40rpx;

  .type-wraper {
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    border-radius: 32rpx;
    background: #FFFFFF;
    border: 2rpx solid #f2f2f2;

    .type-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .u-popup__content {
    border-radius: 40rpx !important;

    .u-toolbar {
      height: 156rpx !important;
      padding: 0 40rpx !important;

      .u-toolbar__wrapper__cancel {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #999999 !important;
      }

      .u-toolbar__title {
        font-size: 36rpx !important;
        line-height: 36rpx !important;
        color: #1F2428 !important;
      }

      .u-toolbar__wrapper__confirm {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #C35943 !important;
      }
    }

    .u-picker__view__column__item {
      font-size: 26rpx !important;
      color: #1F2428 !important;
    }
  }
}
</style>
