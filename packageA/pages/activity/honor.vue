<template>
  <view class="honor">
    <image class="honor-bg" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-07-01/1751360214253.png" mode="aspectFill"></image>
    <u-navbar placeholder title="荣耀之巅" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;" leftIconColor="#FFF" autoBack></u-navbar>

    <view class="honor-content">
      <view class="honor-content-wraper">
        <view class="honor-content-box">
          <view class="content-title">
            <text class="title-text">统计截止时间：{{ formatDate(Date.now() - 24 * 60 * 60 * 1000) }}</text>
            <view class="title-btn" @click="showRuleModal(type)">
              <text class="title-text">奖励规则</text>
              <image class="title-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
            </view>
          </view>

          <view class="content-btn">
            <view class="btn-wraper">
              <view class="btn-item" :class="{ active: type === 1 }" @click="setSortBy(1)">
                预计百万门店
              </view>
              <view class="btn-item" :class="{ active: type === 2 }" @click="setSortBy(2)">
                预计五十万门店
              </view>
            </view>
          </view>

          <view class="ranking-table">
            <cTable :header="rankData.column" :list="rankData.listData" height="864rpx" @scrolltolower="getMore">
              <template #default="scope">
                <view v-if="['roomAmount', 'cardAmount'].includes(scope.prop)" class="rate-item">
                  <view class="item-top">
                    <text class="top-text">{{ scope.data[scope.prop] }}</text>
                    <image v-if="scope.prop === 'cardAmount' && scope.data.markLogo" class="top-img" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-07-14/1752463505382.png"></image>
                  </view>
                  <view class="item-bottom">
                    <text class="item-label">同比</text>
                    <image v-if="scope.data[scope.prop + 'Tb']" class="item-icon" :src="getCompareIconUrl(scope.data[scope.prop + 'Tb'])" mode="aspectFill"></image>
                    <text class="item-text" :class="getCompareTextClass(scope.data[scope.prop + 'Tb'])">{{ Math.abs(scope.data[scope.prop + 'Tb'] || 0) }}</text>
                  </view>
                  <view class="item-bottom">
                    <text class="item-label">环比</text>
                    <image v-if="scope.data[scope.prop + 'Hb']" class="item-icon" :src="getCompareIconUrl(scope.data[scope.prop + 'Hb'])" mode="aspectFill"></image>
                    <text class="item-text" :class="getCompareTextClass(scope.data[scope.prop + 'Hb'])">{{ Math.abs(scope.data[scope.prop + 'Hb'] || 0) }}</text>
                  </view>
                </view>
              </template>
            </cTable>
          </view>

          <view class="content-info">
            <view class="info-title">
              <image class="title-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-27/1735262105323.png" mode="aspectFill"></image>
              <text class="title-text">规则说明</text>
            </view>
            <text class="info-text">
              百万门店：房费收入+售卡金额大于等于100万；
            </text>
            <text class="info-text">
              五十万门店：房费收入+售卡金额大于等于50万；
            </text>
            <text class="info-text">
              奖励门槛：房费收入+售卡金额合计数需同比去年提升；
            </text>
            <text class="info-text">
              售卡门槛：百万门店每月售卡金额需大于等于1万；
            </text>
            <text class="info-text">
              五十万门店每月售卡金额需大于等于5千
            </text>
          </view>
        </view>
      </view>
    </view>
    <cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent" :isRichText="true">
      <template #info>
          <view class="modal-info">
              <image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
              <view>以上政策适应6月份，后续月份将根据赛程进展调整优化</view>
          </view>
      </template>
    </cModal>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, nextTick, onMounted, computed } from 'vue';
import cTable from '@/components/c-table/c-table.vue'
import cModal from "@/components/c-modal/index";
import { formatDate } from '@/utils/util.ts';
import { successCallbackResult } from '@/types'
import { getRyRank } from '@/packageA/api/activity.ts'
import type { RankListParams, TotalRank } from '@/packageA/api/activity'
import { showToast } from '@/utils/util';
import { onLoad } from '@dcloudio/uni-app'

// const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

// 计算属性：优化环比图标URL生成
const getCompareIconUrl = computed(() => {
  return (value: number) => {
    const baseUrl = 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23'
    return `${baseUrl}/${value > 0 ? '1729644716250' : '1729644734157'}.png`
  }
})

// 计算属性：优化CSS类名生成
const getCompareTextClass = computed(() => {
  return (value: number) => {
    if (value > 0) return 'up-text'
    if (value < 0) return 'down-text'
    return ''
  }
})

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean,
  [key: string]: any
}

const areaData = ref<{orgId: number, level: string}>()
onLoad((option: any) => {
  areaData.value = {
    orgId: option.orgId,
    level: option.level
  }
  nextTick(() => {
    getRanks()
  })
});

const type = ref<number>(1)
// 筛选更改 => 1.更新排名
const setSortBy = (val: number) => {
  if (val === type.value) return
  type.value = val
  pageParams.value.pageNum = 1
  getRanks()
}

const rankData = ref<{ column: Array<Column>, listData: Array<TotalRank> }>({
  column: [
    {
      name: '排名',
      prop: 'rankNum',
      width: '120',
    },
    {
      name: '门店名称',
      prop: 'shopName',
      width: '300',
    },
    {
      name: 'MTD房费收入',
      subTitle: '/万元',
      prop: 'roomAmount',
      width: '180',
      slot: 'roomAmount'
    },
    {
      name: 'MTD售卡金额',
      subTitle: '/元',
      prop: 'cardAmount',
      width: '180',
      slot: 'cardAmount'
    },
    {
      name: '预计全月营收',
      subTitle: '/万元',
      prop: 'totalAmount',
      width: '180',
    },
  ],
  listData: []
})
const pageParams = ref({
  pageNum: 1,
  pageSize: 20,
})
const getMore = () => {
  if (pageParams.value.pageNum * pageParams.value.pageSize > rankData.value.listData.length) return
  pageParams.value.pageNum++
  getRanks()
}
// 获取排名
const getRanks = () => {
  if (pageParams.value.pageNum === 1) {
    rankData.value.listData = []
  }
  
  const params: RankListParams = {
    ...pageParams.value,
    ...areaData.value,
    type: type.value,
  }

  getRyRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    rankData.value.listData = rankData.value.listData.concat(res.data)
  })
}

// 奖励规则弹窗
const modalShow = ref<boolean>(false);
const modalTitle = ref<string>("");
const modalContent = ref<string>("");
const showRuleModal = (ruleType:number) => {
  modalShow.value = true;
  modalTitle.value = ruleType == 1 ? '百万营收门店激励' : '五十万营收门店激励';
  const _content = getModalContent(ruleType)
  modalContent.value = _content;
};
// 百万营收门店激励/50万营收门店激励
const getModalContent = (ruleType: number = 1) => { 
  const modalTypeMap =  {
    1:{
      1: '5,000',
      2: '1,000',
      3: '1万',
      4: '100万',
      5: '10,000',
      6: '1,000',
      7: '100万',
      8: '1万',
    },
    2:{
      1: '1,500',
      2: '500',
      3: '5千',
      4: '50万',
      5: '5,000',
      6: '500',
      7: '50万',
      8: '5千',
    }
}

 const selectedMap = modalTypeMap[ruleType] || modalTypeMap[1]; // 默认使用类型1
// ruleType 1.百万营收门店激励 2.五十万营收门店激励
 return `
 <div class='red'>月度奖励：</div><div>${ruleType == 1 ? `业主：奖励`: `门店奖励`}<span class='red'>${selectedMap[1]}</span>元现金<span class='red'>${selectedMap[1]}</span>元运营物资/流量</div>
 <div>城区：现金奖励<span class='red'>${selectedMap[2]}</span>/店</div>
 <div>店长：现金奖励<span class='red'>${selectedMap[2]}</span>/店</div>  
 <div style='font-weight:bold'>激励标准：</div>
 <div>房费和会员卡销售额总和同比提升</div>
 <div>且月均售卡大于等于${selectedMap[3]}</div>
 <div>月度营收和售卡金额合计达${selectedMap[4]}</div>
 <div class='red'>赛季奖励：</div>
 <div>${ruleType == 1 ? `业主：`: ``}门店奖励<span class='red'>${selectedMap[5]}</span>/元现金</div>
 <div>城区：现金奖励<span class='red'>${selectedMap[6]}</span>/店</div>  
 <div>店长：现金奖励<span class='red'>${selectedMap[6]}</span>/店</div>  
 <div style='font-weight:bold'>激励标准：</div>
 <div>门店赛季月均营收达${selectedMap[7]}且每月售卡金额≥${selectedMap[8]}</div>
 <div>赛季内，月度激励不重复发放，新店参考环比提升</div>
 <div><span style='font-weight:bold'>门槛：</span>${ruleType === 1 ? '无欠费、无舞弊行为、数据达到门槛值、管理费免收门店不参与' : '无欠费、无舞弊行为、数据真实有效、对赌门店须达到门槛值，管理费免收门店不参与'}</div>
 ` 
}
let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

// onUnmounted(() => {
//   trackEvent('H004', {time: (Date.now() - startTime) / 1000})
// })
</script>

<style lang="scss" scoped>
.honor {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .honor-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 800rpx;
    z-index: -1;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 84rpx;
      background: #EFEFEF;
      border-radius: 0 84rpx 0 0;
    }
  }

  .honor-content {
    flex: 1;
    overflow: hidden;
    padding: 490rpx 20rpx 0 20rpx;
    display: flex;
    flex-direction: column;

    .honor-content-wraper {
      flex: 1;
      overflow: auto;
    }

    .honor-content-box {
      padding: 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 20rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          font-size: 22rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .title-btn {
          display: flex;
          align-items: center;

          .title-text {
            padding-right: 6rpx;
            font-size: 24rpx;
            line-height: 24rpx;
            color: #1F2428;							
          }

          .title-info {
            width: 22rpx;
            height: 22rpx;
          }
        }
      }

      .content-btn {
        padding-top: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-wraper {
          display: flex;
          align-items: center;
          height: 48rpx;
          border: 2rpx solid #f2f2f2;
          border-radius: 24rpx;

          .btn-item {
            padding: 0 24rpx;
            font-size: 22rpx;
            line-height: 44rpx;
            color: rgba(31, 36, 40, 0.7);
          }

          .active {
            border-radius: 22rpx;
            background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
            font-weight: bold;
            color: #fff;
          }
        }
      }

      .ranking-table {
        margin-top: 40rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .table-num {
          position: relative;
          display: flex;
          width: 108rpx;
          justify-content: flex-start;
          align-items: center;
          
          .num-text {
            display: block;
            padding-left: 30rpx;
            font-size: 22rpx;
          }

          .num-label {
            position: absolute;
            top: -20rpx;
            right: 0;
            padding: 0 8rpx;
            font-size: 18rpx;
            line-height: 34rpx;
            color: #fff;
            background: #ff4d4d;
            border-radius: 20rpx 20rpx 20rpx 0;
          }
        }

        .rate-item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          .item-top {
            display: flex;
            align-items: center;

            .top-text {
              font-size: 24rpx;
              line-height: 24rpx;
              font-weight: bold;
            }

            .top-img {
              margin-left: 8rpx;
              width: 24rpx;
              height: 22rpx;
            }
          }

          .item-bottom {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .item-label {
            padding-right: 8rpx;
            font-size: 20rpx;
            line-height: 28rpx;
            color: rgba(31, 36, 40, 0.7);
          }

          .item-icon {
            width: 16rpx;
            height: 20rpx;
            margin-right: 8rpx;
          }

          .item-text {
            font-size: 24rpx;
            line-height: 28rpx;
            font-weight: bold;
            font-family: "DIN Bold";
            color: #1F2428;
          }

          .up-text {
            color: #FF4D4D;
          }

          .down-text {
            color: #56CC93;
          }
        }
      }

      .content-info {
        display: flex;
        flex-direction: column;
        margin-top: 20rpx;
				padding: 20rpx;
				border-radius: 20rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

        .info-title {
          display: flex;
          align-items: center;
          padding-bottom: 13rpx;

          .title-icon {
            width: 22rpx;
            height: 22rpx;
          }

          .title-text {
            padding-left: 8rpx;
            font-size: 26rpx;
            line-height: 36rpx;
            font-weight: bold;
            color: #C35943;
          }
        }

        .info-text {
          padding-left: 8rpx;
          font-size: 22rpx;
					line-height: 36rpx;
					color: rgba(31, 36, 40, 0.7);
        }
      }
    }
  }
}
.modal-info{
  display: flex;
  align-items: flex-start; 
  width: 100%;
  background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
  border-radius: 20rpx;
  color: #1f2428b3;
  font-size: 22rpx;
  font-family: "苹方-繁";
  text-align: justify;
  line-height: 34rpx;
  padding: 20rpx;
  margin: 40rpx 0;
  .item-info {
    width: 22rpx;
    height: 22rpx;
    flex-shrink: 0;
    margin: 6rpx 8rpx 0 0;
  }
}

:deep(.tbody) {
  .tr {
    padding: 52rpx 0 !important;
  }
}
</style>
