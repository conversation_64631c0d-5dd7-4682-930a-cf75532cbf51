<template>
  <view class="guard">
    <image class="guard-bg" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-01-13/1736758256916.png" mode="aspectFill"></image>
    <u-navbar placeholder title="守护者行动" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;" leftIconColor="#FFF" autoBack></u-navbar>

    <view class="guard-content">
      <view class="guard-btn">
        <view class="btn-item" :class="{ active: btnType === 1 }" @click="setBtnType(1)">
          优秀运营门店
        </view>
        <view class="btn-item" :class="{ active: btnType === 2 }" @click="setBtnType(2)">
          营业榜
        </view>
        <view class="btn-item" :class="{ active: btnType === 3 }" @click="setBtnType(3)">
          0售卡突破榜
        </view>
      </view>
      <btnGroup :level="areaData?.level" v-model:tabType="tabType" @change="handleChange"></btnGroup>

      <view class="guard-content-wraper">
        <view class="guard-content-box">
          <view class="content-title">
            <text class="title-text">统计截止时间：{{ formatDate(Date.now() - 24 * 60 * 60 * 1000) }}</text>
            <view class="title-btn">
              <view class="btn-item" :class="{ active: sortBy === 'desc' }" @click="setSortBy('desc')">
                正序
              </view>
              <view class="btn-item" :class="{ active: sortBy === 'asc' }" @click="setSortBy('asc')">
                倒序
              </view>
            </view>
          </view>

          <view class="content-info" v-if="myRankInfo.myRank">
            <view class="info-title">
              <text class="title-text">我的排名 {{ myRankInfo.myRank }}</text>
            </view>

            <view class="info-content">
              <text class="info-area">{{ myRankInfo.orgName }}</text>
              <view class="info-item">
                <text class="item-text">完成率：</text>
                <text class="item-num">{{ myRankInfo.completionRate }}%</text>
              </view>
            </view>
          </view>

          <view class="content-rank" :style="{ 'justify-content': topThree.length > 1 ? 'space-between' : 'center' }" v-if="sortBy === 'desc'">
            <template v-if="topThree.length > 1">
              <view class="rank-item" :class="['rank-item-' + (index + 1)]" v-for="(item, index) in topThree" :key="item.id">
                <image v-if="index === 1" class="item-medals" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585453391.png" mode="aspectFill"></image>
                <image v-if="index === 0" class="item-medals" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585426241.png" mode="aspectFill"></image>
                <image v-if="index === 2" class="item-medals" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585406693.png" mode="aspectFill"></image>
              
                <view class="item-info">
                  <text class="info-area">{{ item.orgName }}</text>
                  <text class="info-num">完成率：{{ item.completionRate }}%</text>
                </view>
              </view>
            </template>
            <view v-else class="rank-item" :class="['rank-item-2']" v-for="(item) in topThree" :key="item.id">
              <image class="item-medals" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585453391.png" mode="aspectFill"></image>
            
              <view class="item-info">
                <text class="info-area">{{ item.orgName }}</text>
                <text class="info-num">完成率：{{ item.completionRate }}%</text>
              </view>
            </view>
          </view>

          <view class="ranking-table">
            <cTable :header="rankData.column" :list="rankData.listData" :showHeader="false" height="756rpx" @scrolltolower="getMore">
              <template #default="scope">
                <view class="table-num" v-if="scope.prop === 'rank'">
                  <text class="num-text">{{ scope.data[scope.prop] }}</text>
                  <text class="num-label"  v-if="scope.data.isShowIcon">加油</text>
                </view>
                <template v-if="scope.prop === 'completionRate'">完成率：{{ scope.data[scope.prop] }}%</template>
              </template>
            </cTable>
          </view>
        </view>
      </view>
    </view>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, nextTick, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted } from 'vue';
import btnGroup from './components/btn-group.vue'
import cTable from '@/components/c-table/c-table.vue'
import { formatDate } from '@/utils/util.ts';
import { successCallbackResult } from '@/types'
import { guardianActionRank } from '@/packageA/api/activity.ts'
import type { RankParams, MyRankInfo, TotalRank } from '@/packageA/api/assets'
import { showToast } from '@/utils/util';
import { onLoad } from '@dcloudio/uni-app'
import { useCommon } from '@/hooks/useGlobalData';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

const areaData = ref<{orgId: number, level: string}>()
onLoad((option: any) => {
  areaData.value = {
    orgId: option.orgId,
    level: option.level
  }
  nextTick(() => {
    getRanks()
  })
});

const tabType = ref<string>()
// 组织tab切换 => 1.更新排名
const handleChange = () => {
  nextTick(() => {
    pageParams.value.pageNum = 1
    getRanks()
  })
}

const sortBy = ref<string>('desc')
// 排序更改 => 1.更新排名
const setSortBy = (val: string) => {
  if (val === sortBy.value) return
  sortBy.value = val
  pageParams.value.pageNum = 1
  getRanks()
}

const btnType = ref<number>(1)
// 类型更改 => 1.更新排名
const setBtnType = (val: number) => {
  if (val === btnType.value) return
  btnType.value = val
  pageParams.value.pageNum = 1
  getRanks()
}

const myRankInfo = ref<MyRankInfo>({})
const topThree = ref<Array<TotalRank>>([])
const rankData = ref<{ column: Array<Column>, listData: Array<TotalRank> }>({
  column: [
    {
      name: '排名',
      prop: 'rank',
      width: '120',
      slot: 'rank',
    },
    {
      name: '地区',
      prop: 'orgName',
      width: '200',
    },
    {
      name: '完成率',
      prop: 'completionRate',
      slot: 'completionRate',
    },
  ],
  listData: []
})
const pageParams = ref({
  pageNum: 1,
  pageSize: 20,
})
const getMore = () => {
  if (pageParams.value.pageNum * pageParams.value.pageSize > (sortBy.value === 'desc' ? rankData.value.listData.length + topThree.value.length : rankData.value.listData.length)) return
  pageParams.value.pageNum++
  getRanks()
}
// 获取排名
const getRanks = () => {
  if (pageParams.value.pageNum === 1) {
    rankData.value.listData = []
    topThree.value = []
  }

  const params: RankParams = {
    ...pageParams.value,
    ...areaData.value,
    sortBy: sortBy.value,
    tabLevel: tabType.value,
    type: btnType.value,
    lastRank: rankData.value.listData[rankData.value.listData.length - 1]?.rank || '',
    lastData: rankData.value.listData[rankData.value.listData.length - 1]?.completionRate || 0,
  }

  guardianActionRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    if (sortBy.value === 'desc' && params.pageNum === 1) {
      topThree.value = res.data.totalRank.splice(0, 3)
      if (topThree.value.length >= 2) topThree.value.unshift(topThree.value.splice(1, 1)[0]);
    }
    if (params.pageNum === 1) {
      myRankInfo.value = {
        ...res.data.myRankInfo,
        myRank: res.data.myRank
      }
    }
    rankData.value.listData = rankData.value.listData.concat(res.data.totalRank)
  })
}

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('H005', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss" scoped>
.guard {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .guard-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 1088rpx;
    z-index: -1;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 84rpx;
      background: #EFEFEF;
      border-radius: 0 84rpx 0 0;
    }
  }

  .guard-content {
    flex: 1;
    overflow: hidden;
    padding: 530rpx 20rpx 0 20rpx;
    display: flex;
    flex-direction: column;

    .guard-btn {
      display: inline-flex;
      align-items: center;
      height: 48rpx;
      margin: 0 auto;
      border-radius: 24rpx;
      background: #9E3924;

      .btn-item {
        padding: 0 28rpx;
        font-size: 22rpx;
        line-height: 48rpx;
        color: #fff;
      }

      .active {
        border-radius: 22rpx;
        background: linear-gradient(120.3deg, #f8c39b 0%, #fcc77d 100%);
        font-weight: bold;
        color: #9D0402;
      }
    }

    .guard-content-wraper {
      flex: 1;
      overflow: auto;
    }

    .guard-content-box {
      padding: 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 20rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          font-size: 22rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .title-btn {
          display: flex;
          align-items: center;
          height: 48rpx;
          border: 2rpx solid #f2f2f2;
          border-radius: 24rpx;

          .btn-item {
            padding: 0 24rpx;
            font-size: 22rpx;
            line-height: 44rpx;
            color: rgba(31, 36, 40, 0.7);
          }

          .active {
            border-radius: 22rpx;
            background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
            font-weight: bold;
            color: #fff;
          }
        }
      }

      .content-info {
        position: relative;
        margin-top: 40rpx;
        background: linear-gradient(90deg, rgba(247, 94, 59, 0.1) 0%, rgba(247, 94, 59, 0.05) 100%);
        border-radius: 20rpx;

        .info-title {
          display: inline-block;
          padding: 0 28rpx;
          background: linear-gradient(203.5deg, #FFB09F 0%, #F75E3B 100%);
          border-radius: 20rpx 0 20rpx 0;

          .title-text {
            font-size: 28rpx;
            line-height: 52rpx;
            font-weight: bold;
            color: #fff;
          }
        }

        .info-content {
          padding: 28rpx 40rpx 40rpx 40rpx;
          display: flex;
          align-items: center;

          .info-area {
            padding-right: 8rpx;
            font-size: 32rpx;
            font-weight: bold;
            color: #1F2428;
          }

          .info-item {
            padding-left: 40rpx;
            display: flex;
            align-items: center;
            white-space: nowrap;

            .item-text {
              font-size: 24rpx;
              color: #1F2428;
            }

            .item-num {
              font-size: 40rpx;
              line-height: 40rpx;
              font-family: 'DIN Bold';
							color: #1F2428;
							font-weight: bold;
            }
          }
        }
      }

      .content-rank {
        display: flex;
        // justify-content: space-between;
        padding-bottom: 20rpx;
        margin-top: 40rpx;

        .rank-item {
          width: 196rpx;
          position: relative;
          margin-top: 28rpx;
          padding-top: 100rpx;
          padding-bottom: 20rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 20rpx 60rpx 20rpx 20rpx;

          .item-medals {
            position: absolute;
            top: -28rpx;
            left: 60rpx;
            width: 94rpx;
            height: 100rpx;
          }

          .item-info {
            display: flex;
            flex-direction: column;
            align-items: center;

            .info-area {
              padding-bottom: 20rpx;
              font-size: 28rpx;
              line-height: 32rpx;
              text-align: center;
              font-weight: bold;
              // font-family: "AlimamaShuHeiTi";
            }

            .info-num {
              padding: 8rpx 0;
              font-size: 22rpx;
              line-height: 22rpx;
              font-weight: bold;
            }
          }
        }

        .rank-item-1 {
          position: relative;
          top: 20rpx;
          background: linear-gradient(180deg, rgba(145, 186, 255, 0.1) 0%, rgba(20, 116, 250, 0.3) 100%);

          .item-info {
            .info-area {
              color: #113966;
            }

            .info-num {
              color: #113966;
            }
          }
        }

        .rank-item-2 {
          background: linear-gradient(180deg, rgba(255, 220, 92, 0.1) 0%, rgba(252, 173, 13, 0.3) 100%);

          .item-info {
            .info-area {
              color: #7D3023;
            }

            .info-num {
              color: #7D3023;
            }
          }
        }

        .rank-item-3 {
          position: relative;
          top: 20rpx;
          background: linear-gradient(180deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.3) 100%);

          .item-info {
            .info-area {
              color: #661611;
            }

            .info-num {
              color: #661611;
            }
          }
        }
      }

      .ranking-table {
        margin-top: 40rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .table-num {
          position: relative;
          display: flex;
          width: 108rpx;
          justify-content: flex-start;
          align-items: center;
          
          .num-text {
            display: block;
            padding-left: 30rpx;
            font-size: 22rpx;
          }

          .num-label {
            position: absolute;
            top: -20rpx;
            right: 0;
            padding: 0 8rpx;
            font-size: 18rpx;
            line-height: 34rpx;
            color: #fff;
            background: #ff4d4d;
            border-radius: 20rpx 20rpx 20rpx 0;
          }
        }
      }
    }
  }
}
</style>
