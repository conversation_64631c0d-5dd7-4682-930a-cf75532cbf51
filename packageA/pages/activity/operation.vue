<template>
  <view class="operation">
    <image class="operation-bg" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787255259.png" mode="aspectFill"></image>
    <u-navbar placeholder title="经营提升争霸赛" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;" leftIconColor="#FFF" autoBack></u-navbar>

    <view class="operation-content">
      <btnGroup :level="areaData?.level" v-model:tabType="tabType" :showShop="true" @change="handleChange"></btnGroup>
      <view class="operation-content-wraper">
        <view class="operation-content-box">
          <dangType v-if="tabType === '50'" @setType="setType"></dangType>

          <view class="content-title">
            <text class="title-text">统计截止时间：{{ formatDate(Date.now() - 24 * 60 * 60 * 1000) }}</text>
            <view class="title-btn" @click="showRuleModal()">
              <text class="title-text">奖励规则</text>
              <image class="title-info" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
            </view>
          </view>

          <view class="operation-btn" v-if="tabType === '40'">
            <view class="btn-item" :class="{ active: btnType === 1 }" @click="setBtnType(1)">
              单店贡献
            </view>
            <view class="btn-item" :class="{ active: btnType === 2 }" @click="setBtnType(2)">
              星点奖励
            </view>
          </view>

          <template v-if="tabType && tabType !== '50'">
            <view class="content-info" v-if="myRankInfo.rankNum" @click="setStoreData">
              <view class="info-title">
                <text class="title-text">我的排名 {{ btnType === 2 && tabType === '40' ? myRankInfo.xdRank : myRankInfo.rankNum }}</text>
              </view>

              <view class="info-content" v-if="!(btnType === 2 && tabType === '40')">
                <text class="info-area">{{ myRankInfo.orgName }}</text>
                <view class="info-item">
                  <text class="item-text">单店贡献预测完成率：</text>
                  <text class="item-num">{{ myRankInfo.gxActualRate }}%</text>
                </view>
              </view>

              <view class="info-content2" v-else>
                <text class="info-area">{{ myRankInfo.orgName }}</text>
                <view class="info-item-box">
                  <view class="info-item">
                    <image class="my-zuanshi" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787276735.png`" mode="aspectFill"></image>
                    <text class="item-num">{{ myRankInfo.level1 }}</text>
                    <view class="item-info">
                      <text class="info-name">环比</text>
                      <image v-if="myRankInfo.level1Hb" class="info-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${myRankInfo.level1Hb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                      <text class="info-num" :class="[ myRankInfo.level1Hb > 0 ? 'info-num-up' : myRankInfo.level1Hb < 0 ? 'info-num-down' : '']">{{ myRankInfo.level1Hb !== null ? Math.abs(myRankInfo.level1Hb) : '-' }}</text>
                    </view>
                  </view>
                  <view class="info-item">
                    <image class="my-img" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787206591.png`" mode="aspectFill"></image>
                    <text class="item-num">{{ myRankInfo.level2 }}</text>
                    <view class="item-info">
                      <text class="info-name">环比</text>
                      <image v-if="myRankInfo.level2Hb" class="info-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${myRankInfo.level2Hb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                      <text class="info-num" :class="[ myRankInfo.level2Hb > 0 ? 'info-num-up' : myRankInfo.level2Hb < 0 ? 'info-num-down' : '']">{{ myRankInfo.level2Hb !== null ? Math.abs(myRankInfo.level2Hb) : '-' }}</text>
                    </view>
                  </view>
                  <view class="info-item">
                    <image class="my-zuanshi" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787226633.png`" mode="aspectFill"></image>
                    <text class="item-num">{{ myRankInfo.level3 }}</text>
                    <view class="item-info">
                      <text class="info-name">环比</text>
                      <image v-if="myRankInfo.level3Hb" class="info-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${myRankInfo.level3Hb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                      <text class="info-num" :class="[ myRankInfo.level3Hb > 0 ? 'info-num-up' : myRankInfo.level3Hb < 0 ? 'info-num-down' : '']">{{ myRankInfo.level3Hb !== null ? Math.abs(myRankInfo.level3Hb) : '-' }}</text>
                    </view>
                  </view>
                  <view class="info-item">
                    <image class="my-zuanshi" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-17/1750154092063.png`" mode="aspectFill"></image>
                    <text class="item-num">{{ myRankInfo.level4 }}</text>
                    <view class="item-info">
                      <text class="info-name">环比</text>
                      <image v-if="myRankInfo.level4Hb" class="info-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${myRankInfo.level4Hb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                      <text class="info-num" :class="[ myRankInfo.level4Hb > 0 ? 'info-num-up' : myRankInfo.level4Hb < 0 ? 'info-num-down' : '']">{{ myRankInfo.level4Hb !== null ? Math.abs(myRankInfo.level4Hb) : '-' }}</text>
                    </view>
                  </view>
                  <view class="info-item">
                    <text class="item-title">星点奖励</text>
                    <text class="item-num">{{ myRankInfo.xdScore }}</text>
                  </view>
                  <view class="info-item">
                    <text class="item-title">门店分值</text>
                    <text class="item-num">{{  myRankInfo.shopScore }}</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="content-rank" v-if="btnType === 1 || tabType !== '40'" :style="{ 'justify-content': topThree.length > 1 ? 'space-between' : 'center' }">
              <template v-if="topThree.length > 1">
                <view class="rank-item" :class="['rank-item-' + (index + 1)]" v-for="(item, index) in topThree" :key="item.id" @click="chooseItem(item)">
                  <image v-if="index === 1" class="item-medals" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585453391.png" mode="aspectFill"></image>
                  <image v-if="index === 0" class="item-medals" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585426241.png" mode="aspectFill"></image>
                  <image v-if="index === 2" class="item-medals" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585406693.png" mode="aspectFill"></image>
                
                  <view class="item-info">
                    <text class="info-area">{{ item.orgName }}</text>
                    <text class="info-text">单店贡献预测完成率：</text>
                    <text class="info-num">{{ item.gxActualRate }}%</text>
                  </view>
                </view>
              </template>
              <view v-else class="rank-item" :class="['rank-item-2']" v-for="(item) in topThree" :key="item.id" @click="chooseItem(item)">
                <image class="item-medals" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/1732585453391.png" mode="aspectFill"></image>
              
                <view class="item-info">
                  <text class="info-area">{{ item.orgName }}</text>
                  <text class="info-text">单店贡献预测完成率：</text>
                  <text class="info-num">{{ item.gxActualRate }}%</text>
                </view>
              </view>
            </view>
          </template>
          

          <view class="ranking-table">
            <template v-if="!(tabType === '40' && btnType === 2)">
              <cTable v-if="tabType && tabType !== '50'" :header="rankData.column" :list="rankData.listData" :showHeader="false" height="864rpx" @scrolltolower="getMore" @chooseItem="chooseItem">
                <template #default="scope">
                  <template v-if="scope.prop === 'gxActualRate'">单店贡献预测完成率：{{ scope.data[scope.prop] }}%</template>
                </template>
              </cTable>
              <cTable v-else :header="storeRankData.column" :list="storeRankData.listData" height="864rpx" @scrolltolower="getMore">
                <template #default="scope">
                  <view class="table-num" v-if="scope.prop === 'rankNum'">
                    <image v-if="scope.data.rankNum <= 3" class="item-medals" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/${scope.data.rankNum === 1 ? '1732585453391' : scope.data.rankNum === 2 ? '1732585426241' : '1732585406693'}.png`" mode="aspectFill"></image>
                    <text v-else class="num-text">{{ scope.data[scope.prop] }}</text>
                  </view>
                  <view class="table-img" v-if="scope.prop === 'shopType'">
                    <image class="img-top" :class="scope.data.shopType === 1 && 'zuanshi-img'" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/${scope.data.shopType === 4 ? '2025-06-17' : '2025-06-13'}/${scope.data.shopType === 1 ? '1749787276735' : scope.data.shopType === 2 ? '1749787206591' : scope.data.shopType === 3 ? '1749787226633' : '1750154092063'}.png`"></image>
                    <view class="img-bottom">
                      <text class="img-text">上月</text>
                      <image class="img-icon" :class="scope.data.lastShopType === 1 && 'zuanshi-icon'" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/${scope.data.lastShopType === 4 ? '2025-06-17' : '2025-06-13'}/${scope.data.lastShopType === 1 ? '1749787276735' : scope.data.lastShopType === 2 ? '1749787206591' : scope.data.lastShopType === 3 ? '1749787226633' : '1750154092063'}.png`"></image>
                    </view>
                  </view>
                  <view v-if="['valueHb'].includes(scope.prop)" class="rate-item">
                    <image v-if="scope.data[scope.prop]" class="item-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                    <text class="item-text" :class="[scope.data[scope.prop] > 0 ? 'up-text' : scope.data[scope.prop] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop]) + '%' }}</text>
                  </view>
                </template>
              </cTable>
            </template>
            
            <cTable v-else :header="cityRankData.column" :list="cityRankData.listData" height="864rpx" @scrolltolower="getCityMore">
              <template #default="scope">
                <view class="table-num" v-if="scope.prop === 'xdRank'">
                  <image v-if="scope.data.xdRank <= 3" class="item-medals" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-26/${scope.data.xdRank === 1 ? '1732585453391' : scope.data.xdRank === 2 ? '1732585426241' : '1732585406693'}.png`" mode="aspectFill"></image>
                  <text v-else class="num-text">{{ scope.data[scope.prop] }}</text>
                </view>
                <view class="rate-item" v-if="['level1', 'level2', 'level3', 'level4'].includes(scope.prop)">
                  <view class="item-top">{{ scope.data[scope.prop] }}</view>
                  <view class="item-bottom">
                    <image v-if="scope.data[scope.prop + 'Hb']" class="item-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop + 'Hb'] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                    <text class="item-text" :class="[scope.data[scope.prop + 'Hb'] > 0 ? 'up-text' : scope.data[scope.prop + 'Hb'] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop + 'Hb']) }}</text>
                  </view>
                </view>
                <!-- <view v-if="['shopScore'].includes(scope.prop)" class="rate-item">
                  <image v-if="scope.data[scope.prop]" class="item-icon" :src="`https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="item-text" :class="[scope.data[scope.prop] > 0 ? 'up-text' : scope.data[scope.prop] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop]) + '%' }}</text>
                </view> -->
              </template>
              <template #level1>
                <view class="table-img">
                  <image class="header-img header-zuanshi" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787276735.png"></image>
                </view>
              </template>
              <template #level2>
                <view class="table-img">
                  <image class="header-img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787206591.png"></image>
                </view>
              </template>
              <template #level3>
                <view class="table-img">
                  <image class="header-img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-13/1749787226633.png"></image>
                </view>
              </template>
              <template #level4>
                <view class="table-img">
                  <image class="header-img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-17/1750154092063.png"></image>
                </view>
              </template>
            </cTable>
          </view>

          <view class="content-desc">
            <view class="info-title">
              <image class="title-icon" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-27/1735262105323.png" mode="aspectFill"></image>
              <text class="title-text">规则说明</text>
            </view>
            <text class="info-text">
              {{ tabType === '50' ? '排名规则：按照房费收入+售卡金额*5的合计数进行全国排名' : '本月预计单店贡献=店均房费收入*综合费率+店均售卡金额/1.06+店均上月包房收入' }}
            </text>
            <text class="info-text" v-if="tabType !== '50'">该数据为当月进度数据</text>
          </view>
        </view>
      </view>
    </view>
    <cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent" :isRichText="true">
      <template #info>
          <view class="modal-info">
              <image class="item-info" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
              <view>以上政策适应6月份，后续月份将根据赛程进展调整优化</view>
          </view>
      </template>
    </cModal>
    <cPopup v-model:show="popupShow">
      <template #content>
        <view class="view-scoller">
          <view class="full-screen">
            <view class="c-popup-content">
        
              <view class="rotate-wrapper">
              <view class="modal-title">经营排位赛激励政策</view>
                <image src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-18/1750235588220.png" mode="aspectFit" lazy-load class="image-content"></image>
                <view class="city-modal-info">
                  <view>业主获奖门槛：月度卡金额大于<text class="red">3,000元</text></view>
                  <view>星点值：星点值具体以战功体系签批为准</view>
                  <view>城区惩罚：城区内门店分值总和环比下降Top3的城区</view>
                </view>
                <view class="confirm-btn-wrapper">
                  <view class="confirm-btn" @click="handlePopupClose">
                  确认
                </view>
              </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </cPopup>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, nextTick, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted } from 'vue';
import btnGroup from './components/btn-group.vue'
import cTable from '@/components/c-table/c-table.vue'
import cPopup from '@/components/c-popup/c-popup.vue'
import cModal from "@/components/c-modal/index";
import dangType from './components/dang-type.vue'
import { formatDate } from '@/utils/util.ts';
import { successCallbackResult } from '@/types'
import { getZbOrgRank, getZbShopRank, getZbOrgScoreRank } from '@/packageA/api/activity.ts'
import type { RankListParams, MyRankInfo, TotalRank } from '@/packageA/api/activity'
import { showToast } from '@/utils/util';
import { onLoad } from '@dcloudio/uni-app'
import { useCommon } from '@/hooks/useGlobalData';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean,
  headerSlot?: string,
}

const areaData = ref<{orgId: number, level: string}>()
const shopAreaData = ref<{orgId: number, level: string}>()
onLoad((option: any) => {
  areaData.value = {
    orgId: option.orgId,
    level: option.level
  }
  shopAreaData.value = {
    orgId: option.orgId,
    level: option.level
  }
  nextTick(() => {
    getRanks()
  })
});

const tabType = ref<string>('')
const btnType = ref<number>(1)
const setBtnType = (val: number) => {
  if (val === btnType.value) return
  btnType.value = val
  pageParams.value.pageNum = 1
  if (val === 1) {
    getRanks()
  } else {
    getCityRank()
  }
}
// 组织tab切换 => 1.更新排名
const handleChange = () => {
  nextTick(() => {
    pageParams.value.pageNum = 1
    if (tabType.value === '50') {
      chooseItemParam.value = undefined
      getStoreRank()
    } else {
      getRanks()
    }
  })
}

const myRankInfo = ref<MyRankInfo>({})
const topThree = ref<Array<TotalRank>>([])
const rankData = ref<{ column: Array<Column>, listData: Array<TotalRank> }>({
  column: [
    {
      name: '排名',
      prop: 'rankNum',
      width: '120',
    },
    {
      name: '地区',
      prop: 'orgName',
      width: '180',
      canClick: true,
      type: 'choose',
    },
    {
      name: '完成率',
      prop: 'gxActualRate',
      slot: 'gxActualRate',
    },
  ],
  listData: []
})
const pageParams = ref({
  pageNum: 1,
  pageSize: 20,
})
const getMore = () => {
  if (tabType.value && tabType.value !== '50') {
    if (pageParams.value.pageNum * pageParams.value.pageSize > (rankData.value.listData.length + topThree.value.length)) return
    pageParams.value.pageNum++
    getRanks()
  } else {
    if (pageParams.value.pageNum * pageParams.value.pageSize > storeRankData.value.listData.length) return
    pageParams.value.pageNum++
    getStoreRank()
  }
}
// 获取排名
const getRanks = () => {
  if (pageParams.value.pageNum === 1) {
    rankData.value.listData = []
    topThree.value = []
  }
  
  const params: RankListParams = {
    ...pageParams.value,
    ...areaData.value,
    tabLevel: tabType.value,
  }

  getZbOrgRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    if (params.pageNum === 1) {
      topThree.value = res.data.totalRank.splice(0, 3)
      if (topThree.value.length >= 2) topThree.value.unshift(topThree.value.splice(1, 1)[0]);
    }
    if (params.pageNum === 1) {
      myRankInfo.value = {
        ...res.data.myRank
      }
    }
    rankData.value.listData = rankData.value.listData.concat(res.data.totalRank)
    console.log('获取数据成功', rankData.value.listData)
  })
}

const storeRankData = ref<{ column: Array<Column>, listData: Array<TotalRank> }>({
  column: [
    {
      name: '全国排名',
      prop: 'rankNum',
      width: '120',
      slot: 'rankNum',
    },
    {
      name: '',
      prop: 'shopType', // 门店类型 1:钻石门店 2:黄金门店 3:白银门店 4:青铜门店
      width: '120',
      slot: 'shopType',
    },
    {
      name: '门店名称',
      prop: 'shopName',
      width: '300',
    },
    {
      name: '房费收入',
      subTitle: '/万元',
      prop: 'roomAmount',
      width: '180',
    },
    {
      name: '售卡金额',
      subTitle: '/元',
      prop: 'cardAmount',
      width: '180',
    },
    // {
    //   name: '环比提升',
    //   subTitle: '名次',
    //   prop: 'fraudulentCount',
    //   slot: 'fraudulentCount',
    // },
    // {
    //   name: '环比提升',
    //   subTitle: '名次排名',
    //   prop: 'fraudulentCount',
    //   slot: 'fraudulentCount',
    // },
  ],
  listData: []
})

const chooseItemParam = ref<{orgId: number, level: string}>()
// 点击我的排名
const setStoreData = () => {
  tabType.value = '50' // 清空tab选中
  pageParams.value.pageNum = 1
  chooseItemParam.value = {
    orgId: myRankInfo.value.orgId,
    level: myRankInfo.value.level,
  }
  getStoreRank()
}

// 列表中选择下钻
const chooseItem = (data) => {
  if (!data.canClick) return

  tabType.value = '50'
  pageParams.value.pageNum = 1
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
  }
  getStoreRank()
}

const type = ref<string>(''); // 预警类型
const setType = (item: {label: string; value: number}) => {
  type.value = (item.value).toString();
  pageParams.value.pageNum = 1
  getStoreRank()
}

const getStoreRank = () => { 
  if (pageParams.value.pageNum === 1) {
    storeRankData.value.listData = []
  }

  const params = {
    ...pageParams.value,
    ...(chooseItemParam.value || shopAreaData.value), // tab入口使用最高权限
    dangType: type.value
  }

  getZbShopRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    storeRankData.value.listData = storeRankData.value.listData.concat(res.data)
    console.log('获取数据成功', storeRankData.value.listData)
  })
}


// 奖励规则弹窗
const modalShow = ref<boolean>(false);
const modalTitle = ref<string>("");
const modalContent = ref<string>("");
const popupShow = ref<boolean>(false);
const showRuleModal = () => {
if (['40', '50'].includes(tabType.value)){
  popupShow.value = true
  modalShow.value = !popupShow.value;
} else {
   modalShow.value = true;
   popupShow.value = !modalShow.value;
}
  // tabType 地区总部10 大区30 城区40 门店50
  let _content: string = '';
  let _title: string = '';
    switch (tabType.value) {
    case '10':
     _content = getRegionalContent()
     _title = '地区总部激励规则弹窗'
      break;
    case '30':
      _content = getRedBlackContent();
      _title = '大区红黑榜'
      break;
    default:
  }
  modalContent.value = _content;
  modalTitle.value = _title
};

const cityRankData = ref<{ column: Array<Column>, listData: Array<TotalRank> }>({
  column: [
    {
      name: '全国排名',
      prop: 'xdRank',
      width: '120',
      slot: 'xdRank',
    },
    {
      name: '',
      prop: 'level1', // 钻石门店
      width: '120',
      headerSlot: 'level1',
      slot: 'level1',
    },
    {
      name: '',
      prop: 'level2', // 黄金门店
      width: '120',
      headerSlot: 'level2',
      slot: 'level2',
    },
    {
      name: '',
      prop: 'level3', // 白银门店
      width: '120',
      headerSlot: 'level3',
      slot: 'level3',
    },
    {
      name: '',
      prop: 'level4', // 青铜门店
      width: '120',
      headerSlot: 'level4',
      slot: 'level4',
    },
    {
      name: '星点奖励',
      prop: 'xdScore',
      width: '120',
    },
    {
      name: '门店分值',
      prop: 'shopScore',
      width: '120',
      // slot: 'shopScore'
    },
  ],
  listData: []
})

const getCityMore = ()=>{ 
  if (pageParams.value.pageNum * pageParams.value.pageSize > cityRankData.value.listData.length) return
    pageParams.value.pageNum++
    getCityRank()
}

const getCityRank = ()=>{
  if (pageParams.value.pageNum === 1) {
    cityRankData.value.listData = []
  }

  const params = {
    ...pageParams.value,
    ...areaData.value,
  }

  getZbOrgScoreRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    cityRankData.value.listData = cityRankData.value.listData.concat(res.data.totalRank)
    console.log('获取数据成功', cityRankData.value.listData)
  })
}

// 地区总部激励规则弹窗
const getRegionalContent = ()=>{
  return `
  <div style='font-weight:bold'>地区总部激励政策</div>
  <div style='font-weight:bold'>奖励规则：</div>
  <div>地区总部单店贡献<span class='red'>季度预算</span>达成排名<span class='red'>TOP3</span></div>
  <div style='font-weight:bold'>奖励门槛：</div>
  <div>达成单店贡献季度预算</div>
  <div style='font-weight:bold'>奖励标准：</div>
  <div class='red'>6月现金奖励：</div>
  <div style='color:#C35943'>5,000元/10,00元/20,000元</div>
  <div class='red'>Q3：现金奖励：</div>
  <div style='color:#C35943'>10,000元/30,000元/50,000元</div>
  <div class='red'>Q3：星点奖励：</div>
  <div style='color:#C35943'>5,000/10,000/20,000</div>
  `
}
// 大区红黑榜弹窗内容
const getRedBlackContent = ()=>{
  return `
  <div><span style='font-weight:bold'>红榜：</span>大区单店贡献预算达成</div>
  <div><span style='font-weight:bold'>黑榜：</span>单店贡献预算达成率倒数5名</div>
  <div style='font-weight:bold'>奖励标准：</div>
  <div><span class='red'>月度</span>达成<span class='red'>500元/大区</span>和<span class='red'>1000星点</span>值</div>
  <div><span class='red'>季度</span>达成<span class='red'>1000元/大区</span>和<span class='red'>3000星点</span>值</div>
  <div style='font-weight:bold'>惩罚标准：</div>
  <div>达成率<span class='red'>最后两个</span>大区在总部<span class='red'>月度经营提升会</span>上进行汇报</div>
  `
}

const handlePopupClose = () =>{
  popupShow.value = false
}

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('H004', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss" scoped>
.operation {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .operation-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 840rpx;
    padding-bottom: 20rpx;

    z-index: -1;

    &::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 84rpx;
      background: #EFEFEF;
      border-radius: 0 84rpx 0 0;
    }
  }

  .operation-content {
    flex: 1;
    overflow: hidden;
    padding: 420rpx 20rpx 0 20rpx;
    display: flex;
    flex-direction: column;

    .operation-content-wraper {
      flex: 1;
      overflow: auto;
    }

    .operation-content-box {
      padding: 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 20rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          font-size: 22rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .title-btn {
          display: flex;
          align-items: center;

          .title-text {
            padding-right: 6rpx;
            font-size: 24rpx;
            line-height: 24rpx;
            color: #1F2428;							
          }

          .title-info {
            width: 22rpx;
            height: 22rpx;
          }
        }
      }

      .operation-btn {
        position: relative;
        left: 50%;
        transform: translate(-50%, 0);
        display: inline-flex;
        align-items: center;
        height: 48rpx;
        margin: 40rpx auto 0;
        border-radius: 24rpx;
        border: 2rpx solid #f2f2f2;

        .btn-item {
          padding: 0 28rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }

      .content-info {
        position: relative;
        margin-top: 40rpx;
        background: linear-gradient(90deg, rgba(247, 94, 59, 0.1) 0%, rgba(247, 94, 59, 0.05) 100%);
        border-radius: 20rpx;

        .info-title {
          display: inline-block;
          padding: 0 28rpx;
          background: linear-gradient(203.5deg, #FFB09F 0%, #F75E3B 100%);
          border-radius: 20rpx 0 20rpx 0;

          .title-text {
            font-size: 28rpx;
            line-height: 52rpx;
            font-weight: bold;
            color: #fff;
          }
        }

        .info-content {
          padding: 28rpx 40rpx 40rpx 40rpx;
          display: flex;
          align-items: center;

          .info-area {
            padding-right: 8rpx;
            font-size: 32rpx;
            font-weight: bold;
            color: #1F2428;
          }

          .info-item {
            padding-left: 40rpx;
            display: flex;
            align-items: center;
            white-space: nowrap;

            .item-text {
              font-size: 24rpx;
              color: #1F2428;
            }

            .item-num {
              font-size: 40rpx;
              line-height: 40rpx;
              font-family: 'DIN Bold';
							color: #1F2428;
							font-weight: bold;
            }
          }
        }

        .info-content2 {
          display: flex;
          flex-direction: column;
          padding: 28rpx 40rpx 40rpx 40rpx;

          .info-area {
            font-size: 32rpx;
            font-weight: bold;
            color: #1F2428;
            text-align: center;
          }

          .info-item {
            display: flex;
            flex-direction: column;
            margin: 8rpx;
            width: 200rpx;
            padding: 16rpx;

            .item-title {
              font-size: 24rpx;
              line-height: 24rpx;
              font-weight: bold;
              padding-bottom: 28rpx;
              color: #1F2428;
            }

            .item-num {
              font-size: 22rpx;
              line-height: 22rpx;
              color: #1F2428;
            }

            .item-info {
              padding-top: 16rpx;
              height: 24rpx;
              display: flex;
              align-items: center;

              .info-name {
                padding-right: 8rpx;
                font-size: 22rpx;
                color: rgba(31, 36, 40, 0.7);
              }

              .info-icon {
                width: 16rpx;
                height: 20rpx;
              }

              .info-num {
                padding-left: 8rpx;
                font-size: 24rpx;
                font-family: 'DIN Bold';
                color: #1F2428;
                font-weight: bold;
              }

              .num-up {
                color: #FF4D4D;
              }

              .num-down {
                color: #56CC93;
              }
            }

            .my-zuanshi {
              margin-bottom: 25rpx;
              width: 30rpx;
              height: 27rpx;
            }

            .my-img {
              margin:5rpx 0 29rpx 0;
              width: 32rpx;
              height: 18rpx;
            }
          }
        }
      }

      .content-rank {
        display: flex;
        // justify-content: space-between;
        padding-bottom: 20rpx;
        margin-top: 40rpx;

        .rank-item {
          width: 196rpx;
          position: relative;
          margin-top: 28rpx;
          padding-top: 100rpx;
          padding-bottom: 20rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 20rpx 60rpx 20rpx 20rpx;

          .item-medals {
            position: absolute;
            top: -28rpx;
            left: 60rpx;
            width: 94rpx;
            height: 100rpx;
          }

          .item-info {
            display: flex;
            flex-direction: column;
            align-items: center;

            .info-area {
              padding-bottom: 20rpx;
              font-size: 28rpx;
              line-height: 32rpx;
              text-align: center;
              font-weight: bold;
            }

            .info-text {
              padding: 4rpx 30rpx 20rpx 30rpx;
              font-size: 22rpx;
              line-height: 30rpx;
              font-weight: bold;
              text-align: center;
            }

            .info-num {
              font-size: 22rpx;
              line-height: 22rpx;
              font-weight: bold;
              font-family: "DIN Bold";
            }
          }
        }

        .rank-item-1 {
          position: relative;
          top: 20rpx;
          background: linear-gradient(180deg, rgba(145, 186, 255, 0.1) 0%, rgba(20, 116, 250, 0.3) 100%);

          .item-info {
            .info-area, .info-num, .info-text {
              color: #113966;
            }
          }
        }

        .rank-item-2 {
          background: linear-gradient(180deg, rgba(255, 220, 92, 0.1) 0%, rgba(252, 173, 13, 0.3) 100%);

          .item-info {
            .info-area, .info-num, .info-text {
              color: #7D3023;
            }
          }
        }

        .rank-item-3 {
          position: relative;
          top: 20rpx;
          background: linear-gradient(180deg, rgba(255, 178, 161, 0.1) 0%, rgba(247, 94, 59, 0.3) 100%);

          .item-info {
            .info-area, .info-num, .info-text {
              color: #661611;
            }
          }
        }
      }

      .ranking-table {
        margin-top: 40rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .table-num {
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          
          .item-medals {
            position: relative;
            left: 8rpx;
            width: 47rpx;
            height: 50rpx;
          }

          .num-text {
            font-size: 22rpx;
            font-weight: bold;
            font-family: "DIN Bold";
            color: #1F2428;
          }
        }

        .table-img {
          display: flex;
          flex-direction: column;
          align-items: center;

          .img-top {
            width: 32rpx;
            height: 18rpx;
            margin-bottom: 11rpx;
          }

          .zuanshi-img {
            width: 30rpx;
            height: 27rpx;
            margin-bottom: 6rpx;
          }

          .img-bottom { 
            height: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .img-text {
              font-size: 18rpx;
              color: rgba(31, 36, 40, 0.7);
              padding-right: 5rpx;
            }

            .img-icon {
              width: 20rpx;
              height: 12rpx;
            }

            .zuishi-icon {
              width: 19rpx;
              height: 17rpx;
            }
          }
        }

        .rate-item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;

          .item-top {
            font-size: 24rpx;
            line-height: 24rpx;
            font-weight: bold;
          }

          .item-bottom {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .item-icon {
            width: 16rpx;
            height: 20rpx;
            margin-right: 8rpx;
          }

          .item-text {
            font-size: 24rpx;
            line-height: 24rpx;
            font-weight: bold;
            font-family: "DIN Bold";
            color: #1F2428;
          }

          .up-text {
            color: #FF4D4D;
          }

          .down-text {
            color: #56CC93;
          }
        }

        .table-img {
          display: flex;
          align-items: center;
          justify-content: center;

          .header-img {
            width: 32rpx;
            height: 18rpx;
          }

          .header-zuanshi {
            width: 30rpx;
            height: 27rpx;
          }
        }
      }

      .content-desc {
        display: flex;
        flex-direction: column;
        margin-top: 20rpx;
				padding: 20rpx;
				border-radius: 13rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

        .info-title {
          display: flex;
          align-items: center;
          padding-bottom: 20rpx;

          .title-icon {
            width: 22rpx;
            height: 22rpx;
          }

          .title-text {
            padding-left: 8rpx;
            font-size: 26rpx;
            line-height: 36rpx;
            font-weight: bold;
            color: #C35943;
          }
        }

        .info-text {
          padding-left: 8rpx;
          font-size: 22rpx;
					line-height: 36rpx;
					color: rgba(31, 36, 40, 0.7);
        }
      }
    }
  }
}
.modal-info{
  display: flex;
  align-items: flex-start; 
  width: 100%;
  background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
  border-radius: 20rpx;
  color: #1f2428b3;
  font-size: 22rpx;
  font-family: "苹方-繁";
  text-align: justify;
  line-height: 34rpx;
  padding: 20rpx;
  margin: 40rpx 0;
  .item-info {
    width: 22rpx;
    height: 22rpx;
    flex-shrink: 0;
    margin: 6rpx 8rpx 0 0;
  }
}
.full-screen {
  position: relative;
  .c-popup-content{
    width:100%;
    height:100%;
    overflow: auto;
    border-radius: 20rpx;
    .rotate-wrapper {
      overflow: visible;
      width: 100%;
      height: 100%; 
      width: 1430rpx;
      height: 618rpx;
      // width: 80vh;
      // height: 90vw;
      padding: 40rpx 50rpx;
      .modal-title{
        height: 36rpx;
        color: #1f2428;
        font-size: 36rpx;
        font-weight: bold;
        font-family: "苹方-繁";
        margin-bottom: 40rpx;
      }
      .image-content{
        // width: calc(100% - 80rpx);
        width: 1350rpx;
        height: 551rpx;
      }
      .city-modal-info{
        margin-top: 40rpx;
        padding:20rpx;
        width: 630rpx;
        background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
        font-size: 24rpx;
        border-radius: 20rpx;
        line-height: 36rpx;
        .red{
          color:#CA6D5A;
        }
      }
      .confirm-btn-wrapper{
        width: 100%;
        display: flex;
        justify-content: center;
        .confirm-btn{
          width: 670rpx;
          font-size: 28rpx;
          color:#fff;
          border-radius: 40rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, hsl(11, 92%, 60%) 100%);
          line-height: 80rpx;
          height: 80rpx;
          font-weight: bold;
          text-align: center;
          margin-top: 40rpx;
        }
      }
    }
  }
}  
</style>

<style lang="scss"> 
.operation {
  c-popup {
    .u-popup__content {
      border-radius: 20rpx;
      transform: rotate(90deg);

      .view-scoller {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
