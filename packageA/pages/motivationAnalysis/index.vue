<template>
  <view class="motivation-analysis">
    <view class="detail-bg" :class="classObject"></view>
    <u-navbar placeholder title="动因分析" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;" leftIconColor="#FFF" autoBack></u-navbar>
    
    <cTabs :list="tabList" v-model:modelValue="tabIndex" @click="handleTabClick"></cTabs>
    
    <assetsDetail v-if="tabIndex === 0" :params="pageParams"></assetsDetail>
    <financeDetail v-if="tabIndex === 1" :params="pageParams"></financeDetail>
    <operationsDetail v-if="tabIndex === 2" :params="pageParams"></operationsDetail>
    <memberDetail v-if="tabIndex === 3" :params="pageParams"></memberDetail>
		<monitor v-if="tabIndex === 4" :params="pageParams"/>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, ComponentInternalInstance, computed } from 'vue';
import cTabs from '@/components/c-tabs2/index.vue'
import operationsDetail from '../operations/detail.vue'
import financeDetail from '../finance/detail.vue'
import memberDetail from '../member/detail.vue'
import assetsDetail from '../assets/detail.vue'
import monitor from '../monitor/monitor.vue'
import { useCommon } from '@/hooks/useGlobalData';
import { onLoad } from '@dcloudio/uni-app'
import { showToast } from '@/utils/util';

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const pages = ref(globalData.value.pages)
  
// 响应式数据  
const tabList = reactive([  
  { name: '资产', value: 'assets' },  
  { name: '财务', value: 'finance', disabled: !pages.value.includes(2) },  
  { name: '运营', value: 'operations' },  
  { name: '会员', value: 'member' },  
  { name: '舆情', value: 'monitor' }
]);
const tabIndex = ref<number>(0)
const setTab = (name: string) => {
  tabIndex.value = tabList.findIndex((item) => item.value === name)
}

const handleTabClick = (item: any) => {
	if (item.disabled) showToast('暂无查看权限，请走申请流程')
}

const classObject = computed(() => {
  if (tabIndex.value === 4) {
    return 'monitor-bg'
  }
})

const pageParams = ref({})
onLoad((option: any) => {
  pageParams.value = {...option}
  setTab(option.tabName)
});
</script>

<style lang="scss">
.motivation-analysis {
  position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;

  .detail-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 442rpx;
		background: linear-gradient(52.4deg, #ff5733 0%, #c35943 31%, #c35943 79%, #f75735 100%);

    &::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: -118rpx;
      width: 118rpx;
      height: 118rpx;
      background: radial-gradient(circle at 0% 100%,transparent 118rpx,#C35943 0)
    }
	}

  .monitor-bg {
    height: 642rpx;
  }

  operations-detail, 
  finance-detail,
  member-detail, 
  assets-detail,
  monitor {
    flex: 1;
    overflow: hidden;
  }
}
</style>
