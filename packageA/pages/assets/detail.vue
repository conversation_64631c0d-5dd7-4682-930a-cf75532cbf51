<template>
  <view class="assets-detail">
    <view class="detail-area">
      <cAreaPicker @setArea="setArea" :defaultName="areaData.orgName" :showShop="false"></cAreaPicker>
    </view>

    <view class="detail-content">
      <!-- table ---start -->
      <view class="table-view">
        <cTabs :list="classTypeList" v-model:modelValue="classTypeIndex" :isBig="false" :itemWidth="150" textColor="#1F2428" @change="changeClassType"></cTabs>
        <cDate dateType="month" :minData="Date.parse(`2024/11/1`)" @setDate="setDate"></cDate>

        <view class="content-title">
          <view class="title-btn">
            <!-- <view class="btn-item" :class="{ active: tabType === 1 }" @click="setTabType(1)">
              财务
            </view> -->
            <view class="btn-item" :class="{ active: tabType === 2 }" @click="setTabType(2)">
              运营
            </view>
            <view class="btn-item" :class="{ active: tabType === 3 }" @click="setTabType(3)">
              会员
            </view>
          </view>
        </view>

        <view class="target-table">
          <cTable ref="targetTable" :header="operationsData.column" :isPaginate="false" :isZPaging="true">
            <template #default="scope">
              <template v-if="['trueValue', 'falseValue'].includes(scope.prop)">
                {{ scope.data.dataName === '净利润率' ? scope.data[scope.prop] || '-' + '%' : scope.data[scope.prop] || '-' }}
              </template>
              <view class="target-item" v-else>
                <text class="item-text">{{ scope.data.dataName }}</text>
                <text class="item-unit" v-if="scope.data.unit">/{{ scope.data.unit }}</text>
              </view>
            </template>
          </cTable>
        </view>
      </view>
      <!-- table ---end -->

      <!-- chart ---start -->
      <view class="chart-view">
        <cTabs :list="dataTypeList" v-model:modelValue="dataType" :isBig="false" :itemWidth="tabType === 2 ? 130 : 170" textColor="#1F2428" @change="changeDataType"></cTabs>
        <areaCharts v-if="driveDetail.data.length" :lineNum="2" :detail="driveDetail" :leftUnit="chartUnit"></areaCharts>
        <view class="empty-wraper" v-else>
          <emptyVue />
        </view>
      </view>
      <!-- chart ---end -->
    </view>
  </view>
</template>

<script lang="ts" setup name="assetsDetail">
import { ref, reactive, watch, nextTick, onMounted, onUnmounted, getCurrentInstance, computed } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import cDate from '@/components/c-date/index.vue'
import cTable from '@/components/c-table/c-table.vue'
import areaCharts from '@/components/c-chart/areaCharts/index.vue'
import { queryClassContrast, queryClassMonth } from '@/packageA/api/assets'
import { successCallbackResult } from '@/types'
import type { QueryClassContrastParams, QueryClassMonthParams, QueryClassMonthRes } from '@/packageA/api/assets'
import { showToast, formatDate, formatMonth } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import emptyVue from '@/components/empty/empty.vue';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: number,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
})

const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: null,
  level: '',
  orgName: ''
})
// 切换区域 1-更新表格 2-更新图表
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name

  setTabType()
}

const classTypeList = reactive([  
  { name: '有无店长' },  
  // { name: '是否联营' },
  // { name: '是否对赌' },
  // { name: '是否舞弊' },
  // { name: '是否售卡' }
]);
const classTypeIndex = ref<number>(0)
// 切换tab 
const changeClassType = () => {
  
}

// 切换财务、运营、会员 1-更新表格 2-更新图表
const tabType = ref<number>(2)
const setTabType = (type?: number) => {
  if (type === tabType.value) return
  if (type) tabType.value = type

  if (tabType.value === 1) {
    dataTypeList.value = [  
      { name: '经营收入', value: 1 },  
      { name: '运营收入', value: 2 },
      { name: '会员与渠道收入', value: 3 },
      { name: '销售费用', value: 4 },
      { name: '管理费用', value: 5 },
      { name: '人工成本', value: 6 },
      { name: '净利润率', value: 7 }
    ]
  } else if (tabType.value === 2) {
    dataTypeList.value = [  
      { name: '房费收入', value: 8 },  
      { name: 'OCC', value: 9 },
      { name: 'ADR', value: 10 },
      { name: 'RevPar', value: 11 },
      { name: 'CRS占比', value: 12 }
    ]
  } else {
    dataTypeList.value = [  
      { name: '直销占比', value: 13 },  
      { name: '会员占比', value: 14 },
      { name: '会员复购率', value: 15 },
      { name: '售卡金额', value: 16 },
      { name: '售卡动销率', value: 17 },
      { name: '零售卡门店', value: 18 }
    ]
  }
  dataType.value = 0

  getClassContrast()
  changeDataType()
}

// 更换月份 1-更新表格
const searchDate = ref<string>(formatMonth(Date.parse(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`)))
const setDate = (date: string) => {
  searchDate.value = date
  getClassContrast()
  changeDataType()
}

const targetTable = ref<InstanceType<typeof cTable> | null>(null);
const operationsData = ref<{ column: Array<Column> }>({
  column: [
    {
      name: '指标',
      prop: 'dataName',
      width: 220,
      slot: 'dataName'
    },
    {
      name: '有店长',
      prop: 'trueValue',
      slot: 'trueValue',
    },
    {
      name: '无店长',
      prop: 'falseValue',
      slot: 'falseValue',
    }
  ]
});

// 获取图表数据
const getClassContrast = () => {
  const params: QueryClassContrastParams = {
    ...areaData.value,
    classType: classTypeIndex.value + 1,
    tabType: tabType.value,
    searchDate: searchDate.value
  }

  queryClassContrast(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    targetTable.value.setLocalPaging(res.data)
  })
}

const chartUnit = computed(() => {
  const unit = dataTypeList.value[dataType.value]?.value || 0
  if (!unit) return ''
  if ([8].includes(unit)) return '万元'
  if ([9, 12, 13, 14, 15, 17].includes(unit)) return '%'
  if ([10, 11, 16].includes(unit)) return '元'
  if ([18].includes(unit)) return '门店数'
  return ''
})

// 切换下面图表tab 1-更新图表
let dataTypeList = ref<Array<{name: string, value: number}>>([]);
const dataType = ref<number>(0)
const driveDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})

const changeDataType = () => {
  const params: QueryClassMonthParams = {
    ...areaData.value,
    dataType: dataTypeList.value[dataType.value].value,
    searchDate: searchDate.value
  }

  driveDetail.value = {
    categories: [],
    data: []
  }
  return queryClassMonth(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    driveDetail.value.data = []
    driveDetail.value.categories = res.data.map((item: QueryClassMonthRes) => item.slotDate.slice(2))
    driveDetail.value.data.push({
      name: '有店长',
      data: res.data.map((item: QueryClassMonthRes) => item.existValue || 0)
    })
    driveDetail.value.data.push({
      name: '无店长',
      data: res.data.map((item: QueryClassMonthRes) => item.noneValue || 0)
    })
  })
}

watch(() => props.params, (val) => {
  nextTick(() => {
    if (val.id) setArea(props.params.id, props.params.level, props.params.orgName)
  })
}, {deep: true, immediate: true})

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
  setTabType()
})

onUnmounted(() => {
  trackEvent('D004', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.assets-detail {
  position: relative;
	width: 100%;
	height: 100%;
  display: flex;
  flex-direction: column;

  .detail-area {
    padding: 0 20rpx;
  }

  .detail-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx;
    margin-top: 20rpx;

    .table-view {
      padding: 40rpx;
      margin-bottom: 20rpx;
      background: #fff;
      border-radius: 22rpx;

      .date-wraper {
        width: 100%;
        justify-content: space-between;
        border-radius: 27rpx;
      }

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 0;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .title-btn {
        display: flex;
        align-items: center;
        height: 48rpx;
        border: 2rpx solid #f2f2f2;
        border-radius: 24rpx;

        .btn-item {
          padding: 0 24rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }

      .target-table {
        height: 588rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .target-item {
          display: flex;
          align-items: center;
          justify-content: center;

          .item-unit {
            padding-top: 4rpx;
            font-size: 18rpx;
            line-height: 18rpx;
          }

          .item-info {
            margin-left: 8rpx;
            width: 22rpx;
            height: 22rpx;
          }
        }
      }
    }

    .chart-view {
      padding: 40rpx 40rpx 0 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 22rpx;
      height: 784rpx;

      .empty-wraper {
        height: 600rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>