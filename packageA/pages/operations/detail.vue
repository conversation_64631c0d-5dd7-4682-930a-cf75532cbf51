<template>
  <view class="operations-detail">
    <view class="detail-area">
      <cAreaPicker @setArea="setArea" :defaultName="areaData.orgName" :showShop="false"></cAreaPicker>
    </view>

    <view class="detail-content">
      <view class="content-view">
        <!-- 关键指标 ---start -->
        <view class="content-title">
          <text class="title-text">关键指标</text>
          <view class="title-btn">
            <view class="btn-item" :class="{ active: dateType === 'day' }" @click="setDateType('day')">
              日
            </view>
            <view class="btn-item" :class="{ active: dateType === 'month' }" @click="setDateType('month')">
              月
            </view>
          </view>
        </view>

        <cTabs :list="targetList" v-model:modelValue="targetIndex" :isBig="false" :itemWidth="120" textColor="#1F2428" @change="changeTarget"></cTabs>

        <view class="chart-box">
          <barCharts v-if="keyDetail.data.length" :barNum="2" :lineNum="dateType === 'day' ? 0 : 1" :detail="keyDetail" :leftUnit="chartUnit" rightUnit="%"></barCharts>
        </view>
        <!-- 关键指标 ---end -->

        <!-- 关键驱动因素 ---start -->
        <view class="content-title">
          <text class="title-text">关键驱动因素</text>
        </view>
        
        <view class="content-chart">
          <view class="content-btn-left">
            <view v-if="targetIndex === 0" class="btn-item" :class="{ active: driveType === 1 }" @click="setDriveType(1)">
              夜审
            </view>
            <view v-if="targetIndex === 0" class="btn-item" :class="{ active: driveType === 2 }" @click="setDriveType(2)">
              渠道
            </view>
            <view v-if="[1, 2].includes(targetIndex)" class="btn-item" :class="{ active: driveType === 3 }" @click="setDriveType(3)">
              新增网评分
            </view>
            <view v-if="targetIndex === 3" class="btn-item" :class="{ active: driveType === 4 }" @click="setDriveType(4)">
              OCC
            </view>
            <view v-if="targetIndex === 3" class="btn-item" :class="{ active: driveType === 5 }" @click="setDriveType(5)">
              ADR
            </view>
          </view>
        </view>

        <template v-if="driveType === 2">
          <accumulationCharts :detail="channelDetail" leftUnit="%"></accumulationCharts>
          <cDate :dateType="dateType" :minData="dateType === 'day' ? Date.parse(`${new Date().getFullYear()}/${new Date().getMonth() + 1}/1`) : Date.parse(`${new Date().getFullYear()}/1/1`)" @setDate="setDate"></cDate>
          <ringCharts :detail="ringDetail" legendPositon="right" @updateTooltip="updateTooltip">
            <view class="tooltip-info">
              <view class="tooltip-title">
                <text class="text">{{ tooltipInfo?.name }}</text>
                <text class="num">{{ tooltipInfo?.value }}%</text>
              </view>

              <template v-if="tooltipInfo?.constitute">
                <view class="sub-title">
                  <text class="text">构成：</text>
                </view>
                <view class="tooltip-info" v-for="item in tooltipInfo.constitute" :key="item.name">
                  <text class="text">{{ item.name }}：</text>
                  <text class="num">{{ item.value }}%</text>
                </view>
              </template>
              
              <view class="tooltip-rate" v-if="tooltipInfo?.rateTb !== undefined">
                <text class="text">同比</text>
                <view class="content">
                  <image class="icon" v-if="tooltipInfo?.rateTb" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${tooltipInfo?.rateTb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="num" :class="['up-num']">{{ Math.abs(tooltipInfo?.rateTb) }}%</text>
                </view>
              </view> 
              <view class="tooltip-rate" v-if="tooltipInfo?.rateHb !== undefined">
                <text class="text">环比</text>
                <view class="content">
                  <image class="icon" v-if="tooltipInfo?.rateHb" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${tooltipInfo?.rateHb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="num" :class="['down-num']">{{ Math.abs(tooltipInfo?.rateHb) }}%</text>
                </view>
              </view> 
              <view class="sub-title bottom-title" v-if="tooltipInfo?.nightSaleCount !== undefined">
                <text class="text">出租间夜：</text>
                <text class="num">{{ tooltipInfo?.nightSaleCount }}</text>
              </view>
              <view class="tooltip-rate" v-if="tooltipInfo?.nightSaleTb !== undefined">
                <text class="text">同比</text>
                <view class="content">
                  <image class="icon" v-if="tooltipInfo?.nightSaleTb" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${tooltipInfo?.nightSaleTb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="num" :class="['up-num']">{{ Math.abs(tooltipInfo?.nightSaleTb) }}%</text>
                </view>
              </view> 
              <view class="tooltip-rate" v-if="tooltipInfo?.nightSaleHb !== undefined">
                <text class="text">环比</text>
                <view class="content">
                  <image class="icon" v-if="tooltipInfo?.nightSaleHb" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${tooltipInfo?.nightSaleHb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="num" :class="['down-num']">{{ Math.abs(tooltipInfo?.nightSaleHb) }}%</text>
                </view>
              </view> 
            </view>
          </ringCharts>
        </template>
        <view class="chart-box" v-else>
          <barCharts v-if="driveDetail.data.length && driveType !== 3" :barNum="driveType === 1 ? 1 : 2" :lineNum="(driveType === 1 || dateType === 'month') ? 1 : 0" :detail="driveDetail" :leftUnit="leftUnit" :rightUnit="rightUnit"></barCharts>
          <areaCharts v-if="driveDetail.data.length && driveType === 3" :lineNum="1" :detail="driveDetail" :leftUnit="leftUnit"></areaCharts>
          <view class="empty-wraper" v-if="!driveDetail.data.length">
            <emptyVue />
          </view>
        </view>
        <!-- 关键驱动因素 ---end -->
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup name="operationsDetail">
import { ref, reactive, watch, nextTick, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted, computed } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import cDate from '@/components/c-date/index.vue'
import barCharts from '@/components/c-chart/barCharts/index.vue'
import areaCharts from '@/components/c-chart/areaCharts/index.vue'
import accumulationCharts from '@/components/c-chart/accumulationCharts/index.vue'
import ringCharts from '@/components/c-chart/ringCharts/index.vue'
import { queryKeyIndex, queryKeyDrive } from '@/api/operations'
import { successCallbackResult } from '@/types'
import type { QueryKeyIndexParams, QueryKeyIndexRes, QueryKeyDriveParams, QueryKeyDriveRes } from '@/api/operations'
import { showToast, formatDate } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import emptyVue from '@/components/empty/empty.vue';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
})

const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: null,
  level: '',
  orgName: ''
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name

  changeTarget()
}

const targetList = reactive([  
  { name: '房费收入' },  
  { name: 'OCC' },
  { name: 'ADR' },
  { name: 'RevPar' }
]);
const targetIndex = ref<number>(0)
const chartUnit = computed(() => {
  if ([0].includes(targetIndex.value)) return '万元'
  if ([1].includes(targetIndex.value)) return '%'
  if ([2, 3].includes(targetIndex.value)) return '元'
  return ''
})
// 切换tab，也是初始化入口
const changeTarget = () => {
  if (targetIndex.value === 0) {
    driveType.value = 1
  } else if (targetIndex.value >= 1 && targetIndex.value <= 2) {
    driveType.value = 3
  } else if (targetIndex.value === 3) {
    driveType.value = 4
  }
  keyDetail.value = {
    categories: [],
    data: []
  }
  getKeyIndexRes().then((res) => {
    keyDetail.value = res
  })
  setDriveType()
}
const leftUnit = computed(() => {
  if ([1].includes(driveType.value)) return '门店数'
  if ([2, 4].includes(driveType.value)) return '%'
  if ([5].includes(driveType.value)) return '元'
  return ''
})

const rightUnit = computed(() => {
  if ([1].includes(driveType.value)) return '夜审率%'
  if ([4, 5].includes(driveType.value)) return '%'
  return ''
})
const dateType = ref<string>('day')
const setDateType = (type: string) => {
  if (type === dateType.value) return
  dateType.value = type
  if (type === 'day') {
    ringDate.value = formatDate(Date.now() - 24 * 60 * 60 * 1000)
  } else {
    ringDate.value = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-01`
  }
  keyDetail.value = {
    categories: [],
    data: []
  }
  getKeyIndexRes().then((res) => {
    keyDetail.value = res
  })
  setDriveType()
}

const keyDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 动因分析关键指标查询
const getKeyIndexRes = (dataType?: number) => {
  const params: QueryKeyIndexParams = {
    ...areaData.value,
    dataType: dataType || targetIndex.value + 1,
    dateType: dateType.value
  }

  return queryKeyIndex(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: dateType.value === 'day' ? '上周同期' : '去年同期',
      data: res.data.map((item: QueryKeyIndexRes) => item.termValue)
    })
    data.push({
      name: dateType.value === 'day' ? '当日' : '当年',
      data: res.data.map((item: QueryKeyIndexRes) => item.actualValue)
    })
    if (dateType.value === 'month') {
      data.push({
        name: '完成率',
        data: res.data.map((item: QueryKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    return {
      categories: res.data.map((item: QueryKeyIndexRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }
  })
}
// 关键因素
const driveType = ref<number>(1)
// 切换关键驱动因素tab，也是初始化入口
const setDriveType = (type?: number) => {
  if (type === driveType.value) return
  if (type) driveType.value = type

  driveDetail.value = {
    categories: [],
    data: []
  }

  if (driveType.value < 4) {
    getKeyDrive()
  } else {
    getKeyIndexRes(driveType.value - 2).then((res) => {
      driveDetail.value = res
    })
  }
}

const ringDate = ref<string>(formatDate(Date.now() - 24 * 60 * 60 * 1000))
const setDate = (date: string) => {
  ringDate.value = dateType.value === 'day' ? date : date + '-01'
  const item: QueryKeyDriveRes = keyDriveRes.value?.filter((item: QueryKeyDriveRes) => item.slotDate === ringDate.value)?.[0]
  const ringList = [{ name: '散客', 
                      value: item.fitOrderRate,
                      rateTb: item.fitOrderRateTb,
                      rateHb: item.fitOrderRateHb,
                      nightSaleCount: item.fitNightSaleCount,
                      nightSaleTb: item.fitNightSaleTb,
                      nightSaleHb: item.fitNightSaleTb
                    }, 
                    { name: '协议', 
                      value: item.agreementOrderRate,
                      rateTb: item.agreementOrderRateTb,
                      rateHb: item.agreementOrderRateHb,
                      nightSaleCount: item.agreementNightSaleCount,
                      nightSaleTb: item.agreementNightSaleTb,
                      nightSaleHb: item.agreementNightSaleHb
                    }, 
                    { name: '集团贡献率', 
                      value: item.groupOrderRate, 
                      constitute: [{ name: '直销', value: item.directOrderRate }, { name: '线下会员', value: item.memberOrderRate }, { name: '分销', value: item.distributionOrderRate }],
                      rateTb: item.groupOrderRateTb,
                      rateHb: item.groupOrderRateHb,
                      nightSaleCount: item.groupNightSaleCount,
                      nightSaleTb: item.groupNightSaleTb,
                      nightSaleHb: item.groupNightSaleHb
                    }, 
                    { name: '其他', 
                      value: item.otherOrderRate,
                    }]
  ringDetail.value = ringList
  // .sort(function(a, b){return a.value - b.value})
}

const keyDriveRes = ref<Array<QueryKeyDriveRes>>()
const driveDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
const ringDetail = ref<Array<{name: string, value: number | string}>>([])
const channelDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
const getKeyDrive = () => {
  const params: QueryKeyDriveParams = {
    ...areaData.value,
    dataType: driveType.value,
    dateType: dateType.value
  }
  queryKeyDrive(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    keyDriveRes.value = res.data
    if (driveType.value === 2) {
      channelDetail.value.data = []
      channelDetail.value.categories = res.data.map((item: QueryKeyDriveRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7))
      channelDetail.value.data.push({
        name: '散客',
        data: res.data.map((item: QueryKeyDriveRes) => item.fitOrderRate)
      })
      channelDetail.value.data.push({
        name: '协议',
        data: res.data.map((item: QueryKeyDriveRes) => item.agreementOrderRate)
      })
      channelDetail.value.data.push({
        name: '集团贡献率',
        data: res.data.map((item: QueryKeyDriveRes) => item.groupOrderRate)
      })
      channelDetail.value.data.push({
        name: '其他',
        data: res.data.map((item: QueryKeyDriveRes) => item.otherOrderRate)
      })
      const item: QueryKeyDriveRes = res.data.filter((item: QueryKeyDriveRes) => item.slotDate === ringDate.value)?.[0]
      if (!item) return
      const ringList = [{ name: '散客', 
                          value: item.fitOrderRate,
                          rateTb: item.fitOrderRateTb,
                          rateHb: item.fitOrderRateHb,
                          nightSaleCount: item.fitNightSaleCount,
                          nightSaleTb: item.fitNightSaleTb,
                          nightSaleHb: item.fitNightSaleTb
                        }, 
                        { name: '协议', 
                          value: item.agreementOrderRate,
                          rateTb: item.agreementOrderRateTb,
                          rateHb: item.agreementOrderRateHb,
                          nightSaleCount: item.agreementNightSaleCount,
                          nightSaleTb: item.agreementNightSaleTb,
                          nightSaleHb: item.agreementNightSaleHb
                        }, 
                        { name: '集团贡献率', 
                          value: item.groupOrderRate, 
                          constitute: [{ name: '直销', value: item.directOrderRate }, { name: '线下会员', value: item.memberOrderRate }, { name: '分销', value: item.distributionOrderRate }],
                          rateTb: item.groupOrderRateTb,
                          rateHb: item.groupOrderRateHb,
                          nightSaleCount: item.groupNightSaleCount,
                          nightSaleTb: item.groupNightSaleTb,
                          nightSaleHb: item.groupNightSaleHb
                        }, 
                        { name: '其他', 
                          value: item.otherOrderRate,
                        }]
      ringDetail.value = ringList
    } else if (driveType.value === 1) {
      driveDetail.value.data = []
      driveDetail.value.categories = res.data.map((item: QueryKeyDriveRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7))
      driveDetail.value.data.push({
        name: '运营门店数',
        data: res.data.map((item: QueryKeyDriveRes) => item.operationShopCount || 0)
      })
      driveDetail.value.data.push({
        name: '夜审率',
        data: res.data.map((item: QueryKeyDriveRes) => item.nightAuditRate || 0)
      })
    } else if (driveType.value === 3) {
      driveDetail.value.data = []
      driveDetail.value.categories = res.data.map((item: QueryKeyDriveRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7))
      driveDetail.value.data.push({
        name: '新增网评分',
        data: res.data.map((item: QueryKeyDriveRes) => item.addPoint || 0)
      })
    }
  })
}

// tooltip更新
const tooltipInfo = ref()
const updateTooltip = (info) => {
  tooltipInfo.value = info
}

watch(() => props.params, (val) => {
  nextTick(() => {
    if (val.id) setArea(props.params.id, props.params.level, props.params.orgName)
  })
}, {deep: true, immediate: true})

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('D022', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.operations-detail {
  position: relative;
	width: 100%;
	height: 100%;
  display: flex;
  flex-direction: column;

  .detail-area {
    padding: 0 20rpx;
  }

  .detail-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx;
    margin-top: 20rpx;

    .content-view {
      padding: 0 40rpx 40rpx 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 22rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 0;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .content-chart {
        position: relative;

        .content-btn-left {
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      .title-btn, .content-btn-left {
        display: flex;
        align-items: center;
        height: 48rpx;
        border: 2rpx solid #f2f2f2;
        border-radius: 24rpx;

        .btn-item {
          padding: 0 24rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }
    }
  }

  .chart-box {
    min-height: 680rpx;

    .empty-wraper {
      height: 680rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tooltip-info {
    .tooltip-title {
      display: flex;
      align-items: center;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: #1F2428;
        font-weight: bold;
      }

      .num {
        font-size: 24rpx;
        padding-left: 20rpx;
        font-weight: bold;
      }
    }

    .sub-title {
      display: flex;
      align-items: center;
      padding: 20rpx 0;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: #1F2428;
      }

      .num {
        font-size: 24rpx;
        font-weight: bold;
      }
    }

    .bottom-title {
      padding: 42rpx 0 8rpx 0;
    }

    .tooltip-info {
      display: flex;
      align-items: center;
      padding-bottom: 12rpx;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: rgba(31, 36, 40, 0.7);
      }

      .num {
        font-size: 24rpx;
        font-weight: bold;
      }
    }

    .tooltip-rate {
      display: flex;
      align-items: center;
      padding-top: 12rpx;

      .text {
        padding-right: 8rpx;
        font-size: 22rpx;
        line-height: 24rpx;
        color: rgba(31, 36, 40, 0.7);
      }

      .content {
        display: inline-flex;
        align-items: center;

        .icon {
          width: 16rpx;
          height: 20rpx;
        }

        .num {
          padding-left: 8rpx;
        }

        .text {
          padding-left: 8rpx;
          font-size: 24rpx;
          font-weight: bold;
        }

        .up-text {
          color: #FF4D4D;
        }

        .down-text {
          color: #56CC93;
        }
      }
    }
  }
}
</style>