<template>
  <view class="finance-shop">
    <view class="shop-top">
      <view class="shop-date">
        <cDatePicker :showRange="false" :maxDate="maxDate" @setDate="setDate"></cDatePicker>
      </view>

      <view class="shop-info">
        <view class="info-top">
          <view class="info-label">{{ labelText }}</view>
          <view class="top-title">
            <image class="title-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-30/1735522312841.png" mode="aspectFill"></image>
            <text class="title-text">经营收入</text>
          </view>

          <view class="top-num-wraper">
            <text class="top-num">{{ gmvTotal.num || 0 }}</text>
            <text class="top-text">元</text>
          </view>
          <!-- <view class="top-diff">
            <text class="info-name">环比</text>
            <image v-if="gmvTotal.valueHb" class="info-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${gmvTotal.valueHb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
            <text class="info-num" :class="[ gmvTotal.valueHb > 0 ? 'info-num-up' : gmvTotal.valueHb < 0 ? 'info-num-down' : '']">{{ gmvTotal.valueHb !== null ? Math.abs(gmvTotal.valueHb) : '-' }}%</text>
          </view> -->
        </view>

        <view class="info-bottom">
          <view class="info-item" v-for="(item, index) in gmvList" :key="item.id">
            <text class="item-title">{{ item.title }}</text>
            <text class="item-num" v-if="item.num !== undefined">{{ item.num || 0 }}{{ item.unit }}</text>
            <!-- <view class="item-info">
              <text class="info-name">环比</text>
              <image v-if="item.valueHb" class="info-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${item.valueHb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
              <text class="info-num" :class="[ item.valueHb > 0 ? 'info-num-up' : item.valueHb < 0 ? 'info-num-down' : '']">{{ item.valueHb !== null ? Math.abs(item.valueHb) : '-' }}{{ index < 3 ? '%' : '元' }}</text>
            </view> -->
          </view>
        </view>
      </view>
    </view>

    <view class="shop-bottom">
      <cTabs :list="targetList" v-model:modelValue="targetIndex" :isBig="false" :itemWidth="180" textColor="#1F2428" @change="changeTarget"></cTabs>

      <view class="chart-box">
        <barCharts v-if="keyDetail.data.length" :barNum="2" :lineNum="['day', 'month'].includes(dateData.dateType) ? 0 : 1" :detail="keyDetail" :minNum="-1" :leftUnit="targetIndex < 5 ? '元' : '%'"></barCharts>
      </view>
    </view>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, getCurrentInstance, ComponentInternalInstance } from 'vue';
import cDatePicker from '@/components/c-date-picker/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import barCharts from '@/components/c-chart/barCharts/index.vue'
import { onLoad } from '@dcloudio/uni-app'
import { singleStoreOperatingIncome, shopKeyIndex } from '@/packageA/api/finance'
import { successCallbackResult } from '@/types'
import type { DateData, shopKeyIndexRes, QuerySingleStoreOperatingIncome } from '@/packageA/api/finance'
import { showToast, formatDate } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

const shopId = ref('')
const maxDate = ref('')
let startTime: number = 0
onLoad((option: any) => {
  shopId.value = option.shopId
  maxDate.value = option.maxDate
  if (dateData.value.atime > maxDate.value) dateData.value.atime = maxDate.value
  uni.setNavigationBarTitle({
    title: option.shopName
  });
  getData()
});

onMounted(() => {
  trackEvent('D013', {time: (Date.now() - startTime) / 1000})
})

const labelText = ref<string>('预测值')

const dateData = ref<DateData>({
  dateType: 'day',
  atime: formatDate(Date.now() - 24 * 60 * 60 * 1000)
})
// 切换日期选择、初始化入口
const setDate = (type: string, value1: string | number, value2?: string | number) => {
  if (type === 'day') {
    dateData.value = {
      dateType: 'day',
      atime: value2
    }
    if (value2!.toString().slice(0, 7) < maxDate.value.slice(0, 7)) {
      labelText.value = '实际值'
    } else {
      labelText.value = '预测值'
    }
  } else if (type === 'month') {
    dateData.value = {
      dateType: 'month',
      year: value1,
      month: value2
    }
    const dateStr = value1!.toString() + '-' + (Number(value2) > 9 ? value2 : '0' + value2)?.toString()
    if (dateStr.slice(0, 7) < maxDate.value.slice(0, 7)) {
      labelText.value = '实际值'
    } else {
      labelText.value = '预测值'
    }
  } else if (type === 'quarter') {
    dateData.value = {
      dateType: 'quarter',
      year: value1,
      quarter: value2
    }
    labelText.value = '实际值'
  } else if (type === 'year') {
    dateData.value = {
      dateType: 'year',
      year: value1,
    }
    labelText.value = '实际值'
  }

  getData()
}

const gmvTotal = ref({
  num: '',
  valueHb: 0
})
// 经营收入list
const gmvList = ref([
  {
    id: 1,
    title: '运营收入',
    num: '',
    unit: '元',
    valueHb: 0,
    prop: 'operatingIncome'
  },
  {
    id: 2,
    title: '会员与渠道收入',
    num: '',
    unit: '元',
    valueHb: 0,
    prop: 'memberChannel'
  },
  {
    id: 3,
    title: 'OP rate',
    num: '30',
    unit: '%',
    valueHb: 0,
    prop: 'netProfitRate'
  },
  {
    id: 4,
    title: '销售费用',
    num: '',
    unit: '元',
    valueHb: 0,
    prop: 'sellingExpenses'
  },
  {
    id: 5,
    title: '管理费用',
    num: '',
    unit: '元',
    valueHb: 0,
    prop: 'overHeadFee'
  }
])

const targetList = reactive([  
  { name: '经营收入' },  
  { name: '运营收入' },
  { name: '会员与渠道收入' },
  { name: '管理费用' },
  { name: '销售费用' },
  { name: 'OP rate' },
]);
const targetIndex = ref<number>(0)
const changeTarget = () => {
  getShopKyeIndex()
}

const keyDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 获取图表数据
const getShopKyeIndex = () => {
  const params: QuerySingleStoreOperatingIncome = {
    shopId: shopId.value,
    ...dateData.value
  }

  keyDetail.value = {
    categories: [],
    data: []
  }
  shopKeyIndex({...params, dataType: targetIndex.value}).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: ['day', 'month'].includes(dateData.value.dateType) ? '上周同期' : '去年同期',
      data: res.data.map((item: shopKeyIndexRes) => item.termValue)
    })
    data.push({
      name: ['day', 'month'].includes(dateData.value.dateType) ? '当日' : '当年',
      data: res.data.map((item: shopKeyIndexRes) => item.actualValue)
    })
    if (['quarter', 'year'].includes(dateData.value.dateType)) {
      data.push({
        name: '完成率',
        data: res.data.map((item: shopKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    keyDetail.value = {
      categories: res.data.map((item: shopKeyIndexRes) => ['day', 'month'].includes(dateData.value.dateType) ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }
  })
}

const getData = () => {
  const params: QuerySingleStoreOperatingIncome = {
    shopId: shopId.value,
    ...dateData.value
  }

  singleStoreOperatingIncome(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    gmvTotal.value.num = res.data.data.businessIncome
    gmvList.value.forEach((item) => {
      item.num = res.data.data[item.prop]
    })
  })

  getShopKyeIndex()
}
</script>

<style lang="scss" scoped>
.finance-shop {
  width: 100%;
  height: 100%;
  padding: 20rpx 20rpx 60rpx 20rpx;
  overflow: auto;
  background: #EFEFEF;

  .shop-top {
    padding: 0 30rpx 30rpx;
    border-radius: 20rpx;
    background: #fff;

    .shop-date {
      padding: 0 10rpx;
    }

    .shop-info {
      border-radius: 20rpx;

      .info-top {
        position: relative;
        margin: 0 10rpx;
        background: linear-gradient(0deg, rgba(247, 94, 59, 0) 0%, rgba(247, 94, 59, 0.05) 100%);
        border-radius: 20rpx 20rpx 0 0;

        .info-label {
          position: absolute;
          top: 20rpx;
          right: 0;

          padding: 8rpx 20rpx;
          background: linear-gradient(203.5deg, rgba(255, 178, 161, 0.05) 0%, rgba(247, 94, 59, 0.2) 100%);
          border-radius: 19rpx 0 0 19rpx;

          font-size: 22rpx;
          line-height: 22rpx;
          color: #C35943;
          font-weight: bold;
        }

        .top-title {
          display: inline-flex;
          align-items: center;
          width: fit-content;
          height: 52rpx;
          padding: 0 16rpx;
          border-radius: 20rpx 0 20rpx 0;
          background: linear-gradient(203.5deg, #FFB09F 0%, #F75E3B 100%);

          .title-icon {
            width: 30rpx;
            height: 30rpx;
          }

          .title-text {
            padding-left: 8rpx;
            font-size: 28rpx;
            font-weight: bold;
            color: #fff;
          }
        }

        .top-num-wraper {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          padding: 40rpx 0 28rpx 0;

          .top-num {
            font-size: 52rpx;
            text-align: center;
            line-height: 52rpx;
            color: #1F2428;
            font-family: 'DIN Bold';
            font-weight: bold;
          }

          .top-text {
            font-size: 32rpx;
            color: #1F2428;
          }
        }

        .top-diff {
          height: 36rpx;
          display: flex;
          justify-content: center;
          align-items: center;

          .info-name {
            padding-right: 16rpx;
            font-size: 28rpx;
            color: #1F2428;
            font-weight: bold;
          }

          .info-icon {
            width: 22rpx;
            height: 28rpx;
          }

          .info-num {
            padding-left: 16rpx;
            font-size: 36rpx;
            font-family: 'DIN Bold';
            color: #1F2428;
            font-weight: bold;
          }

          .num-up {
            color: #FF4D4D;
          }

          .num-down {
            color: #56CC93;
          }
        }
      }

      .info-bottom {
        padding-top: 50rpx;
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        
        .info-item {
          display: flex;
          flex-direction: column;
          margin: 10rpx 8rpx;
          width: 200rpx;
          padding: 28rpx 16rpx;
          border: 2rpx solid #f2f2f2;
          border-radius: 20rpx;

          .item-title {
            font-size: 24rpx;
            line-height: 24rpx;
            font-weight: bold;
            padding-bottom: 28rpx;
            color: #1F2428;
          }

          .item-num {
            font-size: 22rpx;
            line-height: 22rpx;
            color: #1F2428;
          }

          .item-info {
            padding-top: 16rpx;
            height: 24rpx;
            display: flex;
            align-items: center;

            .info-name {
              padding-right: 8rpx;
              font-size: 22rpx;
              color: rgba(31, 36, 40, 0.7);
            }

            .info-icon {
              width: 16rpx;
              height: 20rpx;
            }

            .info-num {
              padding-left: 8rpx;
              font-size: 24rpx;
              font-family: 'DIN Bold';
              color: #1F2428;
              font-weight: bold;
            }

            .num-up {
              color: #FF4D4D;
            }

            .num-down {
              color: #56CC93;
            }
          }
        }
      }
    }
  }

  .shop-bottom {
    padding: 40rpx 20rpx;
    margin-top: 20rpx;
    border-radius: 20rpx;
    background: #fff;

    .chart-box {
      min-height: 680rpx;
    }
  }
}
</style>