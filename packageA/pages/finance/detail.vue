<template>
  <view class="finance-detail">
    <view class="detail-area">
      <cAreaPicker @setArea="setArea" :defaultName="areaData.orgName" :showShop="false"></cAreaPicker>
    </view>

    <view class="detail-content">
      <view class="content-view">
        <!-- 关键指标 ---start -->
        <view class="content-title">
          <text class="title-text">关键指标</text>
          <view class="title-btn">
            <view class="btn-item" :class="{ active: dateType === 'day' }" @click="setDateType('day')">
              日
            </view>
            <view class="btn-item" :class="{ active: dateType === 'month' }" @click="setDateType('month')">
              月
            </view>
          </view>
        </view>

        <cTabs :list="targetList" v-model:modelValue="targetIndex" :isBig="false" :itemWidth="170" textColor="#1F2428" @change="changeTarget"></cTabs>

        <view class="chart-box">
          <barCharts v-if="keyDetail.data.length" :barNum="2" :lineNum="dateType === 'day' ? 0 : 1" :detail="keyDetail" leftUnit="万元" rightUnit="%"></barCharts>
        </view>
        <!-- 关键指标 ---end -->

        <!-- 关键驱动因素 ---start -->
        <view class="content-title">
          <text class="title-text">关键驱动因素</text>
        </view>

        <template v-if="targetIndex >= 2">
          <cDate :dateType="dateType" :minDate="minDate" :maxDate="maxDate" @setDate="setDate"></cDate>
          <ringCharts v-if="ringDetail.length" :detail="ringDetail" legendPositon="left" @updateTooltip="updateTooltip">
            <view class="tooltip-info">
              <view class="tooltip-title">
                <text class="text">{{ tooltipInfo?.name }}</text>
                <text class="num">{{ tooltipInfo?.value }}%</text>
              </view>

              <view class="tooltip-num">{{ tooltipInfo?.number }}万元</view>
            </view>
          </ringCharts>

          <view class="ranking-view">
            <view class="ranking-btn">
              <cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleChange"></cBtnGroup>
            </view>
            
            <view class="ranking-table">
              <cTable ref="rankTable" :header="managementData.columnShow" :isZPaging="true" @updateList="updateList" @getMore="getRanks" @chooseItem="chooseItem">
                <template #default="scope">
                  <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
                  <template v-if="['gmvCompletionRate', 'occ', 'crs'].includes(scope.prop)">{{ scope.data[scope.prop] !== null ? `${scope.data[scope.prop]}%` : '-' }}</template>
                </template>
              </cTable>
            </view>
          </view>
        </template>
        <template v-else>
          <cTabs :list="dataTypeList" v-model:modelValue="dataType" :isBig="false" :itemWidth="150" textColor="#1F2428" @change="changeDataType"></cTabs>
          <view class="chart-box">
            <barCharts v-if="driveDetail.data.length" :barNum="2" :lineNum="dateType === 'month' ? 1 : 0" :detail="driveDetail" :leftUnit="chartUnit" rightUnit="%"></barCharts>
            <view class="empty-wraper" v-else>
              <emptyVue />
            </view>
          </view>
        </template>
        <!-- 关键驱动因素 ---end -->
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup name="operationsDetail">
import { ref, reactive, watch, nextTick, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted, computed } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import cBtnGroup from '@/components/c-btn-group/index.vue'
import cTable from '@/components/c-table/c-table.vue'
import barCharts from '@/components/c-chart/barCharts/index.vue'
import ringCharts from '@/components/c-chart/ringCharts/index.vue'
import cDate from '@/components/c-date/index.vue'
import { queryKeyIndex, queryKeyDrive, indexDriverRank } from '@/packageA/api/finance'
import { successCallbackResult } from '@/types'
import type { QueryKeyIndexParams, QueryKeyIndexRes, QueryKeyDriveParams, QueryKeyDriveRes, QueryIndexDriverRank, IndexDriverRankRes } from '@/packageA/api/finance'
import { queryKeyIndex as queryOperationsIndex } from '@/api/operations' 
import { queryKeyIndex as queryMemberIndex } from '@/packageA/api/member'
import { showToast, formatDate } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import emptyVue from '@/components/empty/empty.vue';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
})

const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: null,
  level: '',
  orgName: ''
})
// TODO: 切换区域 => 1. 更新关键指标 2. 更新关键驱动因素
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name

  changeTarget()
}

const targetList = reactive([  
  { name: '运营收入' },  
  { name: '会员与渠道收入' },
  { name: '管理费用' },
  { name: '销售费用' }
]);
const targetIndex = ref<number>(0)
// TODO: 入口、切换关键指标tab => 1. 更新关键指标 2. 更新关键驱动因素
const changeTarget = () => {
  if (targetIndex.value === 0) {
    dataTypeList.value = [  
      { name: '房费收入', value: 1 },  
      { name: 'OCC', value: 2 },
      { name: 'ADR', value: 3 },
      { name: 'RevPar', value: 4 },
    ]
  } else if (targetIndex.value === 1) {
    dataTypeList.value = [  
      { name: '售卡金额', value: 1 },  
      { name: '售卡门店', value: 2 },
      { name: '直销占比', value: 3 },
      { name: '会员占比', value: 4 },
    ]
  }
  dataType.value = 0

  getKeyIndexRes()
  // 关键驱动因素
  if (targetIndex.value === 0) {
    getOperationsIndexRes()
  } else if (targetIndex.value === 1) {
    getMemberIndexRes()
  } else { // 管理费、销售费用
    getKeyDrive()
    // setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  }
}
const dateType = ref<string>('day')
const ringDate = ref<string>(formatDate(Date.now() - 24 * 60 * 60 * 1000))
// TODO: 切换日月 => 1. 更新关键指标 2. 更新关键驱动因素
const setDateType = (type: string) => {
  if (type === dateType.value) return
  dateType.value = type
  if (type === 'day') {
    ringDate.value = maxDate.value || formatDate(Date.now() - 24 * 60 * 60 * 1000)
  } else {
    ringDate.value = maxDate.value ? `${maxDate.value.slice(0, 7)}-01` : `${new Date().getFullYear()}-${new Date().getMonth() + 1}-01`
  }
  getKeyIndexRes()
  // 关键驱动因素
  if (targetIndex.value === 0) {
    getOperationsIndexRes()
  } else if (targetIndex.value === 1) {
    getMemberIndexRes()
  } else { // 管理费、销售费用
    getKeyDrive()
    // setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  }
}

const keyDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 动因分析关键指标查询
const getKeyIndexRes = (dataType?: number) => {
  const params: QueryKeyIndexParams = {
    ...areaData.value,
    dataType: dataType || targetIndex.value + 1,
    dateType: dateType.value
  }

  keyDetail.value = {
    categories: [],
    data: []
  }
  queryKeyIndex(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: dateType.value === 'day' ? '上周同期' : '去年同期',
      data: res.data.map((item: QueryKeyIndexRes) => item.termValue)
    })
    data.push({
      name: dateType.value === 'day' ? '当日' : '当年',
      data: res.data.map((item: QueryKeyIndexRes) => item.actualValue)
    })
    if (dateType.value === 'month') {
      data.push({
        name: '完成率',
        data: res.data.map((item: QueryKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    keyDetail.value = {
      categories: res.data.map((item: QueryKeyIndexRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }
  })
}

let dataTypeList = ref<Array<{name: string, value: number}>>([]);
const dataType = ref<number>(0)
// TODO: 切换下面图表tab 1-更新关键驱动因素
const changeDataType = () => {
  // 关键驱动因素
  if (targetIndex.value === 0) {
    getOperationsIndexRes()
  } else if (targetIndex.value === 1) {
    getMemberIndexRes()
  } else { // 管理费、销售费用
    getKeyDrive()
    // setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  }
}

const chartUnit = computed(() => {
  const unit = dataTypeList.value[dataType.value]?.value || 0
  if (!unit) return ''
  if ([1].includes(unit) && targetIndex.value === 0) return '万元'
  if (([2].includes(unit) && targetIndex.value === 0) || ([3, 4].includes(unit) && targetIndex.value === 1)) return '%'
  if (([3, 4].includes(unit) && targetIndex.value === 0) || ([1].includes(unit) && targetIndex.value === 1)) return '元'
  if ([2].includes(unit) && targetIndex.value === 1) return '门店数'
  return ''
})

const driveDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 动因分析关键驱动因素 - 运营柱状图
const getOperationsIndexRes = () => {
  const params: QueryKeyIndexParams = {
    ...areaData.value,
    dataType: dataTypeList.value[dataType.value].value,
    dateType: dateType.value,
    fromSource: 1
  }

  driveDetail.value = {
    categories: [],
    data: []
  }
  queryOperationsIndex(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: dateType.value === 'day' ? '上周同期' : '去年同期',
      data: res.data.map((item: QueryKeyIndexRes) => item.termValue)
    })
    data.push({
      name: dateType.value === 'day' ? '当日' : '当年',
      data: res.data.map((item: QueryKeyIndexRes) => item.actualValue)
    })
    if (dateType.value === 'month') {
      data.push({
        name: '完成率',
        data: res.data.map((item: QueryKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    driveDetail.value = {
      categories: res.data.map((item: QueryKeyIndexRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }
  })
}

// 动因分析关键驱动因素 - 会员柱状图
const getMemberIndexRes = () => {
  const params: QueryKeyIndexParams = {
    ...areaData.value,
    dataType: dataTypeList.value[dataType.value].value,
    dateType: dateType.value,
    fromSource: 1
  }

  driveDetail.value = {
    categories: [],
    data: []
  }
  queryMemberIndex(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    const data: Array<{name: string, data: Array<string | number>}> = []
    data.push({
      name: dateType.value === 'day' ? '上周同期' : '去年同期',
      data: res.data.map((item: QueryKeyIndexRes) => item.termValue)
    })
    data.push({
      name: dateType.value === 'day' ? '当日' : '当年',
      data: res.data.map((item: QueryKeyIndexRes) => item.actualValue)
    })
    if (dateType.value === 'month') {
      data.push({
        name: '完成率',
        data: res.data.map((item: QueryKeyIndexRes) => item.actualValueCompletionRate)
      })
    }
    driveDetail.value = {
      categories: res.data.map((item: QueryKeyIndexRes) => dateType.value === 'day' ? (item.weekday === 6 ? item.slotDate.slice(5) + '(周六)' : item.weekday === 7 ? item.slotDate.slice(5) + '(周日)' : item.slotDate.slice(5)) : item.slotDate.slice(2, 7)),
      data
    }
  })
}

const minDate = ref<string>(dateType.value === 'day' ? `${new Date().getFullYear()}/${new Date().getMonth() + 1}/1` : `${new Date().getFullYear()}/1/1`)
const maxDate = ref<string>('')
const ringDetail = ref<Array<{name: string, value: number | string, number: number}>>([])
const setDate = (date: string) => {
  ringDate.value = dateType.value === 'day' ? date : date + '-01'
  getKeyDrive()
}
// 动因分析关键驱动因素 - 环状图
const getKeyDrive = () => {
  const params: QueryKeyDriveParams = {
    ...areaData.value,
    dataType: targetIndex.value - 1,
    dateType: dateType.value,
    slotDate: ringDate.value
  }
  ringDetail.value = []
  queryKeyDrive(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }
    if (!res.data) return
    if (minDate.value > res.data.slotDate) {
      minDate.value = dateType.value === 'day' ? `${res.data.slotDate.slice(0, 7)}/1`.replace(/-/g, '/') : `${res.data.slotDate.slice(0, 4)}/1/1`
    }
    if (!maxDate.value) {
      maxDate.value = res.data.slotDate
      ringDate.value = res.data.slotDate
    }
    
    const item: QueryKeyDriveRes = res.data
    if (!item) return
    if (targetIndex.value === 2) {
      ringDetail.value = [{ name: '差旅费', 
                            value: item.travelExpensesFeeRate,
                            number: item.travelExpensesFee
                          }, 
                          { name: '业务招待费', 
                            value: item.businessEnterTexpensesRate,
                            number: item.businessEnterTexpenses
                          }, 
                          { name: '福利费', 
                            value: item.benefitFeeRate,
                            number: item.benefitFee
                          }, 
                          { name: '技术服务费', 
                            value: item.technicalServiceFeeRate,
                            number: item.technicalServiceFee
                          },
                          { name: '其他', 
                            value: item.otherFeeRate,
                            number: item.otherFee
                          }]
    } else {
      ringDetail.value = [{ name: '营销费', 
                            value: item.marketingFeeRate,
                            number: item.marketingFee
                          }, 
                          { name: '营销品费', 
                            value: item.marketingMaterialFeeRate,
                            number: item.marketingMaterialFee
                          }, 
                          { name: '其他', 
                            value: item.otherFeeRate,
                            number: item.otherFee
                          }]
    }

    setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  })
}

// --------start 排行榜------------
const managementData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '序号',
      prop: 'num',
      width: '76',
      slot: 'num',
    },
  ],
  columnShow: [],
})

// 组织排行榜相关操作
const setRank = (params: {updateColumn?: boolean, level?: string, initSort?: boolean, isReload?: boolean}) => {
  if (params.updateColumn) setRankColumn(params.level!) // 更新表头
  if (params.initSort) { // 初始化sort参数
    sortData.value = {
      orderByColumn: targetIndex.value === 2 ? 'marketing_fee' : 'travel_expenses_fee',
      isAsc: 'desc'
    }
  }
  if (params.isReload) {
    rankTable.value?.reload() // 更新列表，会触发getMore
  }
}

// 重置表头
const setRankColumn = (level: string) => {
  managementData.value.columnShow = JSON.parse(JSON.stringify(managementData.value.column))
  if (targetIndex.value === 3) {
    managementData.value.columnShow.splice(1, 0, {
      name: '营销费',
      prop: 'marketingFee',
      width: '180',
      sort: true,
      sortType: 'desc',
      orderByColumn: 'marketing_fee'
    }, 
    {
      name: '营销品费',
      prop: 'marketingMaterialFee',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'marketing_material_fee'
    },
    {
      name: '其他',
      prop: 'otherFee',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'other_fee'
    });
  } else if (targetIndex.value === 2) {
    managementData.value.columnShow.splice(1, 0, {
      name: '差旅费',
      prop: 'travelExpensesFee',
      width: '180',
      sort: true,
      sortType: 'desc',
      orderByColumn: 'travel_expenses_fee'
    }, 
    {
      name: '业务招待费',
      prop: 'businessEnterTexpenses',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'business_enter_texpenses'
    },
    {
      name: '福利费',
      prop: 'benefitFee',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'benefit_fee'
    },
    {
      name: '技术服务费',
      prop: 'technicalServiceFee',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'technical_service_fee'
    },
    {
      name: '其他',
      prop: 'otherFee',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 'other_fee'
    });
  }
  if (Number(level) <= 20) { // 地区总部
    managementData.value.columnShow.splice(1, 0, {
      name: '地区总部',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '负责人',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '30') { // 大区
    managementData.value.columnShow.splice(1, 0, {
      name: '大区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '大区总',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '40') { // 城区
    managementData.value.columnShow.splice(1, 0, {
      name: '城区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '城区总',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '50') { // 门店
    managementData.value.columnShow.splice(1, 0, {
      name: '门店ID',
      prop: 'orgId',
      width: '90',
    }, {
      name: '门店',
      prop: 'orgName',
      width: '200',
    }, {
      name: '店长',
      prop: 'orgUserName',
      width: '90',
    });
  }
}
const tabType = ref<string>('10')
// 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const handleChange = (level: string) => {
  setRank({updateColumn: true, level, initSort: true, isReload: true})
}

// 列表中选择下钻 => 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const chooseItemParam = ref<{orgId: number, level: string, tabType: string}>()
const chooseItem = (data: IndexDriverRankRes) => {
  if (!data.canClick) return

  tabType.value = ''
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
    tabType: (data.level === '10' ? Number(data.level) + 20 : Number(data.level) + 10).toString()
  }

  setRank({updateColumn: true, level: chooseItemParam.value.tabType, initSort: true, isReload: true})
}

// 排序参数（组织、品牌）
const sortData = ref<{orderByColumn: string, isAsc: string}>({
  orderByColumn: targetIndex.value === 2 ? 'marketing_fee' : 'travel_expenses_fee',
  isAsc: 'desc'
})
// 更新排序 => 1.更新列表
const updateList = (item: Column) => {
  sortData.value = {
    orderByColumn: item.orderByColumn as string, 
    isAsc: item.sortType as string
  }

  setRank({updateColumn: false, initSort: false, isReload: true})
}

const rankTable = ref<InstanceType<typeof cTable> | null>(null);
// 获取排名数据、初始化、加载更多、更新列表触发
const getRanks = (pageNum?: number, pageSize?: number) => {
  const params: QueryIndexDriverRank = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    orgId: tabType.value ? areaData.value.orgId : chooseItemParam.value?.orgId,
    level: tabType.value ? areaData.value.level : chooseItemParam.value?.level,
    tabType: tabType.value || chooseItemParam.value?.tabType,
    dataType: targetIndex.value - 1,
    dateType: dateType.value,
    slotDate: ringDate.value,
    ...sortData.value,
  }

  indexDriverRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    rankTable.value.setData(res.rows)
  })
}
// --------end 排行榜------------

// tooltip更新
const tooltipInfo = ref()
const updateTooltip = (info) => {
  tooltipInfo.value = info
}

watch(() => props.params, (val) => {
  nextTick(() => {
    if (val.id) setArea(props.params.id, props.params.level, props.params.orgName)
  })
}, {deep: true, immediate: true})

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('D015', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.finance-detail {
  position: relative;
	width: 100%;
	height: 100%;
  display: flex;
  flex-direction: column;

  .detail-area {
    padding: 0 20rpx;
  }

  .detail-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx;
    margin-top: 20rpx;

    .content-view {
      padding: 0 40rpx 40rpx 40rpx;
      margin-bottom: 80rpx;
      background: #fff;
      border-radius: 22rpx;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 0;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .content-chart {
        position: relative;

        .content-btn-left {
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      .ranking-view {
        background: #fff;
        border-radius: 22rpx;

        .ranking-title {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .title-left {
            display: flex;
            align-items: center;
            height: 32rpx;

            .title-text {
              font-size: 32rpx;
              font-weight: bold;
            }

            .sub-title-text {
              padding-left: 20rpx;
              font-size: 24rpx;
              color: #999999;
            }
          }
        }

        .ranking-btn {
          padding: 40rpx 0 20rpx 0;
        }

        .ranking-table {
          height: 756rpx;
          border-radius: 20rpx;
          overflow: hidden;

          .name-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .item-text {
              display: -webkit-box;
              max-width: 120rpx;
              overflow: hidden;//文本超出隐藏
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;//文本显示方式，默认水平
              -webkit-line-clamp: 2;//设置显示多少行
            }

            .line-item {
              text-decoration: underline;
            }

            .item-info {
              padding-top: 12rpx;
              display: flex;
              align-items: center;

              .info-text {
                font-size: 18rpx;
                line-height: 20rpx;
                color: #999999;
              }

              .item-icon {
                width: 12rpx;
                height: 16rpx;
                margin: 0 8rpx;
              }

              .item-num {
                font-size: 20rpx;
                line-height: 20rpx;
                font-weight: bold;
                font-family: "DIN Bold";
              }

              .up-num {
                color: #FF4D4D;
              }

              .down-num {
                color: #56CC93;
              }
            }
          }
        }
      }

      .title-btn, .content-btn-left {
        display: flex;
        align-items: center;
        height: 48rpx;
        border: 2rpx solid #f2f2f2;
        border-radius: 24rpx;

        .btn-item {
          padding: 0 24rpx;
          font-size: 22rpx;
          line-height: 44rpx;
          color: rgba(31, 36, 40, 0.7);
        }

        .active {
          border-radius: 22rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
          font-weight: bold;
          color: #fff;
        }
      }
    }
  }

  .chart-box {
    min-height: 680rpx;

    .empty-wraper {
      height: 680rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tooltip-info {
    .tooltip-title {
      display: flex;
      align-items: center;

      .text, .num {
        font-size: 22rpx;
        line-height: 24rpx;
        color: #1F2428;
        font-weight: bold;
      }

      .num {
        font-size: 24rpx;
        padding-left: 20rpx;
        font-weight: bold;
      }
    }

    .tooltip-num {
      padding-top: 14rpx;
      font-size: 22rpx;
      line-height: 22rpx;
      color: #1F2428;
    }
  }

  .ring-tooltip {
    right: 80rpx;
    left: inherit !important;
  }
}
</style>