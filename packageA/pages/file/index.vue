<template>
  <view class="file-page">
    <view class="file-info">
      <text class="info-title">门店基本信息</text>
      <view class="info-item" v-for="item in Object.keys(baseInfo)" :key="item">
        <text class="item-label">{{ baseInfo[item] }}：</text>
        <text class="item-value">{{ detail?.[item] || '-' }}</text>
      </view>
    </view>

    <view class="file-info">
      <text class="info-title">门店基本信息</text>
      <view class="info-item" v-for="item in Object.keys(documentInfo)" :key="item">
        <text class="item-label">{{ documentInfo[item] }}：</text>
        <text class="item-value">{{ Number(detail?.[item]) ? '有' : '无' }}</text>
      </view>
    </view>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { queryShopDetail } from '@/packageA/api/file';
import type { QueryShopDetailRes } from '@/packageA/api/file';
import { showToast } from '@/utils/util';
import { successCallbackResult } from '@/types'
import { formatDate } from '@/utils/util.ts';
import { useCommon } from '@/hooks/useGlobalData';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

const shopId = ref<string | number>()
onLoad((options : any) => {
  shopId.value = options.shopId
  getData()
})
const baseInfo = ref({
  shopId: 'ID',
  shopName: '门店名称',
  storeType: '门店类型',
  contractNo: '合同号',
  brandName: '酒店品牌',
  signCompany: '签约公司',
  signUserName: '签约人',
  storeStandard: '门店装修标准',
  signDate: '签约时间',
  contractExpirDate: '合同到期日',
  mailAddress: '通讯地址',
  developUserName: '拓展经理',
  contacNumber: '前台电话',
  beginDate: '开业时间',
  openSystemDate: '开通系统时间',
  contractRoomNumber: '合同间数',
  pmsRoomNumber: 'PMS房量',
  shopUserName: '店长',
  shopUserSalary: '店长工资'
})

const documentInfo = ref({
  fireSafetyCert: '消防安全检查合格证',
  specialIndustryCert: '特种行业许可证',
  hygienicCert: '卫生许可证',
  taxRegistCert: '税务登记证',
  businessCert: '营业执照',
  // integrationThreeCert: '三证合一'
})

const detail = ref<QueryShopDetailRes>()
const getData = () => {
  queryShopDetail({ shopId: shopId.value }).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    if (res.data.signDate) res.data.signDate = formatDate(res.data.signDate)
    if (res.data.contractExpirDate) res.data.contractExpirDate = formatDate(res.data.contractExpirDate)
    if (res.data.beginDate) res.data.beginDate = formatDate(res.data.beginDate)
    if (res.data.openSystemDate) res.data.openSystemDate = formatDate(res.data.openSystemDate)
    detail.value = res.data
  })
}

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('H010', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss" scoped>
.file-page {
  width: 100%;
  height: 100%;
  padding: 28rpx 20rpx 60rpx 20rpx;
  background: rgba(229, 229, 229, 0.6);
  overflow: auto;

  .file-info {
    padding: 40rpx;
    margin-bottom: 28rpx;
    background: #fff;
    border-radius: 20rpx;

    .info-title {
      display: block;
      padding-bottom: 30rpx;
      font-size: 32rpx;
      line-height: 32rpx;
      font-weight: bold;
      color: #1F2428;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 11rpx 0;

      .item-label {
        font-size: 28rpx;
        line-height: 48rpx;
        color: rgba(31, 36, 40, 0.7);
        white-space: nowrap;
      }

      .item-value {
        font-size: 28rpx;
        text-align: right;
        line-height: 48rpx;
        color: #1F2428;
      }
    }
  }
}
</style>