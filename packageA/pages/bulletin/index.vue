<template>
  <view class="bulletin">
    <view class="bulletin-input">
      <up-search v-model="title"  placeholder="请输入搜索内容" @custom="search" />
    </view>

    <z-paging ref="pagingRef" v-model="listData" :fixed="false" @query="queryList">
      <view v-for="item in listData" :key="item.shopId" class="bulletin-item" @click="handleClick(item)">
        <view class="item-left">
          <image class="item-img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-25/1732497388091.png" mode="aspectFill"></image>
          <view class="item-info">
            <text class="item-name" :class="[!item.isCheck && 'item-unread']">{{ item.title }}</text>
            <text class="item-time">{{ item.updateTime }}</text>
          </view>
        </view>

        <up-icon name="arrow-right" color="#C5C5C5" size="20rpx" />
      </view>

      <template v-slot:empty>
        <view class="empty-wraper">
          <empty></empty>
        </view>
      </template>
    </z-paging>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script setup lang="ts">
import { ref, unref } from 'vue';
import type { DownloadTask } from '@dcloudio/uni-app'; // 导入 DownloadTask 类型
import type zPaging from 'z-paging/components/z-paging/z-paging.vue';
import empty from '@/components/empty/empty.vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { hideLoading, showLoading, showToast } from '@/utils/util';
import { successCallbackResult } from '@/types'
import { getList, insertRecord } from '@/packageA/api/bulletin'
import type { ListRes } from '@/packageA/api/bulletin'

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const title = ref<string>('');
const listData = ref<Array<ListRes>>([]);

const pagingRef = ref<InstanceType<typeof zPaging> | null>(null);
const queryList = (pageNum?: number, pageSize?: number) => {
  getList({ title: title.value, pageNum: pageNum || 1, pageSize: pageSize || 20 }).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return;
    }
    pagingRef.value.complete(res.data);
  });
};

const search = () => {
  pagingRef.value.reload();
};

let pageFrom = '';
let downFileTask: DownloadTask = null
const handleClick = (item: ListRes) => {
  showLoading()
  downFileTask = uni.downloadFile({
    url: unref(item.fileUrl),
    success: (res) => {
      hideLoading()
      uni.openDocument({
        filePath: res.tempFilePath,
        success: () => {
          uni.setStorageSync('open', 'complete')
          if (!item.isCheck) {
            const params = {
              title: item.title,
              fileId: item.fileId,
              fileUrl: item.fileUrl
            }
            insertRecord(params).then((res: successCallbackResult) => {
              if (res.code !== 200) {
                showToast(res.message || '系统异常，请稍后重试')
                return;
              }

              item.isCheck = 1
            });
          }
        }
      })
    }
  })
};

onLoad((option: any) => {
  pageFrom = option.from;
});
onUnload(() => {
  if (downFileTask) {
    downFileTask.abort()
  }
})
</script>

<style lang="scss" scoped>
.bulletin {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .bulletin-input {
    padding: 20rpx;
    background: #fff;
    border-radius: 0 0 20rpx 20rpx;
  }

  .bulletin-item {
    padding: 34rpx 40rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;;

    .item-left {
      display: flex;

      .item-img {
        width: 90rpx;
        height: 90rpx;
      }

      .item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 6rpx 30rpx 8rpx 40rpx;

        .item-name {
          max-width: 450rpx;
          padding-right: 24rpx;
          font-size: 32rpx;
          font-weight: bold;
          line-height: 32rpx;
          color: #1F2428;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .item-unread {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background: #FF4D4D;
          }
        }

        .item-time {
          font-size: 24rpx;
          line-height: 24rpx;
          color: rgba(31, 36, 40, 0.7);
        }
      }
    }
  }

  .empty-wraper {
    width: 100%;
    height: 392rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 24rpx;
  }
}

:deep(.u-search) {
  flex: none !important;
  background: linear-gradient(90deg, rgb(245, 245, 245) 0%, rgba(245, 245, 245, 0.5) 100%);
  border-radius: 32rpx;

  .u-search__content {
    background: transparent !important;

    .u-search__content__icon {
      display: none;
    }

    .u-search__content__input {
      background: transparent !important;
    }

    .u-search__content__input--placeholder {
      font-size: 24rpx !important;
      line-height: 64rpx !important;
      color: #999999 !important;
      font-weight: bold;
    }

    .u-search__content__input {
      font-size: 24rpx !important;
      line-height: 64rpx !important;
      color: #1F2428 !important;
      font-weight: bold;
    }
  }

  .u-search__action {
    width: 104rpx !important;
    margin: 0 8rpx !important;
    padding: 0 28rpx;
    font-size: 24rpx;
    line-height: 48rpx;
    font-weight: bold;
    color: #fff;
    border-radius: 24rpx;
    background: linear-gradient(198.3deg, rgb(255, 178, 161) 0%, rgb(247, 94, 59) 100%);
  }
}

:deep(z-paging) {
  flex: 1;
  margin: 40rpx 20rpx;

  .zp-l-text-rpx {
		font-size: 22rpx !important;
	}

  .zp-empty-view {
    flex: none;
  }
}
</style>
