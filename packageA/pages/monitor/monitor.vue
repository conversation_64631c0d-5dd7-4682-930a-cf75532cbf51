<template>
	<view class="container">
		<view class="area">
			<cAreaPicker @setArea="setArea" :defaultName="areaData.orgName" :showShop="false"></cAreaPicker>
		</view>
    <view class="description">
      <view class="fr">
        <image class="img" src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-21/1745200634744.png"/>
        <text class="text">说明</text>
      </view>
      <view class="details">
        2025-04-17之后的新增网评分不包含携程/美团等分销渠道点评分。按渠道查看点评分时，各分销渠道点评分不支持查看，直销App/小程序点评分可正常查看。
      </view>
    </view>
		<view class="content">
			<view class="section">
				<view class="">
					<view class="title fr">
						<text class="title-text">关键指标</text>
						<c-subsection-vue v-model="dateTypeIndex" :list="subsectionList" @change="updateData" />
					</view>
					<!-- 渠道picker -->
					<view class="picker-section">
						<view class="picker fr" @click="show = true">
							<view class="">{{channelLable}}</view>
							<up-icon name="arrow-down" color="#1F2428" size="22rpx" />
						</view>
					</view>
					<!-- 关键指标tab -->
					<view class="tabs">
						<cTabsVue :list="tabs" v-model="dataTypeIndex" :isBig="false"
							itemStyle="width:108rpx;height: 60rpx;align-items:flex-start;" @change="handleDataType" />
					</view>
					<view class="key-indicators-chars">
						<stackedLineChartVue ref="stackedLineChartRef" :data="motivationAnalysisData" customFullScreenEvent
							@customFullScreenEvent="handleFullScreen(1)" :full-screen-data="motivationAnalysisFullScreenData" />
					</view>
					<view class="store-ranking-block fr"
						@click="route_to_view(`/packageB/pages/store-ranking/store-ranking?areaData=${JSON.stringify(areaData)}&dateType=${subsectionList[dateTypeIndex].value}`,()=>{trackEvent('009')})">
						<view class="btn fr">
							<view class="btn-text">查看门店排名</view>
							<up-icon name="arrow-right" color="#C35943" size="20rpx" />
						</view>
					</view>
				</view>
				<view v-if="dataTypeIndex != 1 && dataTypeIndex != 4">
					<view class="title fr">
						<text class="title-text">关键驱动因素</text>
					</view>
					<!-- 关键驱动因素tab -->
					<view class="tabs">
						<cTabsVue :list="tab1s.column" v-model="currentIndex" :isBig="false" @change="handleFactors"
							itemStyle="width:150rpx;height: 60rpx;align-items:flex-start;" />
					</view>
					<template v-if="tab1s.column[currentIndex]?.value !== 4">
						<view class="Key-driving-factors-line-chars">
							<stackedLineChartVue ref="stackedLineChart1Ref" :data="motivationAnalysisFactorsData"
								customFullScreenEvent @customFullScreenEvent="handleFullScreen(2)"
								:full-screen-data="motivationAnalysisFactorsFullScreenData" />
						</view>
					</template>
					<template v-else>
						<!-- <view class="picker-section">
							<view class="picker fr">
								<view class="">尚客优</view>
								<up-icon name="arrow-down" color="#1F2428" size="22rpx" />
							</view>
						</view> -->
						<view class="Key-driving-factors-ring-chars">
							<ringChartVue />
						</view>
					</template>
				</view>
			</view>
      <view
        class="shop-inspection-statistics-block fr"
        @click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-statistics',()=>{trackEvent('010')})">
				<view class="btn">前往查看巡店统计</view>
			</view>
		</view>
	</view>
	<up-picker :show="show" :columns="columns" confirmColor="#C35943" closeOnClickOverlay @confirm="confirm"
		:defaultIndex="defaultIndex" @cancel="close" @close="close" keyName="name" />
</template>

<script setup lang="ts">
	import { ref, getCurrentInstance, ComponentInternalInstance, computed, onMounted, watch, nextTick } from 'vue';
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import stackedLineChartVue from '@/components/stacked-line-chart/stacked-line-chart.vue';
	import ringChartVue from '@/components/ring-chart/ring-chart.vue';
	import cAreaPicker from '@/components/c-area-picker/index.vue'
	import cSubsectionVue from "@/components/c-subsection/c-subsection.vue";
	import { useCommon } from '@/hooks/useGlobalData';
	import { motivationAnalysis, motivationAnalysisFactors } from '@/packageA/api/monitor';
	import { OrgLevel } from '@/types/dataCockpit';
	import dayjs from 'dayjs';
	import { CHANNELLIST } from '@/packageA/constant';

	const props = defineProps({
		params: {
			type: Object,
			default: () => { }
		}
	})

	const { globalData, route_to_view, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const subsectionList : { name : string, value : 'day' | 'month' }[] = [
		{ name: '日', value: 'day' },
		{ name: '月', value: 'month' },
	]
	const tabs : { name : string, value : 1 | 2 | 3 | 4 | 5 }[] = [
		{ name: '卫生分', value: 1 },
		{ name: '位置分', value: 2 },
		{ name: '服务分', value: 3 },
		{ name: '设施分', value: 4 },
		{ name: '差评率', value: 5 },
	]
	const tab1s = ref({
		defaultColumn: [
			{ name: '产品标准', value: 4 },
			{ name: '物资采集率', value: 2 },
			{ name: '早餐覆盖率', value: 3 },
			{ name: '店长派遣率', value: 1 },
		],
		column: []
	})

	const { deptName, orgName, ...rest } = globalData.value.user.orgList[0];
	const dataTypeIndex = ref(0),
		dateTypeIndex = ref(0),
		currentIndex = ref(0),
		columns = ref([CHANNELLIST]),
		defaultIndex = ref([0]),
		show = ref(false),
		channelIndex = ref(0),
		areaData = ref<OrgLevel>({ // 区域数据
			...rest
		}),
		stackedLineChartRef = ref(),
		motivationAnalysisData = ref({}), // 关键指标图表数据
		motivationAnalysisFullScreenData = ref({}), // 关键指标全屏图表数据
		stackedLineChart1Ref = ref(),
		motivationAnalysisFactorsData = ref({}), // 关键驱动因素图表数据
		motivationAnalysisFactorsFullScreenData = ref({}) // 关键驱动因素全屏图表数据
	const channelLable = computed(() => columns.value[0][channelIndex.value].name)

	// onMounted(() => {
	// 	handleDataType(tabs[dataTypeIndex.value])
	// })

	// 舆论动因分析
	const toMotivationAnalysis = async (isFullScreen : boolean = false) => {
		try {
			const { orgName, ...rest } = areaData.value
			const params = {
				...rest,
				dateType: isFullScreen ? 'dayMonth' as 'dayMonth' : subsectionList[dateTypeIndex.value].value,
				dataType: tabs[dataTypeIndex.value].value,
				channelType: columns.value[0][channelIndex.value].value
			}
			const { data } = await motivationAnalysis(params)
			if (data) {
				const res = {
					categories: data.map(({ slotDate }) =>
						(dateTypeIndex.value == 0 ? (dayjs(slotDate).day() == 0 ? dayjs(slotDate).format('MM-DD') + '(周日)' : dayjs(slotDate).day() == 6 ? dayjs(slotDate).format('MM-DD') + '(周六)' : dayjs(slotDate).format('MM-DD')) : dayjs(slotDate).format('YYYY-MM').slice(2, 7))
					),
					value1: {
						name: dateTypeIndex.value == 0 ? '上周同期' : '去年同期',
						data: data.map(({ termScore }) => termScore == '-' ? 0 : termScore)
					},
					value2: {
						name: dateTypeIndex.value == 0 ? tabs[dataTypeIndex.value].name : '当年',
						data: data.map(({ score }) => score == '-' ? 0 : score),
					}
				}
				if (isFullScreen) {
					motivationAnalysisFullScreenData.value = res
				} else {
					motivationAnalysisData.value = res
				}
			} else {
				if (isFullScreen) {
					motivationAnalysisFullScreenData.value = {}
				} else {
					motivationAnalysisData.value = {}
				}
			}
		} catch (e) {
			console.error('舆论动因分析 error', e)
		}
	}

	// 舆论动因分析驱动因子
	const toMotivationAnalysisFactors = async (isFullScreen : boolean = false) => {
		try {
			const { orgName, ...rest } = areaData.value
			const params = {
				...rest,
				dateType: isFullScreen ? 'dayMonth' as 'dayMonth' : subsectionList[dateTypeIndex.value].value,
				dataType: tab1s.value.column[currentIndex.value].value
			}
			const { data } = await motivationAnalysisFactors(params)
			if (data) {
				const res = {
					categories: data.factorsList.map(({ slotDate }) =>
						(dateTypeIndex.value == 0 ? (dayjs(slotDate).day() == 0 ? dayjs(slotDate).format('MM-DD') + '(周日)' : dayjs(slotDate).day() == 6 ? dayjs(slotDate).format('MM-DD') + '(周六)' : dayjs(slotDate).format('MM-DD')) : dayjs(slotDate).format('YYYY-MM').slice(2, 7))
					),
					value1: {
						name: dateTypeIndex.value == 0 ? '上周同期' : '去年同期',
						data: data.factorsList.map(({ termValue }) => termValue == '-' ? 0 : termValue)
					},
					value2: {
						name: tab1s.value.column[currentIndex.value].name,
						data: data.factorsList.map(({ value }) => value == '-' ? 0 : value),
					}
				}
				if (isFullScreen) {
					motivationAnalysisFactorsFullScreenData.value = res
				} else {
					motivationAnalysisFactorsData.value = res
				}
			} else {
				if (isFullScreen) {
					motivationAnalysisFactorsFullScreenData.value = {}
				} else {
					motivationAnalysisFactorsData.value = {}
				}
			}
		} catch (e) {
			console.error('舆论动因分析驱动因子 error', e)
		}
	}

	const confirm = (e : any) => {
		channelIndex.value = e.indexs[0]
		show.value = false
		updateData()
	}

	const close = () => {
		show.value = false
	}

	// 区域筛选
	const setArea = (id : number, level : string, name : string) => {
		areaData.value.orgId = id.toString()
		areaData.value.level = Number(level)
		areaData.value.orgName = name
		updateData()
	}

	const handleDataType = (item : any) => {
		setTab1Column(item)
		updateData()
	}

	const setTab1Column = (item : any) => {
		tab1s.value.column = JSON.parse(JSON.stringify(tab1s.value.defaultColumn))
		if (item.value === 1) {
			tab1s.value.column.splice(0, 3)
		} else if (item.value === 4) {
			tab1s.value.column.splice(3, 1)
		}
		currentIndex.value = 0
	}

	const handleFactors = () => {
		toMotivationAnalysisFactors()
	}

	const updateData = () => {
		toMotivationAnalysis()
		toMotivationAnalysisFactors()
	}

	// 全屏显示数据
	const handleFullScreen = async (type : number) => {
		if (type === 1) { // 关键指标全屏图表数据
			if (dateTypeIndex.value == 1) {
				motivationAnalysisFullScreenData.value = motivationAnalysisData.value
				setTimeout(() => { stackedLineChartRef.value.isFullScreen = true }, 500)
			} else { // 关键驱动因素全屏图表数据
				await toMotivationAnalysis(true)
				setTimeout(() => { stackedLineChartRef.value.isFullScreen = true }, 500)
			}
			return
		} else if (type === 2) {
			if (dateTypeIndex.value == 1) {
				motivationAnalysisFactorsFullScreenData.value = motivationAnalysisFactorsData.value
				setTimeout(() => { stackedLineChart1Ref.value.isFullScreen = true }, 500)
			} else { // 关键驱动因素全屏图表数据
				await toMotivationAnalysisFactors(true)
				setTimeout(() => { stackedLineChart1Ref.value.isFullScreen = true }, 500)
			}
			return
		}
	}

	watch(() => props.params, (val) => {
		setTab1Column(tabs[dataTypeIndex.value])
		nextTick(() => {
			if (val.id) setArea(props.params.id, props.params.level, props.params.orgName)
		})
	}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;

		.content {
			overflow-y: auto;
			padding-bottom: 60rpx;
		}

		.area {
			margin-bottom: 20rpx;
		}
	}

  .description {
    border-radius: 20rpx;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
    padding: 16rpx 20rpx;
    margin-bottom: 20rpx;

    .img {
      width: 22rpx;
      height: 22rpx;
      flex-shrink: 0;
    }

    .text {
      color: $uni-text-color;
      font-size: 26rpx;
      font-weight: bold;
      line-height: 26rpx;
      margin-left: 8rpx;
    }

    .details {
      margin-top: 8rpx;
      color: #1f2428b3;
      font-size: 22rpx;
      line-height: 38rpx;
      padding-left: 30rpx;
    }
  }

	.section {
		padding: 40rpx;
		background: #fff;
		border-radius: 26.46rpx;
		margin-bottom: 20rpx;

		.title {
			justify-content: space-between;
			margin-bottom: 40rpx;

			&--tab {
				margin-bottom: 9rpx;
			}

			&-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
			}


			.title-btn-route {
				border-radius: 200rpx;
				border: 1rpx solid #ffffff;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				padding: 8rpx 16rpx;

				.btn-text {
					color: #1f2428;
					font-size: 22rpx;
					line-height: 22rpx;

					&-primary {
						color: #c35943;
					}
				}
			}

			&-btn {
				height: 48rpx;
				border: 2rpx solid #f2f2f2;
				border-radius: 24rpx;

				.btn-item {
					padding: 0 24rpx;
					font-size: 22rpx;
					line-height: 44rpx;
					color: rgba(31, 36, 40, 0.7);
				}

				.active {
					border-radius: 22rpx;
					background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
					font-weight: bold;
					color: #fff;
				}
			}
		}

		.picker-section {
			border-radius: 200rpx;
			border: 2rpx solid #f2f2f2;
			margin-bottom: 40rpx;

			.picker {
				justify-content: space-between;
				padding: 16rpx 40rpx;
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
			}
		}

		.key-indicators-chars {
			height: max-content;
			margin-bottom: 40rpx;
		}

		.store-ranking-block {
			justify-content: center;
			margin-bottom: 40rpx;

			.btn {
				// border-radius: 200rpx;
				// border: 2rpx solid #F75E3B;
				padding: 14rpx 28rpx;
				position: relative;
				background-color: white;
				border-radius: 200rpx;
				/* 设置圆角 */
				z-index: 1;

				/* 确保内容在伪元素之上 */
				&::before {
					content: "";
					position: absolute;
					top: -2rpx;
					left: -2rpx;
					right: -2rpx;
					bottom: -2rpx;
					background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
					z-index: -2;
					/* 确保伪元素在内容下面 */
					border-radius: inherit;
					/* 继承父元素的圆角 */
				}

				&::after {
					content: "";
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: #fff;
					z-index: -1;
					/* 确保伪元素在更底层 */
					border-radius: inherit;
					/* 继承父元素的圆角 */
				}

				&-text {
					color: #c35943;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 16rpx;
				}
			}
		}

		.Key-driving-factors-line-chars {
			height: max-content;
		}

		.Key-driving-factors-ring-chars {
			height: 406rpx;
		}
	}

	.shop-inspection-statistics-block {
		justify-content: center;
		margin-top: 40rpx;

		.btn {
			padding: 20rpx 194rpx;
			border-radius: 200rpx;
			color: #ffffff;
			font-size: 30rpx;
			font-weight: bold;
			text-align: center;
			line-height: 30rpx;
			background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
		}
	}
</style>