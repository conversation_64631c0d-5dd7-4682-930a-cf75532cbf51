import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type DateData = {
  dateType: string, // 查询年：year；查询季： quarter；查询月： month；查询日： day；查询自定义： range
  year?: number,
  quarter?: number,
  month?: number,
  atime?: string,
  startDate?: string,
  endDate?: string,
}

export type QueryOrgDataParams = DateData & {
  orgId: number,
  level: string // 区域层级 0 ：集团， 10：事业部，20：战区，30：大区，40：城区
}

export type QueryOrgRankParams = QueryBrandRankParams & {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
}

export type QueryOrgRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  businessIncome: number, // 经营收入
  orgUserName: string,
  netProfitRate: number, // 净利润率
  [k : string] : any
}

export type QueryBrandRankParams = {
  orgId?: string,
  level?: string,
  orderBy: number, // 1：经营收入、2：净利润率
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryKeyIndexParams = {
  orgId?: string,
  level?: string,
  dataType: number, // 查询数据类型 ：1：运营收入 、2：会员与渠道收入、3：管理费用、4：销售费用
  dateType?: string, // day （查询当月每天的数据和对应日期-7天的同比数据）； month（查询当年各月实际值和完成率和去年同期月的完成率）；
}

export type QueryKeyIndexRes = {
  slotDate: string,
  actualValue: number, // 日/月实际值
  termValue: number, // 日-7/去年同月实际值
  actualValueCompletionRate: number, // 预算完成率，不带%
  weekday: number // 周几
  [k : string] : any
}

export type QueryKeyDriveParams = QueryKeyIndexParams

export type QueryKeyDriveRes = {
  slotDate: string,
  travelExpensesFee: number, // 差旅费
  travelExpensesFeeRate: number, // 差旅费占比
  businessEnterTexpenses: number, // 业务招待费
  businessEnterTexpensesRate: number, // 业务招待费占比
  benefitFee: number, // 福利费
  benefitFeeRate: number, // 福利费占比
  technicalServiceFee: number, // 技术服务费占比
  marketingFee: number, // 营销费
  marketingFeeRate: number, // 营销费占比
  marketingMaterialFee: number, // 营销品费
  marketingMaterialFeeRate: number, // 营销品费占比
  otherFee: number, // 其它费用
  otherFeeRate: number, // 其它费用占比
  [k : string] : any
}

export type shopKeyIndexRes = QueryKeyIndexRes

export type QueryIndexDriverRank = {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
  orgId?: string,
  level?: string,
  dataType: number, // 查询数据类型 ：1：管理费用、2：销售费用
  dateType: string, // 查询时间类型：day （查询天数据）； month（查询月数据）；
  slotDate: string, // 查询时间：日维度到每一天，月维度为每个月第一天如：2024-10-01
  orderByColumn?: string, // 差旅费 ：travel_expenses_fee ; 业务招待费 : business_enter_texpenses ; 福利费: benefit_fee; 技术服务费 : technical_service_fee;营销费: marketing_fee;营销品费 : marketing_material_fee ;其他:other_fee
  isAsc?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type IndexDriverRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  orgUserName: string,
  travelExpensesFee: number, // 差旅费
  businessEnterTexpenses: number, // 业务招待费
  benefitFee: number, // 福利费
  technicalServiceFee: number, // 技术服务费
  marketingFee: number, // 营销费
  marketingMaterialFee: number, // 营销品费
  otherFee: number, // 其他费用
  [k : string] : any
}

export type QuerySingleStoreOperatingIncome = DateData & {
  shopId: string;
}

// 财务-动因分析-关键指标
export const queryKeyIndex = (data : QueryKeyIndexParams) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/keyIndex',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 财务-动因分析-关键驱动因素环形图
export const queryKeyDrive = (data : QueryKeyDriveParams) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/indexDriver',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 财务-动因分析-关键驱动因素排行榜
export const indexDriverRank = (data : QueryIndexDriverRank) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/indexDriverRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 财务 - 单店 - 单店经营收入
export const singleStoreOperatingIncome = (data : QuerySingleStoreOperatingIncome) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/singleStoreOperatingIncome',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 财务-单店-费用明细柱状图
export const shopKeyIndex = (data : QuerySingleStoreOperatingIncome) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/shopKeyIndex',
		method: 'GET',
		service: 'qd',
		data
	})
}
