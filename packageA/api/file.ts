import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type BaseInfo = {
  shopId: string, // 门店id
  shopName: string, // 门店名称
  storeType: string, // 门店类型
  contractNo: string, // 合同编号
  brandName: string, // 酒店品牌
  signCompany: string, // 签约公司
  signUserName: string, // 签约人
  storeStandard: string, // 门店装修标准
  signDate: string, // 签约时间
  contractExpirDate: string, // 合同到期时间
  mailAddress: string, // 通讯地址
  developUserName: string, // 拓展经理名称
  contacNumber: string, // 前台电话联系电话
  openSystemDate: string, // 开通系统时间
  contractRoomNumber: number, // 合同间数
  pmsRoomNumber: number, // pms房量
  shopUserName: string, // 店长姓名
  shopUserSalary: number, // 店长工资
  beginDate: string, // 开业时间
}

export type DocumentInfo = {
  fireSafetyCert: number, // 是否有消防安全检查合格证 0 否 1 是
  specialIndustryCert: string, // 是否有特种行业许可证 0 否 1 是
  hygienicCert: string, // 是否有卫生许可证 0 否 1 是
  taxRegistCert: string, // 是否有税务登记证 0 否 1 是
  businessCert: string, // 是否有营业执照 0 否 1 是
  integrationThreeCert: string, // 是否三证合一 0 否 1 是
}

export type QueryShopDetailRes = BaseInfo & DocumentInfo & {
  [k : string] : any
}

// 查询门店档案信息接口
export const queryShopDetail = (data : { shopId: string }) : Promise<successCallbackResult> => {
	return http({
		url: '/homePage/queryShopDetail',
		method: 'GET',
		service: 'qd',
		data
	})
}
