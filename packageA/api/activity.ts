import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type RankListParams = {
  pageNum: number,
  pageSize: number,
  orgId: number | string,
  level: string, // 区域层级 0 ：集团， 10：地区总部，30：大区，40：城区
  sortBy?: string, //  asc 指标升序，desc指标降序
  tabLevel?: string, // 区域层级 0 ：集团， 10：地区总部，30：大区，40：城区
}

export type MyRankInfo = {
  orgName: string, // 地区名称
  solveCount: number, // 解决数量
  fraudulentCount: number, // 总数量
  completeRate: number, // 完成率
  [k : string] : any
}

export type TotalRank = {
  orgName: string, // 地区名称
  solveCount: number, // 解决数量
  fraudulentCount: number, // 总数量
  completeRate: number, // 完成率
  [k : string] : any
}

export type RankListRes = {
  myRankInfo: MyRankInfo, // 我的排名信息
  totalRank: TotalRank, // 总排名
  myRank: number
}

export type RankParams = RankListParams & {
  type: number // 查询排名类型：1-优秀运营门店榜；2-营业榜；3-零售卡突破榜
}

// 首页 - 猫头鹰战役 - 排行
export const rankList = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/rankList',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 首页 - 守护者行动排行 - 排行
export const guardianActionRank = (data : RankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/guardianActionRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// banner荣誉之战列表查询
export const getRyRank = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/getRyRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// banner经营争霸区域列表查询
export const getZbOrgRank = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/getZbOrgRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// banner经营争霸门店列表查询
export const getZbShopRank = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/getZbShopRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// banner经营争霸门店tab列表查询
export const getZbShopRankTab = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/getZbShopRankTab',
		method: 'GET',
		service: 'qd',
		data
	})
}

// banner经营争霸城区星点奖励列表查询
export const getZbOrgScoreRank = (data : RankListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/owlRank/getZbOrgScoreRank',
		method: 'GET',
		service: 'qd',
		data
	})
}
