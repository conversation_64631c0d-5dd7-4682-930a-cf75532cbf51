import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type ListParams = {
  pageNum: number,
  pageSize: number,
  title: string,
}

export type ListRes = {
  title: string, // 公告名称
  fileUrl: string, // 文件链接
  fileId: string, // 文件id
  isCheck: number, // 是否打开
  fileType: string, // 文件类型
  [k : string] : any
}

export type InsertRecordParams = {
  title: string, // 公告名称
  fileUrl: string, // 文件链接
  fileId: string, // 文件id
}


// 公告 - 获取公告列表
export const getList = (data : ListParams) : Promise<successCallbackResult> => {
	return http({
		url: '/announcement/list',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 公告 - 新增公告查看记录
export const insertRecord = (data : InsertRecordParams) : Promise<successCallbackResult> => {
	return http({
		url: '/announcement/insertRecord',
		method: 'GET',
		service: 'qd',
		data
	})
}
