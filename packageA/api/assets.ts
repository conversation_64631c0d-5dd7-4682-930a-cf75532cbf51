import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type QueryShopClassParams = {
  orgId: number | string,
  level: string // 区域层级 0 ：集团， 10：地区总部，30：大区，40：城区
}

export type QueryShopClassRes = {
  increaseShopCountYear: number, // 年度净增门店数
  increaseShopCountMonth: number, // 月度净增门店数
  openShopCountYear: number, // 年度新开门店数
  openShopCountMonth: number, // 月度新开门店数
  closeShopCountYear: number, // 年度解约门店数
  closeShopCountMonth: number, // 月度解约门店数
  [k : string] : any
}

export type QueryShopCycleRes = {
  slotDate: string, // 月时间
  newShopRate: string, // 新店占比
  fairlyNewShopRate: string, // 次新店占比
  matureShopRate: string, // 成熟店占比
  oldShopRate: string, // 老店占比
  operateShopRate: string, // 运营好店占比
  benchmarkShopRate: string, // 层层标杆店占比
  otherShopRate: string, // 其它门店占比
  [k : string] : any
}

export type QueryOrgRankParams = {
  tabType?: string,
  orgId?: string,
  level?: string,
  orderBy: number, // 排序字段： 1 运营店; 2 欠费店 ;3 舞弊店 ;4 零售卡店; 5 层层标杆店; 6 运营好店
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryOrgRankRes = {
  orgId: string,
  orgName: string,
  orgUserName: string,
  level: string,
  openShopCount: number, // 运营店数
  oweShopCount: number, // 欠费店数
  owlShopCount: number, // 舞弊店数
  zeroCardShopCount: number, // 零售卡店数
  benchmarkShopCount: number, // 层层标杆店数
  operateShopCount: number, // 运营好店数
  [k : string] : any
}

export type QueryClassContrastParams = {
  orgId: string,
  level: string,
  classType: number, // 1 有无店长
  tabType?: number, // 1 财务 2 运营 3 会员
}

export type QueryClassMonthParams = {
  orgId: string,
  level: string,
  dataType: number, // 1:经营收入；2: 运营收入；3:会员与渠道收入；4：销售费用；5：管理费用；6：人工成本；7:净利润率；8:gmv；9: occ; 10:adr;11 :revpar;12 :crs占比；13：直销占比；14: 会员占比；15: 会员复购率：16：会员卡收入；17:售卡动销率；18: 0售卡门店；
}

export type QueryClassMonthRes = {
  slotDate: string,
  hasManagerValue: number, // 有店长值
  noManagerValue: number, // 无店长值
  [k : string] : any
}

// 数据驾驶舱-资产-分类对比详情
export const queryClassContrast = (data : QueryClassContrastParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryClassContrast',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱-资产-门分类对比折线
export const queryClassMonth = (data : QueryClassMonthParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryClassMonth',
		method: 'GET',
		service: 'qd',
		data
	})
}
