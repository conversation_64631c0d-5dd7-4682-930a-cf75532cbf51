import http from '@/utils/http'
import { successCallbackResult } from '@/types/index.d.ts'
import { MotivationParams } from '@/types/dataCockpit/index.ts'


// 舆论动因分析
export const motivationAnalysis = (data :  MotivationParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/motivationAnalysis',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 舆论动因分析驱动因子
export const motivationAnalysisFactors = (data :  MotivationParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/motivationAnalysisFactors',
		method: 'POST',
		service: 'sh',
		data
	})
}
