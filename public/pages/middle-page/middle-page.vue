<template>
	<view>

	</view>
</template>

<script setup>
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import { hideLoading, previewFile, showLoading, showToast } from '../../../utils/util';
	import { onUnmounted } from 'vue';
	let downloadTask = null
	onLoad((options) => {
		if (options.data && JSON.parse(decodeURIComponent(options.data))) {
			const data = JSON.parse(decodeURIComponent(options.data))
			handleData(data)
		}
	})
	onShow(() => {
		const documentStatus = uni.getStorageSync('documentStatus')
		console.log(documentStatus, 'documentStatus');
		if (documentStatus === 'open') {
			uni.removeStorageSync('documentStatus')
			uni.navigateBack()
		}
	})

	const handleData = (data) => {
		const { type, content } = data
		if (type === 3) {
			downloadTask = previewFile(content)
		} else if (type === 4) {
			if (content == 1) { // "尚美e购小程序"
				uni.showModal({
					title: '温馨提示',
					content: '确认跳转尚美e购',
					showCancel: true, //是否显示取消按钮
					success(res) {
						console.log(res, 'ersserserwsesrsers');
						if (res.cancel) {
							uni.navigateBack()
						} else {
							uni.navigateToMiniProgram({
								appId: 'wxdb588e58f8ff6c87',
								envVersion: 'release',
								success(res) {
									console.log(res);
									uni.setStorageSync('documentStatus', 'open')
									hideLoading()
								},
								fail(err) {
									console.log(err);
									showToast('跳转失败')
									uni.navigateBack()
								}
							})
						}
					}
				})
			} else if (content == 2) { // "超级店长-我的门店-门店服务-全员授权"
				uni.navigateToMiniProgram({
					appId: 'wx9e817e1f8b4794d0',
					envVersion: 'release',
					success(res) {
						console.log(res);
						hideLoading()
					},
					fail(err) {
						console.log(err);
						showToast('跳转失败')
					}
				})
			}
		}
	}
	onUnmounted(() => {
		if (downloadTask) {
			downloadTask.abort() // 取消下载任务
		}
	})
</script>

<style>

</style>