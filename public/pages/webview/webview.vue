<template>
	<web-view :src="httpUrl" @load="webviewLoad" @message="postMessage"></web-view>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { useCommon } from '@/hooks/useGlobalData';
	import { ComponentInternalInstance, getCurrentInstance, ref, unref } from 'vue';
	import { dataType } from '@/types/index.d';
	import { useLocalEnv, protocl, localEnv } from '@/utils/env'
	const { globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL
	
	const httpUrl = ref(''),
		options = ref<dataType>({})

	const webviewLoad = () => {

	}

	const postMessage = (e : dataType) => {
	}
	onLoad((opts) => {
		options.value = opts

		// ai回答报告链接
		// if (decodeURIComponent(opts.url).indexOf('?') != -1) {
		// 	opts.url = decodeURIComponent(opts.url)
		// 	httpUrl.value = `${decodeURIComponent(opts.url)}&data=${JSON.stringify(unref(globalData))}&token=${uni.getStorageSync('token')}`
		// 	return
		// }

		if (decodeURIComponent(opts.url).indexOf(webviewURL) != -1) {
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: "#C35943"
			})
			const temp : {
				url ?: string,
				[key : string] : any
			} = {
					...opts,
				}
			delete temp.url
			let params = ''
			for (const [key, value] of Object.entries(temp)) {
				params += `&${key}=${value}`
			}
			httpUrl.value = `${opts.url}?v=${Date.now()}&token=${uni.getStorageSync('token')}${params}`
			return
		}

		httpUrl.value = decodeURIComponent(opts.url)
	})
</script>

<style lang="scss">

</style>