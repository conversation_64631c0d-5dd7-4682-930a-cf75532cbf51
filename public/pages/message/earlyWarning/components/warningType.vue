<template>
  <view class="warning-type">
    <view class="type-wraper" @click="handleOpen">
      <text class="type-text">{{ chooseItem?.value ? chooseItem.label : '请选择预警类型' }}</text>
      <up-icon v-if="!show" name="arrow-down" color="#C5C5C5" size="24rpx" />
      <up-icon v-else name="arrow-up" color="#C5C5C5" size="24rpx" />
    </view>

    <up-picker :show="show" :columns="columns" keyName="label" :itemHeight="50" @confirm.stop="handleConfirm" @cancel.stop="show = false" />
  </view>
</template>

<script lang="ts" setup name="warningType">
import { ref, reactive } from 'vue';

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const props = defineProps({

});

const emit = defineEmits(['setDate', 'setDateRange']);

const show = ref<boolean>(false);
const chooseItem = ref<{label: string; value: number}>();
const columns = reactive<Array<Array<{label: string; value: number}>>>([[
  {
    label: '全部',
    value: 0
  },
  {
    label: '满房预警',
    value: 1
  },
  {
    label: '预订价格异常',
    value: 2
  },
  {
    label: '直联渠道未打开',
    value: 3
  },
  {
    label: '新增差评',
    value: 4
  },
  {
    label: '新增疑似舞弊店',
    value: 5
  },
  {
    label: '新增解约店',
    value: 6
  },
  {
    label: '新增封存店',
    value: 7
  },
  {
    label: '预算空缺',
    value: 8
  },
  {
    label: '账单逾期',
    value: 9
  },
  {
    label: '续约提醒',
    value: 10
  }
]]);

const handleConfirm = (e: any) => {
  chooseItem.value = e.value[0];
  show.value = false;
  emit('setDate', chooseItem.value);
};

const handleOpen = () => {
  show.value = true
}
</script>

<style lang="scss">
.warning-type {
  padding: 40rpx 20rpx;

  .type-wraper {
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    border-radius: 32rpx;
    background: #FFFFFF;

    .type-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .u-popup__content {
    border-radius: 40rpx !important;

    .u-toolbar {
      height: 156rpx !important;
      padding: 0 40rpx !important;

      .u-toolbar__wrapper__cancel {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #999999 !important;
      }

      .u-toolbar__title {
        font-size: 36rpx !important;
        line-height: 36rpx !important;
        color: #1F2428 !important;
      }

      .u-toolbar__wrapper__confirm {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #C35943 !important;
      }
    }

    .u-picker__view__column__item {
      font-size: 26rpx !important;
      color: #1F2428 !important;
    }
  }
}
</style>
