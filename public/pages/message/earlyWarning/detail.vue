<template>
  <view class="detail-container">
    <view class="detail-content" v-if="detail?.pushId">
      <!-- 顶部 -->
      <view class="detail-info">
        <template v-if="pushType === '1'">
          <text class="info-item">门店：{{ detail.alarmShopList[0].shopName }}</text>
          <text class="info-item">门店ID：{{ detail.alarmShopList[0].shopId }}</text>
          <text class="info-item">异常规则：{{ detail.alarmRule }}</text>
        </template>
        <template v-else>
          <text class="info-item">异常规则：{{ detail?.alarmRule }}</text>
          <text class="info-item">统计时间：{{ fullDate(detail?.alarmDate) }}</text>
          <text class="info-item">汇总：共<text class="item-num">{{ detail?.alarmShopList.length }}家</text>门店</text>
        </template>
      </view>

      <template v-if="pushType === '2'">
        <view class="detail-warning" v-if="warningType === '7'">
          <image class="warning-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
          <text class="warning-text">请前往【雅典娜】系统预算申请模块申请预算</text>
        </view>

        <view class="detail-table">
          <cTable :header="tableData.column" :list="detail?.alarmShopList">
            <template #default="scope">
              <text v-if="scope.prop === 'operation'" class="operation-btn">查看实时评分</text>
            </template>
          </cTable>
        </view>
      </template>
    </view>
    <!-- <cWaterMark></cWaterMark> -->
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { onMounted, ref } from 'vue';
import cTable from '@/components/c-table/c-table.vue'
import { successCallbackResult } from '@/types'
import { queryPushDetail } from '@/public/api/earlyWarning'
import type { QueryPushCountRes } from '@/public/api/earlyWarning'
import { showToast, fullDate } from '@/utils/util';

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

const warningType = ref<string>()
const pushType = ref<string>()
const pushId = ref<number>()

const tableData = ref<{ column: Array<Column> }>({
  column: [
    {
      name: '门店ID',
      prop: 'shopId',
    },
    {
      name: '门店名称',
      prop: 'shopName',
      width: '200',
    },
    {
      name: '所属城区',
      prop: 'areaName',
      width: '200',
    }
  ]
});
const setColums = () => {
  if (warningType.value === '3') {
    tableData.value.column.push({
      name: '网评分',
      prop: 'points',
      width: '100',
    });
    
    // tableData.value.column.push({
    //   name: '操作',
    //   width: '90',
    //   prop: '',
    //   slot: 'operation'
    // });

  } else if (warningType.value === '8') {
    tableData.value.column[0].width = '120'
    tableData.value.column.push({
      name: '管理费/元',
      prop: 'manageFee',
      width: '150',
    });
    tableData.value.column.push({
      name: '店长及营销经理工资/元',
      prop: 'storeSalary',
      width: '250',
    });
  } else if (warningType.value === '9') {
    tableData.value.column[0].width = '120'
    tableData.value.column.push({
      name: '合同到期日期',
      prop: 'contractExpirDate',
      width: '180',
    });
  }

  getData()
}

const detail = ref<QueryPushCountRes>()
const getData = () => {
  queryPushDetail({ pushId: pushId.value }).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    detail.value = res.data
  })
}

onLoad((option: any) => {
  warningType.value = option.type;
  pushId.value = option.pushId;
  pushType.value = option.pushType;
  const titleList = ['满房预警', '预订价格异常', '直联渠道未打开', '新增差评', '新增疑似舞弊店', '新增解约店', '新增封存店', '预算空缺', '账单逾期', '续约提醒']
  
  uni.setNavigationBarTitle({
    title: titleList[Number(warningType.value)]
  });
})

onMounted(() => {
  setColums()
})
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20rpx 20rpx 40rpx 20rpx;
  height: 100%;
  overflow: hidden;
  
  .detail-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 40rpx;
    background: #fff;
    border-radius: 20rpx;
  }

  .detail-info {
    padding: 22rpx 20rpx;
    margin-bottom: 20rpx;
    background: #F5F5F5;
    border-radius: 20rpx;

    .info-item {
      display: block;
      font-size: 24rpx;
      font-weight: bold;
      line-height: 36rpx;
      color: #1F2428;

      .item-num {
        color: #C35943;
      }
    }
  }

  .detail-warning {
    height: 54rpx;
    margin-bottom: 20rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    background: #F5F5F5;
    border-radius: 20rpx;

    .warning-icon {
      width: 22rpx;
      height: 22rpx;
    }

    .warning-text {
      padding-left: 8rpx;
      font-size: 22rpx;
      color: rgba(31, 36, 40, 0.7);
    }
  }

  .detail-table {
    flex: 1;
    // height: 756rpx;
    border-radius: 20rpx;
    overflow: hidden;

    .operation-btn {
      color: #C35943;
    }
  }
}
</style>