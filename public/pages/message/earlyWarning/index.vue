<template>
  <view class="warning">
    <!-- <view class="warning-type">
      <cTabs :list="tabList" v-model:modelValue="tabIndex" textColor="#1F2428"></cTabs>
    </view> -->
    <view class="warning-btn">
      <view class="btn-item" :class="{ active: pushStatus === 1 }" @click="setPushStatus(1)">
        {{ `未读(${unReadCount})` }}
      </view>
      <view class="btn-item" :class="{ active: pushStatus === 2 }" @click="setPushStatus(2)">
        已读
      </view>
      <view class="btn-item" :class="{ active: pushStatus === 0 }" @click="setPushStatus(0)">
        全部
      </view>
    </view>

    <warningType @setDate="setType"></warningType>

    <z-paging ref="pagingRef" v-model="listData" :fixed="false" @query="queryList">
      <view v-for="item in listData" :key="item.pushId" class="warning-item">
        <view class="item-time">
					{{ fullDate(item.pushDate) }}
				</view>

        <view class="item-content" @click="toDetail(item)">
          <view class="item-title">
            <text class="title-text" :class="[item.pushStatus === 1 && 'item-unread']">{{ item.pushTitle }}</text>
            <up-icon v-if="item.type !== '0'" name="arrow-right" color="#C5C5C5" size="20rpx" />
            <view class="item-btn" v-else @click="toAdjustment(item)">
              <text class="btn-text">去调价</text>
              <up-icon name="arrow-right" color="#fff" size="14rpx" />
            </view>
          </view>

          <view class="item-info">
            <template v-if="item.pushType === '1'">
              <view class="info-item">
                <text class="item-label">门店：</text>
                <text class="item-value">{{ item.alarmShopList[0].shopName }}</text>
              </view>
              <view class="info-item">
                <text class="item-label">门店ID：</text>
                <text class="item-value">{{ item.alarmShopList[0].shopId }}</text>
              </view>
              <view class="info-item">
                <text class="item-label">异常规则：</text>
                <text class="item-value">{{ item.alarmRule }}</text>
              </view>
            </template>

            <template v-else>
              <view class="info-item">
                <text class="item-label">异常规则：</text>
                <text class="item-value">{{ item.alarmRule }}</text>
              </view>
              <view class="info-item">
                <text class="item-label">统计时间：</text>
                <text class="item-value">{{ fullDate(item.alarmDate) }}</text>
              </view>
              <view class="info-item">
                <text class="item-label">汇总：</text>
                <text class="item-value">{{ item.summaryDesc }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>

      <template v-slot:empty>
        <view class="empty-wraper">
          <empty></empty>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted } from 'vue';
import type zPaging from 'z-paging/components/z-paging/z-paging.vue';
import empty from '@/components/empty/empty.vue'
import warningType from './components/warningType.vue'
import { successCallbackResult } from '@/types'
import { queryPushCount, queryPushPage, queryPushDetail } from '@/public/api/earlyWarning'
import type { QueryPushPageParams, QueryPushCountRes } from '@/public/api/earlyWarning'
import { showToast, fullDate } from '@/utils/util';
import { onShow } from '@dcloudio/uni-app'
import { useCommon } from '@/hooks/useGlobalData';

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

defineOptions({
  options: {
    styleIsolation: 'shared'
  }
})

const pushStatus = ref<number>(1);
// 已读类型更改
const setPushStatus = (val: number) => {
  if (val === pushStatus.value) return
  pushStatus.value = val
  pagingRef.value.reload();
  getCount();
}

const type = ref<string>(''); // 预警类型
const setType = (item: {label: string; value: number}) => {
  type.value = (item.value - 1).toString();
  pagingRef.value.reload();
  getCount();
}

const unReadCount = ref<number>(0)
let loading = false
const getCount = () => {
  if (loading) return
  loading = true
  queryPushCount({ pushStatus: 1, pushType: type.value === '-1' ? '' : type.value }).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    unReadCount.value = res.data
  }).finally(() => {
    loading = false
  })
}

const listData = ref<Array<QueryPushCountRes>>([]);
const pagingRef = ref<InstanceType<typeof zPaging> | null>(null);
const queryList = (pageNum?: number, pageSize?: number) => {
  const params: QueryPushPageParams = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    type: type.value === '-1' ? '' : type.value,
  }
  if (pushStatus.value) params.pushStatus = pushStatus.value
  queryPushPage(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    pagingRef.value?.complete(res.data);
  })
};

const toDetail = (item: QueryPushCountRes) => {
  if (item.type === '0') return
  uni.navigateTo({
    url: '/public/pages/message/earlyWarning/detail?pushId=' + item.pushId + '&type=' + item.type + '&pushType=' + item.pushType
  })
}

const toAdjustment = (item: QueryPushCountRes) => {
  queryPushDetail({ pushId: item.pushId })
  uni.navigateTo({
    url: `/packageB/pages/scheduled-price-adjustment/scheduled-price-adjustment?shopId=${item?.alarmShopList[0].shopId}`
  })
}

getCount()

onShow(() => {
  queryList()
  getCount()
})

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
})

onUnmounted(() => {
  trackEvent('H002', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.warning {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .warning-btn {
    display: inline-flex;
    align-items: center;
    width: fit-content;
    height: 48rpx;
    margin: 40rpx 20rpx 0 20rpx;
    border: 2rpx solid #f2f2f2;
    border-radius: 24rpx;
    background: #fff;

    .btn-item {
      padding: 0 24rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: rgba(31, 36, 40, 0.7);
    }

    .active {
      border-radius: 22rpx;
      background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
      font-weight: bold;
      color: #fff;
    }
  }

  .warning-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;

    .item-time {
      height: 40rpx;
      padding: 0 27rpx;
      margin-bottom: 20rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 20rpx;

      font-size: 24rpx;
      line-height: 40rpx;
      color: #fff;
    }

    .item-content {
      width: 100%;
      padding: 40rpx 40rpx 30rpx 40rpx;
      background: #fff;
      border-radius: 20rpx;

      .item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 40rpx;
        border-bottom: 2rpx solid #F2F2F2;

        .title-text {
          padding-right: 24rpx;
          font-size: 32rpx;
          font-weight: bold;
          line-height: 32rpx;
          color: #1F2428;
        }

        .item-btn {
          display: flex;
          align-items: center;
          padding: 0 28rpx;
          border-radius: 24rpx;
          background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);

          .btn-text {
            padding-right: 8rpx;
            font-size: 22rpx;
            font-weight: bold;
            color: #fff;
            line-height: 48rpx;
          }
        }

        .item-unread {
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background: #FF4D4D;
          }
        }
      }

      .item-info {
        padding-top: 8rpx;

        .info-item {
          display: flex;
          align-items: flex-start;
          padding-top: 22rpx;

          .item-label {
            width: 140rpx;
            font-size: 28rpx;
            line-height: 48rpx;
            color: rgba(31, 36, 40, 0.7);
          }

          .item-value {
            flex: 1;
            font-size: 28rpx;
            line-height: 48rpx;
            // font-weight: bold;
            color: #1F2428;
          }
        }
      }
    }
  }

  .empty-wraper {
    width: 100%;
    height: 392rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 24rpx;
  }

  z-paging {
    flex: 1;
    margin: 0 20rpx 40rpx 20rpx;

    .zp-l-text-rpx {
      font-size: 22rpx !important;
    }

    .zp-empty-view {
      flex: none;
    }
  }
}
</style>
