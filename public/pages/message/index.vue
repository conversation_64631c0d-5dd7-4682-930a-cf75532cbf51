<template>
	<view class="message-page">
    <view class="message-tabs">
      <cTabs :list="tabList" v-model:modelValue="tabIndex" textColor="#1F2428" @change="handleTabChange"></cTabs>
    </view>

    <earlyWarning v-if="tabIndex === 0"></earlyWarning>
    <messageCenter v-if="tabIndex === 1"></messageCenter>
    <cWaterMark></cWaterMark>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance, ComponentInternalInstance } from 'vue';
import cTabs from '@/components/c-tabs2/index.vue'
import earlyWarning from './earlyWarning/index.vue'
import messageCenter from './message-center/message-center.vue'
import { useCommon } from '@/hooks/useGlobalData';
const { trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)
  
// 响应式数据  
const tabList = reactive([  
  { name: '监控预警' },  
  { name: '其他' },  
]);
const tabIndex = ref<number>(0)

const handleTabChange = () => {
	if (tabIndex.value === 1 ) {
		trackEvent('002')
	}
}
</script>

<style lang="scss">
.message-page {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .message-tabs {
    padding-left: 20rpx;
    background: #fff;
    border-radius: 0 0 20rpx 20rpx;
  }

	early-warning {
    flex: 1;
    overflow: hidden;
  }

  message-center {
    flex: 1;
    overflow: auto;
  }
}
</style>