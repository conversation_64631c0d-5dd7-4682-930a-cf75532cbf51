<template>
	<web-view v-if="type == 2" :src="remoteUrl"></web-view>
	<layoutVue class="layout" v-if="type == 1">
		<view class="container" v-if="list.length">
			<view class="education-box">
				<swiper class="swiper-box" :style="{height:imagesHeight[current-1] + 'rpx'}" :current="current - 1"
					@change="onChange">
					<swiper-item v-for="(item, index) in list" :key="index">
						<image :src="item" :id="index" class="swiper-item-image" mode="widthFix" @load="loadImage">
						</image>
					</swiper-item>
				</swiper>

			</view>
			<view class="footer-box" v-if="list.length && current <= list.length - 1">
				<view class="footer-left">
					<view class="study-text">学习进度：</view>
					<view class="complete-text">{{ current }}</view>
					<view class="complete-slash">/</view>
					<view class="complete-total">{{ list.length }}</view>
				</view>
				<view class="footer-right">
					<view class="operation-menu" v-if="current != 1" @click="prevPage">上一页</view>
					<view class="operation-menu" v-if="current <= list.length - 1" @click="nextPage">下一页</view>
				</view>
			</view>
		</view>
		<emptyVue v-else />
	</layoutVue>
  <view v-if="type == 4">
    <video id="myVideo" style="width: 100vw;" :src="remoteUrl"></video>
  </view>
	<!-- <cWaterMark></cWaterMark> -->
</template>

<script setup lang="ts">
	import { onLoad, onShow, onUnload } from '@dcloudio/uni-app'
  import { nextTick, Ref, ref, unref } from 'vue'
	import { queryMessageDetail, readMessage } from '@/public/api/message'
	import { successCallbackResult } from '@/types/index.d';
	import { hideLoading, showLoading, showToast } from '@/utils/util';
	import emptyVue from '@/components/empty/empty.vue';
	const imagesHeight : Ref<Array<number>> = ref([]),
		current : Ref<number> = ref(1),
		list : Ref<[]> = ref([]),
		type = ref(''),
		remoteUrl = ref('')


  let videoContext = null
	let downFileTask = null
	const loadImage = (e : any) : void => {
		const imgWidth = e.detail.width,
			imgHeight = e.detail.height,
			ratio = imgWidth / imgHeight;
		const viewHeight = 750 / ratio;
		imagesHeight.value[e.target.id] = viewHeight
	}
	const prevPage = () : void => {
		current.value = unref(current) - 1
	}
	const nextPage = () : void => {
		current.value = unref(current) + 1
	}

	const onChange = (e) => {
		current.value = e.detail.current + 1
		if (current.value == unref(list).length) {
			showToast('已学习完')
		}
	}

	const toQueryMessageDetail = async (messageId : string) => {
		try {
			const res : successCallbackResult = await queryMessageDetail({ messageId })
			if (res.data) {
				list.value = res.data.imgList
			}
		} catch (e) {
			console.error('获取消息详情接口报错', e)
		}
	}
	const toReadMessage = async (messageId : string | number) => {
		try {
			await readMessage({ messageId })
		} catch (e) {
			console.log('获取消息详情接口报错', e)
		}
	}

	const handlePDF = () => {
		showLoading()
		downFileTask = uni.downloadFile({
			url: unref(remoteUrl),
			success(res) {
				hideLoading()
				uni.openDocument({
					filePath: res.tempFilePath,
					success() {
						uni.setStorageSync('open', 'complete')
					}
				})
			}
		})
	}

	onLoad(async (opts) => {
		const { messageId, readStatus, type: fileType, url } = opts
		type.value = fileType
		remoteUrl.value = url
		if (fileType == 1) {
			await toQueryMessageDetail(messageId)
		}
		if (fileType == 3) {
			handlePDF()
		}
		if (readStatus == 0) {
			toReadMessage(messageId)
		}
    await nextTick()
    if(fileType == 4) {
      let timer = setTimeout(() => {
        clearTimeout(timer)
        timer = null
        videoContext = uni.createVideoContext('myVideo')
        videoContext.requestFullScreen({
          direction: 0
        })
      }, 500)
    }
	})

	onShow(() => {
		if (unref(type) == '3') {
			const isOpenPDF = uni.getStorageSync('open')
			if (isOpenPDF == 'complete') {
				uni.navigateBack({
					success() {
						uni.removeStorageSync('open')
					}
				})
			}
		}
	})
	onUnload(() => {
		if (downFileTask) {
			downFileTask.abort()
		}
	})
</script>

<style lang="scss" scoped>
	.layout {
		:deep() {
			.empty-box {
				margin-top: 50%;
			}
		}
	}

	.education-box {
		position: relative;
		width: 100%;
		height: auto;
		padding-bottom: 106rpx;
		box-sizing: border-box;
	}

	.swiper-box {
		width: 100%;
	}

	.swiper-item-image {
		width: 100%;
		height: auto;
		display: block;
	}

	.footer-box {
		background: #fff;
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx 0 42rpx;
		box-sizing: border-box;
	}

	.footer-left {
		display: flex;
		align-items: center;
		padding-left: 32rpx;
	}

	.study-text {
		font-size: 24rpx;
		font-weight: 500;
		color: $uni-text-color;
		line-height: 32rpx;
	}

	.complete-text {
		font-size: 24rpx;
		font-weight: 400;
		color: $uni-text-color;
		line-height: 32rpx;
	}

	.complete-slash {
		font-size: 24rpx;
		font-weight: 400;
		color: #040000;
		line-height: 32rpx;
		padding: 0 8rpx;
	}

	.complete-total {
		font-size: 24rpx;
		font-weight: 400;
		color: #040000;
		line-height: 32rpx;
	}

	.footer-right {
		display: flex;
		align-items: center;
		padding-right: 32rpx;
	}

	.operation-menu {
		padding-left: 24rpx;
		font-size: 24rpx;
		font-weight: 400;
		color: #040000;
		line-height: 32rpx;
	}

	.menu-group {
		position: absolute;
		bottom: 511rpx;
		left: 50%;
		transform: translateX(-50%);
		box-sizing: border-box;
	}
</style>