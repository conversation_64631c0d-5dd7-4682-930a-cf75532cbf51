<template>
	<layoutVue>
		<view class="container fl">
			<view class="fl" v-for="(item,index) in list" :key="index">
				<view class="time">
					{{ item.sendTime }}
				</view>
				<view class="message fl"
					@click="route_to_view(`/public/pages/message/message-center/detail?messageId=${item.messageId}&readStatus=${item.readStatus}&type=${item.type}&url=${item.remoteUrl}`)">
					<image class="banner"
						src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-22/1729589859121.png"
						mode=""></image>
					<view class="title fr">
						<view class="spot" v-if="!item.readStatus"></view>
						<text class="text">{{ item.title }}</text>
					</view>
					<text class="desc">{{ item.describe }}</text>
				</view>
			</view>
		</view>
	</layoutVue>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, getCurrentInstance, ref } from 'vue';
	import { queryMessageList } from '@/public/api/message';
	import { useCommon } from '@/hooks/useGlobalData';
	const { route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	
	const list = ref([])

	// 获取消息列表
	const toQueryMessageList = async () => {
		try {
			const { code, data } = await queryMessageList({})
			if (code == 200) list.value = data
		} catch (e) {
			console.error('获取消息列表 error', e)
		}
	}

	toQueryMessageList()
</script>

<style scoped lang="scss">
	.container {
		align-items: center;
		padding: 0 20rpx;
	}

	.time {
		border-radius: 116rpx;
		background: #00000066;
		color: #ffffff;
		font-size: 24rpx;
		text-align: center;
		line-height: 24rpx;
		padding: 8rpx 32rpx;
		margin: 20rpx auto;
	}

	.message {
		border-radius: 20rpx;
		background: #ffffff;
		padding: 40rpx;

		.banner {
			width: 630rpx;
			height: 160rpx;
			flex-shrink: 0;
			margin-bottom: 40rpx;
		}

		.title {
			margin-bottom: 20rpx;

			.spot {
				width: 14rpx;
				height: 14rpx;
				border-radius: 200rpx;
				background: #ff4d4d;
				margin-right: 16rpx;
			}

			.text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}
		}

		.desc {
			color: #1f2428b3;
			font-size: 24rpx;
			line-height: 36rpx;
		}
	}
</style>