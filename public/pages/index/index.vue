<template>
	<view class="index">
		<homeVue ref="homeRef" v-if="activeName == 'home'" :tabbarHeight="tabbarHeight" @updateActiveName="change" />
		<dataVue ref="dataRef" v-if="activeName == 'dataCockpit'" :tabbarHeight=" tabbarHeight" :tabName="tabName"></dataVue>
		<workBenchVue ref="workbenchRef" v-if="activeName == 'workbench'" :tabbarHeight=" tabbarHeight"></workBenchVue>
		<myVue ref="myVue" v-if="activeName == 'my'" :tabbarHeight=" tabbarHeight" />
		<cTabBarVue :model="activeName" @change="change" ref="cTabbar" />
    <cWaterMark></cWaterMark>
	</view>
</template>

<script setup lang="ts">
	import cTabBarVue from '@/components/c-tab-bar/c-tab-bar.vue';
	import { onLoad, onShow, onUnload, onHide } from '@dcloudio/uni-app'
	import { ComponentInternalInstance, computed, getCurrentInstance, nextTick, provide, ref, watch } from 'vue'
	import { useCommon } from '@/hooks/useGlobalData'
	import { newPageInstance } from '@/types/index.d';
	import homeVue from '../components/home/<USER>';
	import myVue from '../components/my/my.vue';
	import dataVue from '../components/dataCockpit/dataCockpit.vue'
	import workBenchVue from '../components/work-bench/work-bench.vue';

	const activeName = ref(''),
		cTabbar = ref(null),
		homeRef = ref(null),
		aiRef = ref(null),
		workbenchRef = ref<InstanceType<typeof workBenchVue>>(null),
		tabName = ref('assets'),
		{ trackEvent, route_to_view } = useCommon(getCurrentInstance() as ComponentInternalInstance),
		tabbarHeight = ref(0)
	const change = (value : string, query ?: any) => {
		nextTick(() => {
			if (value == 'ai') {
					trackEvent('016')
				if (query) {
					route_to_view('/ai/pages/index/index?query=' + encodeURIComponent(query))
				} else {
					route_to_view('/ai/pages/index/index')
				}
				return
			} else if (query && value === 'dataCockpit') {
				tabName.value = query
				trackEvent('D000')
			} else if (value === 'my') {
				trackEvent('000')
			} else if (value === 'home') {
				trackEvent('H000')
			}
			activeName.value = value
		})
	}

  const pageOptions = ref()
  provide('pageOptions', pageOptions) // ai助手跳转携带参数
  const dataRef = ref(null)

	onLoad(() => {
    activeName.value = 'home'
    trackEvent('H000')
	})

	onShow(() => {
		const pages = getCurrentPages()
		const curPage : newPageInstance = pages[pages.length - 1]
		if (curPage && curPage.route == 'public/pages/index/index' && curPage.pageOptions) {
      const options = JSON.parse(JSON.stringify(curPage.pageOptions))
      pageOptions.value = options
      nextTick(()=> {
        if (options.activeName) {
          activeName.value = options.activeName
          if (options.tabName) {
            tabName.value = options.tabName
            dataRef.value.setTab(options.tabName)
          }
        }
      })
      nextTick(()=>{
        switch (options.tabName) {
          case 'operations':
            uni.$emit('operationsRefresh')
            break
          case 'member':
            uni.$emit('memberRefresh')
            break
          case 'monitor':
            uni.$emit('monitorRefresh')
            break
        }
      })
			delete curPage.pageOptions
		}
    uni.$emit('refresh')
		if (activeName.value == 'ai') {
			nextTick(() => {
				aiRef.value.webScoketInit()
			})
		}

		if (activeName.value == 'workbench') {
			nextTick(() => {
				workbenchRef.value.getWaringInfo()
				workbenchRef.value.getListInItinerary()
				workbenchRef.value.getListOfStoreInspectionReviews()
			})
		}
		if (activeName.value == 'home') {
			nextTick(() => {
				homeRef.value?.getCount()
			})
		}
	})

	onHide(() => {
		if (activeName.value == 'ai') {
			nextTick(() => {
				if (aiRef.value.socketTask) {
					aiRef.value.socketTask.close()
					aiRef.value.socketTask = null
				}
			})
		}
	})
	onUnload(() => {
    uni.$off('refresh')
	})
</script>

<style lang="scss">
	.index {
		width: 100%;
		height: 100%;
	}

	:deep(.home) {
		.u-notice-bar {
			padding: 0 !important;
		}

		.uni-tooltip-popup {
			width: 300rpx;
		}
	}

	:deep(.ui-area-picker) {
		.u-picker__view__column__item {
			font-size: 20rpx;
		}
	}
</style>