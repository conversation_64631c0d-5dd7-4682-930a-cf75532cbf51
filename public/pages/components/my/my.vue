<template>
	<view class="container">
		<view class="bg"></view>
		<u-navbar placeholder title="我的" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700;">
			<template #left></template>
		</u-navbar>
		<userInfoVue />
		<assetsVue />
		<view class="banner">
			<swiper class="swiper" circular>
				<swiper-item class="swiper-item" v-for="(item,index) in adList" :key="index">
					<image :src="item.bannerImg" mode="" @click="handleImageClick(item)"></image>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, getCurrentInstance, onMounted, onUnmounted, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import userInfoVue from './components/user-info/user-info.vue';
	import assetsVue from './components/assets/assets.vue';
	import { listHomePageBanner } from '@/api';
	const { route_to_view, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const adList = ref([])

	onMounted(() => {
		uni.$on('refresh', () => {
			toListHomePageBanner()
		})
		toListHomePageBanner()
	})

	// 获取banner
	const toListHomePageBanner = async () => {
		try {
			const { data } = await listHomePageBanner({})
			if (data) adList.value = data
		} catch (e) {
			console.error('获取banner error', e)
		}
	}

	const handleImageClick = (item : any) => {
		trackEvent('004')
		if (item.bannerType == 2) {
			route_to_view(`/public/pages/webview/webview?url=${(encodeURIComponent(item.bannerUrl))}`)
		} else if (item.bannerType == 1) {
			uni.navigateToMiniProgram({
				appId: item.appId,
				envVersion: 'release',
				path: item.appPath,
				success(res) {
					console.log(res);
				},
				fail(err) {
					console.log(err);
				}
			})
		}
	}

	onUnmounted(() => { //为了避免重复监听，在组件销毁前，关闭监听
		uni.$off('refresh');
	})
</script>

<style scoped lang="scss">
	.container {}

	.bg {
		width: 100%;
		height: 510rpx;
		position: absolute;
		background: linear-gradient(35.7deg, rgb(247, 222, 215) 0%, rgb(251, 242, 240) 52%, rgb(251, 242, 240) 100%);
		border-radius: 0 0 120rpx 0;
		z-index: -1;
	}

	.banner {
		width: 710rpx;
		height: 160rpx;
		border-radius: 20rpx;
		margin: 20rpx;

		.swiper {
			width: 100%;
			height: 100%;

			.swiper-item {
				border-radius: 20rpx;
			}

			image {
				width: 100%;
				height: 100%;
			}
		}
	}
</style>