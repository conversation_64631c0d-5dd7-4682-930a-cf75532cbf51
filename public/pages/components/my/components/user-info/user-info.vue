<template>
	<view class="container fr">
		<view class="fr user">
			<image class="img" :src="globalData.user.avatar" mode=""></image>
			<view class="fl info">
				<view class="name">{{globalData.user.userName}}</view>
				<view class="fr">
					<view class="id">员工ID：{{globalData.user.userId}}</view>
					<view class="permission fr"
						@click="route_to_view('/packageB/pages/my-permissions/my-permissions',()=>{trackEvent('001')})">
						<image class="lock" src="/static/images/lock.png" mode=""></image>
						<view style="margin-right: 8rpx;">
							我的权限</view>
						<up-icon name="arrow-right" color="#000" size="20rpx" />
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="message"
			@click="route_to_view('/packageB/pages/message-center/message-center',()=>{trackEvent('002')})">
			<image class="img" src="/static/images/message.png" mode=""></image>
			<view class="spot" v-if="messageCount > 0">{{ messageCount }}</view>
		</view> -->
	</view>
</template>

<script setup lang="ts">
	import { getCurrentInstance, ComponentInternalInstance, onMounted, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { queryUnReadMessageCount } from '@/api';
	const { globalData, route_to_view, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

	const messageCount = ref()

	onMounted(() => {
		uni.$on('refresh', () => {
			toQueryUnReadMessageCount()
		})
		toQueryUnReadMessageCount()
	})

	// 获取未读消息数
	const toQueryUnReadMessageCount = async () => {
		try {
			const { code, data } = await queryUnReadMessageCount({})
			if (code == 200) messageCount.value = data.count
		} catch (e) {
			console.error('获取未读消息数 error', e)
		}
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 50rpx 20rpx 0;
		justify-content: space-between;
		align-items: flex-start;
	}

	.user {
		.img {
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			flex-shrink: 0;
		}

		.info {
			margin-left: 40rpx;

			.name {
				color: #1f2428;
				font-size: 36rpx;
				font-weight: bold;
				line-height: 36rpx;
				margin-bottom: 28rpx;
			}

			.id {
				color: #1f2428;
				font-size: 24rpx;
				line-height: 24rpx;
			}

			.permission {
				color: #1f2428;
				font-size: 22rpx;
				line-height: 22rpx;
				width: 168rpx;
				height: 38rpx;
				border-radius: 200rpx;
				background: linear-gradient(90deg, #ffffff 0%, #ffffff4d 100%);
				margin-left: 28rpx;
				justify-content: center;

				.lock {
					width: 24rpx;
					height: 24rpx;
					flex-shrink: 0;
					margin-right: 8rpx;
				}

				.right {
					width: 8rpx;
					height: 14.4rpx;
					flex-shrink: 0;
					margin-left: 8rpx;
				}
			}
		}
	}

	.message {
		margin-right: 12rpx;
		margin-top: 8rpx;
		position: relative;

		.img {
			width: 48rpx;
			height: 48rpx;
			flex-shrink: 0;
		}

		.spot {
			border-radius: 32rpx;
			opacity: 1;
			border: 2rpx solid #ffffff;
			background: #ff4d4d;
			position: absolute;
			top: -8rpx;
			right: -12rpx;
			color: #ffffff;
			font-size: 22rpx;
			font-weight: 700;
			font-family: "DIN Bold";
			line-height: 22rpx;
			width: 32rpx;
			height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: content-box;
		}
	}
</style>