<template>
  <view
    class="fr section"
    @click="route_to_view('/packageB/pages/manage-assets/manage-assets',()=>{trackEvent('003')})">
    <view class="fr block">
      <image class="img" src="/static/images/store.png" mode=""></image>
      <text class="text">门店</text>
    </view>
    <view class="fr block">
      <image class="img" src="/static/images/manager.png" mode=""></image>
      <text class="text">店长</text>
    </view>
    <!--<view class="fr block">
      <image class="img" src="/static/images/reception.png" mode=""></image>
      <text class="text">前台</text>
    </view>-->
  </view>
</template>

<script setup lang="ts">
  import { getCurrentInstance, ComponentInternalInstance } from 'vue';
  import { useCommon } from '@/hooks/useGlobalData';

  const {route_to_view, trackEvent} = useCommon(getCurrentInstance() as ComponentInternalInstance)
</script>

<style lang="scss" scoped>
  .section {
    border-radius: 20rpx;
    opacity: 1;
    border: 2rpx solid #ffffff;
    background: #ffffff4d;
    padding: 40rpx;
    justify-content: space-between;
    margin: 40rpx 20rpx 0;
  }

  .block {
    width: 305rpx;
    height: 126rpx;
    border-radius: 20rpx;
    border: 2rpx solid #f2f2f2;
    background: #ffffff;
    justify-content: center;

    .img {
      width: 70rpx;
      height: 70rpx;
      flex-shrink: 0;
    }

    .text {
      color: #1f2428;
      font-size: 28rpx;
      font-weight: bold;
      line-height: 28rpx;
      margin-left: 14rpx;
    }
  }
</style>