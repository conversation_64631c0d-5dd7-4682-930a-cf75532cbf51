<template>
	<view class="home">
		<view class="home-bg"></view>
		<u-navbar placeholder title="首页" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;">
			<template #left></template>
		</u-navbar>

		<view class="home-header">
			<view class="header-left">
				<image class="left-img" :src="userInfo.avatar" mode="aspectFill" @tap="(env !== 'release' && env !== 'trial') ? handleSwitchEnv() : () => {}"></image>

				<view class="left-info">
					<text class="info-name">{{ userInfo.userId }}-{{ userInfo.userName }}</text>
					<text class="info-role">{{ userInfo.roleName }}</text>
				</view>
			</view>

			<view class="header-right">
				<view class="right-warning" @click="toWarning">
					<image class="right-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-20/1732088536999.png" mode="aspectFill"></image>
					<text class="right-text" v-if="unReadCount">{{ unReadCount }}</text>
				</view>
				<view class="right-area" @click="areaShow = true">
					<image class="right-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644619014.png" mode="aspectFill"></image>
					<text class="right-text">{{ chooseArea.deptName }}</text>
				</view>
			</view>
		</view>

		<view class="home-swiper">
			<up-swiper
				:list="swiperList"
				:autoplay="true"
				height="160rpx"
				:interval="5000"
				@change="(e: any) => current = e.current"
				@click="toActivity"
			>
				<template #indicator>
					<view
						class="indicator"
					>
						<view
							v-for="(item, index) in swiperList"
							:key="index"
							class="indicator__dot"
							:class="[index === current && 'indicator__dot--active']"
						/>
					</view>
				</template>
			</up-swiper>
		</view>

		<view class="scroller-wraper">
			<view class="home-notice" @click="toBulletin">
				<image class="notice-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644788042.png" mode="aspectFill"></image>
				<image class="notice-img" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644802447.png" mode="aspectFill"></image>
				<view class="notice-wrap">
					<up-notice-bar :text="noticeList" icon="" :disableTouch="false" direction="column" duration="3000"  bgColor="transparent" color="#000" fontSize="22rpx"></up-notice-bar>
					<view class="notice-up"></view>
				</view>
			</view>

			<view class="home-king">
				<view class="king-item" v-for="item in kingList" :key="item.name" @click="handleClickKing(item)">
					<image class="king-img" :src="item.url" mode="aspectFill"></image>
					<text class="king-text">{{ item.name }}</text>
					<text class="king-num" v-if="item.num">{{ item.num }}</text>
				</view>
			</view>

			<view class="home-assets">
				<view class="assets-title" @click="toDataCockpit('assets', 1)">资产</view>
				<view class="assets-top">
					<template v-for="(item, index) in assetsList" :key="index">
						<view class="top-item" :class="[item.prop === 'totalShop' ? 'shop-item' : 'room-item']" :key="item.prop" v-if="index < 2">
							<view class="item-title">
								<image v-if="index === 0" class="title-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644647957.png" mode="aspectFill"></image>
								<image v-else class="title-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644681273.png" mode="aspectFill"></image>
								<text class="title-text">{{ item.name }}</text>
								<image class="title-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644695365.png" mode="aspectFill" @click="showModal(item.name, item.info)"></image>
							</view>

							<text class="item-num">{{ queryPropertyRes?.[`${item.prop}Count`] }}</text>

							<view class="item-info">
								<text class="info-name">距上月末</text>
								<image class="info-icon" v-if="queryPropertyRes?.[`${item.prop}CountCompare`]" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${queryPropertyRes[`${item.prop}CountCompare`] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
								<text class="info-num" :class="[ queryPropertyRes?.[`${item.prop}CountCompare`] > 0 ? 'num-up' : queryPropertyRes?.[`${item.prop}CountCompare`] < 0 ? 'num-down' : '']">{{ queryPropertyRes?.[`${item.prop}CountCompare`] !== null ? Math.abs(queryPropertyRes?.[`${item.prop}CountCompare`]) : '-' }}</text>
							</view>
						</view>
					</template>
				</view>

				<view class="assets-bottom">
					<template v-for="(item, index) in assetsList" :key="index">
						<view class="bottom-item" :key="item.name" v-if="index > 1">
							<view class="item-title">
								<text class="title-text">{{ item.name }}</text>
								<image class="title-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill" @click="showModal(item.name, item.info)"></image>
							</view>

							<text class="item-num">{{ queryPropertyRes?.[`${item.prop}Count`] }}</text>

							<view class="item-info">
								<text class="info-name">距上月末</text>
								<image v-if="queryPropertyRes?.[`${item.prop}CountCompare`]" class="info-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${queryPropertyRes[`${item.prop}CountCompare`] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
								<text class="info-num" :class="[ queryPropertyRes?.[`${item.prop}CountCompare`] > 0 ? 'info-num-up' : queryPropertyRes?.[`${item.prop}CountCompare`] < 0 ? 'info-num-down' : '']">{{ queryPropertyRes?.[`${item.prop}CountCompare`] !== null ? Math.abs(queryPropertyRes?.[`${item.prop}CountCompare`]) : '-' }}</text>
							</view>
						</view>
					</template>
				</view>
			</view>

			<view class="home-key">
				<view class="key-title">
					<text class="title-text">关键指标概览</text>
					<text class="subtitle-text">本月</text>
				</view>

				<view class="key-content">
					<view class="key-item" v-for="item in queryIndexDataRes" :key="item.dataName" @click="toDataCockpit(item.jumpUrl)">
						<view class="item-title">
							<text class="title-name">{{ item.dataName }}/</text>
							<text class="title-unit">{{ item.unit }}</text>
							<image class="title-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill" @click.stop="showModal(item.dataName, item.dataDesc)"></image>
						</view>

						<view class="item-chart">
							<view class="chart-progress" :style="{ background: `conic-gradient(#FFB2A1 0, #F75E3B ${Math.min(100, item.valueCompletion) || 0}%, #FFF3F0 ${Math.min(100, item.valueCompletion) || 0}%, #FFF3F0)` }"></view>
							<view class="circle-start" />
							<view class="circle-end" :style="{ transform: `rotate(${(360 * (Math.min(99.5, item.valueCompletion) || 0)) / 100 - 180}deg)` }">
								<view class="end-point"></view>
							</view>

							<view class="chart-info">
								<view class="info-title">
									{{ item.actualValue?.toString().split('.')[0].length >= 6 ? parseInt(item.actualValue) : item.actualValue || '-' }}
								</view>
								<view class="info-num">
									<text class="num">
										{{ item.valueCompletion || '-' }}
									</text>
									<text class="symbol">
										%
									</text>
								</view>
							</view>
						</view>
						
						<view class="item-info-wraper">
							<text class="item-info">同比：{{ item.valueTb || '-' }}% </text>
							<text class="item-info">环比：{{ item.valueHb || '-' }}% </text>
						</view>
					</view>
				</view>

				<view class="key-info">
					<image class="info-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
					<text class="info-text">提醒：经营收入、净利润率为预测值，月初到结账前无数据。</text>
				</view>
			</view>
		</view>

		<up-picker :show="areaShow" :columns="areaList" keyName="deptName" :itemHeight="50" @confirm="handleConfirm" @cancel="areaShow = false" />
		<cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, getCurrentInstance, ComponentInternalInstance } from 'vue';
import cModal from '@/components/c-modal/index'
import type { Assets } from '@/types/home.ts'
import { useCommon } from '@/hooks/useGlobalData';
import { queryProperty, queryIndexData } from '@/public/api/home'
import type { QueryPropertyParams, QueryPropertyRes, QueryIndexDataRes } from '@/public/api/home'
import { queryPushCount } from '@/public/api/earlyWarning'
import { queryUnReadMessageCount } from '@/api';
import { showToast } from '@/utils/util';
import { successCallbackResult } from '@/types'
import { getList } from '@/public/api/bulletin'
import type { ListRes } from '@/packageA/api/bulletin'
import { env, switchEnv } from "@/utils/switchEnv";

const emit = defineEmits(['updateActiveName']);
const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)
const	{ trackEvent, qdTrackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

defineOptions({
	options: {
		styleIsolation: 'shared'
	}
})

const unReadCount = ref<number>(0)
const getCount = () => {
	unReadCount.value = 0
	queryPushCount({ pushStatus: 1, pushType: '' }).then((res: successCallbackResult) => {
		if (res.code !== 200) {
			showToast(res.message || '系统异常，请稍后重试')
			return
		}
		unReadCount.value += res.data
	});
	queryUnReadMessageCount({}).then((res: successCallbackResult) => {
		if (res.code !== 200) {
			showToast(res.message || '系统异常，请稍后重试')
			return
		}
		unReadCount.value += res.data.count
	});
}
const toWarning = () => {
	uni.navigateTo({
		url: '/public/pages/message/index'
	})
	trackEvent('H001')
}

const swiperList = ref<Array<string>>([
	'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-06-17/1750123804657.png',
	'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-07-01/1751360455786.png'
]);
const current = ref<number>(0);
const toActivity = () => {
	if (current.value === 0) {
    qdTrackEvent('homepage_module_click', {
      page_id: 'smart_ops_homepage',
      title: '首页',
      module_name: '顶部banner',
      content_name: '经营提升争霸赛',
      sort_no: 1
    })
		uni.navigateTo({
			url: '/packageA/pages/activity/operation?orgId=' + chooseArea.value.orgId + '&level=' + chooseArea.value.level
		})
	} else {
		uni.navigateTo({
			url: '/packageA/pages/activity/honor?orgId=' + chooseArea.value.orgId + '&level=' + chooseArea.value.level
		})
	}
	trackEvent('H003')
}

const noticeList = ref<Array<string>>([]);
const getNoticeList = () => {
	getList({ title: '', pageNum: 1, pageSize: 5 }).then((res: successCallbackResult) => {
		if (res.code !== 200) {
			showToast(res.message || '系统异常，请稍后重试')
			return;
		}
		noticeList.value = res.data.map((item: ListRes) => item.title)
	});
}
const toBulletin = () => {
	uni.navigateTo({
		url: '/packageA/pages/bulletin/index'
	})
}

// 金刚区
const kingList = ref<Array<{id: number, name: string, num?: number, url: string}>>([
	{
		name: '超利分享',
		id: 1,
		url: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644884720.png'
	},
	{
		name: '巡店',
		id: 2,
		url: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644904498.png'
	},
	// {
	// 	name: 'AI问答',
	// 	id: 3,
	// 	url: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644917399.png'
	// },
	{
		name: '智能调价',
		id: 4,
		num: 0,
		url: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-11-20/1732088552693.png'
	},
	{
		name: '门店档案',
		id: 5,
		url: 'https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644942907.png'
	}
])
// 金刚区点击事件
const handleClickKing = (item: {id: number, name: string, num?: number, url: string}) => {
	console.log('00000', item)
	if (item.id === 2 && globalData.value.user.gray) {
		uni.navigateTo({
			url: '/packageB/pages/shop-inspection/shop-inspection-plan-list'
		})
	}
	if (item.id === 4) {
		uni.navigateTo({
			url: '/packageB/pages/select-store/select-store?from=scheduled-price-adjustment'
		})
	}
	if (item.id === 5) {
		uni.navigateTo({
			url: '/packageB/pages/select-store/select-store?from=file'
		})
		trackEvent('H009')
	} else if (item.id === 1) {
		trackEvent('H006')
		showToast('努力开发中，敬请期待')
	} else if (item.id === 2) {
		trackEvent('H007')
	} else if (item.id === 4) {
		trackEvent('H008')
	}
}

// 资产
const assetsList = ref<Array<Assets>>([
	{
		name: '门店数量',
		info: '合同存续门店（不包含解约）剔除封存门店的数量',
		prop: 'totalShop'
	},
	{
		name: '房间数量',
		info: '合同存续门店（不包含解约）剔除封存门店的房间总量',
		prop: 'room'
	},
	{
		name: '运营店',
		info: '在运营的门店数量',
		prop: 'operateShop'
	},
	{
		name: '零售卡店',
		info: '从本月截止到目前为止售卡数量为0的门店',
		prop: 'zeroCardShop'
	},
	{
		name: '舞弊店',
		info: '疑似舞弊门店的数量',
		prop: 'owlShop'
	},
	{
		name: '问题店',
		info: '管理费欠款金额大于1000元的门店数量（月初更新）',
		prop: 'oweShop'
	}
])

const areaShow = ref<boolean>(false)
const areaList = reactive<Array<Array<{deptId: number, deptName: string}>>>([userInfo.value.orgList])
const chooseArea = ref<{orgId: number, deptName: string, level: string}>(userInfo.value.orgList[0])
const handleConfirm = (e: any) => {
	areaShow.value = false;
	if (e.value[0].orgId !== chooseArea.value.orgId) {
		chooseArea.value = e.value[0]
		getData()
	}
};

const modalShow = ref<boolean>(false)
const modalTitle = ref<string>('')
const modalContent = ref<string>('')
const showModal = (title: string, content: string) => {
	modalShow.value = true
	modalTitle.value = title
	modalContent.value = content
}

const queryPropertyRes = ref<Array<QueryPropertyRes>>()
const queryIndexDataRes = ref<Array<QueryIndexDataRes>>()
const getData = () => {
	const params: QueryPropertyParams = {
		orgId: chooseArea.value.orgId,
		level: chooseArea.value.level
	}
	queryProperty(params).then((res: successCallbackResult) => {
		if (res.code !== 200) {
			showToast(res.message || '系统异常，请稍后重试')
			return
		}

		queryPropertyRes.value = res.data
	})

	queryIndexData(params).then((res: successCallbackResult) => {
		if (res.code !== 200) {
			showToast(res.message || '系统异常，请稍后重试')
			return
		}

		queryIndexDataRes.value = res.data
	})

	getNoticeList()
	getCount()
}

const toDataCockpit = (name: string, type: number) => {
	emit('updateActiveName', 'dataCockpit', name)
	if (type) {
		trackEvent('H011')
	} else {
		trackEvent('H012')

	}
}

const handleSwitchEnv = () => {
	if (env !== 'release' && env !== 'trial') {
		switchEnv();
	}
}

getData()

defineExpose({
	getCount
})
</script>

<style lang="scss">
.home {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.home-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 470rpx;
		border-radius: 0 0 120rpx 0;
		background: linear-gradient(52.4deg, #ff5733 0%, #c35943 31%, #c35943 79%, #f75735 100%);
	}

	.home-header {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		padding: 24rpx 40rpx 60rpx 40rpx;

		.header-left {
			display: flex;
			align-items: center;

			.left-img {
				width: 140rpx;
				height: 140rpx;
				border-radius: 50%;
				overflow: hidden;
				background: red;
        flex-shrink: 0;
			}

			.left-info {
				padding: 24rpx 40rpx;

				.info-name {
					padding-bottom: 16rpx;
					display: block;
					font-size: 36rpx;
					line-height: 36rpx;
					color: #fff;
					font-weight: bold;
				}

				.info-role {
					display: inline-block;
					padding: 0 16rpx;
					font-size: 22rpx;
					line-height: 36rpx;
					color: #fff;
					font-weight: bold;
					border: 2rpx solid #ffffff;
					border-radius: 20rpx;
					background: #ffffff33;
				}
			}
		}

		.header-right {
			height: 100%;
			display: flex;
			align-items: flex-end;
			flex-direction: column;
			justify-content: space-between;
			padding-bottom: 38rpx;

			.right-warning {
				position: relative;
				width: 48rpx;
				height: 48rpx;

				.right-icon {
					position: relative;
					width: 48rpx;
					height: 48rpx;
          flex-shrink: 0;
				}

				.right-text {
					position: absolute;
					top: -10rpx;
					right: -14rpx;

					width: 36rpx;
					height: 36rpx;
					border-radius: 18rpx;
					opacity: 1;
					border: 2rpx solid #ffffff;
					background: #ff4d4d;

					font-size: 22rpx;
					line-height: 32rpx;
					text-align: center;
					font-family: 'DIN Bold';
					font-weight: bold;
					color: #fff;
				}
			}

			.right-area {
				display: flex;
				align-items: center;

				.right-icon {
					width: 24rpx;
					height: 24rpx;
          flex-shrink: 0;
				}

				.right-text {
					padding-left: 8rpx;
					font-size: 22rpx;
					line-height: 24rpx;
					color: #fff;
				}
			}
		}
	}

	.home-swiper{
		position: relative;
		margin: 0 20rpx 20rpx;
		height: 160rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.home-notice {
		position: relative;
		margin-bottom: 20rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		height: 46rpx;
		background: linear-gradient(90deg, #ffffff 0%, #ffffff4d 100%);
		border-radius: 23rpx;

		.notice-icon {
			width: 28rpx;
			height: 24rpx;
		}

		.notice-img {
			margin-left: 8rpx;
			margin-right: 16rpx;
			width: 44rpx;
			height: 24rpx;
		}

		.notice-wrap {
			position: relative;
			display: flex;
			flex: 1;
			height: 32rpx;

			.notice-up {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}

	.home-king {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		padding: 40rpx 60rpx;
		background: #fff;
		border-radius: 20rpx;

		.king-item {
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;

			.king-img {
				width: 90rpx;
				height: 90rpx;
			}

			.king-text {
				padding-top: 20rpx;
				font-size: 22rpx;
				line-height: 22rpx;
				color: #1F2428;
			}

			.king-num {
				position: absolute;
				top: -4rpx;
				right: -4rpx;
				padding: 5rpx 10rpx;
				border: 2rpx solid #ffffff;
				background: #ff4d4d;
				border-radius: 18rpx;

				font-size: 22rpx;
				line-height: 22rpx;
				font-weight: bold;
				color: #fff;
			}
		}
	}

	.scroller-wraper {
		flex: 1;
		overflow: auto;
		padding: 0 20rpx 20rpx;

		.home-assets {
			padding: 40rpx;
			border-radius: 20rpx;
			background: #fff;

			.assets-title {
				font-size: 32rpx;
				line-height: 32rpx;
				font-weight: bold;
			}

			.assets-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 36rpx 0 20rpx 0;

				.top-item {
					position: relative;
					width: 304rpx;
					border-radius: 20rpx;

					.item-title {
						display: flex;
						align-items: center;
						width: 216rpx;
						height: 52rpx;
						padding: 12rpx 16rpx;
						border-radius: 20rpx 0 20rpx 0;

						.title-icon {
							width: 26rpx;
							height: 26rpx;
						}

						.title-text {
							padding: 0 8rpx;
							font-size: 28rpx;
							line-height: 28rpx;
							font-weight: bold;
							color: #fff;
						}

						.title-info {
							width: 22rpx;
							height: 22rpx;
						}
					}

					.item-num {
						display: block;
						padding: 20rpx 28rpx;
						font-size: 48rpx;
						line-height: 48rpx;
						font-family: 'DIN Bold';
						font-weight: bold;
						color: #1F2428;
					}

					.item-info {
						display: flex;
						align-items: center;
						padding: 0 27rpx 30rpx 27rpx;

						.info-name {
							padding-right: 8rpx;
							font-size: 22rpx;
							line-height: 22rpx;
							color: #1F2428;
							font-weight: bold;
						}

						.info-icon {
							width: 16rpx;
							height: 20rpx;
						}

						.info-num {
							padding-left: 8rpx;
							font-size: 24rpx;
							line-height: 24rpx;
							font-family: 'DIN Bold';
							color: #1F2428;
							font-weight: bold;
						}

						.num-up {
							color: #FF4D4D;
						}

						.num-down {
							color: #56CC93;
						}
					}
				}

				.shop-item {
					background: linear-gradient(90deg, #FEF1EE 0%, #FEF1EE 100%);

					.item-title {
						background: linear-gradient(203.5deg, #FFB09F 0%, #F75E3B 100%);
					}
				}

				.room-item {
					background: linear-gradient(90deg, #56cc931a 0%, #56cc930d 100%);

					.item-title {
						background: linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%);
					}
				}
			}

			.assets-bottom {
				display: flex;
				justify-content: space-between;

				.bottom-item {
					width: 140rpx;
					padding: 26rpx 5rpx 26rpx 8rpx;
					border: 2rpx solid #f2f2f2;
					background: #ffffff;
					border-radius: 20rpx;

					.item-title {
						display: flex;
						align-items: center;

						.title-text {
							padding-right: 6rpx;
							font-size: 24rpx;
							line-height: 22rpx;
							color: #1F2428;							
						}

						.title-info {
							width: 22rpx;
							height: 22rpx;
						}
					}

					.item-num {
						display: block;
						padding: 20rpx 0;
						font-size: 28rpx;
						line-height: 28rpx;
						font-family: 'DIN Bold';
						font-weight: bold;
						color: #1F2428;
					}

					.item-info {
						display: flex;
						align-items: center;
						flex-wrap: wrap;

						.info-name {
							width: 100%;
							padding-right: 8rpx;
							padding-bottom: 8rpx;
							font-size: 20rpx;
							line-height: 20rpx;
							color: #999999;
						}

						.info-icon {
							width: 12rpx;
							height: 16rpx;
							margin-right: 8rpx;
						}

						.info-num {
							font-size: 22rpx;
							line-height: 22rpx;
							font-family: 'DIN Bold';
							color: #1F2428;
							font-weight: bold;
						}

						.info-num-up {
							color: #FF4D4D;
						}

						.info-num-down {
							color: #56CC93;
						}
					}
				}
			}
		}

		.home-key {
			margin: 20rpx 0 200rpx 0;
			padding: 30rpx;
			border-radius: 20rpx;
			background: #fff;

			.key-title {
				padding: 10rpx 10rpx 30rpx 10rpx;
				display: flex;
				align-items: center;

				.title-text {
					font-size: 32rpx;
					line-height: 32rpx;
					font-weight: bold;
				}
				
				.subtitle-text {
					padding-left: 20rpx;
					font-size: 24rpx;
					line-height: 24rpx;
					color: #999999;
				}
			}

			.key-content {
				display: flex;
				flex-wrap: wrap;

				.key-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 196rpx;
					margin: 10rpx;
					padding: 18rpx 0;
					border: 2rpx solid #f2f2f2;
					background: #ffffff;
					border-radius: 20rpx;

					.item-title {
						display: flex;
						align-items: flex-end;

						.title-name {
							font-size: 24rpx;
							line-height: 24rpx;
							color: #1F2428;
							font-weight: bold;
						}

						.title-unit {
							// padding-right: 8rpx;
							font-size: 18rpx;
							line-height: 18rpx;
							color: #999999;
							font-weight: bold;
						}

						.title-info {
							margin: 8rpx 8rpx 0 8rpx;
							width: 22rpx;
							height: 22rpx;
						}
					}

					.item-chart {
						position: relative;
						margin: 20rpx 0 4rpx 0;
						width: 160rpx;
						height: 160rpx;
						display: flex;
						justify-content: center;
						align-items: center;

						.chart-progress {
							position: absolute;
							margin: auto;
							width: 160rpx;
							height: 160rpx;
							border-radius: 50%;
							mask: radial-gradient(transparent, transparent 64rpx, #000 64rpx, #000 0);
							transform: rotate(180deg);
						}

						.circle-start {
							position: absolute;
							top: 144rpx;
							left: 72rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background: #FFB2A1;
							z-index: 1;
						}

						.circle-end {
							display: block;
							position: absolute;
							top: 0;
							left: 72rpx;
							width: 16rpx;
							height: 160rpx;
							z-index: 1;

							.end-point {
								position: absolute;
								top: 0;
								left: 0;
								width: 16rpx;
								height: 16rpx;
								border-radius: 50%;
								background: #F75E3B;
							}
						}

						.chart-info {
							position: relative;
							width: 120rpx;
							height: 120rpx;
							padding: 32rpx 0rpx;
							border-radius: 50%;

							.info-title {
								padding-bottom: 4rpx;
								font-size: 24rpx;
								line-height: 28rpx;
								text-align: center;
								color: #1F2428;
								font-weight: bold;
								font-family: 'DIN Bold';
								font-weight: bold;
							}

							.info-num {
								display: flex;
								justify-content: center;
								align-items: baseline;

								.num {
									padding-right: 2rpx;
									font-size: 24rpx;
									line-height: 24rpx;
									font-weight: bold;
									font-family: 'DIN Bold';
									color: #FF4D4D;
									font-weight: bold;
								}

								.symbol {
									font-size: 16rpx;
									line-height: 16rpx;
									color: #FF4D4D;
								}
							}
						}
					}

					.item-info-wraper {
						display: flex;
						flex-direction: column;

						.item-info {
							padding-top: 16rpx;
							font-size: 20rpx;
							line-height: 20rpx;
							color: #1F2428;
						}
					}
				}
			}

			.key-info {
				margin-top: 20rpx;
				display: flex;
				justify-content: flex-start;
				padding: 16rpx 12rpx;
				border-radius: 20px;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

				.info-icon {
					margin: 8rpx 8rpx 0 8rpx;
					width: 22rpx;
					height: 22rpx;
				}

				.info-text {
					font-size: 22rpx;
					line-height: 38rpx;
					color: rgba(31, 36, 40, 0.7);
				}
				
			}
		}
	}

	.u-popup__content {
    border-radius: 40rpx !important;

    .u-toolbar {
      height: 156rpx !important;
      padding: 0 40rpx !important;

      .u-toolbar__wrapper__cancel {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #999999 !important;
      }

      .u-toolbar__title {
        font-size: 36rpx !important;
        line-height: 36rpx !important;
        color: #1F2428 !important;
      }

      .u-toolbar__wrapper__confirm {
        font-size: 30rpx !important;
        line-height: 30rpx !important;
        color: #C35943 !important;
      }
    }

    .u-picker__view__column__item {
      font-size: 26rpx !important;
      color: #1F2428 !important;
    }
  }

	.c-modal {
		.u-popup__content {
			border-radius: 20rpx !important;
		}
	}
}
</style>