<template>
	<view class="container">
		<view class="area">
			<cAreaPicker @setArea="setArea" :defaultName="areaData.orgName">
				<template #right>
					<view class="area-right"
						@click="route_to_view(`/packageB/pages/select-store/select-store?from=public-sentiment`,()=>{trackEvent('007')})">
						<image class="shop-img"
							src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644837689.png"
							mode="aspectFill"></image>
						<text class="shop-text">门店</text>
					</view>
				</template>
			</cAreaPicker>
		</view>
    <view class="description">
      <view class="fr">
        <image class="img" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-04-21/1745200634744.png"/>
        <text class="text">说明</text>
      </view>
      <view class="details">
        2025-04-17之后的新增网评分不包含携程/美团等分销渠道点评分。按渠道查看点评分时，各分销渠道点评分不支持查看，直销App/小程序点评分可正常查看。
      </view>
    </view>
		<view class="content">
			<view class="section">
				<view class="title fr" style="margin-bottom: 0;">
					<text class="title-text">舆情指标</text>
					<view class="title-btn fr"
						@click="route_to_view(`/packageA/pages/motivationAnalysis/index?tabName=monitor&id=${areaData.orgId}&level=${areaData.level}&orgName=${areaData.orgName}`,()=>{trackEvent('008')})">
						<text class="btn-text">动因分析</text>
						<up-icon name="arrow-right" color="#1F2428" size="20rpx" />
					</view>
				</view>
				<cDatePicker ref="cDatePickerRef" @setDate="setDate"></cDatePicker>

				<view class="target-table">
					<cTable :header="indicatorsData.column1" :list="indicatorsData.list">
						<template #default="scope">
							<view v-if="['tb', 'hb'].includes(scope.prop)" class="rate-item">
								<image v-if="scope.data[scope.prop] && scope.data[scope.prop] !== '-'" class="item-icon"
									:src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop].indexOf('-') ==-1 ? '1729644716250' : '1729644734157'}.png`"
									mode="aspectFill"></image>
								<text class="item-text"
									:class="[scope.data[scope.prop] === '-' ?  '' : scope.data[scope.prop].indexOf('-') == -1 ? 'up-text' : 'down-text']">{{scope.data[scope.prop] !== '-' ? scope.data[scope.prop].replace(/-/g, '') : scope.data[scope.prop]}}</text>
							</view>
						</template>
					</cTable>
				</view>
			</view>

			<view class="section">
				<view class="title fr">
					<text class="title-text">按渠道查看各项评分</text>
				</view>

				<view class="target-table">
					<cTable :header="channelScoreData.column" :list="channelScoreData.list" />
				</view>
			</view>

			<view class="section">
				<view class="title fr">
					<text class="title-text">按差评渠道分布</text>
				</view>
				<view class="negative-comment-chars">
					<ringChartVue :data="negativeChartData" />
				</view>
			</view>

			<view class="section">
				<view class="title fr">
					<text class="title-text">评分按门店分布</text>
				</view>
				<view class="rating-distribution-chars">
					<ringChartVue :data="shopScoreChartData" />
				</view>
			</view>

			<view class="section">
				<view class="title fr title--tab">
					<view class="fr" style="align-items: center;">
						<text class="title-text">排行榜</text>
						<text class="title-tip">本月</text>
					</view>
					<cTabsVue :list="[{name:'组织'},{name:'品牌'}]" v-model="currentIndex" :isBig="false" @change="handleTabChange" />
				</view>

				<template v-if="currentIndex == 0">
					<view class="ranking-btn">
						<cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleBtnGroupChange"></cBtnGroup>
					</view>

					<view class="target-table special">
						<template>
							<cTable ref="rankTable1Ref" :header="rankData.column1" :list="rankData.list1" :custom-style="{height: '660rpx', 'max-height':'none'}" @updateList="sortList"
								@chooseItem="chooseItem" @scrolltolower="rankScrolltolower" loading-more >
								<template #default="scope">
									<template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
								</template>
							</cTable>
						</template>
					</view>
				</template>
				<template v-else>
					<view class="target-table special">
						<template>
							<cTable ref="rankTable2Ref" :header="rankData.column2" :list="rankData.list2" height="660rpx" @updateList="sortList"
								@scrolltolower="rankScrolltolower" loading-more>
								<template #default="scope">
									<template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
								</template>
							</cTable>
						</template>
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
  import { ref, getCurrentInstance, ComponentInternalInstance, onMounted, nextTick, inject, onUnmounted } from 'vue';
	import cDatePicker from '@/components/c-date-picker/index.vue'
	import cTable from '@/components/c-table/c-table.vue'
	import ringChartVue from '@/components/ring-chart/ring-chart.vue'
	import cTabsVue from '@/components/c-tabs/c-tabs.vue';
	import cBtnGroup from '@/components/c-btn-group/index.vue'
	import cAreaPicker from '@/components/c-area-picker/index.vue'
	import { useCommon } from '@/hooks/useGlobalData';
	import { queryBrandRank, queryChannelScore, queryIndicators, queryMonthRank } from '@/public/api/public-sentiment';
	import { OrgLevel, PaginationParams, QueryIndicators_Date } from '@/types/dataCockpit';
	import dayjs from 'dayjs';

	const { globalData, route_to_view, trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const instance = getCurrentInstance();
	const { deptName, ...rest } = globalData.value.user.orgList[0];
	const areaData = ref<OrgLevel>({ // 区域数据
		...rest
	})
	const dateData = ref<QueryIndicators_Date>({ // 日期数据
	})

  const rankTable1Ref = ref<InstanceType<typeof cTable> | null>(null)
  const rankTable2Ref = ref<InstanceType<typeof cTable> | null>(null)

	const indicatorsData = ref({ // 舆情指标table
		column: [
			{
				name: '指标',
				prop: 'dataName',
				width: '170',
				canClick: true,
				type: 'info',
			},
			{
				name: '数值',
				prop: 'score',
				width: '100'
			},
			{
				name: '同比',
				prop: 'tb',
				info: '对比同期增速',
				slot: 'tb',
			},
			{
				name: '环比',
				prop: 'hb',
				info: '对比上期增速',
				slot: 'hb',
			},
			{
				name: '排名',
				subTitle: "新增网评分",
				prop: 'rank',
				width: '110'
			}
		],
		column1: [],
		list: [],
	});

	const channelScoreData = ref({ // 渠道评分table
		column: [
			{
				name: '渠道',
				prop: 'dataName',
			},
			{
				name: '渠道间夜',
				subTitle: '占比',
				prop: 'rate',
				width: 110
			},
			{
				name: '新增网评分',
				prop: 'integratedScore',
				width: 126
			},
			{
				name: '卫生分',
				prop: 'sanitationScore',
				width: 96
			},
			{
				name: '位置分',
				prop: 'positionScore',
				width: 96
			},
			{
				name: '服务分',
				prop: 'serverScore',
				width: 96
			},
			{
				name: '设施分',
				prop: 'infrastructureScore',
				width: 96
			}
		],
		list: []
	});

	const rankData = ref<any>({
		column: [
			{
				name: '排名',
				prop: 'num',
				width: '76',
				slot: 'num',
			},
			{
				name: '地区总部',
				prop: 'orgName',
				width: '120',
			},
			{
				name: '负责人',
				prop: 'name',
				width: '90',
			},
			{
				name: '新增网',
				subTitle: '评分',
				prop: 'integratedScore',
				width: '120',
				sort: true,
				sortType: 'desc',
				orderByColumn: 'integratedScore',
			},
			{
				name: '差评率',
				prop: 'negativeRate',
				width: '120',
				sort: true,
				sortType: '',
				orderByColumn: 'negativeRate',
			},
			{
				name: '3分以下',
				subTitle: '门店',
				prop: 'fourNum',
				width: '120',
			},
			{
				name: '3～4分',
				subTitle: '门店',
				prop: 'threeNum',
				width: '120',
			},
			{
				name: '4～4.5分',
				subTitle: '门店',
				prop: 'twoNum',
				width: '120',
			},
			{
				name: '4.5～5分',
				subTitle: '门店',
				prop: 'oneNum',
				width: '120',
			}
		],
		column1: [],
		column2: [
			{
				name: '排名',
				prop: 'num',
				width: '76',
				slot: 'num',
			},
			{
				name: '品牌',
				prop: 'brandName',
				width: '134',
			},
			{
				name: '新增网评分',
				prop: 'integratedScore',
				width: '140',
				sort: true,
				sortType: 'desc',
				orderByColumn: 'integratedScore',
			},
			{
				name: '差评率',
				prop: 'negativeRate',
				width: '120',
				sort: true,
				sortType: '',
				orderByColumn: 'negativeRate',
			},
			{
				name: '3分以下',
				subTitle: '门店',
				prop: 'fourNum',
				width: '120',
			},
			{
				name: '3～4分',
				subTitle: '门店',
				prop: 'threeNum',
				width: '120',
			},
			{
				name: '4～4.5分',
				subTitle: '门店',
				prop: 'twoNum',
				width: '120',
			},
			{
				name: '4.5～5分',
				subTitle: '门店',
				prop: 'oneNum',
				width: '120',
			}
		],
		list1: [],
		list2: []
	})

	const currentIndex = ref(0),
		tabType = ref(areaData.value.level) // 10 总区；30 大区；40 城区；50 门店；
	const negativeChartData = ref() // 差评渠道分布chart数据
	const shopScoreChartData = ref() // 门店分布chart数据
	const pagination = ref<PaginationParams>({
		pageNum: 1,
		pageSize: 20
	})
	const sortParams = ref<{ orderBy : number, sortBy : string }>()
	const queryMonthRankParams = ref<{ [key : string] : any }>({})
	const loadMore = ref<boolean>(true)
	const tableWidth = ref<string>()

  const pageOptions : any = inject('pageOptions')
  const cDatePickerRef = ref(null)

  onMounted(() => {
    uni.$on('monitorRefresh', () => {
      init()
    })
    init()
  })

  onUnmounted(()=> {
    uni.$off('monitorRefresh')
  })

  const init = () => {
    if (pageOptions.value?.areaData) { // ai助手跳转携带参数
      const data = pageOptions.value.areaData
      if (data.orgId) {
        areaData.value = Object.assign(areaData.value, data)
      } else {
        areaData.value = { ...areaData.value, ...rest }
      }
    }
    if (pageOptions.value?.dateType === 'month') {
      cDatePickerRef.value.dateType = 'month'
      // 获取当前年份（字符串格式）
      const currentYear = dayjs().format('YYYY');
      // 获取当前月份（字符串格式，自动补零）
      const currentMonthMM = dayjs().format('MM');
      const currentMonth = dayjs().format('M');
      cDatePickerRef.value.dateShow = `${currentYear}-${currentMonthMM}`;
      setDate('month', currentYear, currentMonth)
    } else {
      cDatePickerRef.value.dateType = 'day'
      setDate('day', undefined, dayjs().subtract(1, 'day').format('YYYY-MM-DD')) // 日，今年，昨天
    }
    pageOptions.value = null
		if (currentIndex.value == 0) {
			setHeader(tabType.value)
		}
		const query = uni.createSelectorQuery().in(instance.proxy);
		query.select(`.target-table`).boundingClientRect((res : any) => {
			tableWidth.value = res.width + 'px !important'
		}).exec();
	}

	// 区域筛选
	const setArea = (id : number, level : string, name : string) => {
		trackEvent('006')
		areaData.value.orgId = id.toString()
		areaData.value.level = Number(level)
		areaData.value.orgName = name
		nextTick(() => {
			if (!tabType.value) {
				tabType.value = Number(level) <= 10 ? 10 : Number(level)
			}
			toQueryIndicators()
			toQueryChannelScore()
			if (currentIndex.value == 0) { // 组织
				resetMonth()
				setHeader(tabType.value)
			} else { // 品牌
				resetBrand()
				toQueryBrandRank()
			}
		})
	}

	// 时间筛选
	const setDate = (type : string, value1 : string | number, value2 ?: string | number) => {
		indicatorsData.value.column1 = JSON.parse(JSON.stringify(indicatorsData.value.column))
		if (type === 'day') {
			dateData.value = {
				dateType: 'day',
				year: dayjs(value2).year(),
				month: (dayjs(value2).month() + 1).toString().padStart(2, '0'),
				day: dayjs(value2).date().toString().padStart(2, '0')
			}
		} else if (type === 'month') {
			dateData.value = {
				dateType: 'month',
				year: value1 as number,
				month: value2.toString().padStart(2, '0')
			}
		} else if (type === 'quarter') {
			dateData.value = {
				dateType: 'quarter',
				year: value1 as number,
				quarter: value2 as number
			}
		} else if (type === 'year') {
			dateData.value = {
				dateType: 'year',
				year: value1 as number
			}
			indicatorsData.value.column1 = [{
				name: '指标',
				prop: 'dataName',
				canClick: true,
				type: 'info',
			},
			{
				name: '数值',
				prop: 'score',
			},
			{
				name: '同比',
				prop: 'tb',
				info: '对比同期增速',
				slot: 'tb',
			},
			{
				name: '排名',
				subTitle: "新增网评分",
				prop: 'rank',
			}]
		} else if (type === 'range') {
			dateData.value = {
				dateType: 'range',
				startDate: value1 as string,
				endDate: value2 as string
			}
			indicatorsData.value.column1 = [{
				name: '指标',
				prop: 'dataName',
				canClick: true,
				type: 'info',
			},
			{
				name: '数值',
				prop: 'score',
			},
			{
				name: '排名',
				subTitle: "新增网评分",
				prop: 'rank',
			}]
		}
		toQueryIndicators()
		toQueryChannelScore()
	}

	// 查询舆论指标
	const toQueryIndicators = async () => {
		indicatorsData.value.list = []
		try {
			const { orgName, ...rest } = areaData.value
			const params = {
				...rest,
				...dateData.value
			}
			const { data } = await queryIndicators(params)
			if (data) {
				indicatorsData.value.list = data
			}
		} catch (e) {
			console.error('查询舆论指标 error', e)
		}
	}

	const formatChartsData = (v : any) => {
		return v.map(({ dataName, count }) => {
			return {
				name: dataName,
				value: Number(count),
				labelShow: false
			}
		})
	}

	// 获取渠道评分
	const toQueryChannelScore = async () => {
		channelScoreData.value.list = []
		try {
			const { orgName, ...rest } = areaData.value
			const params = {
				...rest,
				...dateData.value
			}
			const { data } = await queryChannelScore(params)
			if (data) {
				channelScoreData.value.list = data.scoreList
        const notZeroNegativeList = data.negativeList.filter(item => item.count != '0')
        if (notZeroNegativeList.length > 0) {
          negativeChartData.value = { originData: notZeroNegativeList, data: formatChartsData(notZeroNegativeList) }
        } else {
          negativeChartData.value = []
        }
				shopScoreChartData.value = { originData: data.shopScoreList, data: formatChartsData(data.shopScoreList) }
			}
		} catch (e) {
			console.error('获取渠道评分 error', e)
		}
	}

	// 评分组织排行榜
	const toQueryMonthRank = async () => {
		try {
			const params = {
				tabType: tabType.value,
				...areaData.value,
				...pagination.value,
				startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
				endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
				...sortParams.value,
				...queryMonthRankParams.value
			}
			const { data } = await queryMonthRank(params)
			if (data) {
				if (data.length < pagination.value.pageSize) {
          loadMore.value = false
          rankTable1Ref.value && (rankTable1Ref.value.customLoadStatus = 'nomore')
        } else {
          rankTable1Ref.value && (rankTable1Ref.value.customLoadStatus = 'loadmore')
        }
        nextTick(() => {
					if (rankData.value.list1 && rankData.value.list1.length > 0)
						rankData.value.list1.push(...data)
					else
						rankData.value.list1 = data
				})
			} else {
        loadMore.value = false
        rankTable1Ref.value && (rankTable1Ref.value.customLoadStatus = 'nomore')
      }
		} catch (e) {
			console.error('评分组织排行榜 error', e)
		}
	}

	// 评分品牌排行榜
	const toQueryBrandRank = async () => {
		try {
			const params = {
				...areaData.value,
				...pagination.value,
				startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
				endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
				...sortParams.value
			}
			const { data } = await queryBrandRank(params)
			if (data) {
				if (data.length < pagination.value.pageSize) {
          loadMore.value = false
          rankTable2Ref.value.customLoadStatus = 'nomore'
        } else {
          rankTable2Ref.value.customLoadStatus = 'loadmore'
        }
				nextTick(() => {
					if (rankData.value.list2 && rankData.value.list2.length > 0)
						rankData.value.list2.push(...data)
					else
						rankData.value.list2 = data
				})
			} else {
        loadMore.value = false
        rankTable2Ref.value.customLoadStatus = 'nomore'
      }
		} catch (e) {
			console.error('评分品牌排行榜 error', e)
		}
	}

	// 区域维度切换
	const handleBtnGroupChange = (level : string) => {
		queryMonthRankParams.value = {}
		resetMonth()
		setHeader(level)
	}

	// tab切换
	const handleTabChange = (item: any) => {
    sortParams.value = null
    if (item.index == 1) { // 品牌
      resetBrand()
      toQueryBrandRank()
    } else { // 组织
      tabType.value = Number(areaData.value.level) <= 10 ? 10 : Number(areaData.value.level)
      resetMonth()
      setHeader(tabType.value)
    }
  }

	// 表格下钻
	const chooseItem = (data : any) => {
		if (!data.canClick) return
		sortParams.value = null
		tabType.value = null
		loadMore.value = true
		rankData.value.list1 = []
		queryMonthRankParams.value = {
			orgId: data.orgId,
			level: data.level,
			tabType: (data.level == 10 ? Number(data.level) + 20 : Number(data.level) + 10).toString()
		}
		reset()
		setHeader(queryMonthRankParams.value.tabType)
	}

	// 表格排序
	const sortList = (item : any) => {
		const orderBy = item.orderByColumn == 'integratedScore'
			? 1
			: item.orderByColumn == 'negativeRate'
				? 2
				: item.orderByColumn == 'positiveRate'
					? 3
					: item.orderByColumn == 'comments'
						? 4 : null
		sortParams.value = {
			orderBy,
			sortBy: item.sortType
		}
		reset()
		if (currentIndex.value == 0) {
			rankData.value.list1 = []
			toQueryMonthRank()
		} else {
			rankData.value.list2 = []
			toQueryBrandRank()
		}
	}

	const reset = () => {
		loadMore.value = true
		pagination.value = {
			pageNum: 1,
			pageSize: 20
		}
	}

	const resetMonth = () => {
		rankData.value.list1 = []
		queryMonthRankParams.value = {}
		reset()
	}

	const resetBrand = () => {
		rankData.value.list2 = []
		reset()
	}

	const rankScrolltolower = () => {
		if (!loadMore.value) return
		pagination.value.pageNum++
		if (currentIndex.value == 0) {
      rankTable1Ref.value.customLoadStatus = 'loading'
			toQueryMonthRank()
		} else {
      rankTable2Ref.value.customLoadStatus = 'loading'
			toQueryBrandRank()
		}
	}


	const setHeader = (level : number | string) => {
		rankData.value.column1 = JSON.parse(JSON.stringify(rankData.value.column))
		switch (Number(level)) {
			case 10:
				rankData.value.column1.splice(1, 2,
					{
						name: '地区总部',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '负责人',
						prop: 'name',
						width: '90'
					},
				);
				break;
			case 30:
				rankData.value.column1.splice(1, 2,
					{
						name: '大区',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '大区总',
						prop: 'name',
						width: '90'
					},
				);
				break;
			case 40:
				rankData.value.column1.splice(1, 2,
					{
						name: '城区',
						prop: 'orgName',
						width: '120',
						canClick: true,
						type: 'choose'
					},
					{
						name: '城区总',
						prop: 'name',
						width: '90'
					},
				);
				break;
			case 50:
				rankData.value.column1 = [
					{
						name: '排名',
						prop: 'num',
						width: '76',
						slot: 'num'
					},
					{
						name: '门店ID',
						prop: 'orgId',
						width: '90'
					},
					{
						name: '门店',
						prop: 'orgName',
						width: '200'
					},
					{
						name: '新增网评分',
						prop: 'integratedScore',
						width: '120',
						sort: true,
						sortType: 'desc',
						orderByColumn: 'integratedScore'
					},
					{
						name: '点评量',
						prop: 'comments',
						width: '120',
						sort: true,
						sortType: '',
						orderByColumn: 'comments'
					},
					{
						name: '好评数',
						prop: 'positiveCount',
						width: '120'
					},
					{
						name: '好评率',
						prop: 'positiveRate',
						width: '120',
						sort: true,
						sortType: '',
						orderByColumn: 'positiveRate'
					},
					{
						name: '差评数',
						prop: 'negativeCount',
						width: '120',
					},
					{
						name: '差评率',
						prop: 'negativeRate',
						width: '120',
						sort: true,
						sortType: '',
						orderByColumn: 'negativeRate'
					}
				]
				break;
			default:
				break;
		}
		toQueryMonthRank()
	}

	defineOptions({
		options: {
			styleIsolation: 'shared'
		}
	})
</script>

<style scoped lang="scss">
  .special {
    :deep() {
      .empty-img .empty {
        width: v-bind(tableWidth);
        height: 660rpx;
      }
    }
  }

	.container {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;

		.area {
			margin-bottom: 20rpx;

			.area-right {
				height: 100%;
				display: flex;
				align-items: center;
				padding: 0 28rpx;
				margin-left: 20rpx;
				border-radius: 32rpx;
				background: #F9EEEC;

				.shop-img {
					width: 28rpx;
					height: 25rpx;
				}

				.shop-text {
					padding-left: 8rpx;
					font-size: 22rpx;
					color: #1F2428;
					font-weight: 600;
				}
			}
		}
	}

  .description {
    border-radius: 20rpx;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
    padding: 16rpx 20rpx;
    margin-bottom: 20rpx;

    .img {
      width: 22rpx;
      height: 22rpx;
      flex-shrink: 0;
    }

    .text {
      color: $uni-text-color;
      font-size: 26rpx;
      font-weight: bold;
      line-height: 26rpx;
      margin-left: 8rpx;
    }

    .details {
      margin-top: 8rpx;
      color: #1f2428b3;
      font-size: 22rpx;
      line-height: 38rpx;
      padding-left: 30rpx;
    }
  }

	.content {
		overflow-y: auto;
		padding-bottom: 192rpx;
	}

	.section {
		padding: 32rpx 40rpx 40rpx;
		background: #fff;
		border-radius: 23.07rpx;
		margin-bottom: 20rpx;

		.title {
			justify-content: space-between;
			margin-bottom: 40rpx;

			&--tab {
				margin-bottom: 9rpx;
			}

			.title-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
			}

			.title-tip {
				color: #999999;
				font-size: 24rpx;
				line-height: 24rpx;
				margin-left: 20rpx;
			}

			.title-btn {
				border-radius: 200rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				padding: 8rpx 16rpx;

				.btn-text {
					color: #1f2428;
					font-size: 22rpx;
					line-height: 22rpx;
					margin-right: 8rpx;
				}
			}
		}

		.target-table {
			border-radius: 20rpx;
			overflow: hidden;

			.target-item {
				display: flex;
				align-items: center;
				justify-content: center;

				.item-info {
					margin-left: 8rpx;
					width: 22rpx;
					height: 22rpx;
				}
			}

			.rate-item {
				display: flex;
				align-items: center;
				justify-content: center;

				.item-icon {
					width: 16rpx;
					height: 20rpx;
				}

				.item-text {
					padding-left: 8rpx;
					font-size: 24rpx;
					line-height: 24rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					color: #1F2428;
					white-space: nowrap;
				}

				.up-text {
					color: #FF4D4D;
				}

				.down-text {
					color: #56CC93;
				}
			}
		}

		.negative-comment-chars {
			height: 596rpx;
		}

		.rating-distribution-chars {
			height: 442rpx;
		}

		.ranking-btn {
			margin-bottom: 20rpx;
		}
	}
</style>