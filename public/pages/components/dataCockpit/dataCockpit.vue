<template>
	<view class="data-cockpit">
		<view class="data-bg" :class="classObject"></view>
		<u-navbar placeholder title="数据驾驶舱" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700; color: #fff;">
			<template #left></template>
		</u-navbar>
    <cTabs :list="tabList" v-model:modelValue="tabIndex" @change="handleTabChange" @click="handleTabClick"></cTabs>

    <assetsIndex v-if="tabIndex === 0"></assetsIndex>
		<financeIndex v-if="tabIndex === 1"></financeIndex>
    <operationsIndex v-if="tabIndex === 2"></operationsIndex>
    <memberIndex v-if="tabIndex === 3"></memberIndex>
		<publicSentimentVue v-if="tabIndex === 4" />
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, getCurrentInstance, ComponentInternalInstance, computed } from 'vue';
import cTabs from '@/components/c-tabs2/index.vue'
import operationsIndex from './operations.vue'
import memberIndex from './member.vue'
import assetsIndex from './assets.vue'
import financeIndex from './finance.vue'
import publicSentimentVue from '../public-sentiment/public-sentiment.vue';
import { useCommon } from '@/hooks/useGlobalData';
import { showToast } from '@/utils/util';

const { trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const pages = ref(globalData.value.pages)

const props = defineProps({
  tabName: {
    type: String,
    default: '',
  }
});
  
// 响应式数据  
const tabList = reactive([  
  { name: '资产', value: 'assets' },  
  { name: '财务', value: 'finance', disabled: !pages.value.includes(2) },  
  { name: '运营', value: 'operations' },  
  { name: '会员', value: 'member' },  
  { name: '舆情', value: 'monitor' }
]);
const tabIndex = ref<number>(0)

const setTab = (name: string) => {
  tabIndex.value = tabList.findIndex((item) => item.value === name)
}

watch(() => props.tabName, (val) => {
  setTab(val)
}, {immediate: true, deep: true})

const handleTabChange = () => {
	if (tabIndex.value === 0 ) {
		trackEvent('D001')
	} else if (tabIndex.value === 1) {
		trackEvent('D010')
	} else if (tabIndex.value === 2 ) {
		trackEvent('D016')
	} else if (tabIndex.value === 3 ) {
		trackEvent('D023')
	} else if (tabIndex.value === 4 ) {
		trackEvent('005')
	}
}

const handleTabClick = (item: any) => {
	if (item.disabled) showToast('暂无查看权限，请走申请流程')
}

const classObject = computed(() => {
  if (tabIndex.value === 1) {
    return 'data-big-bg'
  }
  if (tabIndex.value === 4) {
    return 'public-sentiment-bg'
  }
})

defineExpose({
  setTab
})
</script>

<style lang="scss">
	.data-cockpit {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.data-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 750rpx;
			height: 442rpx;
			background: linear-gradient(52.4deg, #ff5733 0%, #c35943 31%, #c35943 79%, #f75735 100%);

			&::after {
				content: '';
				position: absolute;
				right: 0;
				bottom: -118rpx;
				width: 118rpx;
				height: 118rpx;
				background: radial-gradient(circle at 0% 100%, transparent 118rpx, #C35943 0);
			}
		}

		.data-big-bg {
			height: 596rpx;
		}

    .public-sentiment-bg {
      height: 642rpx;
    }

		.data-area {
			padding: 0 20rpx;
		}

		.data-content {
			position: relative;
			flex: 1;
			overflow: auto;
			padding: 0 20rpx 20rpx;
			margin-top: 20rpx;

			.target-view {
				padding: 32rpx 40rpx 40rpx;
				background: #fff;
				border-radius: 22rpx;

				.target-title {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.title-text {
						font-size: 32rpx;
						font-weight: 600;
					}

					.title-btn {
						display: flex;
						align-items: center;
						padding: 8rpx 16rpx;
						background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
						border-radius: 16rpx;

						.btn-text {
							padding-right: 8rpx;
							font-size: 22rpx;
							line-height: 22rpx;
							color: #1F2428;
						}
					}
				}

				.target-table {
					height: 558rpx;
					border-radius: 20rpx;
					overflow: hidden;

					.target-item {
						display: flex;
						align-items: center;
						justify-content: center;

						.item-info {
							margin-left: 8rpx;
							width: 22rpx;
							height: 22rpx;
						}
					}

					.rate-item {
						display: flex;
						align-items: center;
						justify-content: center;

						.item-icon {
							width: 16rpx;
							height: 20rpx;
						}

						.item-text {
							padding-left: 8rpx;
							font-size: 24rpx;
							line-height: 24rpx;
							font-weight: 700;
							font-family: "DDin";
							color: #1F2428;
						}

						.up-text {
							color: #FF4D4D;
						}

						.down-text {
							color: #56CC93
						}
					}
				}
			}

			.ranking-view {
				padding: 32rpx 40rpx 40rpx;
				margin: 20rpx 0 200rpx 0;
				background: #fff;
				border-radius: 22rpx;

				.ranking-title {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;

					.title-left {
						display: flex;
						align-items: center;
						height: 32rpx;

						.title-text {
							font-size: 32rpx;
							font-weight: 600;
						}

						.sub-title-text {
							padding-left: 20rpx;
							font-size: 24rpx;
							color: #999999;
						}
					}
				}

				.ranking-btn {
					padding: 9rpx 0 20rpx 0;
				}

				.ranking-table {
					height: 558rpx;
					border-radius: 20rpx;
					overflow: hidden;

					.target-item {
						display: flex;
						align-items: center;
						justify-content: center;

						.item-info {
							margin-left: 8rpx;
							width: 22rpx;
							height: 22rpx;
						}
					}

					.rate-item {
						display: flex;
						align-items: center;
						justify-content: center;

						.item-icon {
							width: 16rpx;
							height: 20rpx;
						}

						.item-text {
							padding-left: 8rpx;
							font-size: 24rpx;
							line-height: 24rpx;
							font-weight: 700;
							font-family: "DDin";
							color: #1F2428;
						}

						.up-text {
							color: #FF4D4D;
						}

						.down-text {
							color: #56CC93
						}
					}
				}
			}
		}

	operations-index,
	finance-index,
	assets-index, 
	member-index,
	public-sentiment-vue {
    flex: 1;
    overflow: hidden;
  }
}
</style>