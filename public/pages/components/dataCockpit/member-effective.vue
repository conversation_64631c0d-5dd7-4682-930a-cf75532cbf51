<template>
  <view class="member-effective">
      <view class="member-tab-wrapper">
      <view class="member-tab-btn">
        <view class="btn-item" @click="setMemberTab(0)"> 售卡-直销 </view>
        <view class="btn-item active" @click="setMemberTab(1)"> 移动端有效量 </view>
      </view>
    </view>
    <view class="data-area">
      <cAreaPicker @setArea="setArea" @toSearch="toSearch" :defaultName="defaultAreaName" :showShop="false"></cAreaPicker>
      <cDatePicker ref="cDatePickerRef" :showRule="true" @setDate="setDate"></cDatePicker>
    </view>

    <view class="data-content">
      <view class="target-view">
        <view class="target-title">
        <view class="target-title-left">
          <text class="title-text">移动端总有效量</text>
          <view @click.stop="showModal()">
            <text class="sub-title-text">规则说明</text>
            <image class="item-info" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644816266.png" mode="aspectFill"></image>
          </view>
        </view>
        <view class="target-title-right" v-if="effectiveData?.effectiveTotal?.actualValue">
            <text class="num">{{ effectiveData.effectiveTotal.actualValue }} /</text>
            <view class="rate-item">
              <image class="item-icon" v-if="effectiveData.effectiveTotal.valueHb" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${effectiveData.effectiveTotal.valueHb > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
              <text class="item-text" :class="[effectiveData.effectiveTotal.valueHb > 0 ? 'up-text' : effectiveData.effectiveTotal.valueHb < 0 ? 'down-text' : '']">{{ Math.abs(effectiveData.effectiveTotal.valueHb) + '%' }}</text>
            </view>
        </view>
        </view>
  
        <view class="target-table-wrapper">
          <view class="target-table-container">
            <view class="target-table">
              <cTable ref="targetTable" :header="effectiveData.columnShow" :isPaginate="false" :isZPaging="true">
               <template #default="scope">
                <template v-if="['actualValue'].includes(scope.prop)">
                  {{ scope.data[scope.prop] || '-' }}
                </template>
                <view v-if="['valueTb', 'valueHb'].includes(scope.prop)" class="rate-item">
                  <image v-if="scope.data[scope.prop]" class="item-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                  <text class="item-text" :class="[scope.data[scope.prop] > 0 ? 'up-text' : scope.data[scope.prop] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop]) + '%' }}</text>
                </view>
               </template>
              </cTable>
            </view>
            <mixCharts :barNum="1" :yAxisNum="1" :detail="effectiveDetail" :baseCustomColor="chartDataColor[rankTheadType]['baseCustomColor']" :gradientColor="chartDataColor[rankTheadType]['gradientColor']" class="effective-chart"></mixCharts>
          </view>
        </view>
      </view>

      <view class="ranking-view">
        <view class="ranking-title">
          <view class="title-left">
            <text class="title-text">排行榜</text>
          </view>
        </view>

        <template>
          <view class="ranking-btn">
            <cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleChange"></cBtnGroup>
          </view>
          
          <view class="ranking-table">
            <cTable ref="rankTable" :header="rankData.columnShow" :isZPaging="true" @updateList="updateList" @getMore="getRanks" @chooseItem="chooseItem">
              <template #default="scope">
                <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
              </template>
            </cTable>
          </view>
        </template>  
      </view>
    </view>
  </view>
  <cModal v-model:show="modalShow" :title="modalTitle" :content="modalContent"></cModal>
</template>

<script setup lang="ts" name="memberEffective">
  import {
    ref,
    reactive,
    getCurrentInstance,
    ComponentInternalInstance,
    onMounted,
    nextTick,
    onUnmounted,
    inject
  } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cDatePicker from '@/components/c-date-picker/c-date-picker.vue'
import cTable from '@/components/c-table/c-table.vue'
import cBtnGroup from '@/components/c-btn-group/index.vue'
import cModal from "@/components/c-modal/index";
import mixCharts from "@/components/c-chart/mixCharts/index.vue";
import { queryOrgEffective, queryEffectiveRank,queryEffectiveBoard } from '@/public/api/member'
import { successCallbackResult } from '@/types'
import type { DateData, QueryOrgDataParams, QueryEffectiveRankRes, QueryBrandRankParams, EffectiveTotal, QueryOrgEffectiveRes, EffectiveBoardIndexVO } from '@/public/api/member'
import { formatDate, showToast } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import { onLoad } from '@dcloudio/uni-app'
import dayjs from "dayjs";

const emits = defineEmits(['changeTab'])

const	{ trackEvent, qdTrackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  canCollapse?: boolean,
  type?: string,
  showUnit?: boolean
}

onLoad((option: any) => {
  areaData.value.orgId = option.id;
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_membership_mobile_effect',
    button_name: '数舱-会员-移动端有效量',
  })
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_membership_mobile_org_rank',
    button_name: '数舱-会员-移动端有效量-排行榜组织',
  })
});

const activeMemberTab = ref<number>(1)
const setMemberTab = (tabIndex: number) =>{
  if(activeMemberTab.value !== tabIndex)
    emits('changeTab', tabIndex)
}

const toSearch = () => {
  uni.navigateTo({
    url: `/packageB/pages/select-store/select-store?from=member&orgId=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}`
  })

  trackEvent('D025')
}

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)

// 组织树相关
const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: userInfo.value.orgList[0].orgId,
  level: userInfo.value.orgList[0].level,
  orgName: userInfo.value.orgList[0].orgName,
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name
  
  nextTick(() => {
    if (!tabType.value) {
      tabType.value = Number(level) <= 10 ? '10' : level.toString()
    }
    getOrgData()
    getEffectiveBoard()
    rankLevel.value = tabType.value
    setRank({initSort: true, isReload: true})
  })
}

// --------start 会员指标------------
const effectiveData = ref<{ column: Array<Column>, columnShow: Array<Column> , effectiveTotal:EffectiveTotal}>({
  column: [
    {
      name: '指标',
      prop: 'dataName',
      width: '280',
      canCollapse: true,
    },
    {
      name: '实际值',
      prop: 'actualValue',
      width: '120',
      slot: 'actualValue',
    },
    {
      name: "环比",
      prop: "valueHb",
      width: "120",
      slot: "valueHb",
      info: '日环比：环比前一天\n月环比：环比上月同期\n季环比：环比上个季度\n年环比：环比去年同期',
    }
  ],
  columnShow: [],
  effectiveTotal:{}
});

const dateData = ref<DateData>({
  dateType: 'day',
  atime: formatDate(Date.now() - 24 * 60 * 60 * 1000)
})
// 切换日期选择、初始化入口
const setDate = (type: string, value1: string | number, value2?: string | number) => {
  if (type === 'day') {
    dateData.value = {
      dateType: 'day',
      atime: value2
    }
    effectiveData.value.columnShow = effectiveData.value.column
  } else if (type === 'month') {
    dateData.value = {
      dateType: 'month',
      year: value1,
      month: value2
    }
    effectiveData.value.columnShow = effectiveData.value.column
  } else if (type === 'quarter') {
    dateData.value = {
      dateType: 'quarter',
      year: value1,
      quarter: value2
    }
    effectiveData.value.columnShow = effectiveData.value.column
  } else if (type === 'year') {
    dateData.value = {
      dateType: 'year',
      year: value1,
    }
  } else if (type === 'range') {
    dateData.value = {
      dateType: 'range',
      startDate: value1,
      endDate: value2
    }
    effectiveData.value.columnShow = JSON.parse(JSON.stringify(effectiveData.value.column.filter((item: Column) => (item.prop !== 'valueHb' ))))
  }

  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_membership_mobile_date_choose',
    button_name: '数舱-会员-移动端有效量-日期选择',
  })
  getOrgData()
  getEffectiveBoard()
  // 更新排行榜 (更新数据-根据返回type判断表头样式-更新表头)
  if(!isRankInitialized.value) {
    setRank({initSort: true, isReload: true})
  }
}

const targetTable = ref<InstanceType<typeof cTable> | null>(null);
// 数据驾驶舱-会员移动端有效量查询
const getOrgData = () => {
  const params: QueryOrgDataParams = {
    ...areaData.value,
    ...dateData.value
  }

  queryOrgEffective(params).then((res: QueryOrgEffectiveRes) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    effectiveData.value.effectiveTotal = res.data.effectiveTotal
    targetTable.value.setData(res.data.effectiveList)
  })
}
// --------end 会员指标------------

// --------start 排行榜------------
const rankData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: "排名",
      prop: "num",
      width: "76",
      slot: "num",
    },
    {
      name: "移动端",
      subTitle:"总有效量",
      prop: "mobileEffectiveQuantity",
      width: "200",
      sort: true,
      sortType: "desc",
      orderByColumn: 1,
    },
    {
      name: "扫码",
      subTitle:"下单量",
      prop: "scanCodeOrderQuantity",
      width: "200",
      sort: true,
      sortType: "",
      orderByColumn: 2,
    },
    {
      name: "扫码下单",
      subTitle:"有效量",
      prop: "scanCodeOrderEffectiveQuantity",
      width: "200",
      sort: true,
      sortType: "",
      orderByColumn: 2,
    },
    {
      name: "移动端",
      subTitle:"间夜量",
      prop: "mobileOvernightQuantity",
      width: "160",
      sort: true,
      sortType: "",
      orderByColumn: 3,
    },
    {
      name: "移动端间夜",
      subTitle:"有效量",
      prop: "mobileOvernightEffectiveQuantity",
      width: "160",
      sort: true,
      sortType: "",
      orderByColumn: 3,
    },
    {
      name: "APP首次",
      subTitle: "登录量",
      prop: "appLoginQuantity",
      width: "160",
      sort: true,
      sortType: "",
      orderByColumn: 7,
    },
    {
      name: "APP首次登录",
      subTitle: "有效量",
      prop: "appLoginEffectiveQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 7,
    },
    {
      name: "APP首次",
      subTitle: "下单量",
      prop: "appOrderQuantity",
      width: "160",
      sort: true,
      sortType: "",
      orderByColumn: 6,
    },
    {
      name: "APP首次下单",
      subTitle: "有效量",
      prop: "appOrderEffectiveQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 6,
    },
    {
      name: "APP",
      subTitle:"间夜量",
      prop: "appOvernightQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 4,
    },
    {
      name: "小程序",
      subTitle:"间夜量",
      prop: "miniOvernightQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 5,
    },
    {
      name: "APP间夜",
      subTitle:"有效量",
      prop: "appOvernightEffectiveQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 4,
    },
    {
      name: "小程序间夜",
      subTitle:"有效量",
      prop: "miniOvernightEffectiveQuantity",
      width: "180",
      sort: true,
      sortType: "",
      orderByColumn: 5,
    },
 ],
  columnShow: [],
})
// 排行榜表头类型 1： 7月1号之前，2：7月2号之后；3：跨7月1号
const rankTheadType = ref<string>('2')
const rankLevel = ref<string>(areaData.value.level)
const updateColumn = ref<boolean>(true)
// 排行榜相关操作
const setRank = (params: {initSort?: boolean, isReload?: boolean}) => {
  // if (params.updateColumn) setRankColumn(params.level!) // 更新表头
  if (params.initSort) { // 初始化sort参数
    sortData.value = {
      orderBy: 1,
      sortBy: 'desc'
    }
  }
  if (params.isReload) {
    rankTable.value?.reload() // 更新列表，会触发getMore
  }
}

// 重置表头
const setRankColumn = () => {
  const level = rankLevel.value
  const rankTheadTypeColumns: Record<string, string[]> = {
    '1': [
      'num',
      'mobileEffectiveQuantity',
      'scanCodeOrderQuantity',
      'scanCodeOrderEffectiveQuantity',
      'appLoginQuantity',
      'appLoginEffectiveQuantity',
      'appOrderQuantity',
      'appOrderEffectiveQuantity'
    ],
    '2': [
      'num',
      'mobileEffectiveQuantity',
      'mobileOvernightQuantity',
      'mobileOvernightEffectiveQuantity',
      'appLoginQuantity',
      'appLoginEffectiveQuantity',
      'appOrderQuantity',
      'appOrderEffectiveQuantity',
      'appOvernightQuantity',
      'miniOvernightQuantity',
      'appOvernightEffectiveQuantity',
      'miniOvernightEffectiveQuantity'
    ],
    '3': [
      'num',
      'mobileEffectiveQuantity',
      'scanCodeOrderQuantity',
      'scanCodeOrderEffectiveQuantity',
      'mobileOvernightQuantity',
      'mobileOvernightEffectiveQuantity',
      'appLoginQuantity',
      'appLoginEffectiveQuantity',
      'appOrderQuantity',
      'appOrderEffectiveQuantity',
      'appOvernightQuantity',
      'miniOvernightQuantity',
      'appOvernightEffectiveQuantity',
      'miniOvernightEffectiveQuantity'
    ]
  };
  // 根据 rankTheadType 筛选表头列
  const filteredColumns = rankData.value.column.filter((column) =>
    rankTheadTypeColumns[rankTheadType.value].includes(column.prop)
  );
  rankData.value.columnShow = JSON.parse(JSON.stringify(filteredColumns));
 
  if (Number(level) <= 20) { // 地区总部
    rankData.value.columnShow.splice(1, 0, {
      name: '负责人',
      prop: 'orgUserName',
      width: '90',
    });
    rankData.value.columnShow.splice(1, 0, {
      name: '地区总部',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '30') { // 大区
    rankData.value.columnShow.splice(1, 0, {
      name: '大区总',
      prop: 'orgUserName',
      width: '90',
    });
    rankData.value.columnShow.splice(1, 0, {
      name: '大区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '40') { // 城区
    rankData.value.columnShow.splice(1, 0, {
      name: '城区总',
      prop: 'orgUserName',
      width: '90',
    });
    rankData.value.columnShow.splice(1, 0, {
      name: '城区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '50') { // 门店
    rankData.value.columnShow.splice(1, 0, {
      name: '店长',
      prop: 'orgUserName',
      width: '90',
    });
    rankData.value.columnShow.splice(1, 0, {
      name: '门店',
      prop: 'orgName',
      width: '200',
    });
    rankData.value.columnShow.splice(1, 0, {
      name: '门店ID',
      prop: 'orgId',
      width: '90',
    });
  }

}

const tabType = ref<string>('10')
// 1.更新表头 2.初始化排序 3.更新列表
const handleChange = (level: string) => {
  rankLevel.value = level
  setRank({initSort: true, isReload: true})
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_membership_mobile_org_rank_choose',
    button_name: '数舱-会员-移动端有效量-排行版组织-层级选择',
    content_name: level === '地区总部' ? '' : level === '30' ? '大区' : level === '40' ? '城区' : '门店',
  })
}

// 列表中选择下钻 => 1.更新表头 2.初始化排序 3.更新列表
const chooseItemParam = ref<{orgId: number, level: string, tabType: string}>()
const chooseItem = (data: QueryEffectiveRankRes) => {
  if (!data.canClick) return

  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_membership_mobile_org_rank_choose',
    button_name: '数舱-会员-移动端有效量-排行版组织-层级选择',
    content_name: data.level === '地区总部' ? '' : data.level === '30' ? '大区' : data.level === '40' ? '城区' : '门店',
  })
  tabType.value = ''
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
    tabType: (data.level === '10' ? Number(data.level) + 20 : Number(data.level) + 10).toString()
  }
  rankLevel.value = chooseItemParam.value.tabType 
  setRank({initSort: true, isReload: true})
}

// 排序参数
const sortData = ref<{orderBy: number, sortBy: string}>({
  orderBy: 1,
  sortBy: 'desc'
})
// 更新排序 => 1.更新列表
const updateList = (item: Column) => {
  sortData.value = {
    orderBy: item.orderByColumn as number, 
    sortBy: item.sortType as string
  }
  updateColumn.value = false
  setRank({initSort: false, isReload: true})
}

const rankTable = ref<InstanceType<typeof cTable> | null>(null);
// 获取排名数据、初始化、加载更多、更新列表触发
const getRanks = (pageNum?: number, pageSize?: number) => {
  const params: QueryBrandRankParams = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    orgId: tabType.value ? areaData.value.orgId : chooseItemParam.value?.orgId,
    level: tabType.value ? areaData.value.level : chooseItemParam.value?.level,
    ...sortData.value,
    ...dateData.value
  }
  params.tabType = tabType.value || chooseItemParam.value?.tabType
 
  queryEffectiveRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    } 
    rankTheadType.value = res.data.type
    if(updateColumn.value){
      setRankColumn()
    } 
    updateColumn.value = true
    rankTable.value.setData(res.data.dataList || []);
  })
}
// --------end 排行榜------------

const modalShow = ref<boolean>(false);
const modalTitle = ref<string>("");
const modalContent = ref<string>("");
const showModal = () => {
  modalShow.value = true;
  modalTitle.value = "规则说明";
  const _content = `
  1、移动端总有效量=APP首次登录有效量+APP首次下单有效量+移动端间夜有效量(APP+小程序)。\n
  1个APP首次登录量计1个移动端有效量，1个APP首次下单量计2个移动端有效量。\n
  移动端间夜=APP间夜+小程序间夜，APP间夜计1个有效量，小程序间夜计0.5个有效量(筛选时间内总移动端有效量向上取整)。\n
  2、APP首次登录移动端有效量:APP首次登录是指扫码至登录时间≤30分钟，下载APP并首次登录。\n
  3、APP首次下单移动端有效量:APP首次下单是指首次登录至首次下单时间≤30天 \n
  4、移动端间夜有效量:包括APP和小程序正常统计的直销间夜
  `
  modalContent.value = _content;
};

// 图表相关
const effectiveDetail = ref<{
  categories: Array<string>;
  data: Array<{ name: string; data: Array<string | number> }>;
}>({
  categories: [],
  data: [],
});
const buildChartDataByType = {
  '1': (resData: any[]) => {
    return [
      {
        name: '移动端总有效量',
        data: resData.map((item) => item.mobileEffectiveQuantity)
      },
      {
        name: '扫码下单有效量',
        data: resData.map((item) => item.scanCodeOrderEffectiveQuantity)
      },
      {
        name: 'APP首次登录有效量',
        data: resData.map((item) => item.appLoginEffectiveQuantity)
      },
      {
        name: 'APP首次下单有效量',
        data: resData.map((item) => item.appOrderEffectiveQuantity)
      }
    ];
  },
  '2': (resData: any[]) => {
    return [
      {
        name: '移动端总有效量',
        data: resData.map((item) => item.mobileEffectiveQuantity)
      },
      {
        name: '移动端间夜有效量',
        data: resData.map((item) => item.mobileOvernightEffectiveQuantity)
      },
      {
        name: 'APP首次登录有效量',
        data: resData.map((item) => item.appLoginEffectiveQuantity)
      },
      {
        name: 'APP首次下单有效量',
        data: resData.map((item) => item.appOrderEffectiveQuantity)
      },
      // {
      //   name: 'APP间夜有效量',
      //   data: resData.map((item) => item.appOvernightEffectiveQuantity)
      // },
      // {
      //   name: '小程序间夜有效量',
      //   data: resData.map((item) => item.miniOvernightEffectiveQuantity)
      // },
    ];
  },
  '3': (resData: any[]) => {
    return [
      {
        name: '移动端总有效量',
        data: resData.map((item) => item.mobileEffectiveQuantity)
      },
      {
        name: '扫码下单有效量',
        data: resData.map((item) => item.scanCodeOrderEffectiveQuantity)
      },
      {
        name: '移动端间夜有效量',
        data: resData.map((item) => item.mobileOvernightEffectiveQuantity)
      },
      {
        name: 'APP首次登录有效量',
        data: resData.map((item) => item.appLoginEffectiveQuantity)
      },
      {
        name: 'APP首次下单有效量',
        data: resData.map((item) => item.appOrderEffectiveQuantity)
      },
      // {
      //   name: 'APP间夜有效量',
      //   data: resData.map((item) => item.appOvernightEffectiveQuantity)
      // },
      // {
      //   name: '小程序间夜有效量',
      //   data: resData.map((item) => item.miniOvernightEffectiveQuantity)
      // },
    ];
  }
};
const chartDataColor = reactive({
  '1': {
     baseCustomColor:['#FCAD0D','#6046F2','#43BF83','#91BAFF'],
     gradientColor:['#FFDC5C','#6046F2','#BCE3D0','#1474FB']
  },
  '2': {
     baseCustomColor:['#FCAD0D','#F75E3B','#43BF83','#91BAFF'],
     gradientColor:['#FFDC5C','#FFB2A1','#BCE3D0','#1474FB']
  },
  '3': {
     baseCustomColor:['#FCAD0D','#6046F2','#F75E3B','#43BF83','#91BAFF'],
     gradientColor:['#FFDC5C','#6046F2','#FFB2A1','#BCE3D0','#1474FB']
  },
})

// 判断是否7月1日之前
const isBeforeJulyFirst = () => {
  const currentDate = dayjs();
  const julyFirst = dayjs().month(6).date(1); // month 从 0 开始，6 代表 7 月
  return currentDate.isBefore(julyFirst)
};

// 获取图表数据
const getEffectiveBoard = () => {
  const params: QueryOrgDataParams = {
    ...areaData.value,
    ...dateData.value
  }
  effectiveDetail.value = {
    categories: [],
    data: []
  }

  queryEffectiveBoard(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }
    const chartData = buildChartDataByType[res.data.type || '2'](res.data?.dataList);
    effectiveDetail.value = {
      categories: res.data?.dataList?.map((item: EffectiveBoardIndexVO) => item.date) || [],
      data:chartData
    }
  })
}

const pageOptions : any = inject('pageOptions')
const cDatePickerRef = ref<InstanceType<typeof cDatePicker> | null>(null)
const defaultAreaName = ref<string>('')
const isRankInitialized = ref<boolean>(true) //排行榜是否已经初始化
let startTime: number = 0
onMounted(() => {
  uni.$on('memberRefresh', () => {
    init()
  })
  init()
})

onUnmounted(()=> {
  uni.$off('memberRefresh')
})

const init = () => {
  startTime = Date.now()
  if (pageOptions.value?.areaData) { // ai助手跳转携带参数
    const data = pageOptions.value.areaData
    if (data.orgId) {
      areaData.value = Object.assign(areaData.value, data)
      defaultAreaName.value = areaData.value.orgName
    } else {
      areaData.value.orgId = userInfo.value.orgList[0].orgId
      areaData.value.level = userInfo.value.orgList[0].level
      areaData.value.orgName = userInfo.value.orgList[0].orgName
      defaultAreaName.value = userInfo.value.orgList[0].orgName
    }
  }
  if (pageOptions.value?.dateType === 'month') {
    cDatePickerRef.value.dateType = 'month'
    // 获取当前年份（字符串格式）
    const currentYear = dayjs().format('YYYY');
    // 获取当前月份（字符串格式，自动补零）
    const currentMonthMM = dayjs().format('MM');
    const currentMonth = dayjs().format('M');
    cDatePickerRef.value.dateShow = `${currentYear}-${currentMonthMM}`;
    setDate('month', currentYear, currentMonth)
    rankTheadType.value = Number(currentMonth) < 7 ? '1' : '2'
  } else {
    cDatePickerRef.value.dateType = 'day'
    setDate('day', new Date().getFullYear(), formatDate(Date.now() - 24 * 60 * 60 * 1000))
    rankTheadType.value = isBeforeJulyFirst() ? '1' : '2'
  }
  isRankInitialized.value = false
  rankLevel.value = areaData.value.level
  setRank({initSort: true, isReload: true})
  pageOptions.value = null
}

onUnmounted(() => {
  // trackEvent('D024', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.member-effective {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .member-tab-wrapper {
    display: flex;
    justify-content: center;
    .member-tab-btn {
      display: inline-flex;
      align-items: center;
      width: fit-content;
      height: 48rpx;
      margin: 0 20rpx 30rpx;
      border-radius: 24rpx;
      background: #F3DED9;
      .btn-item {
        padding: 0 24rpx;
        font-size: 22rpx;
        line-height: 48rpx;
        color: #1F2428;
      }

      .active {
        border-radius: 22rpx;
        background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
        color: #fff;
      }
    }
  }
  .data-area {
    padding: 0 20rpx;
  }

  .data-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx 20rpx;
		margin-top: 20rpx;

    .target-view {
      padding: 32rpx 40rpx 40rpx;
      background: #fff;
      border-radius: 22rpx;

      .target-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-left {
          display: flex;
          .title-text {
            font-size: 32rpx;
            font-weight: bold;
          }
          
          .sub-title-text {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #999999;
          }
          .item-info {
            margin-left: 8rpx;
            width: 22rpx;
            height: 22rpx;
          }
        }
        &-right {
        display: flex;
        align-items: center;
          .num {
            color: #1f2428;
            font-size: 22rpx;
            font-weight: 500;
            font-family: "苹方-简";
          }
          .rate-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8rpx;
            .item-icon {
              width: 16rpx;
              height: 20rpx;
              margin-right: 8rpx;
            }

            .item-text {
              font-size: 24rpx;
              line-height: 24rpx;
              font-weight: bold;
              font-family: "DIN Bold";
              color: #1f2428;
            }

            .up-text {
              color: #ff4d4d;
            }

            .down-text {
              color: #56cc93;
            }
          }
        }
      }

      .target-table-wrapper {
        overflow-x: auto;
        margin-top: 40rpx;
        .target-table-container {
          min-width: max-content;
          height: 100%;
          overflow: hidden;
          display: flex; 
          .target-table {
            height: 580rpx;
            border-radius: 20rpx;
            flex: 1;

            .target-item {
              display: flex;
              align-items: center;
              justify-content: center;

              .item-unit {
                padding-top: 4rpx;
                font-size: 18rpx;
                line-height: 18rpx;
              }

              .item-info {
                margin-left: 8rpx;
                width: 22rpx;
                height: 22rpx;
              }
            }

            .rate-item {
              display: flex;
              align-items: center;
              justify-content: center;

              .item-icon {
                width: 16rpx;
                height: 20rpx;
              }

              .item-text {
                padding-left: 8rpx;
                font-size: 24rpx;
                line-height: 24rpx;
                font-weight: bold;
                font-family: "DIN Bold";
                color: #1f2428;
              }

              .up-text {
                color: #ff4d4d;
              }

              .down-text {
                color: #56cc93;
              }
            }

            .th,
            .tbody {
              position: relative;

              .tr {
                .td:first-child {
                  position: sticky;
                  left: 0;
                  background: #f3f7ff;
                  z-index: 1;
                  min-width: 40px;
                  max-width: 40px;
                }
              }
            }
          }

          .target-table-mini {
            height: 504rpx;
          }

          .effective-chart {
            margin-left: 20rpx;
            width: 800rpx;
            background: #FFF9F7;
            border-radius: 28rpx;
            padding: 20rpx 20rpx 0 20rpx;
            height: 580rpx;
            box-sizing: border-box;
          }
     
        }
      }
    }

    .ranking-view {
      padding: 32rpx 40rpx 40rpx;
      margin: 20rpx 0 200rpx 0;
      background: #fff;
      border-radius: 22rpx;

      .ranking-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .title-left {
          display: flex;
          align-items: center;
          height: 32rpx;

          .title-text {
            font-size: 32rpx;
            font-weight: bold;
          }

          .sub-title-text {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .ranking-btn {
        padding: 40rpx 0 20rpx 0;
      }

      .ranking-table {
        height: 756rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .name-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .item-text {
            display: -webkit-box;
            max-width: 120rpx;
            overflow: hidden;//文本超出隐藏
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;//文本显示方式，默认水平
            -webkit-line-clamp: 2;//设置显示多少行
          }

          .line-item {
            text-decoration: underline;
          }

          .item-info {
            padding-top: 12rpx;
            display: flex;
            align-items: center;

            .info-text {
              font-size: 18rpx;
              line-height: 20rpx;
              color: #999999;
            }

            .item-icon {
              width: 12rpx;
              height: 16rpx;
              margin: 0 8rpx;
            }

            .item-num {
              font-size: 20rpx;
              line-height: 20rpx;
              font-weight: bold;
              font-family: "DIN Bold";
            }

            .up-num {
              color: #FF4D4D;
            }

            .down-num {
              color: #56CC93;
            }
          }
        }
      }
    }
  }
}
</style>