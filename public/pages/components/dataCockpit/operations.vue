<template>
  <view class="operations-index">
    <view class="data-area">
      <cAreaPicker @setArea="setArea" @toSearch="toSearch" :defaultName="defaultAreaName"></cAreaPicker>
    </view>

    <view class="data-content">
      <view class="target-view">
        <view class="target-title">
          <text class="title-text">运营指标</text>
          <view class="title-btn" @click="toDetail">
            <text class="btn-text">动因分析</text>
            <up-icon name="arrow-right" color="#1F2428" size="20rpx" />
          </view>
        </view>

        <cDatePicker ref="cDatePickerRef" @setDate="setDate"></cDatePicker>

        <view class="target-table">
          <cTable ref="targetTable" :header="operationsData.columnShow" :isPaginate="false" :isZPaging="true">
            <template #default="scope">
              <template v-if="['actualValue', 'valueBudget', 'valueCompletion'].includes(scope.prop)">
                {{ scope.data[scope.prop] || '-' }}{{ scope.prop === 'valueCompletion' || scope.data.unit === '%' ? '%' : '' }}
              </template>
              <view v-if="['valueTb', 'valueHb'].includes(scope.prop)" class="rate-item">
                <image v-if="scope.data[scope.prop]" class="item-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                <text class="item-text" :class="[scope.data[scope.prop] > 0 ? 'up-text' : scope.data[scope.prop] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop]) + '%' }}</text>
              </view>
            </template>
          </cTable>
        </view>
      </view>

      <view class="ranking-view">
        <view class="ranking-title">
          <view class="title-left">
            <text class="title-text">排行榜</text>
            <text class="sub-title-text">本月</text>
          </view>
          <cTabs :list="typeList" v-model:modelValue="typeIndex" :isBig="false" textColor="#1F2428" @change="handleChangeType"></cTabs>
        </view>

        <template v-if="typeIndex === 0">
          <view class="ranking-btn">
            <cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleChange"></cBtnGroup>
          </view>
          
          <view class="ranking-table">
            <cTable ref="rankTable" :header="managementData.columnShow" :isZPaging="true" @updateList="updateList" @getMore="getRanks" @chooseItem="chooseItem">
              <template #default="scope">
                <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
                <template v-if="['gmvCompletionRate', 'occ', 'crs'].includes(scope.prop)">{{ scope.data[scope.prop] !== null ? `${scope.data[scope.prop]}%` : '-' }}</template>
              </template>
            </cTable>
          </view>
        </template>
        
        <view v-else class="brand-table">
            <cTable ref="brandTable" :header="brandData.column" :isZPaging="true" @updateList="updateList" @getMore="getRanks">
              <template #default="scope">
                <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
                <template v-else>{{ `${scope.data[scope.prop]}%` }}</template>
              </template>
            </cTable>
          </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="operationsIndex">
  import {
    ref,
    reactive,
    getCurrentInstance,
    ComponentInternalInstance,
    onMounted,
    nextTick,
    onUnmounted,
    inject
  } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cDatePicker from '@/components/c-date-picker/index.vue'
import cTable from '@/components/c-table/c-table.vue'
import cBtnGroup from '@/components/c-btn-group/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import { queryOrgData, queryOrgRank, queryBrandRank } from '@/api/operations'
import { successCallbackResult } from '@/types'
import type { DateData, QueryOrgDataParams, QueryOrgRankRes, QueryBrandRankParams } from '@/api/operations'
import { formatDate, showToast } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import { onLoad } from '@dcloudio/uni-app'
import dayjs from "dayjs";

const	{ trackEvent, qdTrackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

onLoad((option: any) => {
  areaData.value.orgId = option.id;
});

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)

const toSearch = () => {
  uni.navigateTo({
    url: `/packageB/pages/select-store/select-store?from=operations&orgId=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}`
  })

  trackEvent('D018')
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_operations_hotel',
    button_name: '数舱-运营-门店',
  })
}

// 组织树相关
const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: userInfo.value.orgList[0].orgId,
  level: userInfo.value.orgList[0].level,
  orgName: userInfo.value.orgList[0].orgName,
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name
  
  nextTick(() => {
    if (!tabType.value) {
      tabType.value = Number(level) <= 10 ? '10' : level.toString()
    }
    getOrgData()
    setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  })
}

// --------start 运营指标------------
const operationsData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '指标',
      prop: 'dataName',
      width: '180',
      canClick: true,
      type: 'info',
      showUnit: true
    },
    {
      name: '实际值',
      prop: 'actualValue',
      width: '160',
      slot: 'actualValue',
    },
    {
      name: '预算',
      prop: 'valueBudget',
      width: '160',
      slot: 'valueBudget',
    },
    {
      name: '完成率',
      prop: 'valueCompletion',
      width: '120',
      slot: 'valueCompletion',
    },
    {
      name: '同比',
      prop: 'valueTb',
      width: '150',
      info: '对比同期增速',
      slot: 'valueTb',
    },
    {
      name: '环比',
      prop: 'valueHb',
      width: '150',
      info: '对比上期增速',
      slot: 'valueHb',
    },
    {
      name: '排名(完成率)',
      subTitle: '地区总部',
      prop: 'valueRank',
      width: '150',
    }
  ],
  columnShow: []
});

const dateData = ref<DateData>({
  dateType: 'day',
  atime: formatDate(Date.now() - 24 * 60 * 60 * 1000)
})
// 切换日期选择、初始化入口
const setDate = (type: string, value1: string | number, value2?: string | number) => {
  if (type === 'day') {
    dateData.value = {
      dateType: 'day',
      atime: value2
    }
    operationsData.value.columnShow = operationsData.value.column
  } else if (type === 'month') {
    dateData.value = {
      dateType: 'month',
      year: value1,
      month: value2
    }
    operationsData.value.columnShow = operationsData.value.column
  } else if (type === 'quarter') {
    dateData.value = {
      dateType: 'quarter',
      year: value1,
      quarter: value2
    }
    operationsData.value.columnShow = operationsData.value.column
  } else if (type === 'year') {
    dateData.value = {
      dateType: 'year',
      year: value1,
    }
    operationsData.value.columnShow = operationsData.value.column.filter((item: Column) => item.prop !== 'valueHb')
  } else if (type === 'range') {
    dateData.value = {
      dateType: 'range',
      startDate: value1,
      endDate: value2
    }
    operationsData.value.columnShow = JSON.parse(JSON.stringify(operationsData.value.column.filter((item: Column) => (item.prop !== 'valueBudget' && item.prop !== 'valueCompletion' && item.prop !== 'valueTb' && item.prop !== 'valueHb' && item.prop !== 'valueRank'))))
    operationsData.value.columnShow.forEach((item: Column) => {
      delete item.width
    })
  }

  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_operations_date_choose',
    button_name: '数舱-运营-日期选择',
  })
  getOrgData()
}

const targetTable = ref<InstanceType<typeof cTable> | null>(null);
// 数据驾驶仓运营指标查询
const getOrgData = () => {
  const params: QueryOrgDataParams = {
    ...areaData.value,
    ...dateData.value
  }

  queryOrgData(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    targetTable.value.setLocalPaging(res.data)
  })
}
// --------end 运营指标------------

// --------start 排行榜------------
const managementData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num',
    },
    {
      name: '房费收入',
      subTitle: '/万元',
      prop: 'gmv',
      width: '180',
      sort: true,
      sortType: '',
      orderByColumn: 1,
    }, 
    {
      name: '完成率',
      subTitle: '房费收入',
      prop: 'gmvCompletionRate',
      width: '140',
      sort: true,
      sortType: 'desc',
      orderByColumn: 6,
      slot: 'gmvCompletionRate',
    }, 
    {
      name: 'OCC',
      prop: 'occ',
      width: '120',
      sort: true,
      sortType: '',
      slot: 'occ',
      orderByColumn: 2,
    }, 
    {
      name: 'ADR',
      prop: 'adr',
      subTitle: '/元',
      width: '120',
      sort: true,
      sortType: '',
      orderByColumn: 3,
    }, 
    {
      name: 'RevPar',
      prop: 'revpar',
      subTitle: '/元',
      width: '130',
      sort: true,
      sortType: '',
      orderByColumn: 4,
    },
    {
      name: 'CRS占比',
      prop: 'crs',
      width: '140',
      sort: true,
      sortType: '',
      orderByColumn: 5,
      slot: 'crs',
    }
  ],
  columnShow: [],
})

const brandData = ref<{ column: Array<Column> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num'
    },
    {
      name: '品牌',
      prop: 'brandName',
      width: '134',
    }, 
    {
      name: '房费收入',
      subTitle: '/万元',
      prop: 'gmv',
      width: '180',
      sort: true,
      sortType: 'desc',
      orderByColumn: 1,
    }, 
    {
      name: 'OCC',
      prop: 'occ',
      width: '100',
      sort: true,
      sortType: '',
      slot: 'occ',
      orderByColumn: 2,
    }, 
    {
      name: 'ADR',
      prop: 'adr',
      subTitle: '/元',
      width: '100',
      sort: true,
      sortType: '',
      orderByColumn: 3,
    }, 
    {
      name: 'RePar',
      prop: 'revpar',
      subTitle: '/元',
      width: '110',
      sort: true,
      sortType: '',
      orderByColumn: 4,
    },
    {
      name: 'CRS占比',
      prop: 'crs',
      width: '120',
      sort: true,
      sortType: '',
      orderByColumn: 5,
      slot: 'crs',
    }
  ]
})
// 组织排行榜相关操作
const setRank = (params: {updateColumn?: boolean, level?: string, initSort?: boolean, isReload?: boolean}) => {
  if (params.updateColumn) setRankColumn(params.level!) // 更新表头
  if (params.initSort) { // 初始化sort参数
    sortData.value = {
      orderBy: typeIndex.value === 0 ? 6 : 1,
      sortBy: 'desc'
    }
  }
  if (params.isReload) {
    if (typeIndex.value === 0) {
      rankTable.value?.reload() // 更新列表，会触发getMore
    } else {
      brandTable.value?.reload()
    }
  }
}

// 重置表头
const setRankColumn = (level: string) => {
  managementData.value.columnShow = JSON.parse(JSON.stringify(managementData.value.column))
  if (Number(level) <= 20) { // 地区总部
    managementData.value.columnShow.splice(1, 0, {
      name: '负责人',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '地区总部',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '30') { // 大区
    managementData.value.columnShow.splice(1, 0, {
      name: '大区总',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '大区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '40') { // 城区
    managementData.value.columnShow.splice(1, 0, {
      name: '城区总',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '城区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '50') { // 门店
    managementData.value.columnShow.splice(1, 0, {
      name: '店长',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '门店',
      prop: 'orgName',
      width: '200',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '门店ID',
      prop: 'orgId',
      width: '90',
    });
  }
}

// 组织、品牌切换
const typeList = reactive([  
  { name: '组织' },  
  { name: '品牌' }
]);
const typeIndex = ref<number>(0)
// 切换组织、品牌 => 1.更新表头 2.初始化排序 3.更新列表
const handleChangeType = () => {
  setRank({updateColumn: !typeIndex.value, level: areaData.value.level, initSort: true})
  if (typeIndex.value === 0) {
    trackEvent('D019')
    qdTrackEvent('smart_web_click', {
      page_id: 'data_asset_page',
      button_id: 'data_operations_org_rank',
      button_name: '数舱-运营-排行榜组织',
    })
  } else if (typeIndex.value === 1) {
    trackEvent('D020')
    qdTrackEvent('smart_web_click', {
      page_id: 'data_asset_page',
      button_id: 'data_operations_brand_rank',
      button_name: '数舱-运营-排行榜品牌',
    })
  }
}
const tabType = ref<string>('10')
// 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const handleChange = (level: string) => {
  setRank({updateColumn: true, level, initSort: true, isReload: true})
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_operations_org_rank_choose',
    button_name: '数舱-运营-排行榜组织_层级选择',
    content_name: level === '地区总部' ? '' : level === '30' ? '大区' : level === '40' ? '城区' : '门店',
  })
}

// 列表中选择下钻 => 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const chooseItemParam = ref<{orgId: number, level: string, tabType: string}>()
const chooseItem = (data: QueryOrgRankRes) => {
  if (!data.canClick) return

  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_operations_org_rank_choose',
    button_name: '数舱-运营-排行榜组织_层级选择',
    content_name: data.level === '地区总部' ? '' : data.level === '30' ? '大区' : data.level === '40' ? '城区' : '门店',
  })
  tabType.value = ''
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
    tabType: (data.level === '10' ? Number(data.level) + 20 : Number(data.level) + 10).toString()
  }
  setRank({updateColumn: true, level: chooseItemParam.value.tabType, initSort: true, isReload: true})
}

// 排序参数（组织、品牌）
const sortData = ref<{orderBy: number, sortBy: string}>({
  orderBy: typeIndex.value === 0 ? 6 : 1,
  sortBy: 'desc'
})
// 更新排序 => 1.更新列表
const updateList = (item: Column) => {
  sortData.value = {
    orderBy: item.orderByColumn as number, 
    sortBy: item.sortType as string
  }
  setRank({updateColumn: false, initSort: false, isReload: true})
}

const rankTable = ref<InstanceType<typeof cTable> | null>(null);
const brandTable = ref<InstanceType<typeof cTable> | null>(null);
// 获取排名数据、初始化、加载更多、更新列表触发
const getRanks = (pageNum?: number, pageSize?: number) => {
  const params: QueryBrandRankParams = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    orgId: tabType.value || typeIndex.value ? areaData.value.orgId : chooseItemParam.value?.orgId,
    level: tabType.value || typeIndex.value ? areaData.value.level : chooseItemParam.value?.level,
    ...sortData.value,
  }

  if (typeIndex.value === 0) {
    params.tabType = tabType.value || chooseItemParam.value?.tabType
  }

  const fn = typeIndex.value === 0 ? queryOrgRank : queryBrandRank
  fn(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    if (typeIndex.value === 0) {
      rankTable.value.setData(res.data)
    } else {
      brandTable.value.setData(res.data)
    }
  })
}
// --------end 排行榜------------

// 跳转动因分析
const toDetail = () => {
  uni.navigateTo({
    url: `/packageA/pages/motivationAnalysis/index?id=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}&tabName=operations`
  })
  trackEvent('D021')
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_operations_reason',
    button_name: '数舱-运营-动因分析',
  })
}

const pageOptions : any = inject('pageOptions')
const cDatePickerRef = ref(null)
const defaultAreaName = ref<string>('')

let startTime: number = 0
onMounted(() => {
  uni.$on('operationsRefresh', () => {
    init()
  })
  init()
})

onUnmounted(()=> {
  uni.$off('operationsRefresh')
})

const init = () => {
  startTime = Date.now()
  if (pageOptions.value?.areaData) { // ai助手跳转携带参数
    const data = pageOptions.value.areaData
    if (data.orgId) {
      areaData.value = Object.assign(areaData.value, data)
      defaultAreaName.value = areaData.value.orgName
    } else {
      areaData.value.orgId = userInfo.value.orgList[0].orgId
      areaData.value.level = userInfo.value.orgList[0].level
      areaData.value.orgName = userInfo.value.orgList[0].orgName
      defaultAreaName.value = userInfo.value.orgList[0].orgName
    }
  }
  if (pageOptions.value?.dateType === 'month') {
    cDatePickerRef.value.dateType = 'month'
    // 获取当前年份（字符串格式）
    const currentYear = dayjs().format('YYYY');
    // 获取当前月份（字符串格式，自动补零）
    const currentMonthMM = dayjs().format('MM');
    const currentMonth = dayjs().format('M');
    cDatePickerRef.value.dateShow = `${currentYear}-${currentMonthMM}`;
    setDate('month', currentYear, currentMonth)
  } else {
    cDatePickerRef.value.dateType = 'day'
    setDate('day', new Date().getFullYear(), formatDate(Date.now() - 24 * 60 * 60 * 1000))
  }
  setRank({updateColumn: true, level: areaData.value.level, initSort: true, isReload: true})
  pageOptions.value = null
}

onUnmounted(() => {
  trackEvent('D017', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.operations-index {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .data-area {
    padding: 0 20rpx;
  }

  .data-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx 20rpx;
		margin-top: 20rpx;

    .target-view {
      padding: 32rpx 40rpx 40rpx;
      background: #fff;
      border-radius: 22rpx;

      .target-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }

        .title-btn {
          display: flex;
          align-items: center;
          padding: 8rpx 16rpx;
          background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
          border-radius: 16rpx;

          .btn-text {
            padding-right: 8rpx;
            font-size: 22rpx;
            line-height: 22rpx;
            color: #1F2428;
          }
        }
      }

      .target-table {
        height: 504rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .target-item {
          display: flex;
          align-items: center;
          justify-content: center;

          .item-unit {
            padding-top: 4rpx;
            font-size: 18rpx;
            line-height: 18rpx;
          }

          .item-info {
            margin-left: 8rpx;
            width: 22rpx;
            height: 22rpx;
          }
        }

        .rate-item {
          display: flex;
          align-items: center;
          justify-content: center;

          .item-icon {
            width: 16rpx;
            height: 20rpx;
          }

          .item-text {
            padding-left: 8rpx;
            font-size: 24rpx;
            line-height: 24rpx;
            font-weight: bold;
            font-family: "DIN Bold";
            color: #1F2428;
          }

          .up-text {
            color: #FF4D4D;
          }

          .down-text {
            color: #56CC93;
          }
        }

        .th, .tbody {
          position: relative;

          .tr {
            .td:first-child {
              position: sticky;
              left: 0;
              background: #F3F7FF;
              z-index: 1;
              min-width: 40px;
              max-width: 40px;
            }
          }
        }
      }
    }

    .ranking-view {
      padding: 32rpx 40rpx 40rpx;
      margin: 20rpx 0 200rpx 0;
      background: #fff;
      border-radius: 22rpx;

      .ranking-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .title-left {
          display: flex;
          align-items: center;
          height: 32rpx;

          .title-text {
            font-size: 32rpx;
            font-weight: bold;
          }

          .sub-title-text {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .ranking-btn {
        padding: 8rpx 0 20rpx 0;
      }

      .ranking-table, .brand-table {
        height: 756rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .name-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .item-text {
            display: -webkit-box;
            max-width: 120rpx;
            overflow: hidden;//文本超出隐藏
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;//文本显示方式，默认水平
            -webkit-line-clamp: 2;//设置显示多少行
          }

          .line-item {
            text-decoration: underline;
          }

          .item-info {
            padding-top: 12rpx;
            display: flex;
            align-items: center;

            .info-text {
              font-size: 18rpx;
              line-height: 20rpx;
              color: #999999;
            }

            .item-icon {
              width: 12rpx;
              height: 16rpx;
              margin: 0 8rpx;
            }

            .item-num {
              font-size: 20rpx;
              line-height: 20rpx;
              font-weight: bold;
              font-family: "DIN Bold";
            }

            .up-num {
              color: #FF4D4D;
            }

            .down-num {
              color: #56CC93;
            }
          }
        }
      }
    }
  }
}
</style>