<template>
  <view class="assets-index">
    <view class="data-area">
      <cAreaPicker @setArea="setArea">
        <template #right>
          <view class="area-right" @click="toDetail">
            <image class="shop-img" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/1729644837689.png" mode="aspectFill"></image>
            <text class="shop-text">分类对比</text>
          </view>
        </template>
      </cAreaPicker>
    </view>

    <view class="data-content">
      <view class="shop-view">
        <text class="shop-time">数据截止日期：{{ formatDate(Date.now() - 24 * 60 * 60 * 1000) }}</text>
        <view class="shop-content">
          <view class="shop-item item-card">
            <text class="item-title">净增门店</text>
            <view class="item-text">
              <text class="text">本年累计</text>
              <text class="num">{{ shopClassData?.increaseShopCountYear }}</text>
            </view>
            <view class="item-text">
              <text class="text">本月累计</text>
              <text class="num">{{ shopClassData?.increaseShopCountMonth }}</text>
            </view>
          </view>

          <view class="shop-item item-new">
            <text class="item-title">新开店</text>
            <view class="item-text">
              <text class="text">本年累计</text>
              <text class="num">{{ shopClassData?.openShopCountYear }}</text>
            </view>
            <view class="item-text">
              <text class="text">本月累计</text>
              <text class="num">{{ shopClassData?.openShopCountMonth }}</text>
            </view>
          </view>

          <view class="shop-item item-termination">
            <text class="item-title">解约店</text>
            <view class="item-text">
              <text class="text">本年累计</text>
              <text class="num">{{ shopClassData?.closeShopCountYear }}</text>
            </view>
            <view class="item-text">
              <text class="text">本月累计</text>
              <text class="num">{{ shopClassData?.closeShopCountMonth }}</text>
            </view>
          </view>
        </view>

        <view class="shop-title">
          <text class="title-text">门店状态</text>
        </view>

        <view class="shop-chart">
          <view class="shop-btn-left">
            <view class="btn-item" :class="{ active: dataType === 1 }" @click="setDataType(1)">
              周期
            </view>
            <!-- <view class="btn-item" :class="{ active: dataType === 2 }" @click="setDataType(2)">
              标杆类型
            </view> -->
          </view>
        </view>
        <accumulationCharts v-if="dataType === 1" :detail="channelDetail" leftUnit="%"></accumulationCharts>
        <barCharts v-else :barNum="2" :lineNum="0" :detail="channelDetail" leftUnit="%"></barCharts>
      </view>

      <view class="ranking-view">
        <view class="ranking-title">
          <view class="title-left">
            <text class="title-text">排行榜</text>
          </view>
          <cTabs :list="typeList" v-model:modelValue="typeIndex" :isBig="false" textColor="#1F2428" @change="handleChangeType"></cTabs>
        </view>

        <template v-if="typeIndex === 0">
          <view class="ranking-btn">
            <cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleChange"></cBtnGroup>
          </view>
          
          <view class="ranking-table">
            <cTable ref="rankTable" :header="managementData.columnShow" :list="managementData.list" @chooseItem="chooseItem">
              <template #default="scope">
                <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
                <template v-else>
                  <text v-if="(tabType || chooseItemParam?.tabType) !== '50'">{{ scope.data[scope.prop] !== null ? scope.data[scope.prop] : '-' }}</text>
                  <template v-else>
                    <text v-if="!scope.data[scope.prop]">-</text>
                    <image v-else class="rank-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-03/1733193676610.png" mode="aspectFill"></image>
                  </template>
                </template>
              </template>
            </cTable>
            <cTable2 ref="rankTable2" :header="managementData.columnShow" :list="managementData.list" @chooseItem="chooseItem">
              <template #default="scope">
                <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
                <template v-else>
                  <text v-if="(tabType || chooseItemParam?.tabType) !== '50'">{{ scope.data[scope.prop] !== null ? scope.data[scope.prop] : '-' }}</text>
                  <template v-else>
                    <text v-if="!scope.data[scope.prop]">-</text>
                    <image v-else class="rank-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-03/1733193676610.png" mode="aspectFill"></image>
                  </template>
                </template>
              </template>
            </cTable2>
          </view>
        </template>
        
        <view v-else-if="typeIndex === 1" class="brand-table">
          <cTable ref="provinceTable" :header="provinceData.column" :isZPaging="true" @updateList="updateList" @getMore="getRanks">
            <template #default="scope">
              <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
              <template v-else>{{ scope.data[scope.prop] !== null ? scope.data[scope.prop] : '-' }}</template>
            </template>
          </cTable>
        </view>

        <view v-else class="brand-table">
          <cTable ref="brandTable" :header="brandData.column" :isZPaging="true" @updateList="updateList" @getMore="getRanks">
            <template #default="scope">
              <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
              <template v-else>{{ scope.data[scope.prop] !== null ? scope.data[scope.prop] : '-' }}</template>
            </template>
          </cTable>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="assetsIndex">
import { ref, reactive, getCurrentInstance, ComponentInternalInstance, onMounted, nextTick, onUnmounted } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cTable from '@/components/c-table/c-table.vue'
import cTable2 from '@/components/c-table/c-table2.vue'
import cBtnGroup from '@/components/c-btn-group/index.vue'
import cTabs from '@/components/c-tabs2/index.vue'
import accumulationCharts from '@/components/c-chart/accumulationCharts/index.vue'
import barCharts from '@/components/c-chart/barCharts/index.vue'
import { queryShopClass, queryShopCycle, queryOrgRank, queryProvinceRank } from '@/public/api/assets'
import { successCallbackResult } from '@/types'
import type { QueryShopClassParams, QueryShopClassRes, QueryShopCycleRes, QueryOrgRankParams, QueryOrgRankRes } from '@/packageA/api/assets'
import { formatDate, showToast } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import { onLoad } from '@dcloudio/uni-app'

const	{ trackEvent, qdTrackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

onLoad((option: any) => {
  areaData.value.orgId = option.id;
});

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)

// 组织树相关
const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: userInfo.value.orgList[0].orgId,
  level: userInfo.value.orgList[0].level,
  orgName: userInfo.value.orgList[0].orgName,
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_org_choose',
    button_name: '数舱_选择组织',
  })
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name
  
  nextTick(() => {
    if (!tabType.value) {
      tabType.value = Number(level) <= 10 ? '10' : level.toString()
    }
    getShopClass()
    setDataType()
    setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  })
}

// --------start 门店状态------------
// 门店分类累计统计
const shopClassData = ref<QueryShopClassRes>()
const getShopClass = () => {
  const params: QueryShopClassParams = {
    orgId: areaData.value.orgId,
    level: areaData.value.level,
  }

  queryShopClass(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    shopClassData.value = res.data
  })
}

const dataType = ref<number>(1)
const channelDetail = ref<{categories: Array<string>, data: Array<{name: string, data: Array<string | number>}>}>({
  categories: [],
  data: []
})
// 切换周期、标杆类型
const setDataType = (type?: number) => {
  if (type === dataType.value) return
  if (type) dataType.value = type
  if (type === 1) {
    trackEvent('D005')
  } else if (type === 2) {
    trackEvent('D006')
  }

  getChannelData()
}

const getChannelData = () => {
  const params = {
    ...areaData.value,
    dataType: dataType.value,
  }

  channelDetail.value = {
    categories: [],
    data: []
  }
  queryShopCycle(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }
    if (!res.data) return

    channelDetail.value.categories = res.data.map((item: QueryShopCycleRes) => item.slotDate.slice(2))
    if (dataType.value === 1) {
      channelDetail.value.data.push({
        name: '新店',
        data: res.data.map((item: QueryShopCycleRes) => item.newShopRate)
      })
      channelDetail.value.data.push({
        name: '次新店',
        data: res.data.map((item: QueryShopCycleRes) => item.fairlyNewShopRate)
      })
      channelDetail.value.data.push({
        name: '成熟店',
        data: res.data.map((item: QueryShopCycleRes) => item.matureShopRate)
      })
      channelDetail.value.data.push({
        name: '老店',
        data: res.data.map((item: QueryShopCycleRes) => item.oldShopRate)
      })
    } 
    // else {
    //   channelDetail.value.data.push({
    //     name: '运营好店',
    //     data: res.data.map((item: QueryShopCycleRes) => item.operateShopRate)
    //   })
    //   channelDetail.value.data.push({
    //     name: '层层标杆店',
    //     data: res.data.map((item: QueryShopCycleRes) => item.benchmarkShopRate)
    //   })
    // }
  })
}
// --------end 门店状态------------

// --------start 排行榜------------
const commonColumn = ref<Array<Column>>([
  {
    name: '运营店',
    prop: 'openShopCount',
    width: '140',
    sort: true,
    sortType: 'desc',
    orderByColumn: 1,
    slot: 'openShopCount'
  }, 
  {
    name: '问题店',
    prop: 'oweShopCount',
    width: '140',
    sort: true,
    sortType: '',
    orderByColumn: 2,
    slot: 'oweShopCount'
  }, 
  {
    name: '舞弊店',
    prop: 'owlShopCount',
    width: '140',
    sort: true,
    sortType: '',
    orderByColumn: 3,
    slot: 'owlShopCount'
  }, 
  {
    name: '零售卡店',
    prop: 'zeroCardShopCount',
    width: '150',
    sort: true,
    sortType: '',
    orderByColumn: 4,
    slot: 'zeroCardShopCount'
  }, 
  // {
  //   name: '层层标杆店',
  //   prop: 'benchmarkShopCount',
  //   width: '160',
  //   sort: true,
  //   sortType: '',
  //   orderByColumn: 5,
  //   slot: 'benchmarkShopCount'
  // },
  // {
  //   name: '运营好店',
  //   prop: 'operateShopCount',
  //   width: '150',
  //   sort: true,
  //   sortType: '',
  //   orderByColumn: 6,
  //   slot: 'operateShopCount'
  // }
])
const managementData = ref<{ column: Array<Column>, columnShow: Array<Column>, list: Array<any> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num',
    },
    ...commonColumn.value
  ],
  columnShow: [],
  list: [
        {
            "orgId": "1247342",
            "orgName": "北方总部",
            "orgUserName": "谢厚利 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 1278,
            "oweShopCount": 135,
            "owlShopCount": 284,
            "zeroCardShopCount": 813,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247338",
            "orgName": "华东总部",
            "orgUserName": "叶照辉 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 913,
            "oweShopCount": 139,
            "owlShopCount": 268,
            "zeroCardShopCount": 557,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247341",
            "orgName": "华北总部",
            "orgUserName": "徐静 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 886,
            "oweShopCount": 128,
            "owlShopCount": 247,
            "zeroCardShopCount": 566,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247263",
            "orgName": "华南总部",
            "orgUserName": "徐哲 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 486,
            "oweShopCount": 128,
            "owlShopCount": 134,
            "zeroCardShopCount": 363,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247257",
            "orgName": "西南总部",
            "orgUserName": "颜伟志 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 327,
            "oweShopCount": 100,
            "owlShopCount": 79,
            "zeroCardShopCount": 194,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247256",
            "orgName": "电竞酒店事业部",
            "orgUserName": "刘星寒 ",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 30,
            "oweShopCount": 5,
            "owlShopCount": 0,
            "zeroCardShopCount": 10,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1247260",
            "orgName": "高端酒店事业部",
            "orgUserName": "",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 13,
            "oweShopCount": 10,
            "owlShopCount": 0,
            "zeroCardShopCount": 10,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "1969255",
            "orgName": "直营门店",
            "orgUserName": "",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 5,
            "oweShopCount": 0,
            "owlShopCount": 0,
            "zeroCardShopCount": 4,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        },
        {
            "orgId": "3000005",
            "orgName": "哥伦布项目",
            "orgUserName": "",
            "level": "10",
            "provinceId": null,
            "provinceName": null,
            "brandId": null,
            "brandName": null,
            "openShopCount": 1,
            "oweShopCount": 0,
            "owlShopCount": 0,
            "zeroCardShopCount": 1,
            "benchmarkShopCount": 0,
            "operateShopCount": 0,
            "canClick": true
        }
    ],
})

const provinceData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num',
    },
    {
      name: '省份',
      prop: 'provinceName',
      width: '180',
    }, 
    ...commonColumn.value
  ],
  columnShow: [],
})

const brandData = ref<{ column: Array<Column> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num'
    },
    {
      name: '品牌',
      prop: 'brandName',
      width: '134',
    }, 
    ...commonColumn.value
  ]
})
// 组织排行榜相关操作
const setRank = (params: {updateColumn?: boolean, level?: string, initSort?: boolean, isReload?: boolean}) => {
  if (params.updateColumn) setRankColumn(params.level!) // 更新表头
  if (params.initSort) { // 初始化sort参数
    sortData.value = {
      orderBy: 1,
      sortBy: 'desc'
    }
  }
  if (params.isReload) {
    if (typeIndex.value === 0) {
      // rankTable.value?.reload() // 更新列表，会触发getMore
      // getRanks()
    } else if (typeIndex.value === 1) {
      provinceTable.value?.reload()
    } else {
      brandTable.value?.reload()
    }
  }
}

// 重置表头
const setRankColumn = (level: string) => {
  managementData.value.column.forEach((item) => {
    if (item.prop === 'openShopCount') {
      item.sortType = 'desc'
    } else {
      item.sortType = ''
    }
  })
  provinceData.value.column.forEach((item) => {
    if (item.prop === 'openShopCount') {
      item.sortType = 'desc'
    } else {
      item.sortType = ''
    }
  })
  brandData.value.column.forEach((item) => {
    if (item.prop === 'openShopCount') {
      item.sortType = 'desc'
    } else {
      item.sortType = ''
    }
  })
  managementData.value.columnShow = JSON.parse(JSON.stringify(managementData.value.column))
  if (Number(level) <= 20) { // 地区总部
    managementData.value.columnShow.splice(1, 0, {
      name: '负责人',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '地区总部',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '30') { // 大区
    managementData.value.columnShow.splice(1, 0, {
      name: '大区总',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '大区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '40') { // 城区
    managementData.value.columnShow.splice(1, 0, {
      name: '城区总',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '城区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    });
  } else if (level === '50') { // 门店
    managementData.value.columnShow.splice(1, 0, {
      name: '店长',
      prop: 'orgUserName',
      width: '90',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '门店',
      prop: 'orgName',
      width: '200',
    });
    managementData.value.columnShow.splice(1, 0, {
      name: '门店ID',
      prop: 'orgId',
      width: '90',
    });
  }
}

// 组织、品牌切换
const typeList = reactive([  
  { name: '组织' },  
  { name: '省份' },  
  { name: '品牌' }
]);
const typeIndex = ref<number>(0)
// 切换组织、品牌 => 1.更新表头 2.初始化排序 3.更新列表
const handleChangeType = () => {
  setRank({updateColumn: true, level: areaData.value.level, initSort: true})
  if (typeIndex.value === 0) {
    trackEvent('D007')
    qdTrackEvent('smart_web_click', {
      page_id: 'data_asset_page',
      button_id: 'data_asset_org_rank',
      button_name: '数舱_资产排行榜组织',
    })
  } else if (typeIndex.value === 1) {
    trackEvent('D008')
    qdTrackEvent('smart_web_click', {
      page_id: 'data_asset_page',
      button_id: 'data_asset_province_rank',
      button_name: '数舱_资产排行榜省份',
    })
  } else {
    trackEvent('D009')
    qdTrackEvent('smart_web_click', {
      page_id: 'data_asset_page',
      button_id: 'data_asset_brand_rank',
      button_name: '数舱_资产排行榜品牌',
    })
  }
}
const tabType = ref<string>('10')
// 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const handleChange = (level: string) => {
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_asset_org_rank_choose',
    button_name: '数舱_资产排行榜组织层级选择',
    content_name: level === '地区总部' ? '' : level === '30' ? '大区' : level === '40' ? '城区' : '门店',
  })
  setRank({updateColumn: true, level, initSort: true, isReload: true})
}

// 列表中选择下钻 => 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const chooseItemParam = ref<{orgId: number, level: string, tabType: string}>()
const chooseItem = (data: QueryOrgRankRes) => {
  if (!data.canClick) return

  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_asset_org_rank_choose',
    button_name: '数舱_资产排行榜组织层级选择',
    content_name: data.level === '地区总部' ? '' : data.level === '30' ? '大区' : data.level === '40' ? '城区' : '门店',
  })

  tabType.value = ''
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
    tabType: (data.level === '10' ? Number(data.level) + 20 : Number(data.level) + 10).toString()
  }

  setRank({updateColumn: true, level: chooseItemParam.value.tabType, initSort: true, isReload: true})
}

// 排序参数
const sortData = ref<{orderBy: number, sortBy: string}>({
  orderBy: 1,
  sortBy: 'desc'
})
// 更新排序 => 1.更新列表
const updateList = (item: Column) => {
  sortData.value = {
    orderBy: item.orderByColumn as number, 
    sortBy: item.sortType as string
  }

  setRank({updateColumn: false, initSort: false, isReload: true})
}

const rankTable = ref<InstanceType<typeof cTable> | null>(null);
const provinceTable = ref<InstanceType<typeof cTable> | null>(null);
const brandTable = ref<InstanceType<typeof cTable> | null>(null);
// 获取排名数据、初始化、加载更多、更新列表触发
const getRanks = (pageNum?: number, pageSize?: number) => {
  const params: QueryOrgRankParams = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    orgId: tabType.value || typeIndex.value ? areaData.value.orgId : chooseItemParam.value?.orgId,
    level: tabType.value || typeIndex.value ? areaData.value.level : chooseItemParam.value?.level,
    ...sortData.value,
  }

  if (typeIndex.value === 0) {
    params.tabType = tabType.value || chooseItemParam.value?.tabType
  } else {
    params.tabType = typeIndex.value
  }

  const fn = typeIndex.value === 0 ? queryOrgRank : queryProvinceRank
  fn(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
      return
    }

    console.log('list========2', typeIndex.value)
    if (typeIndex.value === 0) {
      // rankTable.value.setData(res.data)
      managementData.value.list = res.data
      console.log('list========1', managementData.value.list)
    } else if (typeIndex.value === 1) {
      provinceTable.value.setData(res.data)
    } else {
      brandTable.value.setData(res.data)
    }
  })
}
// --------end 排行榜------------

// 跳转动因分析
const toDetail = () => {
  uni.navigateTo({
    url: `/packageA/pages/motivationAnalysis/index?id=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}&tabName=assets`
  })
  qdTrackEvent('smart_web_click', {
    page_id: 'data_asset_page',
    button_id: 'data_cata_comparion',
    button_name: '输舱_分类对比',
  })
  trackEvent('D003')
}

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
  getShopClass()
  setDataType()
  setRank({updateColumn: true, level: areaData.value.level, isReload: true})
})

onUnmounted(() => {
  trackEvent('D002', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.assets-index {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .data-area {
    padding: 0 20rpx;

    .area-right {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 28rpx;
      margin-left: 20rpx;
      border-radius: 32rpx;
      background: #F9EEEC;

      .shop-img {
        width: 28rpx;
        height: 25rpx;
      }

      .shop-text {
        padding-left: 8rpx;
        font-size: 22rpx;
        color: #1F2428;
        font-weight: bold;
      }
    }
  }

  .data-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx 20rpx;
		margin-top: 20rpx;

    .shop-view {
      padding: 40rpx 40rpx 40rpx;
      background: #fff;
      border-radius: 22rpx;

      .shop-time {
        display: block;
        font-size: 22rpx;
        line-height: 22rpx;
        padding-bottom: 40rpx;
        color: #1F2428;
      }

      .shop-content {
        padding-bottom: 80rpx;
        display: flex;
        justify-content: space-between;

        .shop-item {
          width: 196rpx;
          display: flex;
          flex-direction: column;
          border-radius: 20rpx;
          padding-bottom: 12rpx;

          .item-title {
            display: inline-block;
            padding: 0 16rpx;
            font-size: 28rpx;
            font-weight: bold;
            line-height: 52rpx;
            color: #fff;
            margin-bottom: 20rpx;
            border-radius: 20rpx 0 20rpx 0;
          }

          .item-text {
            padding: 0 8rpx 16rpx 8rpx;
            display: flex;
            align-items: center;

            .text {
              font-size: 22rpx;
              font-weight: bold;
              padding-right: 8rpx;
              color: rgba(31, 36, 40, 0.7);
            }

            .num {
              font-size: 36rpx;
              line-height: 40rpx;
              color: #1F2428;
              font-family: 'DIN Bold';
              font-weight: bold;
            }
          }
        }

        .item-card {
          background: linear-gradient(90deg, #f75e3b1a 0%, #f75e3b0d 100%);

          .item-title {
            width: 144rpx;
            background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
          }
        }

        .item-new {
          background: linear-gradient(90deg, #F6FCF9 0%, #ffff 100%);

          .item-title {
            width: 116rpx;
            background: linear-gradient(203.5deg, #bce3d0 0%, #43bf83 100%);
          }
        }

        .item-termination {
          background: linear-gradient(90deg, #FFF9F4 0%, #FEFDFA 100%);

          .item-title {
            width: 116rpx;
            background: linear-gradient(203.5deg, #ffcf9e 0%, #ff8c1a 100%);
          }
        }
      }

      .shop-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 40rpx;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .shop-chart {
        position: relative;

        .shop-btn-left {
          position: absolute;
          left: 0;
          top: 0;

          display: flex;
          align-items: center;
          height: 48rpx;
          border: 2rpx solid #f2f2f2;
          border-radius: 24rpx;

          .btn-item {
            padding: 0 24rpx;
            font-size: 22rpx;
            line-height: 44rpx;
            color: rgba(31, 36, 40, 0.7);
          }

          .active {
            border-radius: 22rpx;
            background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
            font-weight: bold;
            color: #fff;
          }
        }
      }
    }

    .ranking-view {
      padding: 32rpx 40rpx 40rpx;
      margin: 20rpx 0 200rpx 0;
      background: #fff;
      border-radius: 22rpx;

      .ranking-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .title-left {
          display: flex;
          align-items: center;
          height: 32rpx;

          .title-text {
            font-size: 32rpx;
            font-weight: bold;
          }

          .sub-title-text {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .ranking-btn {
        padding: 8rpx 0 20rpx 0;
      }

      .ranking-table, .brand-table {
        // height: 756rpx;
        border-radius: 20rpx;
        // overflow: hidden;

        .rank-icon {
          width: 32rpx;
          height: 32rpx;
        }

        .name-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .item-text {
            display: -webkit-box;
            max-width: 120rpx;
            overflow: hidden;//文本超出隐藏
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;//文本显示方式，默认水平
            -webkit-line-clamp: 2;//设置显示多少行
          }

          .line-item {
            text-decoration: underline;
          }

          .item-info {
            padding-top: 12rpx;
            display: flex;
            align-items: center;

            .info-text {
              font-size: 18rpx;
              line-height: 20rpx;
              color: #999999;
            }

            .item-icon {
              width: 12rpx;
              height: 16rpx;
              margin: 0 8rpx;
            }

            .item-num {
              font-size: 20rpx;
              line-height: 20rpx;
              font-weight: bold;
              font-family: "DIN Bold";
            }

            .up-num {
              color: #FF4D4D;
            }

            .down-num {
              color: #56CC93;
            }
          }
        }
      }
    }
  }
}
</style>