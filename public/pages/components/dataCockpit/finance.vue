<template>
  <view class="finance-index">
    <view class="data-area">
      <cAreaPicker @setArea="setArea" @toSearch="toSearch"></cAreaPicker>
    </view>

    <view class="data-info">
      <view class="info-title">
        <image class="title-icon" src="https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-27/1735262105323.png"></image>
        <text class="title-text">提醒：实际值隔月更新</text>
      </view>
      <view class="info-content">
        【日月维度】当月数据为预测值，【季年】为实际值累计；在月初结账前，当月预测数据暂无，且上月数据为预测值。
      </view>
    </view>

    <view class="data-content">
      <view class="target-view">
        <view class="target-title">
          <text class="title-text">财务指标</text>
          <view class="title-btn" @click="toDetail">
            <text class="btn-text">动因分析</text>
            <up-icon name="arrow-right" color="#1F2428" size="20rpx" />
          </view>
        </view>

        <cDatePicker :showRange="false" :maxDate="maxDate" @setDate="setDate"></cDatePicker>

        <view class="target-table">
          <cTable ref="targetTable" :header="operationsData.columnShow" :isPaginate="false" :isZPaging="true">
            <template #default="scope">
              <template v-if="['actualValue', 'valueBudget', 'valueCompletion'].includes(scope.prop)">
                {{ scope.data[scope.prop] || '-' }}{{ scope.prop === 'valueCompletion' || scope.data.unit === '%' ? '%' : '' }}
              </template>
              <view v-if="['valueTb', 'valueHb'].includes(scope.prop)" class="rate-item">
                <image v-if="scope.data[scope.prop]" class="item-icon" :src="`https://tr-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-23/${scope.data[scope.prop] > 0 ? '1729644716250' : '1729644734157'}.png`" mode="aspectFill"></image>
                <text class="item-text" :class="[scope.data[scope.prop] > 0 ? 'up-text' : scope.data[scope.prop] < 0 ? 'down-text' : '']">{{ Math.abs(scope.data[scope.prop]) + '%' }}</text>
              </view>
            </template>
          </cTable>
        </view>
      </view>

      <view class="ranking-view">
        <view class="ranking-title">
          <view class="title-left">
            <text class="title-text">排行榜</text>
            <text class="sub-title-text">{{ (Number(maxDate.slice(5, 7)) - 1) || 12 }}月</text>
          </view>
          <!-- <cTabs :list="typeList" v-model:modelValue="typeIndex" :isBig="false" @change="handleChangeType"></cTabs> -->
        </view>

        <view class="ranking-btn">
          <cBtnGroup :level="areaData.level" v-model:tabType="tabType" @change="handleChange"></cBtnGroup>
        </view>
        
        <view class="ranking-table">
          <cTable ref="rankTable" :header="managementData.columnShow" :isZPaging="true" @updateList="updateList" @getMore="getRanks" @chooseItem="chooseItem">
            <template #default="scope">
              <template v-if="scope.prop === 'num'">{{ scope.index + 1 }}</template>
              <template v-if="['netProfitRate'].includes(scope.prop)">{{ scope.data[scope.prop] !== null ? `${scope.data[scope.prop]}%` : '-' }}</template>
            </template>
          </cTable>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts" name="operationsIndex">
import { ref, getCurrentInstance, ComponentInternalInstance, onMounted, onUnmounted, nextTick } from 'vue';
import cAreaPicker from '@/components/c-area-picker/index.vue'
import cDatePicker from '@/components/c-date-picker/index.vue'
import cTable from '@/components/c-table/c-table.vue'
import cBtnGroup from '@/components/c-btn-group/index.vue'
import { queryOrgData, queryOrgRank } from '@/public/api/finance'
import { successCallbackResult } from '@/types'
import type { DateData, QueryOrgDataParams, QueryOrgRankRes, QueryBrandRankParams } from '@/public/api/finance'
import { formatDate, showToast } from '@/utils/util';
import { useCommon } from '@/hooks/useGlobalData';
import { onLoad } from '@dcloudio/uni-app'

const	{ trackEvent } = useCommon(getCurrentInstance() as ComponentInternalInstance)

type Column = {
  name: string,
  prop: string,
  width?: string,
  slot?: string,
  info?: string,
  subTitle?: string,
  sort?: true,
  sortType?: string,
  orderByColumn?: string | number,
  canClick?: boolean,
  type?: string,
  showUnit?: boolean
}

onLoad((option: any) => {
  areaData.value.orgId = option.id;
});

const instance = getCurrentInstance() as ComponentInternalInstance
const { globalData } = useCommon(instance)
const userInfo = ref(globalData.value.user)

const toSearch = () => {
  uni.navigateTo({
    url: `/packageB/pages/select-store/select-store?from=finance&maxDate=${maxDate.value}&orgId=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}`
  })

  trackEvent('D012')
}

// 组织树相关
const areaData = ref<{orgId: number | null, level: string, orgName: string}>({
  orgId: userInfo.value.orgList[0].orgId,
  level: userInfo.value.orgList[0].level,
  orgName: userInfo.value.orgList[0].orgName,
})
// 切换区域
const setArea = (id: number, level: string, name: string) => {
  areaData.value.orgId = id
  areaData.value.level = level
  areaData.value.orgName = name
  
  nextTick(() => {
    if (!tabType.value) {
      tabType.value = Number(level) <= 10 ? '10' : level.toString()
    }
    getOrgData()
    setRank({updateColumn: true, level: tabType.value, initSort: true, isReload: true})
  })
}

// --------start 财务指标------------
const operationsData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '指标',
      prop: 'dataName',
      width: '260',
      canClick: true,
      type: 'info',
      showUnit: true
    },
    {
      name: '实际值',
      prop: 'actualValue',
      width: '160',
      slot: 'actualValue',
    },
    {
      name: '预算',
      prop: 'valueBudget',
      width: '160',
      slot: 'valueBudget',
    },
    {
      name: '完成率',
      prop: 'valueCompletion',
      width: '120',
      slot: 'valueCompletion',
    },
    // {
    //   name: '同比',
    //   prop: 'valueTb',
    //   width: '150',
    //   info: '对比同期增速',
    //   slot: 'valueTb',
    // },
    // {
    //   name: '环比',
    //   prop: 'valueHb',
    //   width: '150',
    //   info: '对比上期增速',
    //   slot: 'valueHb',
    // },
    {
      name: '排名(完成率)',
      subTitle: '地区总部',
      prop: 'valueRank',
      width: '150',
    }
  ],
  columnShow: []
});

const dateData = ref<DateData>({
  dateType: 'day',
  atime: formatDate(Date.now() - 24 * 60 * 60 * 1000)
})
const maxDate = ref<string>(formatDate(Date.now() - 24 * 60 * 60 * 1000))
// 切换日期选择、初始化入口
const setDate = (type: string, value1: string | number, value2?: string | number) => {
  if (type === 'day') {
    dateData.value = {
      dateType: 'day',
      atime: value2
    }
    operationsData.value.columnShow = JSON.parse(JSON.stringify(operationsData.value.column))
    if (value2!.toString().slice(0, 7) >= maxDate.value.slice(0, 7)) {
      operationsData.value.columnShow.find((item: Column) => item.prop === 'actualValue')!.name = '预测值'
    }
  } else if (type === 'month') {
    dateData.value = {
      dateType: 'month',
      year: value1,
      month: value2
    }
    operationsData.value.columnShow = JSON.parse(JSON.stringify(operationsData.value.column))
    const dateStr = value1!.toString() + '-' + (Number(value2) > 9 ? value2 : '0' + value2)?.toString()
    if (dateStr.slice(0, 7) >= maxDate.value.slice(0, 7)) {
      operationsData.value.columnShow.find((item: Column) => item.prop === 'actualValue')!.name = '预测值'
    }
  } else if (type === 'quarter') {
    dateData.value = {
      dateType: 'quarter',
      year: value1,
      quarter: value2
    }
    operationsData.value.columnShow = operationsData.value.column
  } else if (type === 'year') {
    dateData.value = {
      dateType: 'year',
      year: value1,
    }
    operationsData.value.columnShow = operationsData.value.column.filter((item: Column) => item.prop !== 'valueHb')
  }

  getOrgData()
}

const targetTable = ref<InstanceType<typeof cTable> | null>(null);
// 数据驾驶仓运营指标查询
const getOrgData = () => {
  const params: QueryOrgDataParams = {
    ...areaData.value,
    ...dateData.value
  }

  queryOrgData(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    if (res.data.isCheckout === 0) {
      maxDate.value = formatDate(new Date(new Date().getFullYear(), new Date().getMonth(), 0))
      setDate(dateData.value.dateType, new Date().getFullYear(), maxDate.value)
      return
    }
    targetTable.value.setLocalPaging(res.data.list)
  })
}
// --------end 运营指标------------

// --------start 排行榜------------
const managementData = ref<{ column: Array<Column>, columnShow: Array<Column> }>({
  column: [
    {
      name: '排名',
      prop: 'num',
      width: '76',
      slot: 'num',
    },
    {
      name: '经营收入',
      prop: 'businessIncome',
      subTitle: '/万元',
      width: '180',
      sort: true,
      sortType: 'desc',
      orderByColumn: 1,
    }, 
    {
      name: '净利润率',
      prop: 'netProfitRate',
      width: '140',
      sort: true,
      sortType: '',
      orderByColumn: 2,
      slot: 'netProfitRate',
    },
  ],
  columnShow: [],
})

// 组织排行榜相关操作
const setRank = (params: {updateColumn?: boolean, level?: string, initSort?: boolean, isReload?: boolean}) => {
  if (params.updateColumn) setRankColumn(params.level!) // 更新表头
  if (params.initSort) { // 初始化sort参数
    sortData.value = {
      orderBy: 1,
      sortBy: 'desc'
    }
  }
  if (params.isReload) {
    rankTable.value?.reload() // 更新列表，会触发getMore
  }
}

// 重置表头
const setRankColumn = (level: string) => {
  managementData.value.columnShow = JSON.parse(JSON.stringify(managementData.value.column))
  if (Number(level) <= 20) { // 地区总部
    managementData.value.columnShow.splice(1, 0, {
      name: '地区总部',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '负责人',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '30') { // 大区
    managementData.value.columnShow.splice(1, 0, {
      name: '大区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '大区总',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '40') { // 城区
    managementData.value.columnShow.splice(1, 0, {
      name: '城区',
      prop: 'orgName',
      width: '120',
      canClick: true,
      type: 'choose',
    }, {
      name: '城区总',
      prop: 'orgUserName',
      width: '90',
    });
  } else if (level === '50') { // 门店
    managementData.value.columnShow.splice(1, 0, {
      name: '门店ID',
      prop: 'orgId',
      width: '90',
    }, {
      name: '门店',
      prop: 'orgName',
      width: '200',
    }, {
      name: '店长',
      prop: 'orgUserName',
      width: '90',
    });
  }
}

const tabType = ref<string>('10')
// 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const handleChange = (level: string) => {
  setRank({updateColumn: true, level, initSort: true, isReload: true})
}

// 列表中选择下钻 => 组织tab切换 => 1.更新表头 2.初始化排序 3.更新列表
const chooseItemParam = ref<{orgId: number, level: string, tabType: string}>()
const chooseItem = (data: QueryOrgRankRes) => {
  if (!data.canClick) return

  tabType.value = ''
  chooseItemParam.value = {
    orgId: data.orgId,
    level: data.level,
    tabType: (data.level === '10' ? Number(data.level) + 20 : Number(data.level) + 10).toString()
  }

  setRank({updateColumn: true, level: chooseItemParam.value.tabType, initSort: true, isReload: true})
}

// 排序参数
const sortData = ref<{orderBy: number, sortBy: string}>({
  orderBy: 1,
  sortBy: 'desc'
})
// 更新排序 => 1.更新列表
const updateList = (item: Column) => {
  sortData.value = {
    orderBy: item.orderByColumn as number, 
    sortBy: item.sortType as string
  }

  setRank({updateColumn: false, initSort: false, isReload: true})
}

const rankTable = ref<InstanceType<typeof cTable> | null>(null);
// 获取排名数据、初始化、加载更多、更新列表触发
const getRanks = (pageNum?: number, pageSize?: number) => {
  const params: QueryBrandRankParams = {
    pageNum: pageNum || 1,
    pageSize: pageSize || 20,
    orgId: tabType.value ? areaData.value.orgId : chooseItemParam.value?.orgId,
    level: tabType.value ? areaData.value.level : chooseItemParam.value?.level,
    tabType: tabType.value || chooseItemParam.value?.tabType,
    ...sortData.value,
  }

  queryOrgRank(params).then((res: successCallbackResult) => {
    if (res.code !== 200) {
      showToast(res.message || '系统异常，请稍后重试')
    }

    rankTable.value.setData(res.rows)
  })
}
// --------end 排行榜------------

// 跳转动因分析
const toDetail = () => {
  uni.navigateTo({
    url: `/packageA/pages/motivationAnalysis/index?id=${areaData.value.orgId}&level=${areaData.value.level}&orgName=${areaData.value.orgName}&tabName=finance`
  })
  trackEvent('D014')
}

let startTime: number = 0
onMounted(() => {
  startTime = Date.now()
  setDate(dateData.value.dateType, new Date().getFullYear(), dateData.value.atime)
  setRank({updateColumn: true, level: areaData.value.level})
})

onUnmounted(() => {
  trackEvent('D011', {time: (Date.now() - startTime) / 1000})
})
</script>

<style lang="scss">
.finance-index {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .data-area {
    padding: 0 20rpx;
  }

  .data-info {
    margin: 20rpx;
    padding: 16rpx 20rpx 0 20rpx;
    border-radius: 20rpx;
    background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);

    .info-title {
      display: flex;
      align-items: center;

      .title-icon {
        width: 22rpx;
        height: 22rpx;
      }

      .title-text {
        padding-left: 8rpx;
        font-size: 26rpx;
        line-height: 26rpx;
        color: #C35943;
        font-weight: bold;
      }
    }

    .info-content {
      padding: 8rpx 0 8rpx 30rpx;
      font-size: 22rpx;
      line-height: 38rpx;
      color: rgba(31, 36, 40, 0.7);
    }
  }

  .data-content {
    position: relative;
    flex: 1;
    overflow: auto;
    padding: 0 20rpx 20rpx;

    .target-view {
      padding: 32rpx 40rpx 40rpx;
      background: #fff;
      border-radius: 22rpx;

      .target-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-text {
          font-size: 32rpx;
          font-weight: bold;
        }

        .title-btn {
          display: flex;
          align-items: center;
          padding: 8rpx 16rpx;
          background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
          border-radius: 16rpx;

          .btn-text {
            padding-right: 8rpx;
            font-size: 22rpx;
            line-height: 22rpx;
            color: #1F2428;
          }
        }
      }

      .target-table {
        height: 672rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .target-item {
          display: flex;
          align-items: center;
          justify-content: center;

          .item-unit {
            padding-top: 4rpx;
            font-size: 18rpx;
            line-height: 18rpx;
          }

          .item-info {
            margin-left: 8rpx;
            width: 22rpx;
            height: 22rpx;
          }
        }

        .rate-item {
          display: flex;
          align-items: center;
          justify-content: center;

          .item-icon {
            width: 16rpx;
            height: 20rpx;
          }

          .item-text {
            padding-left: 8rpx;
            font-size: 24rpx;
            line-height: 24rpx;
            font-weight: bold;
            font-family: "DIN Bold";
            color: #1F2428;
          }

          .up-text {
            color: #FF4D4D;
          }

          .down-text {
            color: #56CC93;
          }
        }

        .th, .tbody {
          position: relative;

          .tr {
            .td:first-child {
              position: sticky;
              left: 0;
              background: #F3F7FF;
              z-index: 1;
              min-width: 40px;
              max-width: 40px;
            }
          }
        }
      }
    }

    .ranking-view {
      padding: 32rpx 40rpx 40rpx;
      margin: 20rpx 0 200rpx 0;
      background: #fff;
      border-radius: 22rpx;

      .ranking-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .title-left {
          display: flex;
          align-items: center;
          height: 32rpx;

          .title-text {
            font-size: 32rpx;
            font-weight: bold;
          }

          .sub-title-text {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      .ranking-btn {
        padding: 40rpx 0 20rpx 0;
      }

      .ranking-table {
        height: 756rpx;
        border-radius: 20rpx;
        overflow: hidden;

        .name-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .item-text {
            display: -webkit-box;
            max-width: 120rpx;
            overflow: hidden;//文本超出隐藏
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;//文本显示方式，默认水平
            -webkit-line-clamp: 2;//设置显示多少行
          }

          .line-item {
            text-decoration: underline;
          }

          .item-info {
            padding-top: 12rpx;
            display: flex;
            align-items: center;

            .info-text {
              font-size: 18rpx;
              line-height: 20rpx;
              color: #999999;
            }

            .item-icon {
              width: 12rpx;
              height: 16rpx;
              margin: 0 8rpx;
            }

            .item-num {
              font-size: 20rpx;
              line-height: 20rpx;
              font-weight: bold;
              font-family: "DIN Bold";
            }

            .up-num {
              color: #FF4D4D;
            }

            .down-num {
              color: #56CC93;
            }
          }
        }
      }
    }
  }
}
</style>