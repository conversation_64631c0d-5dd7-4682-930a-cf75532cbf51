<template>
	<view class="container">
		<view class="bg"></view>
		<u-navbar placeholder title="工作台" bg-color="transparent" titleStyle="font-size: 36rpx; font-weight:700;color:#FFF;">
			<template #left></template>
		</u-navbar>
		<scroll-view class="scroll-view" scroll-y="auto" :style="{'height':height}">
			<view class="content">
				<view v-if="globalData.user.gray" class="section section-special" style="padding-bottom: 0!important;">
					<view class="title fr">
						<text class="title-text">巡店</text>
					</view>
					<view class="module spection fr">
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-application',()=>{trackEvent('020')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022588828.png"
									mode="" lazy-load />
							</view>
							<view class="text">巡店申请</view>
						</view>
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-plan-list',()=>{trackEvent('021')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022668219.png"
									mode="" lazy-load />
							</view>
							<view class="text">巡店计划</view>
						</view>
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-trip-list',()=>{trackEvent('023')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022728915.png"
									mode="" lazy-load />
							</view>
							<view class="text">巡店行程</view>
						</view>
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-report-list',()=>{trackEvent('022')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022704715.png"
									mode="" lazy-load />
							</view>
							<view class="text">巡店报告</view>
						</view>
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-review',()=>{trackEvent('024')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022751820.png"
									mode="" lazy-load />
								<view class="dot" v-if="messageCount > 0">{{ messageCount }}</view>
							</view>
							<view class="text">巡店审核</view>
						</view>
						<view class="module-item"
							@click="route_to_view('/packageB/pages/shop-inspection/shop-inspection-statistics',()=>{trackEvent('025')})">
							<view class="icon">
								<image class="img"
									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733022775614.png"
									mode="" lazy-load />
							</view>
							<view class="text">巡店统计</view>
						</view>
					</view>
					<view class="trip" v-if="list?.length">
						<view class="trip-title fr">
							<view class="fr">
								<text class="title-text">行程中</text>
								<text class="title-text-primary">({{swiperCurrentIndex + 1}}/{{list?.length}})</text>
							</view>
							<view class="r fr"
								@tap="route_to_view('/packageB/pages/shop-inspection/shop-inspection-trip-list?tripStatus=4')">
								查看所有行程
								<up-icon name="arrow-right" color="#999999" size="22rpx" />
							</view>
						</view>
						<view class="swiper-section">
							<swiper class="swiper" @change="change" :current="swiperCurrentIndex">
								<swiper-item v-for="(item,index) in list" :key="index"
									@tap="route_to_view(`/packageB/pages/shop-inspection/shop-inspection-trip?orderId=${item.orderId}&shopId=${item.shopId}`)">
									<view class="swiper-item">
										<view class="no fr">
											<view class="text">行程编号：{{item.tripCode}}</view>
										</view>
										<view class="fr row">
											<view class="label">计划门店：</view>
											<view class="value">{{item.shopId}}</view>
										</view>
										<view class="fr row">
											<view class="label">巡店时间：</view>
											<view class="value">{{item.startTime}}</view>
										</view>
									</view>
								</swiper-item>
							</swiper>
							<view class="indicator-dots-section fr">
								<view class="indicator-dots" :class="{'indicator-dots-active':swiperCurrentIndex == index}"
									v-for="(item,index) in list" :key="index"></view>
							</view>
						</view>
					</view>
				</view>

				<view class="section section-special">
					<view class="title fr">
						<text class="title-text">预警</text>
						<view class="btn-right">
							<text class="btn-text" @click="toWarning">查看所有预警</text>
							<up-icon name="arrow-right" color="#1F2428" size="20rpx" />
						</view>
					</view>
					<view class="warning-btn">
						<view class="btn-item" :class="{ active: pushStatus === 1 }" @click="setPushStatus(1)">
							{{ `未读(${unReadCount})` }}
						</view>
						<view class="btn-item" :class="{ active: pushStatus === 2 }" @click="setPushStatus(2)">
							已读
						</view>
						<view class="btn-item" :class="{ active: pushStatus === 0 }" @click="setPushStatus(0)">
							全部
						</view>
					</view>
					<template v-if="listData.length">
						<view v-for="item in listData" :key="item.pushId" class="warning-item">
							<view class="item-time">
								{{ fullDate(item.pushDate) }}
							</view>

							<view class="item-content" @click="toDetail(item)">
								<view class="item-title">
									<text class="title-text" :class="[item.pushStatus === 1 && 'item-unread']">{{ item.pushTitle }}</text>
									<up-icon v-if="item.type !== '0'" name="arrow-right" color="#C5C5C5" size="24rpx" />
									<view class="item-btn" v-else @click="toAdjustment(item)">
										<text class="btn-text">去调价</text>
										<up-icon name="arrow-right" color="#fff" size="14rpx" />
									</view>
								</view>

								<view class="item-info">
									<template v-if="item.pushType === '1'">
										<view class="info-item">
											<text class="item-label">门店：</text>
											<text class="item-value">{{ item.alarmShopList[0].shopName }}</text>
										</view>
										<view class="info-item">
											<text class="item-label">门店ID：</text>
											<text class="item-value">{{ item.alarmShopList[0].shopId }}</text>
										</view>
										<view class="info-item">
											<text class="item-label">异常规则：</text>
											<text class="item-value">{{ item.alarmRule }}</text>
										</view>
									</template>

									<template v-else>
										<view class="info-item">
											<text class="item-label">异常规则：</text>
											<text class="item-value">{{ item.alarmRule }}</text>
										</view>
										<view class="info-item">
											<text class="item-label">统计时间：</text>
											<text class="item-value">{{ fullDate(item.alarmDate) }}</text>
										</view>
										<view class="info-item">
											<text class="item-label">汇总：</text>
											<text class="item-value">{{ item.summaryDesc }}</text>
										</view>
									</template>
								</view>
							</view>
						</view>
					</template>
					<empty v-else></empty>
				</view>

        <view class="section section-special">
          <view class="title fr">
            <text class="title-text">智能调价</text>
          </view>
          <view class="module fr">
            <!--<view class="module-item">
              <view class="icon">
                <image
                  class="img"
                  src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023086523.png"
                  mode="" lazy-load />
              </view>
              <view class="text">周边热点事件管理</view>
            </view>-->
            <view class="module-item" @click="route_to_view(`/packageB/pages/select-store/select-store?from=scheduled-price-adjustment`)">
              <view class="icon">
                <image
                  class="img"
                  src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023133981.png"
                  mode="" lazy-load />
              </view>
              <view class="text">营销日历和调价</view>
            </view>
            <view class="module-item" @click="route_to_view(`/packageB/pages/price-adjustment/price-adjustment`,()=>{trackEvent('026')})">
              <view class="icon">
                <image
                  class="img"
                  src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023066387.png"
                  mode="" lazy-load />
                <view class="dot" v-if="messageCount > 0">{{ messageCount }}</view>
              </view>
              <view class="text">调价审核</view>
            </view>
          </view>
        </view>

        <!--<view class="section section-special">-->
				<!--	<view class="title fr">-->
				<!--		<text class="title-text">报告</text>-->
				<!--	</view>-->
				<!--	<view class="module fr">-->
        <!--    &lt;!&ndash;<view class="module-item"-->
        <!--      @click="route_to_view(`/public/pages/webview/webview?url=${webviewURL}/pages/area-data-report/area-data-report`,()=>{trackEvent('013')})">-->
        <!--      <view class="icon">-->
        <!--        <image class="img"-->
        <!--          src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023290837.png"-->
        <!--          mode="" lazy-load />-->
        <!--      </view>-->
        <!--      <view class="text">城区数据报告</view>-->
        <!--    </view>&ndash;&gt;-->
				<!--		<view class="module-item"-->
				<!--			@click="route_to_view(`/packageB/pages/store-data-report/store-data-report?`,()=>{trackEvent('014')})">-->
				<!--			<view class="icon">-->
				<!--				<image class="img"-->
				<!--					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023320804.png"-->
				<!--					mode="" lazy-load />-->
				<!--			</view>-->
				<!--			<view class="text">单店运营报表</view>-->
				<!--		</view>-->
				<!--		&lt;!&ndash; <view class="module-item" @click="route_to_view(`/packageB/pages/service-data/service-data-list?`,()=>{trackEvent('015')})">-->
				<!--							<view class="icon">-->
				<!--								<image class="img"-->
				<!--									src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-12-01/1733023338621.png"-->
				<!--									mode="" lazy-load />-->
				<!--							</view>-->
				<!--							<view class="text">门店服务数据报告</view>-->
				<!--						</view> &ndash;&gt;-->
				<!--	</view>-->
				<!--</view>-->


        <view class="section section-special">
          <view class="title fr">
            <text class="title-text">其他</text>
          </view>
          <view class="module fr">
            <view class="module-item" @click="route_to_view(`/packageB/pages/select-store/select-store?from=shop`,()=>{trackEvent('027')})">
              <view class="icon">
                <image
                  class="img"
                  src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2025-03-17/1742177919880.png"
                  mode="" lazy-load />
              </view>
              <view class="text">门店监控</view>
            </view>
          </view>
        </view>
      </view>
		</scroll-view>
	</view>
</template>

<script setup lang="ts">
	import { ComponentInternalInstance, computed, getCurrentInstance, ref } from 'vue';
	import { useCommon } from '@/hooks/useGlobalData';
	import { successCallbackResult } from '@/types/index.d'
	import { queryPushCount, queryPushPage, selectShopInspectionTripPage, listAuditOrder, queryPushDetail } from '@/public/api/earlyWarning'
	import type { QueryPushPageParams, QueryPushCountRes } from '@/public/api/earlyWarning'
	import { showToast, fullDate } from '@/utils/util';

	const { route_to_view, trackEvent, globalData } = useCommon(getCurrentInstance() as ComponentInternalInstance)
	const webviewURL = getCurrentInstance().appContext.config.globalProperties.$webviewURL

	const list = ref<any>([])

	const swiperCurrentIndex = ref(0)
	const info = uni.getSystemInfoSync();
	const height = computed(() =>
		`calc(100vh - ${uni.upx2px(40)}px - 44px - ${info.safeAreaInsets.top}px)`)
	const change = (event : { detail : { current : number; }; }) => {
		swiperCurrentIndex.value = event.detail.current
	}
	const messageCount = ref(0)

	// 预警 --------start------------
	const toWarning = () => {
		uni.navigateTo({
			url: '/public/pages/message/index'
		})
	}

	const pushStatus = ref<number>(1);
	// 已读类型更改
	const setPushStatus = (val : number) => {
		if (val === pushStatus.value) return
		pushStatus.value = val
		getCount();
		getWaringList();
	}

	const unReadCount = ref<number>(0)
	const getCount = () => {
		queryPushCount({ pushStatus: 1, pushType: '' }).then((res : successCallbackResult) => {
			if (res.code !== 200) {
				showToast(res.message || '系统异常，请稍后重试')
				return
			}
			unReadCount.value = res.data
		});
	}

	const listData = ref<Array<QueryPushCountRes>>([]);
	const getWaringList = (flag ?: boolean) => {
		const params : QueryPushPageParams = {
			pageNum: 1,
			pageSize: 2,
			type: '',
		}
		if (pushStatus.value) params.pushStatus = pushStatus.value
		listData.value = []
		return queryPushPage(params).then((res : successCallbackResult) => {
			if (res.code !== 200) {
				showToast(res.message || '系统异常，请稍后重试')
				return
			}
			listData.value = res.data
			return res
		});
	}

	const toDetail = (item : QueryPushCountRes) => {
		if (item.type === '0') return
		uni.navigateTo({
			url: '/public/pages/message/earlyWarning/detail?pushId=' + item.pushId + '&type=' + item.type + '&pushType=' + item.pushType
		})
	}

	const toAdjustment = (item: QueryPushCountRes) => {
		queryPushDetail({ pushId: item.pushId })
		uni.navigateTo({
			url: `/packageB/pages/scheduled-price-adjustment/scheduled-price-adjustment?shopId=${item?.alarmShopList[0].shopId}`
		})
	}

	const getWaringInfo = () => {
		getCount()
		getWaringList()
	}
	/**
	 * @description: 获取行程中列表
	 */
	const getListInItinerary = async () => {
		try {
			const res = await selectShopInspectionTripPage({
				currentPage: 1,
				pageSize: 15,
				tripStatus: 4
			})
			list.value = res.data.records
		} catch (e) {
			console.log("🚀 ~ getListInItinerary ~ e:", e)
		}
	}
	/**
	 * @description: 查询巡店审核列表总数s
	 */
	const getListOfStoreInspectionReviews = async () => {
		try {
			const res = await listAuditOrder({
				pageNum: 1,
				pageSize: 1,
				status: 1
			})
			messageCount.value = res.data.total
		} catch (e) {
			console.log("🚀 ~ getListOfStoreInspectionReviews ~ e:", e)
		}
	}

	getWaringInfo()
	getListInItinerary()
	getListOfStoreInspectionReviews()

	defineExpose({
		getWaringInfo,
		getListInItinerary,
		getListOfStoreInspectionReviews
	})
	// 预警 --------end------------
</script>

<style scoped lang="scss">
	.bg {
		background: linear-gradient(52.4deg, #ff5733 0%, #c35943 31%, #c25742 79%, #f75735 100%);
		width: 100%;
		height: 314rpx;
		border-radius: 0 0 80rpx 0;
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
	}

	.scroll-view {
		margin-top: 40rpx;
		padding: 0 20rpx;

		.content {
			padding-bottom: 192rpx;
		}
	}

	.section-special {
		padding: 40rpx 0 !important;
	}

	.section {
		border-radius: 20rpx;
		padding: 40rpx;
		background: #fff;
		margin-bottom: 20rpx;

		.title {
			padding: 0 40rpx;
			justify-content: space-between;
			margin-bottom: 40rpx;

			&-text {
				color: #1f2428;
				font-size: 32rpx;
				font-weight: bold;
				line-height: 32rpx;
			}

			.btn-right {
				display: flex;
				align-items: center;
				height: 38rpx;
				padding: 0 16rpx;
				background: linear-gradient(90deg, #f5f5f5 0%, #f5f5f580 100%);
				border-radius: 19rpx;

				.btn-text {
					padding-right: 8rpx;
					font-size: 22rpx;
					color: #1F2428;
				}
			}
		}

		.warning-btn {
			display: inline-flex;
			align-items: center;
			width: fit-content;
			height: 48rpx;
			margin: 0 20rpx 20rpx;
			border: 2rpx solid #f2f2f2;
			border-radius: 24rpx;
			background: #fff;

			.btn-item {
				padding: 0 24rpx;
				font-size: 22rpx;
				line-height: 44rpx;
				color: rgba(31, 36, 40, 0.7);
			}

			.active {
				border-radius: 22rpx;
				background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);
				font-weight: bold;
				color: #fff;
			}
		}

		.warning-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20rpx 40rpx 0 40rpx;

			.item-time {
				height: 40rpx;
				padding: 0 27rpx;
				margin-bottom: 20rpx;
				background: rgba(0, 0, 0, 0.4);
				border-radius: 20rpx;

				font-size: 24rpx;
				line-height: 40rpx;
				color: #fff;
			}

			.item-content {
				width: 100%;
				padding: 40rpx 40rpx 30rpx 40rpx;
				background: rgba(247, 94, 59, 0.08);
				border-radius: 20rpx;

				.item-title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 22rpx;

					.title-text {
						padding-right: 24rpx;
						font-size: 32rpx;
						font-weight: bold;
						line-height: 32rpx;
						color: #1F2428;
					}

					.item-btn {
						display: flex;
						align-items: center;
						padding: 0 28rpx;
						border-radius: 24rpx;
						background: linear-gradient(198.3deg, #ffb2a1 0%, #f75e3b 100%);

						.btn-text {
							padding-right: 8rpx;
							font-size: 22rpx;
							font-weight: bold;
							color: #fff;
							line-height: 48rpx;
						}
					}

					.item-unread {
						position: relative;

						&::after {
							content: '';
							position: absolute;
							top: 0;
							right: 0;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background: #FF4D4D;
						}
					}
				}

				.item-info {
					padding-top: 8rpx;

					.info-item {
						display: flex;
						align-items: flex-start;
						padding-top: 8rpx;

						.item-label {
							width: 120rpx;
							font-size: 24rpx;
							line-height: 44rpx;
							color: rgba(31, 36, 40, 0.7);
						}

						.item-value {
							flex: 1;
							font-size: 24rpx;
							line-height: 44rpx;
							// font-weight: bold;
							color: #1F2428;
						}
					}
				}
			}
		}

		.module.spection {
			// margin-bottom: 20rpx;

			.module-item {
				margin-bottom: 40rpx;
			}
		}

		.module {
			flex-wrap: wrap;

			&-item {
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;

				.icon {
					position: relative;
					margin-bottom: 20rpx;
					width: 90rpx;
					height: 90rpx;

					.img {
						width: 100%;
						height: 100%;
						flex-shrink: 0;
					}

					.dot {
						border-radius: 32rpx;
						opacity: 1;
						border: 2rpx solid #ffffff;
						background: #ff4d4d;
						position: absolute;
						top: -8rpx;
						right: -12rpx;
						color: #ffffff;
						font-size: 22rpx;
						font-weight: 700;
						font-family: "DIN Bold";
						line-height: 22rpx;
						width: 32rpx;
						height: 32rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						box-sizing: content-box;
					}
				}

				.text {
					color: #1f2428;
					font-size: 22rpx;
					text-align: center;
					line-height: 22rpx;
				}

				.dot {
					border-radius: 32rpx;
					opacity: 1;
					border: 2rpx solid #ffffff;
					background: #ff4d4d;
					position: absolute;
					top: -8rpx;
					right: -12rpx;
					color: #ffffff;
					font-size: 22rpx;
					font-weight: 700;
					font-family: "DIN Bold";
					line-height: 22rpx;
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-sizing: content-box;
				}
			}
		}

		.trip {
			padding: 0 40rpx;
			padding-bottom: 40rpx;

			&-title {
				justify-content: space-between;

				.title-text {
					color: #1f2428;
					font-size: 28rpx;
					font-weight: bold;
					padding: 40rpx 0;

					&-primary {
						color: #c35943;
						font-size: 28rpx;
						font-weight: bold;
						line-height: 28rpx;
						margin-left: 16rpx;
					}
				}

				.r {
					color: #999999;
					font-size: 24rpx;
					line-height: 104rpx;
					margin-right: 16rpx;
				}
			}

			.swiper-section {
				.swiper {
					width: 100%;
					height: 208rpx;
					border-radius: 20rpx;

					&-item {
						width: 100%;
						height: 100%;
						background: rgba(247, 94, 59, 0.08);
						border-radius: 20rpx;

						.no {
							justify-content: flex-end;

							.text {
								padding: 14rpx 0;
								width: 428rpx;
								text-align: center;
								margin-bottom: 40rpx;
								color: #1f2428;
								font-size: 24rpx;
								font-weight: bold;
								line-height: 24rpx;
								background: rgba(255, 178, 161, 0.4);
								border-radius: 0 20rpx 0 20rpx;
							}
						}

						.row {
							margin-bottom: 28rpx;
							justify-content: space-between;
							padding: 0 40rpx;

							.label {
								color: #1f2428;
								font-size: 24rpx;
								font-weight: bold;
								line-height: 24rpx;
							}
						}

						.value {
							color: #1f2428;
							font-size: 24rpx;
							line-height: 24rpx;
						}
					}
				}

				.indicator-dots-section {
					justify-content: center;
					margin-top: 20rpx;

					.indicator-dots {
						flex-shrink: 0;
						width: 16rpx;
						height: 16rpx;
						background: #f2f2f2;
						border-radius: 50%;
						margin: 0 16rpx;

						&-active {
							background: linear-gradient(203.5deg, #ffb2a1 0%, #f75e3b 100%);
						}
					}
				}
			}
		}
	}
</style>