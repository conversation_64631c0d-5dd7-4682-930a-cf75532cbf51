import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type QueryPushCountParams = {
  pushStatus: number, // 不传 查询全部消息； 1 查询未读消息 ；2 查询已读消息；
}

export type QueryPushPageParams = QueryPushCountParams & {
  pageNum: number,
  pageSize: number,
  pushStatus?: number, //不传 查询全部消息； 1 查询未读消息 ；2 查询已读消息；
  type: string, // 满房预警:0; 预订价格异常:1; 直联渠道未打开:2; 新增差评:3; 新增疑似舞弊店:4; 新增解约店 :5; 新增封存店 :6;预算空缺:7;账单逾期:8;续约提醒:9;
}

export type AlarmShopList = {
  shopId: string, 
  shopName: string, 
  areaName: string, // 城区名称
  occ: number,
  manageFee: number, // 管理费
  contractExpirDate: string, // 合同到期日期

  [k : string] : any
}

export type QueryPushCountRes = {
  pushId: number, // 预警ID
  type: string, 
  pushType: string, // 推送类型：1 实时；2 定时
  pushStatus: number, // push状态 1 未读 2 已读
  pushTitle: string, // push标题
  alarmRule: string, // 异常规则
  alarmDate: string, // 统计时间
  summaryDesc: string, // 汇总文案
  pushDate: string, // 推送时间
  alarmShopList: Array<AlarmShopList>, // 预警门店信息
  [k : string] : any
}

export type QueryPushDetailParams = {
  pushId: number, // 列表页pushId
}

// 查询用户监控告警消息数
export const queryPushCount = (data : QueryPushCountParams) : Promise<successCallbackResult> => {
	return http({
		url: '/push/queryPushCount',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 查询用户监控告警消息分页
export const queryPushPage = (data : QueryPushPageParams) : Promise<successCallbackResult> => {
	return http({
		url: '/push/queryPushPage',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 查询用户监控告警详情
export const queryPushDetail = (data : QueryPushDetailParams) : Promise<successCallbackResult> => {
	return http({
		url: '/push/queryPushDetail',
		method: 'GET',
		service: 'qd',
		data
	})
}
/**
 * @description: 查询行程列表
 */
export const selectShopInspectionTripPage = (data: any): Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/trip/selectShopInspectionTripPage',
		method: 'POST',
		data
	})
}

/**
 * @description: 巡店待审核列表
 */
export const listAuditOrder = (data : any) : Promise<successCallbackResult> => {
	return http({
		url: '/shopInspection/form/listAuditOrder',
		method: 'POST',
		data
	})
}

// 任务审核列表
export const taskReviewList = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/taskReview/list',
		method: 'POST',
		data
	})
}