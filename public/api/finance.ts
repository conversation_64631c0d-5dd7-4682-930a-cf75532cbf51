import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type DateData = {
  dateType: string, // 查询年：year；查询季： quarter；查询月： month；查询日： day；查询自定义： range
  year?: number,
  quarter?: number,
  month?: number,
  atime?: string,
  startDate?: string,
  endDate?: string,
}

export type QueryOrgDataParams = DateData & {
  orgId: number,
  level: string // 区域层级 0 ：集团， 10：事业部，20：战区，30：大区，40：城区
}

export type QueryOrgRankParams = QueryBrandRankParams & {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
}

export type QueryOrgRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  businessIncome: number, // 经营收入
  orgUserName: string,
  netProfitRate: number, // 净利润率
  [k : string] : any
}

export type QueryBrandRankParams = {
  orgId?: string,
  level?: string,
  orderBy: number, // 1：经营收入、2：净利润率
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryKeyIndexParams = {
  orgId?: string,
  level?: string,
  dataType: number, // 查询数据类型 ：1：运营收入 、2：会员与渠道收入、3：管理费用、4：销售费用
  dateType?: string, // day （查询当月每天的数据和对应日期-7天的同比数据）； month（查询当年各月实际值和完成率和去年同期月的完成率）；
}

export type QueryKeyIndexRes = {
  slotDate: string,
  actualValue: number, // 日/月实际值
  termValue: number, // 日-7/去年同月实际值
  actualValueCompletionRate: number, // 预算完成率，不带%
  weekday: number // 周几
  [k : string] : any
}

export type QueryKeyDriveParams = QueryKeyIndexParams

export type QueryKeyDriveRes = {
  slotDate: string,
  travelExpensesFee: number, // 差旅费
  travelExpensesFeeRate: number, // 差旅费占比
  businessEnterTexpenses: number, // 业务招待费
  businessEnterTexpensesRate: number, // 业务招待费占比
  benefitFee: number, // 福利费
  benefitFeeRate: number, // 福利费占比
  technicalServiceFee: number, // 技术服务费占比
  marketingFee: number, // 营销费
  marketingFeeRate: number, // 营销费占比
  marketingMaterialFee: number, // 营销品费
  marketingMaterialFeeRate: number, // 营销品费占比
  otherFee: number, // 其它费用
  otherFeeRate: number, // 其它费用占比
  [k : string] : any
}

// 财务 - 数据驾驶舱 - 指标
export const queryOrgData = (data : QueryOrgDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/financeMetricsInfo',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 财务 - 数据驾驶舱 - 排行
export const queryOrgRank = (data : QueryOrgRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/financeMetric/financeMetricsRank',
		method: 'GET',
		service: 'qd',
		data
	})
}
