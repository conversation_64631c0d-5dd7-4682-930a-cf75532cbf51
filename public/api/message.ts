import http, { httpUpload } from '@/utils/http'
import { dataType, successCallbackResult } from '../../types'

// 获取消息列表
export const queryMessageList = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/queryMessageList',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 消息已读
export const readMessage = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/readMessage',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 消息详情
export const queryMessageDetail = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/queryMessageDetail',
		method: 'POST',
		service: 'sh',
		data
	})
}