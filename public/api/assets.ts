import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type QueryShopClassParams = {
  orgId: number | string,
  level: string // 区域层级 0 ：集团， 10：地区总部，30：大区，40：城区
}

export type QueryShopCycleParams = QueryShopClassParams & {
  dataType?: string, // 1 :查询周期占比 ，2: 查询标杆类型占比；
}

export type QueryOrgRankParams = {
  tabType?: string,
  orgId?: string,
  level?: string,
  orderBy: number, // 排序字段： 1 运营店; 2 欠费店 ;3 舞弊店 ;4 零售卡店; 5 层层标杆店; 6 运营好店
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryOrgRankRes = {
  orgId: string,
  orgName: string,
  orgUserName: string,
  level: string,
  openShopCount: number, // 运营店数
  oweShopCount: number, // 欠费店数
  owlShopCount: number, // 舞弊店数
  zeroCardShopCount: number, // 零售卡店数
  benchmarkShopCount: number, // 层层标杆店数
  operateShopCount: number, // 运营好店数
  [k : string] : any
}

export type QueryProvinceRankParams = {
  tabType: string, // 1 省份 2 品牌
  orgId?: string,
  level?: string,
  orderBy: number, // 1 gmv; 2 occ ;3 adr ;4 revpar; 5 crs;
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

// 数据驾驶舱-资产-门店分类累计统计
export const queryShopClass = (data : QueryShopClassParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryShopClass',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱-资产-门店状态周期月度对比图
export const queryShopCycle = (data : QueryShopCycleParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryShopCycle',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱-资产-区域组织排行榜
export const queryOrgRank = (data : QueryOrgRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryOrgRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱-资产-省份/品牌排行榜
export const queryProvinceRank = (data : QueryProvinceRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/property/queryProvinceRank',
		method: 'GET',
		service: 'qd',
		data
	})
}
