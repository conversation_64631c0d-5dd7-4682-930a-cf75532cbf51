import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type DateData = {
  dateType: string, // 查询年：year；查询季： quarter；查询月： month；查询日： day；查询自定义： range
  year?: number,
  quarter?: number,
  month?: number,
  atime?: string,
  startDate?: string,
  endDate?: string,
}

export type QueryOrgDataParams = DateData & {
  orgId: number,
  level: string // 区域层级 0 ：集团， 10：事业部，20：战区，30：大区，40：城区
}

export type QueryOrgRankParams = QueryBrandRankParams & {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
}

export type QueryOrgRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  comparetLastMonth: number, // 较上月排名上升或下降多少
  orgUserName: string,
  directSalesRatio: string, // 直销占比
  memberRatio: string, // 会员占比
  memberRepurchaseRate: string, // 会员复购率
  memberIncome: string, // 会员卡收入
  memberIncomeCompletionRate: string, // 完成率会员卡收入
  retailCardStore: string, // 零售卡门店
  [k: string]: any
}

export type QueryBrandRankParams = {
  orgId?: string,
  level?: string,
  orderBy: number, // 1：直销占比、2：会员占比、3：会员复购率、4：会员卡收入、5：完成率会员卡收入、6：零售卡门店
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryKeyIndexParams = {
  orgId?: string,
  level?: string,
  dataType: number, // 1：会员卡收入、2：售卡门店、3：直销占比、4：会员占比
  dateType?: string, // day （查询当月每天的数据和对应日期-7天的同比数据）； month（查询当年各月实际值和完成率和去年同期月的完成率）；
}

export type QueryKeyIndexRes = {
  slotDate: string,
  actualValue: number, // 日/月实际值
  termValue: number, // 日-7/去年同月实际值
  actualValueCompletionRate: number, // 预算完成率，不带%
  [k: string]: any
}

export type QueryKeyDriveParams = QueryKeyIndexParams

export type QueryKeyDriveRes = {
  slotDate: string,
  salesCardsCount: string, // 售卡数量
  salesCardsPrice: string, // 售卡金额
  xmSilverCardCount: string, // 小美银卡数量
  xmSilverCardPrice: string, // 小美银卡金额
  xmSilverCardRate: string, // 小美银卡占比
  xmGoldenCardCount: string, // 小美金卡数量
  xmGoldenCardPrice: string, // 小美金卡金额
  xmGoldenCardRate: string, // 小美金卡占比
  xmDiamondCardCount: string, // 小美钻卡数量
  xmDiamondCardPrice: string, // 小美钻卡金额
  xmDiamondCardRate: string, // 小美钻卡占比
  goldMemberCount: string, // 金卡会员数量
  goldMemberPrice: string, // 金卡会员金额
  goldMemberRate: string, // 金卡会员占比
  roseGoldMemberCount: string, // 玫瑰金会员数量
  roseGoldMemberPrice: string, // 玫瑰金会员金额
  roseGoldMemberRate: string, // 玫瑰金会员占比
  silverCardMemberCount: string, // 银卡会员数量
  silverCardMemberPrice: string, // 银卡会员金额
  silverCardMemberRate: string, // 银卡会员占比
  otherCardMemberCount: string, // 其他卡数量
  otherCardMemberPrice: string, // 其他卡金额
  otherCardMemberRate: string, // 其他卡占比
  dailyCardStoreSales: string, // 每天售卡门店数
  numberCardSalesStores: string, //  月售卡门店数
  retailCardShop: string, // 零售卡门店数
  salesVelocity: string, // 动销率
  repurchaseRate: string, // 复购率
  newlyaddedPaidMembers: string, // 新增付费会员数
  addFreeMembers: string, // 新增免费会员数
  numberActiveMembers: string, //  活跃会员数
  [k: string]: any
}

export type EffectiveTotal = {
  dataName?: string;
  actualValue?: string;
  valueHb?: string;
}

type EffectiveDataItem = {
  dataName: string;
  actualValue: string;
  valueHb: string;
  sub?: EffectiveDataItem[]; // 可选子项数组
}

export type QueryOrgEffectiveRes = {
  code: number | string,
  message: string,
  data: EffectiveTotal,
  effectiveList: EffectiveDataItem[],
  [k: string]: any
}

export type QueryEffectiveRankParams = QueryBrandRankParams & {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
} & DateData

// 移动端有效量图表VO
export type EffectiveBoardIndexVO = {
  date: string, // 时间维度
  mobileEffectiveQuantity: string, // 移动端有效量
  mobileOvernightQuantity: string, // 移动端间夜量
  mobileOvernightEffectiveQuantity?: string, // 移动端间夜有效量
  appOvernightQuantity: string, // APP间夜量
  appOvernightEffectiveQuantity: string, // APP间夜有效量
  miniOvernightQuantity: string, // 小程序间夜量
  miniOvernightEffectiveQuantity: string, // 小程序间夜有效量
  scanCodeOrderQuantity: string, // 扫码下单量
  scanCodeOrderEffectiveQuantity: string, // 扫码下单移动端有效量
  appLoginQuantity: string, // APP首次登录量
  appLoginEffectiveQuantity: string, // APP首次登录有效量
  appOrderQuantity: string, // APP首次下单量
  appOrderEffectiveQuantity: string, // APP首次下单有效量
  [k: string]: any
}

// 移动端有效量排行榜VO
export type QueryEffectiveRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  orgUserName: string,
  mobileEffectiveQuantity: string,
  mobileOvernightQuantity: string,
  mobileOvernightEffectiveQuantity: string,
  appOvernightQuantity: string,
  appOvernightEffectiveQuantity: string,
  miniOvernightQuantity: string,
  miniOvernightEffectiveQuantity: string,
  scanCodeOrderQuantity: string,
  scanCodeOrderEffectiveQuantity: string,
  appLoginQuantity: string,
  appLoginEffectiveQuantity: string,
  appOrderQuantity: string,
  canClick: string,
  [k: string]: any
}

// 数据驾驶舱-会员-会员指标
export const queryOrgData = (data: QueryOrgDataParams): Promise<successCallbackResult> => {
  return http({
    url: '/member/memberMetrics',
    method: 'GET',
    service: 'qd',
    data
  })
}

// 数据驾驶舱-会员-组织-排行榜本月
export const queryOrgRank = (data: QueryOrgRankParams): Promise<successCallbackResult> => {
  return http({
    url: '/member/memberOrganizationRank',
    method: 'GET',
    service: 'qd',
    data
  })
}

// 数据驾驶舱-会员-品牌-排行榜本月
export const queryBrandRank = (data: QueryBrandRankParams): Promise<successCallbackResult> => {
  return http({
    url: '/member/memberBrandRank',
    method: 'GET',
    service: 'qd',
    data
  })
}

// 数据驾驶舱-会员-移动端有效量
export const queryOrgEffective = (data: QueryOrgDataParams): Promise<QueryOrgEffectiveRes> => {
  return http({
    url: '/effective/queryOrgData',
    method: 'GET',
    service: 'qd',
    data
  })
}

// 数据驾驶舱-会员-移动端有效量图表
export const queryEffectiveBoard = (data: QueryOrgDataParams): Promise<successCallbackResult> => {
  return http({
    url: '/effective/queryBoard',
    method: 'GET',
    service: 'qd',
    data
  })
}

// 数据驾驶舱-会员-移动端有效量排行榜
export const queryEffectiveRank = (data: QueryEffectiveRankParams): Promise<successCallbackResult> => {
  return http({
    url: '/effective/queryOrgDataRank',
    method: 'GET',
    service: 'qd',
    data
  })
}




