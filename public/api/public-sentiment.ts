import http from '@/utils/http'
import { successCallbackResult } from '@/types/index.d.ts'
import { QueryIndicatorsParams, QueryMonthRankParams, QueryBrandRankParams } from '@/types/dataCockpit/index.ts'

// 查询舆论指标
export const queryIndicators = (data : QueryIndicatorsParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/queryIndicators',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 获取渠道评分
export const queryChannelScore = (data : QueryIndicatorsParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/queryChannelScore',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 评分组织排行榜
export const queryMonthRank = (data : QueryMonthRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/queryMonthRank',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 评分品牌排行榜
export const queryBrandRank = (data : QueryBrandRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/opinion/queryBrandRank',
		method: 'POST',
		service: 'sh',
		data
	})
}
