import http, { httpUpload } from '@/utils/http'
import { successCallbackResult } from '@/types'

export type QueryPropertyParams = {
  orgId: number,
  level: string // 区域层级 0 ：集团， 10：事业部，20：战区，30：大区，40：城区
}

export type QueryPropertyRes = {
  totalShopCount: number,
  totalShopCountCompare: number,
  roomCount: number,
  roomCountCompare: number,
  operateShopCount: number,
  operateShopCountCompare: number,
  zerocardShopCount: number,
  zerocardShopCountCompare: number,
  owlShopCount: number,
  owlShopCountCompare: number,
  oweShopCount: number,
  oweShopCountCompare: number,
  [k : string] : any
}

export type QueryIndexDataParams = QueryPropertyParams

export type QueryIndexDataRes = {
  dataName: string,
  unit: string,
  actualValue: string,
  valueCompletion: string,
  valueTb: string,
  valueHb: string,
  dataDesc: string,
  [k : string] : any
}

// 首页资产查询接口
export const queryProperty = (data : QueryPropertyParams) : Promise<successCallbackResult> => {
	return http({
		url: '/index/queryProperty',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 首页本月指标概览查询
export const queryIndexData = (data : QueryIndexDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/index/queryIndexData',
		method: 'GET',
		service: 'qd',
		data
	})
}
