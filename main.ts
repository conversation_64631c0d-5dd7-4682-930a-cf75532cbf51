import App from './App'
import layoutVue from './components/layout/layout.vue'
import tabsVue from './components/tabs/tabs.vue'
import uniIcons from './components/uni-icons/uni-icons.vue'
import cButtonVue from './components/c-button/c-button.vue'
import cWaterMark from './components/c-water-mark/index.vue'
import './utils/addInterceptor'
import { globalData, pushError } from './hooks/useGlobalData'
import { createSSRApp } from 'vue'
import { useLocalEnv, protocl } from './utils/env'
import uma from './utils/uma'
import { isObject } from "./utils/util";
export function createApp() {
  const app = createSSRApp(App)
  app.component('layoutVue', layoutVue)
  app.component('tabsVue', tabsVue)
  app.component('uni-icons', uniIcons)
  app.component('cButtonVue', cButtonVue)
  app.component('cWaterMark', cWaterMark)
  app.use(uma)
  app.config.globalProperties.$towxml = require('./wxcomponents/towxml/index')
  app.config.globalProperties.$globalData = globalData
  app.config.globalProperties.$unLaunched = new Promise((resolve) => app.config.globalProperties.$resolve = resolve)
  app.config.globalProperties.$webviewURL = useLocalEnv ? protocl.webviewURL : process.env.webviewURL
  app.config.errorHandler = (err, vm, info) => {
    console.error(err)
    if (vm?.__route__) {
      pushError({ message: `Error in ${vm?.__route__}-${info}:， ${isObject(err) ? JSON.stringify(err) : err.toString()}` })
    } else {
      pushError({ message: `Error in ${vm?._?.vnode?.type?.__name}组件-${info}:， ${isObject(err) ? JSON.stringify(err) : err.toString()}` })
    }
  }
  return {
    app
  }
}

uni.$zp = {
	config: {
		//配置分页默认pageSize为15
		'default-page-size': '20',
		//配置空数据图默认描述文字为：空空如也~~
		// 'empty-view-text': '空空如也~~',
		//...
	}
}
