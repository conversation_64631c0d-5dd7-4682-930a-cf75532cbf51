{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "esModuleInterop": true, "sourceMap": true, "skipLibCheck": true, "importHelpers": true, "allowSyntheticDefaultImports": true, "useDefineForClassFields": true, "resolveJsonModule": true, "lib": ["esnext", "dom"], "types": ["@dcloudio/types"]}, "include": ["utils/*.ts", "api/*.ts"], "exclude": ["node_modules", "unpackage", "static/**/*.png", "store/static/*.png"]}