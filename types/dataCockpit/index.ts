import { DateParams, PaginationParams } from "../index"

export interface OrgLevel {
	orgId : string
	level : number
	orgName ?: string
}

export interface QueryIndicators_Date {
	year ?: number
	quarter ?: number
	month ?: string
	day ?: string
	startDate ?: string
	endDate ?: string
	dateType ?: string
}

export type QueryIndicatorsParams = OrgLevel & QueryIndicators_Date;

export interface QueryRankParams {
	orgId : string
	level : number
	startDate : string
	endDate : string
	orderBy ?: number
	sortBy ?: string
}

export interface BrandRankParams {
	tabType : number
}

export type QueryMonthRankParams = QueryRankParams & PaginationParams & BrandRankParams;

export type QueryBrandRankParams = QueryRankParams & PaginationParams;

export type MotivationParams = {
	dateType : 'day' | 'month' | 'dayMonth'
	dataType : 1 | 2 | 3 | 4 | 5
	orgId : string
	level : number
	channelType ?: number
}

export type shopRankParams = {
	dateType : 'day' | 'month' | 'dayMonth'
	dataType : 1 | 2 | 3 | 4 | 5
	orgId : string
	level : number
	orderBy ?: number
	sortBy ?: string
} & PaginationParams

export type ShopOpinionAnalysisParam = {
	orgId : string
} & DateParams

export type ShopChannelAnalysisParam = {
	orgId : string
	dataType : number
} & DateParams

export type ShopScoreRateParam = {
	orgId : string
	channelType : number
} & DateParams

export type ShopMotivationAnalysisParam = {
	shopId : string
	dateType : 'day' | 'month' | 'dayMonth'
	dataType : 1 | 2 | 3 | 4 | 5
	channelType : number
}

export type ListShopParam = {
	keyWord : string
} & OrgLevel