import { DateParams, PaginationParams } from "../index"

export type ShopIdParams = {
  shopId: string;
};

export type ManagerDataParams = ShopIdParams;

export type CustomerStructureParams = ShopIdParams & DateParams;

export type ShopDataReportDetailParams = ShopIdParams & {
  beginDate: string;
  endDate: string;
};

export type QueryCoreIndicatorsParams = ShopIdParams & {
  queryDate: string;
};

export type QueryShopHealthIndexParams = ShopIdParams & {
  queryType: 1 | 2;
} & DateParams;

export type QueryHomePageRoomStatusParams = ShopIdParams;

export type GetSaleComparisonDataParams = ShopIdParams & DateParams;

export type GetSalesCardBusinessParams = ShopIdParams & DateParams;

export type GetSalesCardBarChartParams = GetSalesCardBusinessParams & PaginationParams;

export type QueryRoomStatusParams = ShopIdParams & {
  floorNum: string;
  roomStatus: string;
  roomTypeName: string;
};

export type ShopLevelParams = ShopIdParams;