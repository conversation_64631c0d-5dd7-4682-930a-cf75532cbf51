export type dataType = {
	[key : string] : any
}

export type successCallbackResult = {
	code : number | string,
	message : string,
	data : any,
	[k : string] : any
}

export type wxWorkCallbackResult = {
	code : string,
	errMsg : string
}

export interface systemInfoSyncType extends UniApp.GetSystemInfoResult {
	environment ?: string
}

export interface requestOptions extends UniApp.RequestOptions {
	APP_BASE_URL ?: string,
	service ?: string
}

type iconType = 'success' | 'error'| 'fail'| 'exception'| 'loading'| 'none'
export type showType = {
	title: string,
	icon: iconType,
	image: string,
	mask: boolean,
	noConflict?: boolean,
	duration: number,
	success: () => void,
	fail: () => void,
	complete: () => void
}

export type fn = (e: any) => void

export type tabItem = {
	readonly label: string,
	readonly value: number | string
}

export interface newPageInstance extends Page.PageInstance{
	refresh?: boolean
	[key: string]: any
}

export type shopRole = {
	isFirstLogin: boolean, // 是否首次登录
	bossShopList: dataType[], //业主权限
	frontDeskShopList: dataType[], //前台权限
	managerShopList: dataType[], //店长权限
	lastLoginInShop: dataType // 上一次登录权限
}

export interface requestOptionsType extends UniApp.RequestOptions{
	[key: string]: any
}

export type DateParams = {
	startDate : string
	endDate : string
}

export type PaginationParams = {
	pageNum : number
	pageSize : number
}