<template>
	<layoutVue class="layout">
		<view class="fl container" :class="{'justCenter': role == 1}">
			<view class="fl tips" v-if="role == 1">
				<image
					src="https://sunmei-smart-operating.oss-cn-shanghai.aliyuncs.com/smartOperating/2024-10-30/1730282036601.png"
					mode="aspectFill" lazy-load class="empty-image"></image>
				<text>对不起，你没有查看权限～</text>
			</view>
			<view class="fl tips" v-else>
				<icon type="warn" size="{{40}}" class="icon" />
				<text>请在企业微信中打开</text>
			</view>
		</view>
	</layoutVue>
</template>

<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app'
	import { ref } from 'vue'
	const role = ref('')
	onLoad((opts) => {
		role.value = opts.role
	})
</script>

<style lang="scss" scoped>
	.layout {
		:deep(.page-layout) {
			height: 100vh;
			background-color: #fff;

		}
		
		.icon {
			margin-bottom: 40rpx;
		}
		
		.container {
			height: 100%;
			padding: 32rpx 0;
			&.justCenter{
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		
		.tips {
			margin: 32rpx 0;
			text-align: center;
			font-size: 28rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		
		.tips2 {
			padding: 0 28rpx;
		}
		
		.empty-image {
			width: 200rpx;
			height: 186rpx;
			margin-bottom: 50rpx;
		}
	}
</style>