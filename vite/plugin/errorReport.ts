
import { transformSync, template  } from '@babel/core';
import { createFilter } from 'vite';
// 自定义插件
export function tryCatchErrorReportPlugin(options: any = {}) {
  const {
    reportFunction = 'uni.$reportError', // 上报函数名
    include = ['**/*.js', '**/*.ts', '**/*.vue'], // 匹配文件
  } = options;

  const filter = createFilter(include, ['node_modules/**']);

  return {
    name: 'uni-try-catch-error-report',
    transform(code: string, id: number | string) {
      if (!filter(id)) return;

      const result = transformSync(code, {
        plugins: [[addErrorReportToTryCatch, { reportFunction }]],
        filename: id,
      });

      return result?.code;
    },
  };
}

// Babel 插件：在 catch 块中添加错误上报
const addErrorReportToTryCatch = () => ({
  visitor: {
    TryStatement(path) {
      // console.log(path,'addErrorReportToTryCatch')
      const catchHandler = path.node.handler;
      if (!catchHandler) return;

      // 获取 catch 块的错误变量名（如 error、err）
      const errorParam = catchHandler.param?.name || 'error';
      if(errorParam != 'noWatch') {
        // 生成微信小程序错误上报代码（假设使用 uni.$reportError）
        const reportNode = template.statement.ast`
				  if (typeof uni.$reportError === 'function') {
				    uni.$reportError(${errorParam});
				  }
				`;

        // 将上报代码插入到 catch 块的开头
        catchHandler.body.body.unshift(reportNode);
      }
    },
  },
});
