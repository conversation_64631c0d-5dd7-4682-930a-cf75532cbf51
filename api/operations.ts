import http from '@/utils/http'
import { successCallbackResult } from '@/types'

export type DateData = {
  dateType: string, // 查询年：year；查询季： quarter；查询月： month；查询日： day；查询自定义： range
  year?: number,
  quarter?: number,
  month?: number,
  atime?: string,
  startDate?: string,
  endDate?: string,
}

export type QueryOrgDataParams = DateData & {
  orgId: number,
  level: string // 区域层级 0 ：集团， 10：事业部，20：战区，30：大区，40：城区
}

export type QueryOrgDataRes = {
  dataName: string,
  unit: string,
  actualValue: number, // 率的返回的是乘100后的，需要前端拼接%，没有预算的指标则返回空
  valueCompletion: number, // 率的返回的是乘100后的，需要前端拼接%，没有预算的指标则返回空
  valueBudget: number,
  valueTb: number, // 率的返回的是乘100后的，需要前端拼接%
  valueHb: number, // 率的返回的是乘100后的，需要前端拼接%
  valueRank: string,
  dataDesc: string, // 指标描述
  [k : string] : any
}

export type QueryOrgRankParams = QueryBrandRankParams & {
  tabType?: string, // 10 查询事业部排行；20 查询战区排行；30 查询大区排行；40 查询城区排行；50 查询门店排行；
}

export type QueryOrgRankRes = {
  orgId: number,
  orgName: string,
  level: string,
  comparetLastMonth: number, // 较上月排名上升或下降多少
  orgUserName: string,
  gmv: number,
  gmvCompletionRate: number, // 保留2位小数，不带%
  occ: number, // 保留2位小数，不带%
  adr: number,
  revpar: number,
  crs: number,
  [k : string] : any
}

export type QueryBrandRankParams = {
  orgId?: string,
  level?: string,
  orderBy: number, // 1 gmv; 2 occ ;3 adr ;4 revpar; 5 crs;
  sortBy?: string, //  asc 指标升序，desc指标降序
  pageNum: number,
  pageSize: number
}

export type QueryBrandRankRes = {
  brandId: string,
  brandName: string,
  gmv: number,
  occ: number, // 保留2位小数，不带%
  adr: number,
  revpar: number,
  crs: number,
  [k : string] : any
}

export type QueryKeyIndexParams = {
  orgId?: string,
  level?: string,
  dataType: number, // 1 gmv; 2 occ ;3 adr ;4 revpar; 5 crs;
  dateType?: string, // day （查询当月每天的数据和对应日期-7天的同比数据）； month（查询当年各月实际值和完成率和去年同期月的完成率）；
}

export type QueryKeyIndexRes = {
  slotDate: string,
  actualValue: number, // 日/月实际值
  termValue: number, // 日-7/去年同月实际值
  actualValueCompletionRate: number, // 预算完成率，不带%
  [k : string] : any
}

export type QueryKeyDriveParams = QueryKeyIndexParams

export type QueryKeyDriveRes = {
  slotDate: string,
  operationShopCount: number, // 运营门店数
  nightAuditReate: number, // 夜审率
  addPoint: number, // 新增均分
  groupOrderRate: number, // 集团贡献占比
  directOrderRate: number, // 直销占比
  memberOrderRate: number, // 线下会员占比
  distributionOrderRate: number, // 分销占比
  groupOrderRateTb: number, // 集团贡献同比
  groupOrderRateHb: number, // 集团贡献环比
  groupNightSaleCount: number, // 集团贡献出租间夜
  groupNightSaleTb: number, // 集团贡献出租间夜同比
  groupNightSaleHb: number, // 集团贡献出租间夜环比
  agreementOrderRate: number, // 协议占比
  agreementOrderRateTb: number, // 协议占比同比
  agreementOrderRateHb: number, // 协议占比环比
  agreementNightSaleCount: number, // 协议出租间夜数
  agreementNightSaleTb: number, // 协议出租间夜同比
  agreementNightSaleHb: number, // 协议出租间夜环比
  fitOrderRate: number, // 散客占比
  fitOrderRateTb: number, // 散客占比同比
  fitOrderRateHb: number, // 散客占比环比
  fitNightSaleCount: number, // 散客出租间夜数
  fitNightSaleTb: number, // 散客出租间夜同比
  fitNightSaleHb: number, // 散客出租间夜数环比
  otherOrderRate: number, //  其它订单占比
  [k : string] : any
}

export type GetOrgListRes = {
  id: number,
  orgId: number,
  orgName: string,
  parentId: number,
  level: string,
  [k : string] : any
}

// 数据驾驶仓运营指标查询
export const queryOrgData = (data : QueryOrgDataParams) : Promise<successCallbackResult> => {
	return http({
		url: '/operation/queryOrgData',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱查询组织运营指标排行榜
export const queryOrgRank = (data : QueryOrgRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/operation/queryOrgRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 数据驾驶舱查询品牌运营指标排行榜
export const queryBrandRank = (data : QueryBrandRankParams) : Promise<successCallbackResult> => {
	return http({
		url: '/operation/queryBrandRank',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 动因分析关键指标查询
export const queryKeyIndex = (data : QueryKeyIndexParams) : Promise<successCallbackResult> => {
	return http({
		url: '/operation/queryKeyIndex',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 动因分析关键驱动因素查询
export const queryKeyDrive = (data : QueryKeyDriveParams) : Promise<successCallbackResult> => {
	return http({
		url: '/operation/queryKeyDrive',
		method: 'GET',
		service: 'qd',
		data
	})
}

// 动因分析关键驱动因素查询
export const getOrgList = (data : {parentId: string}) : Promise<successCallbackResult> => {
	return http({
		url: '/miniOrg/list',
		method: 'GET',
		service: 'qd',
		data
	})
}
