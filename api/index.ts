import http, { httpUpload } from '@/utils/http'
import {dataType, successCallbackResult} from '../types'

// 登陆
export const login = (data: dataType): Promise<successCallbackResult> => {
	return http({
		url: '/login',
		method: 'POST',
		data
	})
}

// 上传图片
// export const fileUpload = (data: dataType): Promise<successCallbackResult> => {
// 	return httpUpload({
// 		url: '/file/fileUpload',
// 		method: 'POST',
// 		...data
// 	})
// }

// 刷新token
export const refreshToken = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/refreshToken',
		method: 'POST',
		data
	})
}

// 获取未读消息数
export const queryUnReadMessageCount = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/messageCenter/queryUnReadMessageCount',
		method: 'POST',
		service: 'sh',
		data
	})
}

// 获取banner
export const listHomePageBanner = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/banner/listHomePageBanner',
		method: 'POST',
		service: 'sh',
		data
	})
}


export const uploadImage = (data : dataType) : Promise<successCallbackResult> => {
	return http({
		url: 'https://smart-operating-web-test.ethank.com.cn/file/fileUpload',
		method: 'POST',
		data
	})
}

// 小程序查询拥有的权限接口
export const getUserPages = () : Promise<successCallbackResult> => {
	return http({
		url: '/miniUser/getUserPages',
		method: 'GET',
		service: 'qd',
	})
}

/**
 * @description: 前端报错告警推送企业微信告警群
 */

export const pushError = (data ?: dataType) : Promise<successCallbackResult> => {
	return http({
		url: '/notice/errorPush',
		method: 'POST',
		service: 'sh',
		data
	})
}